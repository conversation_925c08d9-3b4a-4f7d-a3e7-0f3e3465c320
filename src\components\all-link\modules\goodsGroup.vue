<template>
  <div class="product-link">
    <el-row v-if="!inSelScope.eq" class="mb-10">
      <el-alert :title="inSelScope.tip" :closable="false" type="warning" center show-icon></el-alert>
    </el-row>
    <el-collapse v-if="seledShow" v-show="selectData.length" v-model="collShow">
      <el-collapse-item name="group">
        <template slot="title">
          <i class="header-icon el-icon-sold-out ml-10"></i>
          <el-tooltip placement="top" :open-delay="500">
            <div slot="content">
              <h4>小提示：</h4>
              <ul class="tips ml-20">
                <li v-if="!radio">已选商品组列表，可拖动排序；</li>
                <li>
                  <span>点击“商品数”图标，查看商品详情列表；</span>
                  <ul class="tips-disc ml-20">
                    <li>商品详情列表，可拖动排序；</li>
                  </ul>
                </li>
                <li v-if="!autoCommit">
                  点击提交按钮“
                  <i class="el-icon-check"></i>”，提交已选项。
                </li>
              </ul>
            </div>
            <b class="el-message-box__content">
              <i class="el-icon-info"></i>
              <span>已选商品组</span>
            </b>
          </el-tooltip>
          <el-badge :value="selectData.length" type="success"></el-badge>
        </template>
        <el-table
          :data="selectData"
          :loading="loading"
          :max-height="tabHeight.seled"
          class="custom-table sort-tab"
          size="mini"
          highlight-current-row
          border
        >
          <el-table-column align="center" width="130%">
            <template slot="header" slot-scope="scope">
              <el-input
                v-model="search.searchSel"
                @keyup.enter.native="searchSels()"
                size="mini"
                placeholder="关键字筛选"
                clearable
              />
            </template>
            <template slot-scope="scope">
              <el-button size="mini" type="danger" icon="el-icon-delete" @click="del(scope.row)"></el-button>
            </template>
          </el-table-column>
          <el-table-column type="index" width="37%" align="right"></el-table-column>
          <el-table-column prop="code" :show-overflow-tooltip="true" label="编码" align="left"></el-table-column>
          <el-table-column prop="name" label="组名" width="180%" align="center"></el-table-column>
          <el-table-column prop="goods.length" label="商品数" width="60%" align="right">
            <template slot-scope="scope">
              <el-badge
                @click.native="goodsShow(scope.row)"
                :value="scope.row.goods.length"
                class="item goods-num"
                type="success"
              ></el-badge>
            </template>
          </el-table-column>
          <el-table-column prop="state" label="状态" width="50%" align="center">
            <template slot-scope="scope">{{ $options.filters.stateTip(scope.row.state, state) }}</template>
          </el-table-column>
        </el-table>
      </el-collapse-item>
    </el-collapse>

    <!-- 查询商品组 -->
    <el-row class="mb-10">
      <el-col :span="autoCommit ? 24 : 17">
        <el-input
          v-model="search.search"
          @keyup.enter.native="select()"
          size="mini"
          placeholder="关键字搜索"
          clearable
        >
          <el-button slot="append" icon="el-icon-search" @click="select()"></el-button>
        </el-input>
      </el-col>
      <el-col :span="7" v-if="!autoCommit">
        <div class="dialog-footer">
          <el-tooltip content="取消所选" placement="top" :open-delay="500">
            <el-button @click="cancel" icon="el-icon-close" type="info" size="mini" plain></el-button>
          </el-tooltip>
          <el-tooltip content="确定" placement="top" :open-delay="500">
            <el-button @click="confirm" icon="el-icon-check" type="primary" size="mini"></el-button>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <el-table
      ref="selTab"
      :data="list"
      @selection-change="onSelect"
      @current-change="onRadio"
      :row-class-name="tabRowCla"
      v-loading="loading"
      :max-height="tabHeight.sel"
      class="custom-table"
      size="mini"
      highlight-current-row
      border
    >
      <el-table-column v-if="!radio && selectData.length < maxSel" type="selection" width="35%"></el-table-column>
      <el-table-column type="index" align="right"></el-table-column>
      <el-table-column prop="code" :show-overflow-tooltip="true" label="编码" align="left"></el-table-column>
      <el-table-column prop="name" label="组名" align="center"></el-table-column>
      <el-table-column prop="state" label="状态" align="center">
        <template slot-scope="scope">{{ $options.filters.stateTip(scope.row.state, state) }}</template>
      </el-table-column>
      <el-table-column prop="state" label="店铺名称" align="center">
        <template slot-scope="scope">{{ scope.row.shopName}}</template>
      </el-table-column>

      <el-table-column prop="goods.length" label="商品数" width="60" align="right">
        <template slot-scope="scope">
          <el-badge
            @click.native="goodsShow(scope.row)"
            :value="scope.row.goods.length"
            class="item goods-num"
            type="success"
          ></el-badge>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      small
      :total="pagination.total"
      :page-size="pagination.size"
      :current-page="pagination.current"
      layout="prev, pager, next, jumper"
      @current-change="select"
    ></el-pagination>

    <!-- 查看商品弹窗 -->
    <el-dialog
      @opened="goodsTabOpened"
      @closed="goodsTabClose"
      :visible.sync="goodsTab.show"
      title="商品信息"
      append-to-body
      center
    >
      <el-popover
        :offset="150"
        :open-delay="1000"
        ref="goodsTabTips"
        trigger="hover"
        placement="left"
      >
        <h4>试一试：</h4>
        <ul class="tips ml-20">
          <li>按住图片放大；</li>
          <li>拖动调整顺序。</li>
        </ul>
      </el-popover>
      <el-table
        :data="selectGoods"
        v-popover:goodsTabTips
        max-height="350"
        class="custom-table sort-tab"
        size="mini"
        highlight-current-row
        border
      >
        <el-table-column type="index" width="42%" align="right"></el-table-column>
        <el-table-column
          prop="code"
          :show-overflow-tooltip="true"
          label="条码"
          width="120%"
          align="right"
        ></el-table-column>
        <el-table-column prop="showName" :show-overflow-tooltip="true" label="商品名称" align="center"></el-table-column>
        <el-table-column label="图片" width="50%" align="center">
          <template slot-scope="scope">
            <el-popover placement="right-end" trigger="focus">
              <img :src="scope.row.imageUrl" class="avatar" alt="图片" />
              <img slot="reference" :src="scope.row.imageUrl" class="avatar goods-img-min" />
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          prop="mediumPackageTitle"
          :show-overflow-tooltip="true"
          label="包装"
          width="100%"
          align="left"
        ></el-table-column>
        <el-table-column prop="availableQty" label="库存数" align="right" width="60%"></el-table-column>
        <el-table-column prop="isPromotion" label="促销" align="center" width="80%">
          <template slot-scope="scope">{{ $options.filters.isPromotion(scope.row.isPromotion)}}</template>
        </el-table-column>
        <el-table-column prop="isFragileGoods" label="易碎品" align="center" width="80%">
          <template
            slot-scope="scope"
          >{{ $options.filters.isFragileGoods(scope.row.isFragileGoods)}}</template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center" width="60%">
          <template slot-scope="scope">{{ $options.filters.stateTip(scope.row.status, gosStatus) }}</template>
        </el-table-column>
      </el-table>
      <el-pagination
        small
        :total="selectObj.total"
        :page-size="selectObj.pageSize"
        :current-page="selectObj.pageNum"
        layout="prev, pager, next"
        @current-change="changeSelect"
      ></el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import api from "api";
import Sortable from "sortablejs";

export default {
  props: {
    /**
     * @param autoCommit {@link Boolean}：单选时，是否自动提交，默认自动提交；
     * @param reqGoodsMethod {@link String}：request请求商品时，调用的方法名称；
     * @param returnGoods {@link Boolean}：是否提交商品信息（需异步请求外部接口获得，耗时），默认提交；
     * @param minSel {@link Number}：最少选择数，默认“0”；
     * @param maxSel {@link Number}：最多选择数，默认“1000”；
     * @param radio {@link Boolean}：是否单选，默认“单选”；
     * @param seledShow {@link Boolean}：是否显示已选列表；
     * @param collShow {@link Boolean}：是否展开已选列表；
     * @param pageSize {@link Number}：搜索页显示数量；
     * @param tabHeight {@link Object}：设置各列表高度；
     * @param search {@link Object}：初始搜索条件；
     * @param data {@link Object}：初始选中数据；
     */
    params: {
      type: Object,
      default() {
        return {};
      }
    },
    page_type: {
      type: String,
      default: ''
    },
    is_close: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let radio = this.params.radio == null ? true : !!this.params.radio;
    let autoCommit =
      radio &&
      (this.params.autoCommit == null ? true : !!this.params.autoCommit);
    let seledShow =
      this.params.seledShow == null ? true : !!this.params.seledShow;
    let returnGoods =
      this.params.returnGoods == null ? true : !!this.params.returnGoods;
    let minSel =
        this.params.minSel == null
          ? 0
          : this.params.minSel < 0
          ? 0
          : this.params.minSel,
      maxSel =
        this.params.maxSel == null
          ? 1000
          : this.params.maxSel < minSel
          ? minSel
          : this.params.maxSel;
    let tabHeight = this.params.tabHeight || {};
    tabHeight.sel = tabHeight.sel || 300;
    tabHeight.seled = tabHeight.seled || 180;
    return {
      loading: false,
      goodsTab: {
        show: false
      },
      search: {},
      list: [],
      state: null,
      gosStatus: null,
      pagination: {
        total: 0,
        current: 1,
        size: this.params.pageSize || 5
      },
      tabHeight,
      minSel,
      maxSel,
      radio,
      seledShow,
      autoCommit,
      returnGoods,
      reqGoodsMethod: this.params.reqGoodsMethod || "selectGoods",
      collShow: !this.params.collShow ? "" : "group", //折叠面板展开，取el-collapse-item的name属性值
      selectIds: [],
      selectData: [],
      selectObj: {
        total: 0,
        pageSize: 10,
        pageNum: 1
      },
      goods_idList: [],
      selectGoods: [],
      selectRow: {},
    };
  },
  watch: {
    is_close() {
      this.selectData = [];
      this.$refs.selTab.setCurrentRow();
    }
  },
  computed: {
    inSelScope() {
      let r = {
        eq: true
      };
      if (
        this.minSel &&
        (!this.selectData || this.selectData.length < this.minSel)
      ) {
        r.eq = false;
        r.tip = `请最少选择 ${this.minSel} 个商品组`;
      } else if (
        this.maxSel &&
        (!this.selectData || this.selectData.length > this.maxSel)
      ) {
        r.eq = false;
        r.tip = `请最多选择 ${this.maxSel} 个商品组`;
      }
      return r;
    }
  },
  methods: {
    async changeSelect(data) {
      // let pms = {
      //   skuIdList: this.goods_idList.slice(
      //     this.selectObj.pageSize * (data - 1),
      //     this.selectObj.pageSize * data
      //   )
      // };
      let pms = {
        exhibitionId: this.selectRow.code,
        branchCode: this.selectRow.branchCode,
        pageNum: data,
        pageSize: 10
      };
      let r = await api.goods[this.reqGoodsMethod](pms);
      if (r.code == 200) {
        this.selectGoods = r.data.products || [];
      } else {
        this.$notify.error({
          message: r.msg,
          dangerouslyUseHTMLString: true, //允许html
          offset: 100, //偏移
          duration: 5000
        });
      }
    },
    async select(page = 1) {
      this.loading = true;
      this.pagination.current = page;

      let pms = Object.assign(
        {
          pageFrom: this.pagination.current,
          pageSize: this.pagination.size
        },
        this.search
      );
      const r = await api.goodsGroup.select(pms) || {};
      let namePms = {
        exhibitionIds: ((r.data || {}).rows || []).map(item => {
          return item.mysql_id;
        })
      };

      const shopName = await api.stores.pullStoresName(namePms);
      let shopNameArr = [];
      if (shopName.data.success) {
        shopNameArr = shopName.data.data.shopNames;
      }
      this.loading = false;
      if (r.code == 200) {
        let page = r.data;
        this.$nextTick(() => {
          page.rows.forEach((item, index) => {
            item.shopName = shopNameArr[index];
          });
          this.list = page.rows;
          this.pagination.total = page.total;
          /* 设置选中 */
          if (
            !this.list ||
            !this.list.length ||
            !this.selectData ||
            !this.selectData.length
          )
            return;
          this.list.map((v, i) => {
            this.selectData.map((sv, j) => {
              if (sv && v.id == sv.id) {
                return setTimeout(() => {
                  let row = this.$refs.selTab;
                  if (!row) return;
                  row[!this.radio ? "toggleRowSelection" : "setCurrentRow"](v); //设置选中
                }, 100);
              }
            });
          });
        });
      } else {
        this.$notify.error({
          message: r.msg,
          dangerouslyUseHTMLString: true, //允许html
          offset: 100, //偏移
          duration: 60000
        });
      }
    },
    async getSels() {
      this.loading = true;
      let data = this.params.data;
      if (
        !data ||
        ((!data.ids || !data.ids.length) &&
          (!data.selectData || !data.selectData.length))
      )
        //"selectData"和"ids"，二者有其一
        return;
      if (!data.selectData) {
        //没有实体数据，则根据ids组请求获得
        let pms = {
          id: { _$in: data.ids } /*data.ids[0]*/
        };
        let r = await api.goodsGroup.query(pms);
        this.loading = false;
        if (r.code == 200) {
          data.selectData = r.data || [];
        } else {
          this.$notify.error({
            message: r.msg,
            dangerouslyUseHTMLString: true, //允许html
            offset: 100, //偏移
            duration: 30000
          });
        }
      }
      this.selectData = data.selectData;
    },
    /**
     * 已选商品搜索
     */
    searchSels() {
      let srch = this.search.searchSel;
      if (!srch || !(srch = srch.trim()))
        //搜索条件
        return;
      let srs = this.selectData.filter(
        item => item.search && item.search.indexOf(srch) != -1
      );
      if (!srs.length) {
        this.$message.info("无相关匹配项。");
      } else {
        this.selectData = srs;
        this.$message.success(`已为您匹配成功 ${srs.length} 项。`);
      }
    },
    /**
     * 单选事件
     */
    onRadio(val, oldVal) {
      if (this.radio && val) {
        this.selectData = [val];
        if (this.autoCommit) this.confirm();
      }
    },
    /**
     * 多选事件
     */
    onSelect(objs) {
      if (this.radio) return;
      let gos = Object.assign([], this.list);
      for (let i = 0; i < objs.length; i++) {
        let obj = objs[i];
        let id = obj.id;
        for (let j = 0; j < gos.length; j++) {
          let go = gos[j];
          if (!go) continue;
          if (id == go.id) {
            gos[j] = undefined; //清空相同元素
            break;
          }
        }
        if (this.selIdxOf(id) == -1)
          //已选中列表中不存在此id，则添加
          this.selectData.push(obj);
      }
      /* 将剩余不同元素（未选中），从this.selectData剔除 */
      for (let i = 0; i < gos.length; i++) {
        let go = gos[i];
        if (!go) continue;
        let idx = this.selIdxOf(go.id);
        if (idx >= 0) this.selectData.splice(idx, 1);
      }
    },
    /**
     * 查询所在已选ID列表的索引位置。
     * 不在列表中，则返回"-1"
     * @param id {@link Number}：查询元素；
     * @param sels {@link Array}：被查找数组；
     * @returns {number}
     */
    selIdxOf(id, sels) {
      sels = sels || this.selectData;
      if (!id || !sels || !sels.length) return -1;
      for (let i = 0; i < sels.length; i++) {
        let sel = sels[i];
        if (id == sel.id) return i;
      }
      return -1;
    },
    /**
     * 设置选中的ID组
     * @returns {Array}
     */
    changeSelId() {
      this.selectIds.length = 0;
      if (!this.selectData) return this.selectIds;
      for (let i = 0; i < this.selectData.length; i++) {
        let sel = this.selectData[i];
        this.selectIds.push(sel.id);
      }
      return this.selectIds;
    },
    async addGoodsObjs(row) {
      if (!row) return null;
      if (!row._goods) {
        // let pms = {
        //   skuIdList: row.goods.slice(
        //     this.selectObj.pageSize * (this.selectObj.pageNum - 1),
        //     this.selectObj.pageSize * this.selectObj.pageNum
        //   )
        // };
        let pms = {
          exhibitionId: row.code,
          branchCode: row.branchCode,
        };
        let r = await api.goods[this.reqGoodsMethod](pms);
        if (r.code == 200) {
          // row._goods = r.data.list || [];
          row._goods = r.data.products || [];
          this.selectObj.total = r.data.totalCount;
        } else {
          this.$notify.error({
            message: r.msg,
            dangerouslyUseHTMLString: true, //允许html
            offset: 100, //偏移
            duration: 5000
          });
        }
      }
      return row._goods;
    },
    goodsShow(row) {
      this.loading = true;
      // this.selectObj.total = row.goods.length;
      this.goods_idList = row.goods;
      this.selectRow = row;
      this.addGoodsObjs(row).then(_goods => {
        this.loading = false;
        this.goodsTab.group = row;
        this.goodsTab._goods = _goods;
        this.selectGoods = _goods;
        this.goodsTab.show = true;
      });
    },
    goodsTabOpened() {
      let tbodys = document.querySelectorAll(
        ".el-dialog .sort-tab table > tbody"
      );
      if (!tbodys || !tbodys.length) return;
      tbodys.forEach(tbody =>
        Sortable.create(tbody, {
          scrollSpeed: 10, // 滚动速度。
          scrollSensitivity: 80, // 鼠标必须靠近边缘多少px才能开始滚动。
          fallbackTolerance: 50, // 以像素为单位指定鼠标在被视为拖动之前应移动多远。
          touchStartThreshold: 50, // 在多少像素移动范围内可以取消延迟拖动事件。
          /**
           * 拖拽完成后回调
           * @description: 拖拽直接操作DOM完成后，并不会引起Vue的虚拟DOM变化。
           * 所以后续双向更新数据时，Vue根据Diff算法，会重新再次渲染真实DOM，引起冲突。
           * 参见：https://www.jianshu.com/p/d92b9efe3e6a
           */
          onEnd: e => {
            if (e.newIndex == e.oldIndex) return;
            /* 还原直接操作的真实DOM，将DOM操作交还给Vue */
            let tr = tbody.children[e.newIndex];
            let oldTr = tbody.children[e.oldIndex];
            tbody.insertBefore(
              tr,
              e.newIndex > e.oldIndex ? oldTr : oldTr.nextSibling
            );

            let vals = this.goodsTab._goods.splice(e.oldIndex, 1);
            this.goodsTab._goods.splice(e.newIndex, vals.length - 1, ...vals);
            this.goodsTab.group._goods = this.goodsTab._goods; //更新商品组
            this.goodsTab.group.goods.splice(
              0,
              this.goodsTab.group.goods.length,
              ...this.goodsTab._goods.map(item => item.id)
            );
          }
        })
      );
    },
    goodsTabClose() {
      this.goodsTab = {
        show: false
      };
    },
    del(row) {
      let idx = this.selIdxOf(row.id);
      if (idx >= 0) this.selectData.splice(idx, 1);

      let pgIdx = this.selIdxOf(row.id, this.list);
      if (pgIdx >= 0) {
        //删除当前页的选中
        if (!this.radio) this.$refs.selTab.toggleRowSelection(this.list[pgIdx]);
        //反选
        else this.$refs.selTab.setCurrentRow();
      }

      if (this.radio && this.autoCommit) this.confirm();
    },
    async dict() {
      let state = await api.goodsGroup.state();
      if (state.code == 200) this.$nextTick(() => (this.state = state.data));
      else this.$message.error(state.msg);

      let gosStatus = await api.goods.status();
      if (gosStatus.code == 200)
        this.$nextTick(() => (this.gosStatus = gosStatus.data));
      else this.$message.error(gosStatus.msg);
    },
    /**
     * 初始化加载
     */
    init() {
      if (this.params.search)
        //初始搜索条件
        this.search = Object.assign(this.search, this.params.search);
      this.getSels();
    },
    sortableInit() {
      if (this.radio || !this.seledShow) return;
      let tbodys = document.querySelectorAll(".sort-tab table > tbody");
      if (!tbodys || !tbodys.length) return;
      tbodys.forEach(tbody =>
        Sortable.create(tbody, {
          scrollSpeed: 10, // 滚动速度。
          scrollSensitivity: 80, // 鼠标必须靠近边缘多少px才能开始滚动。
          fallbackTolerance: 50, // 以像素为单位指定鼠标在被视为拖动之前应移动多远。
          touchStartThreshold: 50, // 在多少像素移动范围内可以取消延迟拖动事件。
          /**
           * 拖拽被选中时的回调
           */
          onChoose: e => {
            if (this.tabHeight.seled >= 350) return;
            if (this.timId) clearTimeout(this.timId);
            this.tabHeight.seledCache = this.tabHeight.seled; //缓存原数值
            this.tabHeight.seled = 350; //放大拖拽区域
          },
          /**
           * 拖拽完成后回调
           * @description: 拖拽直接操作DOM完成后，并不会引起Vue的虚拟DOM变化。
           * 所以后续双向更新数据时，Vue根据Diff算法，会重新再次渲染真实DOM，引起冲突。
           * 参见：https://www.jianshu.com/p/d92b9efe3e6a
           */
          onEnd: e => {
            if (e.newIndex != e.oldIndex) {
              /* 还原直接操作的真实DOM，将DOM操作交还给Vue */
              let tr = tbody.children[e.newIndex];
              let oldTr = tbody.children[e.oldIndex];
              tbody.insertBefore(
                tr,
                e.newIndex > e.oldIndex ? oldTr : oldTr.nextSibling
              );

              let vals = this.selectData.splice(e.oldIndex, 1);
              this.selectData.splice(e.newIndex, vals.length - 1, ...vals);
            }
            this.$nextTick(() => {
              if (this.tabHeight.seledCache && this.tabHeight.seledCache < 350)
                this.timId = setTimeout(() => {
                  //拖拽完成后还原区域视图
                  this.tabHeight.seled = this.tabHeight.seledCache;
                  delete this.tabHeight.seledCache;
                  this.timId = clearTimeout(this.timId);
                }, 3000);
            });
          }
        })
      );
    },
    tabRowCla({ row, i }) {
      if (row.state == -1) return "bgc-warn";
      return "";
    },
    /**
     * 确定
     */
    confirm() {
      this.loading = true;
      if (!this.inSelScope.eq) {
        this.$notify.warning({
          message: this.inSelScope.tip,
          dangerouslyUseHTMLString: true, //允许html
          offset: 200 //偏移
        });
        this.loading = false;
        return;
      }
      let r = {
        tag: "goodsGroup", //此返回值的自定义标识（以便区分其它组件的返回值）
        ids: _.cloneDeep(this.changeSelId()),
        data: _.cloneDeep(this.selectData)
      };
      let prs = new Array(!this.returnGoods ? 0 : r.data.length); //是否请求获得商品信息
      for (let i = 0, len = prs.length; i < len; i++) {
        //获取所有商品信息
        let dt = r.data[i];
        prs[i] = this.addGoodsObjs(dt).then(_goods => (dt._goods = _goods));
      }
      Promise.all(prs.map(item => item.catch(e => e))) //等待所有商品信息响应完成后，执行后续操作
        .then(rs => {
          if (this.radio) {
            //单选
            if (!r.data || !r.data.length) {
              r.data = {};
            } else {
              r.data = r.data[0];
              r.id = r.data.id;
            }
          }
          this.$emit("select", r);
          this.loading = false;
        })
        .catch(e => {
          this.$notify.error("所选商品组，提交失败。");
          this.loading = false;
        });
    },
    /**
     * 取消
     */
    cancel() {
      this.selectIds = [];
      this.selectData = [];
      this.$refs.selTab[!this.radio ? "clearSelection" : "setCurrentRow"]();
    }
  },
  mounted() {
    this.init();
    this.sortableInit();
    this.dict();
    this.select();
  },
  filters: {
    //渠道
    channelCodesFilter(val) {
      if (Array.isArray(val)) {
        if (val.length == 0) {
          return "无";
        }
        if (val.indexOf("1") != -1 && val.indexOf("2") != -1) {
          return "b2b,壹块钱";
        } else if (val.indexOf("1") != -1) {
          return "b2b";
        } else if (val.indexOf("2") != -1) {
          return "壹块钱";
        }
        return "未知";
      }
      return "无";
    },
    stateTip(state, dict) {
      return !dict ? "未知" : dict[state];
    },
    isPromotion(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isFragileGoods(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    }
  }
};
</script>
<style lang="scss" scoped rel="stylesheet/scss">
.product-link {
  border: 1px solid #0cdcdc;
  padding: 3px;
}

.tips > li {
  list-style: square;
}

.tips-disc > li {
  list-style: disc;
}

.el-badge.goods-num {
  cursor: pointer;
}

.goods-img-min {
  max-width: 100%;
  max-height: 30px;
}

.el-table .bgc-warn {
  background: oldlace;
}

.el-table .bgc-success {
  background: #f0f9eb;
}
</style>
