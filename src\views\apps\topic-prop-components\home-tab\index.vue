<template>
  <div>
    <div>
      <!--tab基础类配置组件-->
      <tab-switch :content="content" @add_list="add_list" @change_color="change_color"></tab-switch>
      <el-row :gutter="20">
        <div class="title">配置选项卡内容</div>
        <el-col :span="12">
          <el-select v-model="content.active_name"
                     size="mini"
                     placeholder="请选择">
            <el-option
                v-for="item in content.goods_list"
                :key="item.name"
                :label="item.name"
                :value="item.name">
            </el-option>
          </el-select>
        </el-col>

        <!--<el-col :span="8">-->
          <!--<el-button size="mini"-->
                     <!--type="primary"-->
                     <!--:disabled="!can_config"-->
                     <!--@click="tabIsShow=true">配置{{activeName}}-->
          <!--</el-button>-->
        <!--</el-col>-->
        <!--<el-col :span="8">-->
        <!--<el-button size="mini"-->
        <!--type="danger"-->
        <!--:disabled="!can_config"-->
        <!--@click="remove_tab">删除{{activeName}}-->
        <!--</el-button>-->
        <!--</el-col>-->
      </el-row>
    </div>
    <!--选项卡内容-->
    <el-dialog
        :title="activeName"
        :visible.sync="tabIsShow"
        :show-close="false"
        width="50%"
        :before-close="handleClose">
      <el-tabs v-model="cur_config_name" type="card" @tab-click="">
        <el-tab-pane label="商品列表配置" name="goods_config">
          <div v-if="can_config">
            <el-row :gutter="20">
              <div class="title">选择列表模式</div>
              <el-col :span="24">
                <div style="text-align: left">
                  <el-radio-group v-model="content.goods_list[currentTabIndex].type"
                                  v-if="content.goods_list[currentTabIndex]">
                    <el-radio :label="index" v-for="(item,index) in typeList" :key="index">{{item}}
                    </el-radio>
                  </el-radio-group>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <div v-if="content.goods_list[currentTabIndex].list.length">
                <div class="title" style="margin-bottom: 0">预览已选择的商品</div>
                <el-col :span="24">
                  <el-table :data="content.goods_list[currentTabIndex].list"
                            height="300"
                            ref="multipleTable">
                    <el-table-column label="药名">
                      <template slot-scope="scope">
                        <span v-if="scope.row.productName">{{scope.row.productName}}</span>
                        <span v-else>{{scope.row.showName}}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="id" label="药品ID">

                    </el-table-column>
                    <el-table-column prop="availableQty" label="库存">

                    </el-table-column>
                  </el-table>
                </el-col>
              </div>
            </el-row>
            <el-row :gutter="20">
              <div v-if="content.goods_list[currentTabIndex].goods_group.length">
                <div class="title" style="margin-bottom: 0">预览已选商品组</div>
                <el-col :span="24">
                  <el-table :data="content.goods_list[currentTabIndex].goods_group"
                            height="120"
                            ref="multipleTable">
                    <el-table-column prop="name" label="组名">
                    </el-table-column>
                    <el-table-column prop="code" label="编号">
                    </el-table-column>
                    <el-table-column label="数量">
                      <template slot-scope="scope">
                        {{scope.row.ids.length}}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </div>
            </el-row>
            <el-row :gutter="20">
              <div>
                <el-col :span="24">
                  <div style="color: red;margin-top: 10px">
                    选择商品组和选择商品只能生效一个,设置一个自动清除另一个
                  </div>
                  <!--选择商品-->
                  <all-link ref="all_link"
                            @select="onSetLink"
                            :tabs="tabs_goods_option"
                            :params="{
                productlink: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                importGoods: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                goodsGroup: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
            }"></all-link>
                </el-col>
              </div>
            </el-row>
          </div>
        </el-tab-pane>
        <el-tab-pane label="单图配置" name="banner_config">
          <div v-if="can_config">
            <el-row :gutter="20">
              <el-col :span="24">
                <img :src="content.goods_list[this.currentTabIndex].img_url" alt="">
              </el-col>
              <el-col :span="24">
                <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :on-success="onUploadImg">
                  <el-button class="btn-block" type="primary">上传单图</el-button>
                  <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                </el-upload>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="tabIsShow = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="tabIsShow = false" size="mini">确 定</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
  import base from "../base";
  import tabSwitch from './tab'
  import {common} from 'api'

  export default {
    components: {
      tabSwitch
    },
    extends: base,
    contentDefault: {
      goods_list: [
        {
          name: "活动专区",
          goods_group: [],
          list: [],
          type: 0,
          img_url: "",
        },
        {
          name: "为你推荐",
          goods_group: [],
          list: [],
          type: 0,
          img_url: "",
        },
        {
          name: "常购清单",
          goods_group: [],
          list: [],
          type: 0,
          img_url: "",
        },
      ],
      tabs: {
        default_font: "#676773",
        active_font: "#292933",
        active_line: '#00B377 ',
        tab_bg: "#ffffff",
        spread_color: "#E85B5B"
      },
      active_name: "活动专区",
      activeKey: 0
    },
    created() {

    },
    data() {
      return {
        up_loading: false,
        cur_config_name: "goods_config",
        tabIsShow: false,
        currentTabIndex: 0,
        tabs_goods_option: [
          {label: '商品', value: 'productlink'},
          {label: '导入商品', value: 'importGoods'},
          {label: '商品组', value: 'goodsGroup'}
        ],
        typeList: ['一行一列', '一行俩列'],
      }
    },
    watch: {
      tabIsShow(new_val) {
        if (!new_val) {
          // console.log(this.$refs.all_link)
          this.$refs.all_link.close_select()
        }
      },
      'content.active_name'(new_val, old_val) {
        if (new_val) {
          this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.goods_list);
          switch (new_val) {
            case "活动专区":
              this.content.activeKey = 1;
              break;
            case "爆款推荐":
              this.content.activeKey = 2;
              break;
            case "常购清单":
              this.content.activeKey = 3;
              break
          }
        }
      }
    },
    computed: {
      activeName(){
        return this.content.active_name
      },
      can_config() {
        if (this.content.goods_list.length && this.activeName) {
          return true
        } else {
          return false
        }
      },
      group_banner_list() {
        let list = _.get(this, 'content.group_banner.list')
        if (list) {
          return list
        } else {
          return [];
        }
      },
      banner_list() {
        let list = _.get(this, 'content.banner.list')
        if (list) {
          return list
        } else {
          return [];
        }
      },
    },
    methods: {
      async onUploadImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          });
          return;
        }
        this.content.goods_list[this.currentTabIndex].img_url = res.data.url
      },
      handleClose(done) {
        this.$confirm('您已配置完次选项卡了么？')
            .then(_ => {
              done();
            })
            .catch(_ => {
            });
      },
      change_color(type, color) {
        if (type === "default_font") {
          this.content.tabs.color = color
        } else if (type === "active_font") {
          this.content.tabs.active_font = color
        } else if (type === "active_line") {
          this.content.tabs.active_line = color
        } else if (type === "tab_bg") {
          this.content.tabs.tab_bg = color
        } else if (type === "spread_color") {
          this.content.tabs.spread_color = color
        }
      },
      add_list(obj) {
        this.content.goods_list.push(obj)
      },
      tab_click() {
        this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.goods_list);
      },
      remove_tab() {
        const index = common.getRepeatResult('name', this.activeName, this.content.goods_list);
        this.content.goods_list.splice(index, 1)
        if (this.can_config) {
          this.activeName = this.content.goods_list[0].name
        } else {
          this.activeName = ""
        }
      },
      onSetLink(link) {
        function handle_arr(arr = []) {
          return arr.map((item) => {
            let obj = {};
            obj.init_img_url = item.init_img_url;
            obj.imageUrl = item.imageUrl;
            obj.productName = item.productName;
            obj.showName = item.showName;
            obj.mediumPackageTitle = item.mediumPackageTitle;
            obj.fob = item.fob;
            obj.id = item.id;
            obj.availableQty = item.availableQty;
            return obj
          });
        }

        if (link.tag === "goods" || link.tag === "importGoods") {
          let _self_arr = handle_arr(link.data);
          // if (this.content.goods_list[this.currentTabIndex].list.length > 0) {
          //   this.content.goods_list[this.currentTabIndex].list =
          //       [...common.removeRepeat(this.content.goods_list[this.currentTabIndex].list, _self_arr)]
          // } else {
          //   this.content.goods_list[this.currentTabIndex].list = [..._self_arr]
          // }
          this.content.goods_list[this.currentTabIndex].list = [..._self_arr]
          this.content.goods_list[this.currentTabIndex].goods_group = []
        } else if (link.tag === "goodsGroup") {
          let obj = {};
          obj.name = link.data.name;
          obj.ids = link.data.goods;
          obj.code = link.data.code;
          this.content.goods_list[this.currentTabIndex].goods_group.splice(0, 1, obj);
          this.content.goods_list[this.currentTabIndex].list = []
        }
      },


      //备用函数
      list_handleDelete(row) {
        const index = this.content.goods_list[this.currentTabIndex].list.indexOf(row)
        this.content.goods_list[this.currentTabIndex].list.splice(index, 1)
      },
      group_delete(row) {
        const index = this.content.goods_list[this.currentTabIndex].goods_group.indexOf(row);
        this.content.goods_list[this.currentTabIndex].goods_group.splice(index, 1)

      },
    }
  }
</script>

<style scoped lang="scss">
  .el-row {
    text-align: center;

    .title {
      text-align: left;
      line-height: 30px;
      /*background-color: #f2f2f2;*/
      color: #13c2c2;
      padding-left: 10px;
      margin: 10px;
    }
  }

</style>
