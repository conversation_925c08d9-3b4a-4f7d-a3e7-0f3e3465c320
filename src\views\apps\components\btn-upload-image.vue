<template>
    <div class="upload-icon">
        <el-upload
                class="editor-slide-upload"
                :on-success="uploadImage"
                :show-file-list="false"
        >
            <el-button size="mini" type="primary" icon="el-icon-picture"></el-button>
        </el-upload>
    </div>
</template>

<script>
    export default {
        name: "uploadImage",
        props: ['index', 'image'],
        data() {
            return {
                loading: false,
                tImage: this.image || '',
            }
        },
        methods: {
            uploadImage(res, file) {
                this.tImage = res.data.url;
                this.$emit('listenImage', {index: this.index, image: this.tImage})
            }
        }
    }
</script>

<style scoped>
    .upload-icon {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-around;
    }



</style>