<template>
	<div>
		<!--品牌促销-->
		<el-row :gutter="20" class="brand-time">
            <div class="title">模块有效时间设置</div>
            <el-col :span="24">
                <el-date-picker
                    v-model="content.timevalue"
					@change="change_time"
                    type="datetimerange"
                    :picker-options="pickerOptions"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    align="right">
                </el-date-picker>
            </el-col>
        </el-row>
		<p class="blank_20"></p>
		<el-button type="primary" @click="handleAdd" size="mini">添加</el-button>
		<el-button type="primary" @click="handleDelete" size="mini">删除</el-button>
		<p class="blank_20"></p>
		<el-row>
			<el-col :span="4" style="textAlign:center">
				<em>标题名称</em>
			</el-col>
			<el-col :span="18">
				<el-input type="primary" size="mini" v-model="content.title" style="width:80%"></el-input>
			</el-col>
		</el-row>
		<p class="blank_20"></p>
		<el-table :data="list" :row-key="getRowKeys" border fit highlight-current-row style="width: 100%"
		          v-if="list.length>0"
		          @selection-change="handleSelection">
			<el-table-column
				  type="selection"
				  width="55"/>
			<el-table-column label="跳转页面" prop="data.page_url" width="280">
				<template slot-scope="scope">
					<template>
						<el-input v-model.lazy="scope.row.data.page_url" class="edit-input" size="mini" @input="changePageUrl(scope.$index)" />
					</template>
				</template>
			</el-table-column>
			<el-table-column label="图片">
				<template slot-scope="scope">
					<upload-image :index="scope.$index" :image="scope.row.image"
					              v-on:listenImage="getImage"></upload-image>
				</template>
			</el-table-column>
			<el-table-column label="开始/结束" width="240">
				<template slot-scope="scope">
					<template>
						<el-date-picker size="mini" style="width:200px"
						                v-model="scope.row.time"
										:picker-options="pickerOptions2"
						                type="datetimerange"
						                range-separator="至"
						                start-placeholder="开始日期"
						                end-placeholder="结束日期">
						</el-date-picker>
					</template>
				</template>
			</el-table-column>
		</el-table>
		<p class="blank_20"></p>
		<all-link @select="onSetLink" :tabs="tabs" :params="{
			page: {
                branchCode: topic.branchCode
            }
        }"></all-link>

	</div>
</template>

<script>
	import base from '../../base'
	import uploadImage from '../../../components/upload-image'
	import api from 'api'
	import { getUrlParam } from 'config';
	export default {
		name: "brandCollection",
		extends: base,
		contentDefault: {
			list: [],
			title:'',
			timevalue: ''
		},
		data() {
			return {
				tabs: [
					{label: '活动页', value: 'page'}
				],
				selectItem: [],
				loading: false,
				activeIndex: 0,
				pickerOptions: {
                    shortcuts: [{
                        text: '未来一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来六个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来一年',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
				},
				pickerOptions2: {
					disabledDate: (time) => {
						if(this.content.timevalue){
							return time.getTime() <= new Date(this.content.timevalue[0] - 86400000) || time.getTime() >= new Date(this.content.timevalue[1]);
						}
					}
				}
			}
		},
		computed: {
			list() {
				var list = _.get(this, 'content.list')
				if (list) {
					if (list.length > 0) {
						this.$nextTick(function () {
							this.setSort()
						})
					}
					return list
				} else {
					return []
				}
			}
		},
		components: {
			uploadImage
		},
		watch: {
			'content.list': {
				deep: true,
				handler(val) {
					if (val.length && val[this.activeIndex].data.page_url) {
						this.debounce();
					}
				}
			}
		},
		created() {
      this.debounce = _.debounce(this.changeLink, 1000);
    },
		methods: {
			change_time(){
				for (let item of this.content.list) {
					this.$set(item, "time", this.content.timevalue)
				}
				this.content.list=this.content.list.map(item=>{
					return item
				});
			},
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},
			handleAdd() {
				this.list.push({id: '', image: '', data: {page_name: '', page_url: ''}})
			},
			getRowKeys(row) {
				if (!row.id) {
					return
				}
				return row.id
			},
			onSetLink(obj) {
				if (this.selectItem.length === 0) {
					this.$message.warning('请至少选中1个品牌,再添加活动连接');
					return;
				}
				this.selectItem.forEach(item => {
					const index = this.list.indexOf(item);
					let {page_name, page_url, id} = obj.meta;
					this.$set(this.list[index], 'data', {page_name, page_url})
					this.$set(this.list[index], 'id', id)
					// this.list[index].data = {page_name, page_url};
					// this.list[index].id = id;
				});
				this.selectItem = [];
			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = this.list.indexOf(item)
					this.list.splice(index, 1)
				})
			},
			getImage(data) {
				if (data.image) {
					this.list[data.index].image = data.image
				}
			},
			changePageUrl(index) {
        this.activeIndex = index;
      },
			async changeLink() {
        if (this.content.list[this.activeIndex].data.page_url) {
          if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.content.list[this.activeIndex].data.page_url)) {
            this.$message.error('跳转链接格式不正确');
            this.content.list[this.activeIndex].data.page_url = '';
          } else {
            let linkPageUrl = getUrlParam(this.content.list[this.activeIndex].data.page_url, 'url');
            const result = await api.topic.checkPageUrl({ url: linkPageUrl });
            if (((result || {}).data || {}).status != 200) {
              this.$message.error('跳转链接不存在');
              this.content.list[this.activeIndex].data.page_url = '';
            }
          }
        }
      }
		}
	}
</script>
<style lang="scss" scoped rel="stylesheet/scss">
.el-row {
	&.brand-time{
		text-align: center;
	}

	.title {
		text-align: left;
		line-height: 30px;
		background-color: #f2f2f2;
		margin: 10px 0;
		padding-left: 10px;
	}
}
</style>
