<template>
  <div class="topic-menu-list">
    <div>
      <!--模块背景设置-->
      <el-row :gutter="20">
        <div class="title">模块背景设置</div>
        <el-col :span="8">
          <div class="block">
            <span class="demonstration">背景颜色</span>
            <div>
              <el-color-picker v-model="content.bgRes" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="block">
            <span class="demonstration">页面名颜色</span>
            <div>
              <el-color-picker v-model="content.subject_title_color" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="block">
            <span class="demonstration">标语说明颜色</span>
            <div>
              <el-color-picker v-model="content.subject_introduce_color" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>


        <el-col :span="8">
          <div class="block">
            <span class="demonstration">上传背景图</span>
            <div>
              <el-upload
                class="topic-image-upload"
                ref="upload"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                :show-file-list="false"
                :on-success="onUploadImg">
                <el-button class="btn-block" type="primary">上传背景图</el-button>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
              </el-upload>
            </div>
          </div>
        </el-col>
      </el-row>


      <el-row :gutter="20">
        <div class="title">大小title设置</div>
        <el-col :span="24">
          <div class="block">
            <span class="demonstration"></span>
            <div>
              <el-input placeholder="请输入内容" v-model="content.subject_title">
                <template slot="prepend">大title</template>
              </el-input>
            </div>
          </div>
        </el-col>
        <el-col :span="24">
          <div class="block">
            <span class="demonstration"></span>
            <div>
              <el-input placeholder="请输入内容" v-model="content.subject_introduce" style="margin-top: 5px">
                <template slot="prepend">小title</template>
              </el-input>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <div class="title">最大展示页数</div>
        <el-col :span="24">
          <el-select v-model="content.pageCount" placeholder="请选择">
            <el-option
              v-for="item in pageCount_option"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-col>
      </el-row>


    </div>
    <br>
    <!--选择商品-->
    <all-link @select="onSetLink" :tabs="tabs" :params="{
                goodsGroup: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
            }"></all-link>
  </div>
</template>

<script>
  import base from "../../base";

  export default {
    extends: base,
    contentDefault: {
      list: [],
      bgRes: "#578EE1",
      subject_title: "口碑好货",
      subject_title_color: "#ffffff",
      subject_introduce: "大家都在买的常用药",
      subject_introduce_color: "",
      branchCode:null,
      exhibitionId:null,
      pageCount:"8"
    },
    created() {
      this.content.branchCode=this.content.branchCode?this.content.branchCode:null;
      this.content.exhibitionId=this.content.exhibitionId?this.content.exhibitionId:null;
      this.content.pageCount=this.content.pageCount?this.content.pageCount:"8"
    },
    data() {
      return {
        loading: false,
        tabs: [
          {label: '商品组', value: 'goodsGroup'}
        ],
        pageCount_option:[
          {
            value: '4',
            label: '4页'
          },
          {
            value: '5',
            label: '5页'
          },
          {
            value: '6',
            label: '6页'
          },
          {
            value: '7',
            label: '7页'
          },
          {
            value: '8',
            label: '8页'
          },
        ]
      }
    },
    computed: {
      list() {
        let list = _.get(this, 'content.list')
        if (list) {
          return list
        } else {
          return [];
        }
      }
    },
    methods: {
      async onUploadImg(res, file) {
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          });
          return;
        }
        this.content.bgRes = res.data.url;
      },
      onSetLink(link) {
        this.content.branchCode=link.data.branchCode;
        this.content.exhibitionId=link.data.code;
        this.content.list=[];
      }
    }
  }
</script>


<style scoped lang="scss">

  .el-row {
    text-align: center;

    img {
      width: 100%;
    }

    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
  }

</style>
