<template>
    <div class="topic-menu-list">
        <div>
            <div>
                <div>
                    背景图片:
                    <el-button @click="toAdd" type="primary" size="mini">上传图片</el-button>
                    <!--<el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>-->
                </div>
                <div>
                    页面名颜色:
                    <el-color-picker v-model="content.subject_title_color" size="mini"></el-color-picker>
                </div>
                <div>
                    底部标语颜色:
                    <el-color-picker v-model="content.subject_introduce_color" size="mini"></el-color-picker>
                </div>
                <div>
                    加购按钮颜色:
                    <el-color-picker v-model="content.add_color" size="mini"></el-color-picker>
                </div>
                <div>
                    优惠语句背景图片:
                    <el-button @click="toAdd" type="primary" size="mini">上传图片</el-button>
                </div>
            </div>
            <el-input placeholder="请输入内容" v-model="content.subject_title" style="margin-top: 5px">
                <template slot="prepend">页面名</template>
            </el-input>

            <el-input placeholder="请输入内容" v-model="content.subject_introduce" style="margin-top: 5px">
                <template slot="prepend">标语说明</template>
            </el-input>
        </div>
        <el-table :data="list" style="width: 100%;margin-top: 5px" height="250" v-if="list.length>0"
                  ref="multipleTable">
            <el-table-column fixed label="图片" width="80">
                <template slot-scope="scope">
                    <img :src="scope.row.init_img_url" :alt="scope.row.productName" style="width:100%;max-height:50px;"
                         v-if="scope.row.init_img_url">
                    <img :src="scope.row.imageUrl" :alt="scope.row.productName" style="width:100%;max-height:50px;"
                         v-else>
                </template>
            </el-table-column>
            <el-table-column prop="productName" label="药名" width="120">
            </el-table-column>
            <el-table-column label="规格" width="80">
                <template slot-scope="scope">
                    {{scope.row.mediumPackageTitle}}
                </template>
            </el-table-column>
            <el-table-column prop="fob" label="价格" width="80">
                <template slot-scope="scope">
                    {{scope.row.fob}}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                    <div class="edit-button">
                        <el-button @click="handleDelete(scope.row)" type="warning" size="mini">删除</el-button>
                    </div>
                    <!--<div class="edit-button" style="margin-top: 5px">-->
                    <!--<el-button @click="toAdd(scope.row)" type="primary"  size="mini">上传图片</el-button>-->
                    <!--</div>-->

                </template>
            </el-table-column>
        </el-table>
        <div style="margin-top: 10px">
            <!--选择商品-->
            <all-link @select="onSetLink" :tabs="tabs" :params="{
                    productlink: {
                        minSel: 1,
                        search: {
                            status: 1,
                            branchCode: topic.branchCode
                        }
                    },
                    importGoods: {
                        minSel: 1,
                        search: {
                            status: 1,
                            branchCode: topic.branchCode
                        }
                    }
                }"></all-link>
        </div>


        <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addDialog">
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="onUploadImage">
                <span style="width: 200px">背景图片</span>
                <img v-if="content.bg_url" :src="content.bg_url" class="image">
                <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="onUploadImage_dis">
                <span>优惠语句背景图片</span>
                <img v-if="content.discount_url" :src="content.discount_url" class="image">
                <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeAddDialog">取 消</el-button>
                <el-button size="small" type="primary" @click="confirm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import base from "../base";

    export default {
        extends: base,
        contentDefault: {
            list: [],
            bg_url: "",
            subject_title: "",
            subject_title_color: "#ffffff",
            subject_introduce: "",
            subject_introduce_color: "",
            discount_url: "",
            add_color:'#000000',
        },
        data() {
            return {
                loading: false,
                tabs: [
                    {label: '商品', value: 'productlink'},
                    {label: '导入商品', value: 'importGoods'}
                ],
                addDialog: false,
                cur_index: null,
            }
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            },
            moreLink(data) {
                if (!data || !data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            }
        },
        computed: {
            list() {
                let list = _.get(this, 'content.list');
                return !list ? [] : list;
            },
        },
        methods: {
            closeAddDialog() {
                this.addDialog = false;
            },
            toRemove(data) {
                let _self = this;
                return function () {
                    _self.list.splice(_self.list.indexOf(data), 1)
                    _self.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }.confirm(_self)()
            },
            toAdd() {
                this.addDialog = true;
            },
            async onUploadImage(res) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: "warning"
                    });
                    return;
                }
                this.content.bg_url = res.data.url;

            },
            async onUploadImage_dis(res) {

                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: "warning"
                    });
                    return;
                }
                this.content.discount_url = res.data.url;

            },
            onSetLink(r) {
                this.content.list = r.data.slice(0, 1);
            },
            confirm() {
                this.closeAddDialog();
            },
            onSelect(val) {
                this.content.bgRes = this.toColor16(val);
            },
            setlink(e) {
                this.content.link.meta.page_url = e.target.value
            },
            toColor16(str) {
                if (/^(rgb|RGB)/.test(str)) {
                    var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
                    var strHex = "#";
                    for (var i = 0; i < aColor.length; i++) {
                        var hex = Number(aColor[i]).toString(16);
                        if (hex === "0") {
                            hex += hex;
                        }
                        strHex += hex;
                    }

                    if (strHex.length !== 7) {
                        strHex = str;
                    }
                    return strHex.toUpperCase();
                } else {
                    return str;
                }
            },
            handleDelete(row) {
                const index = this.list.indexOf(row)
                this.list.splice(index, 1)
            },

            change_time_limit(row) {
                let index = this.content.list.indexOf(row);
                this.$set(this.content.list[index], "limit_time", this.time_limit_arr[index].limit_time)

            },
        }
    }
</script>

<style scoped lang="scss">
    .topic-image-upload {
        .image {
            display: block;
            width: 100%;
        }

        .uploader-icon {
            width: 200px;
            height: 200px;
            line-height: 200px;
            border: 1px solid #8b8b8b;
            font-size: 50px;
        }
    }

</style>
