<template>
  <div class="fast-entry">
    <!--添加快捷入口-->
    <el-row :gutter="20">
      <div class="title">关联快捷入口</div>
      <el-col :span="8">
        <div class="block">
          <el-button class="btn-block" type="primary" @click="add_editContent = true">关联店铺</el-button>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="
              () => {
                loading = true;
                return true;
              }
            "
            :on-success="onUploadImg"
          >
            <el-button class="btn-block" type="primary" :loading="loading">替换背景图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <el-button class="btn-block" type="primary" @click="content.bgRes = ''">还原背景图</el-button>
        </div>
      </el-col>
    </el-row>
    <br />

    <!--添加快捷入口-->
    <el-row :gutter="20">
      <div class="title">预览关联店铺</div>
      <el-col :span="24">
        <el-table :data="content.list" :lazy="true" size="mini" :row-key="(row) => row.shopCode">
          <!-- <el-table-column type="index" width="30"></el-table-column> -->

          <el-table-column label="店铺logo">
            <template slot-scope="scope">
              <img
                v-if="scope.row.appLogoUrl"
                :src="scope.row.appLogoUrl"
                alt="图"
                class="title-image"
              />
              <i v-else class="el-icon-circle-plus-outline no-img"></i>
            </template>
          </el-table-column>

          <el-table-column label="序号" width="50" type="index">
            <!-- <template slot-scope="scope" style="position: relative">
              <div style="cursor: pointer" @click="handleChangeIndex(scope)">{{ scope.row.$index + 1 }}</div>
              <div class="changeIndex" v-if="scope.row.showChangeIndex">
                <el-input v-model="currentIndex" placeholder="请输入序号" size="small" style="width: 100px"></el-input>
                <el-button type="primary" size="small" style="min-width: 40px; margin-left: 10px" @click="handleSureChange(scope)">确定</el-button>
                <el-button size="small" style="min-width: 40px" @click="handleCloseChange(scope)">取消</el-button>
              </div>
            </template> -->
          </el-table-column>

          <el-table-column label="店铺状态">
            <template slot-scope="scope">
              <b v-if="scope.row.status === 2" style="color: #67C23A">已上线</b>
              <b v-if="scope.row.status === 1" style="color: red">待上线</b>
              <b v-if="scope.row.status === 3" style="color: red">已下线</b>
              <b v-if="scope.row.status === 4" style="color: red">已关闭</b>
            </template>
          </el-table-column>

          <el-table-column label="展示状态">
            <template slot-scope="scope">
              <span v-html="format_text(scope.row.timevalue)"></span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" label="有效时间" width="460">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.timevalue"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="left"
              ></el-date-picker>
              <el-button
                type="danger"
                @click="del_store(scope.row.shopCode)"
                icon="el-icon-delete"
                circle
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <br />

    <!-- 添加内容弹层 -->
    <el-dialog class="banner-dialog" width="70%" title="关联店铺" :visible.sync="add_editContent">
      <allStore
        @emitStore="handle_store"
        :stores="list"
        :params="{
          branchCode: topic.branchCode,
          page_type: topic.page_type === 'h5' ? `control-${topic.page_type}` : topic.page_type,
          content: content
        }"
      ></allStore>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="closeEditContent">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import base from "views/apps/topic-prop-components/base.vue";
import api from "api";
export default {
  extends: base,
  contentDefault: {
    list: [],
    bgRes: "",
    pro_obj: {
      pro_type: "longBar",
      pro_auto: 0,
      pro_align_type: "center",
      default_color: "#ffffff",
      default_opacity: 30,
      active_color: "#555555",
      active_opacity: 100,
      component_name: "fastEntry" //区分模块的标识
    },
    shopType: 1,
  },
  data() {
    return {
      currentIndex: '',
      loading: false,
      add_editContent: false,
      pickerOptions: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      }
    };
  },
  // created() {
  //   this.sortList();
  // },
  computed: {
    list() {
      let list = _.get(this, "content.list");
      if (list) {
        this.$nextTick(function() {
          this.setSort();
        });
        return list;
      } else {
        return [];
      }
    }
  },
  methods: {
    sortList() {
      if (this.content.list && this.content.list.length) {
        this.content.list = this.content.list.map((item, index) => {
          item.$index = index;
          return item;
        });
      }
    },
    handleSureChange(item) {
      const len = this.content.list.length;
      if (this.currentIndex && (+this.currentIndex >= 1 && +this.currentIndex <= len)) {
        this.handleCloseChange(item);
        this.content.list.splice(item.row.$index, 1);
        if (+this.currentIndex === 1) this.content.list.unshift(item.row);
        if (+this.currentIndex === len) {
          this.content.list.push(item.row);
        } 
        if (+this.currentIndex > 1 && +this.currentIndex < len) this.content.list.splice(+this.currentIndex -1, 0, item.row);
        this.currentIndex = '';
        this.sortList();
        return false;
      }
      this.handleCloseChange(item);
    },
    handleCloseChange(item) {
      this.$set(item.row, 'showChangeIndex', false);
    },
    handleChangeIndex(item) {
      this.$set(item.row, 'showChangeIndex', true);
      // item.row.showChangeIndex = true;
    },
    del_store(shopCode) {
      let text = `是否取消该关联`;
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      })
        .then(() => {
          let cur_index = 0;
          this.content.list.forEach((val, index) => {
            if (val.shopCode === shopCode) {
              cur_index = index;
            }
          });
          this.content.list.splice(cur_index, 1);
          this.$message({
            type: "success",
            message: "成功!"
          });
        })
        .catch(() => {});
    },
    handle_store(data) {
      if (data.type === "add") {
        const obj = {
          shopCode: data.data.shopCode,
          timevalue: null
        };
        this.content.list.push(Object.assign(data.data, { timevalue: null }));
      } else {
        let cur_index = 0;
        this.content.list.forEach((val, index) => {
          if (val.shopCode === data.data.shopCode) {
            cur_index = index;
          }
        });
        this.content.list.splice(cur_index, 1);
      }
    },
    format_text(data) {
      if (!data) {
        return "<b style='color: red'>请设置时间</b>";
      }
      if (!data.length) {
        return "<b style='color: red'>请设置时间</b>";
      }
      const _date = new Date().getTime();
      const start = new Date(data[0]).getTime();
      const end = new Date(data[1]).getTime();
      if (_date <= end && _date >= start) {
        return "<b style='color: #67C23A'>展示中</b>";
      } else if (_date < start) {
        return `<b style='color: #000000'>即将在${new Date(
          start
        ).toLocaleDateString()}展示</b>`;
      } else {
        return "<b style='color: #000000'>已过期</b>";
      }
    },
    closeEditContent() {
      this.add_editContent = false;
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.bgRes = res.data.url;
    }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss">
.fast-entry {
  position: relative;
  .changeIndex {
    background: #f6f6f6;
    position: absolute;
    bottom: -30px;
    left: 10px;
    padding: 10px;
    z-index: 100;
    display: flex;
  }
  .container {
    display: flex;
    align-items: center;

    .img {
      width: 65%;

      img {
        display: block;
        width: 100%;
      }
    }

    .button-list {
      margin-left: 10px;
    }
  }

  .content-setting {
    color: #fff;
    background-color: #13c2c2;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .title-image {
    width: 64px;
    height: 64px;
  }

  .topic-image-upload {
    .image {
      display: block;
      width: 100%;
    }

    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }

  .entry-name {
    width: 70%;
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  // single-upload
  .uploader-btn-state {
    text-align: center;
  }

  .topic-image-picker {
    padding: 10px 0;
  }

  .el-table {
    .cell {
      text-align: center;
      padding: 0;
    }

    th .cell {
      color: #606266;
    }
  }

  .banner-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }

    .image {
      width: 64px;
      height: 64px;
    }
  }

  .no-img {
    font-size: 35px;
    display: block;
    color: #caccd0;
  }
}

.el-loading-spinner {
  top: auto !important;
  margin-top: auto !important;
}

.el-row {
  text-align: center;

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
</style>
