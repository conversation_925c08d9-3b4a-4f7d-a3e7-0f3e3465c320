<template>
    <div class="topic-banner">
        <el-form class="data-form"  size="mini" label-width="100px" label-suffix=":">
            <el-form-item label="商品组标题">
                <el-input v-model="content.title"></el-input>
            </el-form-item>
            <el-form-item label="查看全部链接">
                <el-input v-model="content.link"></el-input>
            </el-form-item>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="文字颜色">
                        <el-color-picker v-model="content.color"></el-color-picker>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-button @click="toAdd" class="btn-block mb-10" type="primary">选择商品</el-button>
        <el-table
                :data="list"
                size="mini"
                style="width: 100%">
            <el-table-column label="商品ID" prop="productSid"></el-table-column>
            <el-table-column label="商品名称" prop="productName"></el-table-column>
            <el-table-column label="图片" width="60">
                <template slot-scope="scope"><img style="display:block;width:100%;" :src="scope.row.proPictDir">
                </template>
            </el-table-column>
            <el-table-column
                    width="80"
                    label="操作">
                <template slot-scope="scope">
                    <el-button size="mini" @click="toRemove(scope.row)" type="danger">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog class="banner-dialog" width="70%" title="选择商品" :visible.sync="addDialog">
            <el-input size="small" style="margin-bottom:5px;width: 300px;" v-model="key" placeholder="关键字">
                <!--<p style="width:60px" slot="prepend">普通查询</p>-->
                <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
            </el-input>
            <el-table size="mini" :data="itemList" highlight-current-row @selection-change="onSelect"
                      style="margin-bottom:5px" v-loading="loading">
                <el-table-column
                        type="selection"
                        width="55">
                </el-table-column>
                <el-table-column label="商品ID" prop="productSid"></el-table-column>
                <el-table-column label="商品名称" prop="productName"></el-table-column>
                <el-table-column label="品牌名称" prop="brandName"></el-table-column>
                <el-table-column label="图片" width="50">
                    <template slot-scope="scope"><img style="display:block;width:100%;" :src="scope.row.proPictDir">
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                    small
                    layout="pager"
                    :current-page="pagination.current"
                    :page-size="pagination.size"
                    :total="pagination.total"
                    @current-change="getList">
            </el-pagination>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeAddDialog">取 消</el-button>
                <el-button size="small" type="primary" @click="confirm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import base from '../base'

    export default {
        extends: base,
        contentDefault: {
            list: [],
            color: '#4A4A4A',
            bg_color: '#ffffff',
            title: '',
            link: '#',
        },
        data() {
            return {
                loading: false,
                addDialog: false,
                id: '',
                key: '',
                sku_key: '',
                itemList: [],
                pagination: {
                    size: 7,
                    current: 1,
                    total: 0
                },
                selectRows: [],
            }
        },
        computed: {
            list() {
                var list = _.get(this, 'content.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
        },
        filters: {
            link(data) {
                if (!data.id) {
                    return '';
                }
                return '已选:' + data.label + ',' + data.id + (data.desc ? ',' : '') + data.desc;
            }
        },
        methods: {
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.bg_image = res.data.url
            },
            closeAddDialog() {
                this.addDialog = false;
            },
            toRemove(data) {
                this.list.splice(this.list.indexOf(data), 1)
            },
            toAdd() {
                this.isEdit = false;
                this.addDialog = true;
            },
            async getList(page = 1) {
                this.pagination.current = page;
                this.loading = true;
                const result = await this.$http.get('http://search.playlounge.cn/product/search?v=1&appKey=100001', {
                    params: {
                        pageNum: this.pagination.current,
                        pageSize: this.pagination.size,
                        key: this.key || '',
                    }
                })
                if (result.result.result == 1) {
                    this.itemList = result.data.esProducts;
                    this.pagination.total = result.data.totalCount;
                }
                this.loading = false;
            },
            onSelect(rows) {
                this.selectRows = rows;
            },
            confirm() {
                this.closeAddDialog();
                this.selectRows.forEach(row => {
                    const flag = this.list.forEach(data => {
                        return data.productSid === row.productSid;
                    })
                    if (!flag) {
                        this.list.push(row);
                    }
                })
            },
        },
        mounted() {
            this.getList();
        }
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .topic-banner {
        .data-form {
            padding-right: 30px;
        }
        .container {
            display: flex;
            align-items: center;
            .img {
                width: 65%;
                img {
                    display: block;
                    width: 100%;
                }
            }
            .button-list {
                margin-left: 10px;
            }
        }
        .link-desc {
        }
        .topic-image-upload {
            .image {
                display: block;
                width: 100%;
            }
            .uploader-icon {
                width: 200px;
                height: 200px;
                line-height: 200px;
                border: 1px solid $border-base;
                font-size: 50px;
            }
        }
        .topic-image-picker {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }

</style>
<style lang="scss" rel="stylesheet/scss">
    .topic-banner {
        .banner-dialog {
            .el-dialog__body {
                padding-top: 10px;
            }
        }
    }
</style>
