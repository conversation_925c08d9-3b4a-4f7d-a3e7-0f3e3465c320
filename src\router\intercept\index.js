function intercept(router, Vue) {
    var regexp1 = new RegExp("[/]apps[/]topic|topicPC[/][0-9]{1,4}");
    var regexp2 = new RegExp("[/]apps[/]topic|topicPC[/]start[/][0-9]{1,4}");
    router.beforeEach((to, from, next) => {
      let nowUrl = to.fullPath;
      //如果登录或者页面详情页
      if (nowUrl == '/auth/login' || nowUrl.indexOf("/sys/org") != -1 || regexp1.exec(nowUrl) || regexp2.exec(nowUrl)) {
          next();
          return;
      }
      let temp = -1;
      const menuList = JSON.parse(localStorage.getItem('menuList'));
      if (!menuList || menuList.length == 0) {
          window.location.href = "/";
          return;
      }
      /*if (nowUrl == "/auth/login"){
          console.log(nowUrl)
          let sts = menuList[0].actionUrl.split("/apps/");
          console.log(sts)
          window.location.href = sts[1];
          return;
      }*/
      for (let i = 0; i <menuList.length; i ++) {
          if (nowUrl == menuList[i].actionUrl) {
              temp = 0;
              break;
          }
      }
      if (temp == 0) {
          next();
      } else {
          window.location.href = "/";
      }
  });
  router.afterEach((to, from) => {
  })
}

export default intercept
