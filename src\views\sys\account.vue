<template>
    <div>
        <div class="info-form" v-loading="loading">
            <el-row>
                <div class="grid-content module-title">
                    <span>{{accountInfo.realName}}</span>
                    <!--<i class="el-icon-edit-outline" @click="showDialog = true;accountForm.realName=accountInfo.realName;"></i>-->
                </div>
            </el-row>
            <el-row :gutter="40">
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">角色</div>
                        <span>{{accountInfo.roleNames}}</span>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">密码</div>
                        <el-button v-if="isAdmin" size="mini" type="text" @click="toPwdDialog">修改密码</el-button>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="40">
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">手机</div>
                        <span>{{accountInfo.mobile}}</span>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">邮箱</div>
                        <span>{{accountInfo.mail}}</span>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="40">
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">上次登录</div>
                        <span>{{accountInfo.lastLogin | dateFmt}}</span>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">创建时间</div>
                        <span>{{accountInfo.create_time | dateFmt}}</span>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="40">
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">默认配置地区</div>
                        <span>{{accountInfo.defaultBranch && accountInfo.defaultBranch.branchName || '全部'}}</span>
                        <span @click="modify" class="modify">修改</span>
                    </div>
                </el-col>
            </el-row>
        </div>
        <el-dialog
                title="设置默认配置地区"
                :visible.sync="has_area"
                class="info-form-dialog"
                width="660px">
            <el-radio v-for="item in branchs" :key="item.branchCode" v-model="branchCode" :label="item.branchCode">{{item.branchName}}</el-radio>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="has_area = false">取 消</el-button>
                <el-button size="small" type="primary" :loading="sending" @click="handleArea">{{sending ? '正在提交...' : '确定'}}</el-button>
            </span>
        </el-dialog>
        <el-dialog
                title="编辑账号信息"
                :visible.sync="showDialog"
                width="460px">
            <el-form class="dialog-form" :model="accountForm" :rules="rules" ref="dataForm" label-width="90px"
                     label-position="left" size="small">
                <el-form-item label="名称" prop="realName">
                    <el-input v-model="accountForm.realName"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="showDialog = false">取 消</el-button>
                <el-button size="small" type="primary" :loading="sending" @click="save">{{sending ? '正在提交...' : '确定'}}</el-button>
            </span>
        </el-dialog>
        <el-dialog
                title="修改密码"
                :visible.sync="showPwdDialog"
                :close-on-click-modal="false"
                @close="pwdForm = {}"
                width="460px">
            <el-form class="dialog-form" :model="pwdForm" :rules="pwdRules" ref="data1Form" label-width="90px" label-position="left" size="small">
                <el-form-item label="原密码" prop="oldPwd">
                    <el-input type="password" v-model="pwdForm.oldPwd" placeholder="请输入原密码"></el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="password">
                    <el-input type="password" v-model="pwdForm.password" placeholder="英文、数字及英文符号，不能含空格等"></el-input>
                </el-form-item>
                <el-form-item label="确认新密码" prop="checkPass">
                    <el-input type="password" v-model="pwdForm.checkPass" placeholder="请再次输入新密码"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="showPwdDialog = false">取 消</el-button>
                <el-button size="small" type="primary" :loading="sending" @click="updatePwd">{{sending ? '正在提交...' : '确定'}}</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
    import api from 'api';
    import md5 from 'md5';
    import { fetch, getDate } from '../../utils/time-format';

    export default {
        name: 'Home',
        data() {
            return {
                isAdmin : false,
                has_area: false,
                sending: false,
                loading: false,
                showDialog: false,
                branchCode: '',
                accountInfo: {},
                accountForm: {},
                branchs: [],
                rules: {
                    realName: [
                        { required: true, message: '请填写名称', trigger: 'blur' }
                    ]
                },
                showPwdDialog: false,
                pwdForm: {},
                pwdRules: {
                    oldPwd: [
                        { required: true, message: '请输入原密码', trigger: 'blur' },
                        { min: 5, max: 20, message: '长度应为5-20个字符', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请输入新密码', trigger: 'blur' },
                        { min: 5, max: 20, message: '长度应为5-20个字符', trigger: 'blur' },
                        {
	                        validator: (rule, val, callback) => {
	                        	if (val == this.pwdForm.oldPwd)
			                        var e = new Error('新密码不允许和原密码相同');
		                        return callback(e);
	                        }, trigger: 'blur'
                        }, {
		                    validator: (rule, val, callback) => {
			                    if (/\s/.test(val)
				                    || !/^[A-Za-z\d\.\$_@,;!&]+$/.test(val))
				                    var e = new Error('密码只能包含英文、数字及部分英文符号（"_", ",", ".", "$", "@", ";", "!", "&"），且不能含有空格等');
			                    return callback(e);
		                    }, trigger: 'blur'
	                    }
                    ],
                    checkPass: [
	                    { required: true, message: '请再次确认密码', trigger: 'blur' },
	                    {
		                    validator: (rule, val, callback) => {
			                    if (val !== this.pwdForm.password)
				                    var e = new Error('两次输入密码不一致');
			                    return callback(e);
		                    }, trigger: 'blur'
	                    }
                    ]
                }
            }
        },
        computed: {
            currentAccount() {
                return this.$store.getters[ 'sys/currentAccount' ]
            }
        },
	    filters: {
		    dateFmt(date) {
			    return date ? getDate(date) : '';
		    }
	    },
        mounted() {
            this.current()
        },
        methods: {
            async current(){
                this.loading = true;
                const result = await api.user.current();
                this.loading = false;
                if (result.code === 200) {
                    this.accountInfo = result.data;
                    this.branchCode = result.data.defaultBranch && result.data.defaultBranch.branchCode ? result.data.defaultBranch.branchCode: '';
                    this.$store.dispatch('sys/updateCurrentMember', result.data);
                    if ('admin' == result.data.userName && result.data.id == -1) {
                        this.isAdmin = true;
                    }
                } else {
                    this.$message.error(result.msg);
                }
            },
            async handleArea (){
                this.sending = true;
                let arr = this.branchs.filter(ele => {
                    if(this.branchCode == ele.branchCode){
                        return {
                            branchName: ele.branchName,
                            branchCode: ele.branchCode
                        }
                    }
                })
                const result = await api.user.editDefaultBranch( this.accountInfo.id, arr[0]);
                this.sending = false;
                this.has_area = false;
                if (result.code === 200) {
                    this.current()
                } else {
                    this.$message.error(result.msg);
                }
            },
            async modify(){
                this.branchs = [{branchName: '全部',branchCode: ''}]
                let bho = await api.dict.branchHasOpen();
                if (bho.code == 200)
                    this.$nextTick(() => {
                        this.has_area = true;
                        bho.data.forEach(element => {
                            this.branchs.push({
                                branchName: element.branchName,
                                branchCode: element.branchCode
                            })
                        });
                    });
                else
                    this.$message.error(bho.msg);
            },
            toPwdDialog() {
                this.showPwdDialog = true;
                this.pwdForm = {};
                this.$nextTick(() => this.$refs.pwdForm.resetFields());
            },
            async save() {
                this.$refs.dataForm.validate(async (valid) => {
                    if (!valid)
	                    return false;
                    const result = await api.user.update(this.accountInfo.id, {
                        realName: this.accountForm.realName
                    });
                    if (result.code == 200) {
                        this.$message.success('修改成功');
                        this.showDialog = false;
                        this.accountInfo.realName = this.accountForm.realName;
                    } else {
                        this.$message.error(result.msg);
                    }
                });
            },
            async updatePwd() {
                this.$refs.data1Form.validate(async (valid) => {
                    if (!valid)
	                    return false;
                    const params = {
	                    userName: this.accountInfo.userName,
                        password: md5(this.pwdForm.oldPwd)
                    };
                    let user = await api.user.list(params);
                    if (user.code != 200 || !user.data.rows.length) {
	                    this.$message.error("旧密码输入错误");
	                    return false;
                    }
                    let userentry = user.data.rows[0];
                    userentry.password = md5(this.pwdForm.password);
                    const result = await api.user.update(this.accountInfo.id,userentry);
                    if (result.code == 200) {
                        this.$message.success('密码修改成功');
                        this.showPwdDialog = false;
                        this.$router.replace('/auth/login');
                    } else {
                        this.$message.error(result.msg);
                    }
                });
            }
        }
    }
</script>
<style lang="scss" rel="stylesheet/scss">
.info-form {
    .modify {
        margin-left: 66px;
        color: #17C2C2;
        cursor: pointer;
    }
}
    .info-form-dialog .el-dialog__body .el-radio{
        width: 170px;
    }

</style>
