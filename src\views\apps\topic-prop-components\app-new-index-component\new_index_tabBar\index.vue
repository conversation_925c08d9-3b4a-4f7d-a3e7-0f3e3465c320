<template>
  <section>
    <!-- 文字颜色 文字hover 颜色选择 -->
    <div>
      背景颜色:
      <el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>文字颜色:
      <el-color-picker v-model="content.color" size="mini"></el-color-picker>文字选中颜色:
      <el-color-picker v-model="content.hoverColor" size="mini"></el-color-picker>
    </div>
    <p class="blank_10"></p>
    <!-- 背景图片上传 -->
    <el-upload
      class="topic-pic-upload"
      ref="upload"
      accept="image/jpeg,image/jpg, image/png, image/gif"
      :max-size="1"
      :show-file-list="false"
      :before-upload="() => {loading = true; return true;}"
      :on-success="onUploadImage"
    >
      <el-button class="btn-block" type="primary" size="mini" :loading="loading">上传背景图</el-button>
      <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
    </el-upload>
    <p class="blank_10"></p>
    <el-table
      :data="dataList"
      style="width: 100%"
      height="250"
      v-if="dataList.length>0"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="30"></el-table-column>
      <el-table-column fixed label="默认/点击" width="120">
        <template slot-scope="scope">
          <div class="pic-select">
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = false; return true;}"
              :on-success="function(res,file){
                                if (res.code !== 200) {
                                    $message({
                                        message: `[${res.code}]${res.msg}`,
                                        type: 'warning'
                                    })
                                    return;
                                }
                            scope.row.image=res.data.url
                            }"
            >
              <img v-if="scope.row.image" :src="scope.row.image" class="image" />
              <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :max-size="1"
              :show-file-list="false"
              :before-upload="() => {loading = false; return true;}"
              :on-success="function(res,file){
                                if (res.code !== 200) {
                                    $message({
                                        message: `[${res.code}]${res.msg}`,
                                        type: 'warning'
                                    })
                                    return;
                                }
                            scope.row.hoverImage=res.data.url
                            }"
            >
              <img v-if="scope.row.hoverImage" :src="scope.row.hoverImage" class="image" />
              <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="文字" width="100" prop="text">
        <template slot-scope="scope">
          <el-input type="text" size="mini" v-model="scope.row.text">{{scope.row.text}}</el-input>
        </template>
      </el-table-column>
      <el-table-column label="链接地址">
        <template slot-scope="scope">
          <p class="icon-text"></p>
          <el-input type="text" size="mini" v-model="scope.row.link">{{scope.row.link}}</el-input>
        </template>
      </el-table-column>
    </el-table>
    <!-- 连接地址选择 -->
    <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
    {{ bubbleDataList.length }}
    <el-table
      :data="bubbleDataList"
      style="width: 100%"
      height="250"
      v-if="bubbleDataList.length > 0"
      ref="multipleTable"
    >
     <el-table-column label="位置" width="150" prop="position">
        <template slot-scope="scope">
          <p>首页底部Tab</p>
        </template>
      </el-table-column>
      <el-table-column label="模块" width="200" prop="bubbleSelectIndex">
        <template slot-scope="scope">
            <el-select
            v-model="scope.row.bubbleSelectIndex"
            placeholder="请选择按钮模块"
            size="mini"
            clearable
            :disabled="scope.row.bubbleStatus === 1"  
            >
            <el-option
                v-for="item in moduleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            ></el-option>
            </el-select>
        </template>
      </el-table-column>
      <el-table-column label="气泡提示文案" prop="bubbleTips">
        <template slot-scope="scope">
            <el-input
                type="text"
                size="mini"
                v-model="scope.row.bubbleTips"
                maxlength="20"
                clearable
                :disabled="scope.row.bubbleStatus === 1" 
            >
            </el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="50" prop="bubbleStatus">
        <template slot-scope="scope">
          <el-button 
            type="text" 
            @click="handleStatusChange(scope.row.bubbleStatus,scope.$index)">
            {{ scope.row.bubbleStatus === 1 ? "下线" : "上线" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </section>
</template>
<script>
import base from "../../base";

export default {
  extends: base,
  contentDefault: {
    list: [],
    color: "#a0a0a0", //文字颜色
    hoverColor: "#00dc82", //文字选中颜色,
    bgColor: "#FFFFFF", //背景颜色,
    bgImage: "",
    bubbleList: [] // 添加气泡数据列表的初始化
  },
  data() {
    return {
      textArr: [
        {
          image:
            "http://upload.ybm100.com/ybm/applayoutbanner/6ef594e4-6ace-47d8-b2b1-a01753cefa72.png",
          hoverImage:
            "http://upload.ybm100.com/ybm/applayoutbanner/337305ab-643f-439e-840e-43cbfe4bb839.png",
          text: "首页",
          link: ""
        },
        {
          image:
            "http://upload.ybm100.com/ybm/applayoutbanner/968f7c39-a2d7-48b6-821b-55d74725144b.png",
          hoverImage:
            "http://upload.ybm100.com/ybm/applayoutbanner/b3385977-d242-45be-af36-6bd866a64901.png",
          text: "全部药品",
          link: ""
        },
        {
          image:
            "http://upload.ybm100.com/ybm/applayoutbanner/75e5dad6-35d9-41ba-90c0-6938bf4f29a6.png",
          hoverImage:
            "http://upload.ybm100.com/ybm/applayoutbanner/6b8813e6-64e8-42c7-a733-d1c786584ef3.png",
          text: "发现",
          link: ""
        },
        {
          image:
            "http://upload.ybm100.com/ybm/applayoutbanner/29b346bc-dbfc-438c-8e5d-51f138770f0e.png",
          hoverImage:
            "http://upload.ybm100.com/ybm/applayoutbanner/b6036602-da6a-4ad4-b8aa-89e2b312b97b.png",
          text: "采购单",
          link: ""
        },
        {
          image:
            "http://upload.ybm100.com/ybm/applayoutbanner/0cb45f8a-9222-4385-9b60-f62417a4cd7e.png",
          hoverImage:
            "http://upload.ybm100.com/ybm/applayoutbanner/df10896d-db9d-420d-a33d-4d37beaa534c.png",
          text: "我的",
          link: ""
        }
      ],
      loading: false,
      selectionTable: [], //请选中的图标列表添加连接
      moduleOptions: [
          { value: 1, label: '一键补货' },
          { value: 2, label: '购物车' },
          { value: 3, label: '订单' },
          { value: 4, label: '我的' }
      ]
    };
  },
  computed: {
    dataList() {
      var dataList = _.get(this, "content.list");
      if (dataList) {
        return dataList;
      } else {
        return [];
      }
    },
    bubbleDataList() {
      var bubbleDataList = _.get(this, "content.bubbleList");
      if (bubbleDataList) {
        return bubbleDataList;
      } else {
        return [];
      }
    }
  },
  created() {
    if (this.content.list.length === 0) {
      this.initDataList();
    }
    
    if (this.content.bubbleList && this.content.bubbleList.length > 0) return
    this.initBubbleDataList();
  },
  methods: {
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.bgImage = res.data.url;
      this.content.bgColor = "";
    },
    handleSelectionChange(val) {
      this.selectionTable = val;
    },
    initDataList() {
      for (var i = 0; i < this.textArr.length; i++) {
        if (!this.dataList[i]) {
          this.dataList[i] = {};
        }
        this.dataList[i] = {
          image: this.textArr[i].image,
          hoverImage: this.textArr[i].hoverImage,
          text: this.textArr[i].text,
          link: this.textArr[i].link
        };
      }
    },
    selectUrl(link) {
      this.selectionTable.map((item, array, index) => {
        let dataListIndex = this.dataList.indexOf(item);
        if (dataListIndex >= 0) {
          this.dataList[dataListIndex].link = link.meta.page_url;
        }
      });
    },
    onSetLink(link) {
      this.selectUrl(link);
      this.$refs.multipleTable.clearSelection(); //清空选项
      this.content.list = this.dataList;
    },
    initBubbleDataList() {

      if (!this.content.bubbleList) {
        this.$set(this.content, 'bubbleList', []);
      }

      this.content.bubbleList.push({
        bubbleTips: "",
        bubbleStatus: 0,
        bubbleId: `${new Date().getTime()}_${Math.random().toString(36).substring(2, 7)}`,
        bubbleSelectIndex: "",
      });
    },
    /**
     * 修改气泡提示文案上下线状态 status为1时 上线 为 0 时下线 
     * 下线弹出提示框 确认之后改为下线 
     * @param {number | undefined} status - 目前这条记录的上下线状态
     * @param {number} index - 当前行索引
     */
    handleStatusChange(status,index) {
        if(status === 1) {
            this.$confirm("确定要下线吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
            .then(() => {
                this.$set(this.content.bubbleList[index], 'bubbleStatus', 0);
            })
            .catch(() => {});
        } else {
            this.$confirm("确定要上线吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
            .then(() => {
                this.$set(this.content.bubbleList[index], 'bubbleStatus', 1);
                this.$set(this.content.bubbleList[index], 'bubbleId', `${Date.now()}_${Math.random().toString(36).substring(2, 7)}`);

                this.$message({
                    message: "上线成功",
                    type: "success"
                });
            })
            .catch(() => {});
      }
    }
  },
  watch: {
    "content.bgColor"(new_val) {
      if (new_val) {
        this.content.bgImage = "";
      }
    }
  }
};
</script>

<style lang="scss" scoped rel="stylesheet/scss">
.topic-image-upload {
  width: 45%;

  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 100%;
    height: 100%;
    line-height: 100%;
    border: 1px solid $border-base;
    font-size: 30px;
  }
}

.topic-image-picker {
  padding-top: 10px;
  padding-bottom: 10px;
}

.icon-text {
  white-space: nowrap !important;
  text-overflow: ellipsis;
}

.pic-select {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
</style>
