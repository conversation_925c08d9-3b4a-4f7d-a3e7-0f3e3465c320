<template>
  <div>
    <el-row :gutter="20">
      <div class="title">倒计时模版设置</div>
      <div class="block">
        <el-radio v-model="content.type" label="1" @change="handleChangeType">按天/时/分/秒</el-radio>
        <el-radio v-model="content.type" label="2" @change="handleChangeType">按/时/分/秒</el-radio>
      </div>
      <div class="type-demo" v-if="content.type === '1'">
        <span>优惠仅剩：00天00时00分00秒</span>
      </div>
      <div class="type-demo" v-if="content.type === '2'">
        <span>优惠仅剩：00:00:00</span>
      </div>
    </el-row>

    <el-row :gutter="20">
      <div class="title">倒计时间设置</div>
      <el-col :span="24">
        <div class="block">
          <el-date-picker
            style="margin: auto"
            v-model="content.all_limit_time"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </div>
      </el-col>
    </el-row>

    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <!-- <el-col :span="8">
        <div class="block">
          <span class="demonstration">背景颜色</span>
          <div>
            <el-color-picker v-model="content.banner.bgRes" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>-->
      <!-- <el-col :span="8">
        <div class="block">
          <span class="demonstration">页面名颜色</span>
          <div>
            <el-color-picker v-model="content.banner.subject_title_color" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">标语说明颜色</span>
          <div>
            <el-color-picker v-model="content.banner.subject_introduce_color" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>-->

      <el-col :span="8">
        <div class="block">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :on-success="onUploadBackGround"
          >
            <el-button class="btn-block" type="primary">上传背景图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
          <el-button class="btn-block" @click="content.banner.bgRes = ''">清除背景图</el-button>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">模块图片设置</div>
      <div class="block noflex">
        <div class="uploadImage" v-if="content.banner.image">
          <img :src="content.banner.image" />
        </div>
        <el-upload
          class="topic-image-upload"
          style="display: block"
          accept="image/jpeg,image/jpg, image/png, image/gif"
          :show-file-list="false"
          :on-success="onUploadImg"
        >
          <el-button class="btn-block" type="primary">上传图片</el-button>
          <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
      </div>
    </el-row>

    <el-row :gutter="20">
      <div class="title">图片链接设置</div>
      <div class="block">
        <el-radio-group v-model="linkType">
          <el-radio :label="'link'">链接</el-radio>
          <el-radio :label="'point'">锚点</el-radio>
          <!-- <el-radio :label="'topic'">店铺</el-radio> -->
          <el-radio :label="'stores'">商详</el-radio>
        </el-radio-group>
      </div>

      <div class="topic-image-picker">
        <el-input placeholder="链接地址" v-model.trim="content.banner.imageUrl">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="linkType==='link'">
        <all-link
          @select="onSetImageLink"
          :tabs="[{label: '活动页', value: 'page'}]"
          :params="{branchCode: topic.branchCode, from: 'pc'}"
        ></all-link>
      </div>

      <div v-if="linkType==='point'" class="block">
        <el-radio-group v-model="content.banner.imageUrl" @change="handleChangeFloor">
          <el-radio-button v-for="count in coreLength" :key="count" :label="`#floor${count - 1}`">楼层{{count}}</el-radio-button>
        </el-radio-group>
      </div>

      <!-- <el-col :span="24">
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
      </el-col>-->
      <!-- <div v-if="linkType==='topic'">
        <control-page @select="onSetImageLink" :params="{branchCode: topic.branchCode}"></control-page>>
      </div> -->
    </el-row>

    <el-row :gutter="20">
      <div class="title" style="marginTop: 20px">倒计时商品关联</div>
      <div class="block" style="padding: 0; marginTop: 20px;">
        <el-table :data="dataList" style="width: 100%" height="250" ref="multipleTable">
          <el-table-column width="50" fixed type="index" label="序号"></el-table-column>

          <el-table-column label="图片">
            <template slot-scope="scope">
              <img
                :src="scope.row.imageUrl"
                :alt="scope.row.productName"
                style="width:100%;max-height:50px;"
              />
            </template>
          </el-table-column>

          <el-table-column prop="entry" label="展示状态">
            <template slot-scope="scope">
              <span v-html="format_text(content.all_limit_time)"></span>
            </template>
          </el-table-column>

          <el-table-column prop="productName" label="商品名称"></el-table-column>

          <el-table-column label="规格">
            <template slot-scope="scope">{{scope.row.mediumPackageTitle}}</template>
          </el-table-column>

          <el-table-column label="包装">
            <template slot-scope="scope">{{scope.row.mediumPackageTitle}}</template>
          </el-table-column>

          <el-table-column prop="fob" label="价格">
            <template slot-scope="scope">{{scope.row.fob}}</template>
          </el-table-column>
        </el-table>
      </div>
    </el-row>

    <!--选择商品-->
    <all-link
      @select="onSetLink"
      ref="allLinkGoodsIds"
      :tabs="tabs"
      :params="{
        productlink: {
          search: { //初始搜索条件
            status: 1,
            branchCode: topic.branchCode,
            productType : 1 //排除秒杀商品
          },
        },
        importGoods: {
          minSel: 1,
          search: {
            status: 1,
            needFilter : true,
            branchCode: topic.branchCode
          }
        }
      }"
    ></all-link>
  </div>
</template>

<script>
import base from "../../base";
import api from "api"
export default {
  extends: base,
  props: {
    coreLength: Number
  },
  contentDefault: {
    banner: {
      branchCode: "",
      exhibitionId: "",
      list: [],
      bgRes: "",
      subject_title: "值得买",
      subject_title_color: "#ffffff",
      subject_introduce: "上新限时特惠",
      subject_introduce_color: "#ffffff",
      image: "",
      imageUrl: ""
    },
    type: "1",
    branchCode: null,
    exhibitionId: null,
    all_limit_time: null
  },
  created() {
    this.content.all_limit_time ? this.content.all_limit_time : null;
    this.content.branchCode ? this.content.branchCode : null;
    this.content.exhibitionId ? this.content.exhibitionId : null;
    this.debounce = _.debounce(this.changeLink, 1000);
  },
  data() {
    return {
      linkType: "link",
      tabs: [
        { label: "商品", value: "productlink" },
        { label: "导入商品", value: "importGoods" }
      ],
      all_limit_time: null,
      pickerOptions: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      goodsGroup: []
    };
  },
  computed: {
    dataList: {
      get() {
        var dataList = _.get(this, "content.banner.list");
        if (dataList.length < this.maxLength) {
          // this.$message.error(`您选择的商品包含秒杀，请重新选择${this.maxLength}个商品`)
        }
        return dataList || [];
      },
      set(val) {
        this.content.banner.list = val;
      }
    }
  },
  methods: {
    format_text(data) {
      if (!data) {
        return "<b style='color: red'>请设置时间</b>";
      }
      const _date = new Date().getTime();
      const start = new Date(data[0]).getTime();
      const end = new Date(data[1]).getTime();
      if (_date <= end && _date >= start) {
        return "<b style='color: #13c2c2'>展示中</b>";
      } else if (_date < start) {
        return `<b style='color: #000000'>即将在${new Date(
          start
        ).toLocaleDateString()}展示</b>`;
      } else {
        return "<b style='color: #000000'>已过期</b>";
      }
    },
    handleChangeFloor(value) {
      this.content.banner.imageUrl = value;
    },
    onSetImageLink(link) {
      this.content.banner.imageUrl = link.meta.page_url;
    },
    handleChangeType(value) {
      this.type = value;
      this.content.type = value;
    },
    async onUploadBackGround(res, file) {
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.banner.bgRes = res.data.url;
    },
    async onUploadImg(res, file) {
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.banner.image = res.data.url;
    },
    onSetLink(link) {
      this.content.branchCode = link.data.branchCode;
      this.content.exhibitionId = link.data.code;
      this.content.banner.list = link.data;
    },
    async changeLink() {
      if (this.content.banner.imageUrl) {
        const result = await api.topic.checkPageUrl({ url: this.content.banner.imageUrl });
        if (((result || {}).data || {}).status != 200) {
          this.$message.error('跳转链接不存在');
          this.content.banner.imageUrl = '';
        }
        
      }
    }
  },
  //监听input输入值变化
  watch:{
    'content.banner.imageUrl': {
      handler(val, oldVal) {
        if (val && this.linkType === 'link') {
          this.debounce();
        }
      }
    }
  }
};
</script>

<style scoped lang="scss">
.el-row {
  text-align: center;

  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 35px;
    background-color: #f2f2f2;
    padding-left: 15px;
  }
  .block {
    display: flex;
    padding: 20px 15px 20px 15px;
    align-items: center;
    background: #fff;
    .widthAuto {
      width: auto;
    }
    .btn-block {
      width: auto;
    }
    .btn-block:last-child {
      margin-left: 20px;
    }
  }
  .noflex {
    display: block;
  }
  .type-demo {
    text-align: left;
    padding-left: 15px;
    margin-bottom: 20px;
    span {
      border: 1px solid #f2f2f2;
      display: inline-block;
      padding: 5px 10px;
    }
  }
  .justify {
    justify-content: center;
  }
  .topic-image-upload {
    .el-upload--text {
      display: block;
    }
  }
}
</style>
