<template>
    <div>
        <el-input size="small" style="margin-bottom:5px" v-model="key" placeholder="关键字">
            <!--<p style="width:60px" slot="prepend">普通查询</p>-->
            <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
        </el-input>

        <el-table size="mini" :data="list"  highlight-current-row  @current-change="onSelect" style="margin-bottom:5px" v-loading="loading">
            <el-table-column label="名称" prop="item_name">
                <template slot-scope="scope">
                    <p>{{scope.row.productName}}</p>
                    {{scope.row.productSid}}
                </template>
            </el-table-column>
            <el-table-column label="图片" width="100">
                <template slot-scope="scope"><img style="display:block;width:100%;" :src="scope.row.proPictDir">
                </template>
            </el-table-column>
        </el-table>

        <el-pagination
                small
                layout="pager"
                :current-page="pagination.current"
                :page-size="pagination.size"
                :total="pagination.total"
                @current-change="getList">
        </el-pagination>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                id: '',
                key: '',
                sku_key: '',
                list: [],
                pagination: {
                    size: 5,
                    current: 1,
                    total: 0
                },
                loading: false,
                manualId: ''
            }
        },
        methods: {
            async getList(page = 1) {
                this.pagination.current = page;
                this.pagination.size = 5;
                this.loading = true;
                const result = await this.$http.get('http://search.yunbaiplus.com/product/search?v=1&appKey=100001', {
                    params: {
                        pageNum: this.pagination.current,
                        pageSize: this.pagination.size,
                        key: this.key || '',
                    }
                })
                if(result.result.result == 1){
                    this.list = result.data.esProducts;
                    this.pagination.total = result.data.totalCount;
                }
                this.loading = false;
            },
            onSelect(row) {
                this.$emit('select', {
                    type: 'item',
                    label: '商品',
                    id: row.productSid,
                    desc: row.productName,
                    action: 'GO_DETAILS',
                    meta: row
                })
            }
        },
        mounted() {
            this.getList();
        },
        filters: {
            tradeType(value) {
                return {
                    1: '国内贸易',
                    2: '跨境贸易',
                }[ value ] || ''
            }
        }
    }
</script>
