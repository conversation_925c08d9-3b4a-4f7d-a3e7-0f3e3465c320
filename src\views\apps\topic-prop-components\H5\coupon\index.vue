<template>
  <div class="two-images">
    <div class="blank_10"></div>
    <label class="demonstration">背景色:</label>
    <el-color-picker v-model="content.color" size="mini"></el-color-picker>
    <br />
    <el-row :gutter="20" class="brand-time">
      <div class="title">标题设置</div>
      <el-col :span="24">
        <el-input
          size="small"
          v-model="content.title"
          placeholder="请输入标题"
          @input="()=>{
            if(content.title.length>10){
              this.$message.error('标题最大允许输入10个汉字')
              content.title=content.title.substring(0, 10)
            }
          }"
        />
      </el-col>
    </el-row>
    <!--选择模块-->
    <el-form ref="form" :model="content.form" label-width="150px">
      <el-form-item label="选择模块" prop="module">
        <el-radio-group v-model="content.form.module" @change="moduleChange">
          <el-radio :label="1">模块一</el-radio>
          <el-radio :label="2">模块二</el-radio>
          <el-radio :label="3">模块三</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排列方式" prop="orient">
        <el-radio-group v-model="content.form.orient" @change="orientChange">
          <el-radio :label="1">一排一个</el-radio>
          <el-radio :label="2">一排二个</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="价格字体" prop="fobFont">
        <el-select v-model="content.form.fobFont" placeholder="价格字体">
          <el-option v-for="(item,index) in fontStyle" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-row :gutter="20">
      <div class="title">内容设置</div>
      <el-button style="width: 300px" type="primary" size="small" @click="toEdit({}, -1, '1')">添加优惠券</el-button>
    </el-row>
    <!--优惠券列表-->
    <el-table :data="list" size="mini" :row-key="row => row.templateId" style="width: 100%; marginTop: 10px">
      <el-table-column label="图片">
        <template slot-scope="scope">
          <div class="container">
            <div class="img">
              <img v-if="scope.row.image" :src="scope.row.image" alt="图" class="title-image" />
              <i v-else class="el-icon-circle-plus-outline no-img"></i>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="券ID" prop="templateId">
        <template slot-scope="scope">
          <div>{{scope.row.templateId}}</div>
        </template>
      </el-table-column>
      <el-table-column label="人群" prop="customerGroupName">
        <template slot-scope="scope">
          <div>{{scope.row.customerGroupName}}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <div class="button-list">
            <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑</el-button>
            <el-button size="mini" @click="handleCancle(scope.row, scope.$index)" type="danger">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <coupon-settings
      v-if="addDialog"
      :visible="addDialog"
      :dataForm="dataForm"
      :isEdit="isEdit"
      :editIndex="editIndex"
      :orient="content.form.orient"
      :fileList="fileList"
      @saveDialog="saveDialog"
    ></coupon-settings>
  </div>
</template>

<script>
import base from "../../base";
import api from "api";
import { AppWebsite } from "config";
import couponSettings from './couponSettings';
export default {
  extends: base,
  components: {
    couponSettings
  },
  contentDefault: {
    color: "#ffffff",
    list: [],
    form: {
      module: 1,
      orient: 1,
      fobFont: "AVGARDD"
    },
    title:""
  },
  data() {
    return {
      addDialog: false,
      dataForm: {},
      editIndex: '',
      isEdit: false,
      fileList: [
        {
          url:
            "http://upload.ybm100.com/ybm/applayoutbanner/0156617e-a5ef-4792-bce3-40aac920da8a.png"
        }
      ],
      fontStyle: ["DINOffcPro-CondBlack", "AVGARDD"],
      dynamicConfig: {
        fob: "56", //优惠券价格
        info: "满65000元可用", //优惠信息
        couponBgColor: "#fff", // 优惠券背景颜色
        fobColor: "#000", // 价格颜色
        infoColor: "#fff", //优惠信息颜色
        typeColor: "#FF9532", // 优惠券类型颜色
        centerColor: "#FF9532", //立即领取颜色
        templateId: '',
        couponTitle: '',
        image: '',
        defaultImg: '',
      }
    };
  },
  created() {
    this.moduleChange();
  },
  computed: {
    list() {
      var list = _.get(this, "content.list");
      if (list) {
        if (list[0]) {
          this.$nextTick(function() {
            this.setSort();
          });
        }
        return list;
      } else {
        return [];
      }
    }
  },
  methods: {
    setDefaultImage() {
      this.fileList.splice(0, this.fileList.length);
      if (this.content.form.module == 1) {
        switch (this.content.form.orient) {
          case 1:
            this.fileList.push({
              url:
                "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/a9a8a03cb657538fdb9c8a909e681ebb.png"
            });
            break;
          case 2:
            this.fileList.push(
              {
                url:
                  "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/c8978b3d93f5ff362d0bb351bad7ed30.png"
              },
              {
                url:
                  "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/5cd9424fa928525a3d2a6318b8dc8f93.png"
              },
              {
                url:
                  "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/488bbedac26e9a309cd70ae75da4b50d.png"
              },
              {
                url:
                  "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/3c1e78f01f81ecc45a42dae91bdbf3b3.png"
              },
              {
                url:
                  "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/a9154d1e13e3c7832a0821b29690118a.png"
              },
              {
                url:
                  "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/7540d51c64d34682cd3495c20b218dcd.png"
              }
            );
            break;
        }
      } else if (this.content.form.module == 2) {
        switch (this.content.form.orient) {
          case 1:
            this.fileList.push({
              url:
                "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/917afa6c04d081489e064046f4b5f5ff.png"
            });
            break;
          case 2:
            this.fileList.push(
              {
                url:
                  "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/e1d9f996a5aacc0aa48320a405f56c0b.png"
              },
              {
                url:
                  "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/e0b42f8fd1d9f35143bbf0b19eb96e17.png"
              },
            );
            break;
        }
      } else if (this.content.form.module == 3) {
        switch (this.content.form.orient) {
          case 1:
            this.fileList.push({
              url:
                "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/5be7ad4e5de1f08b2c6a85679c5c332e.png"
            });
            break;
          case 2:
            this.fileList.push(
              {
                url:
                  "http://upload.ybm100.com/ybm/app/layout/cmsimages/2020-12/2b604c0a8393528bd3dbabcfc646ad3b.png"
              },
            );
            break;
        }
      }
    },
    moduleChange() {
      //切换模块，切换主题色
      let centerColor, infoColor, color, fobColor, couponBgColor, typeColor;
      if (this.content.form.module == 1) {
        centerColor = "#FF9532";
        infoColor = "#FF9532";
        color = "#fff";
        fobColor = "#000";
        couponBgColor = "#fff";
        typeColor = "#FF9532";
      } else if (this.content.form.module == 2) {
        centerColor = "#FF9B2C";
        infoColor = "#FF9B2C";
        color = "#9D9D9D";
        fobColor = "#000";
        couponBgColor = "#fff";
        typeColor = "#FF9B2C";
      } else {
        centerColor = "#fff";
        infoColor = "#E79070";
        color = "#E02C2C";
        fobColor = "#000";
        couponBgColor = "#fff";
        typeColor = "#E79070";
      }
      this.dynamicConfig.centerColor = centerColor;
      this.dynamicConfig.infoColor = infoColor;
      this.dynamicConfig.color = color;
      this.dynamicConfig.fobColor = fobColor;
      this.dynamicConfig.couponBgColor = couponBgColor;
      this.dynamicConfig.typeColor = typeColor;
      this.setDefaultImage();
    },
    orientChange() {
      this.moduleChange();
      this.content.list = [];
    },
    closeAddDialog() {
      this.addDialog = false;
    },
    toEdit(data, index, status) {
      if (status == "1") {
        if(this.list.length === 10) {
          this.$message.error('最多添加10条数据');
          return;
        }
        //新建时图片清空
        this.isEdit = false;
        this.dataForm = JSON.parse(JSON.stringify(this.dynamicConfig));
        this.editIndex = '';
      } else {
        this.isEdit = true;
        this.editIndex = index;
        this.dataForm = JSON.parse(JSON.stringify(data));
      }
      this.addDialog = true;
    },
    //删除
    handleCancle(row,index) {
      let _self = this;
      return function () {
        const index = _self.content.list.indexOf(row)
        _self.content.list.splice(index, 1)
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    saveDialog(type, psData, index) {
      this.closeAddDialog();
      if (type == 'edit') {
        this.content.list.splice(index, 1, psData)
      } else {
        let list = _.get(this, "content.list");
        if (list) {
          this.content.list.push(psData);
        } else {
          this.$set(this.content, 'list', []);
          this.content.list.push(psData);
        }
      }
    },
  }
};
</script>

<style lang="scss" scoped rel="stylesheet/scss">
.el-row {
  text-align: center;
  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
.file-list {
  @include flexbox($rowRank: flex-start, $wrap: wrap);
  li {
    margin-right: 2px;
  }
}
.two-images {
  .container {
    display: flex;
    align-items: center;
    .img {
      width: 30%;
      img {
        display: block;
        width: 300%;
        height: 60px;
      }
    }
    .button-list {
      margin-left: 10px;
    }
  }
  .el-icon-circle-plus-outline {
    font-size: 35px;
    color: #c7bdbd;
  }
  .topic-image-picker {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}
</style>
