<template>
  <div class="setMeal">
    <div class="title">
      生效时间设置
    </div>
    <div class="block">
      <el-date-picker
        v-model="content.timevalue"
        type="datetimerange"
        :picker-options="pickerOptions0"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        align="right">
      </el-date-picker>
    </div>
    <div class="title">
      模块背景设置
    </div>
    <div class="block">
      <div>边框颜色<span><el-color-picker v-model="content.borderColor" size="mini" ></el-color-picker></span></div>
      <div style="margin-left: 20px">背景色<span><el-color-picker v-model="content.bgColor" size="mini" ></el-color-picker></span></div>
      <!-- <div>滚动条默认颜色<span><el-color-picker v-model="content.scrollColor" size="mini" ></el-color-picker></span></div>
      <div>滚动条激活颜色<span><el-color-picker v-model="content.scrollActiveColor" size="mini" ></el-color-picker></span></div> -->
    </div>
    <div class="block">
      <el-upload
        class="topic-image-upload"
        ref="upload"
        accept="image/jpeg,image/jpg, image/png, image/gif"
        :show-file-list="false"
        :on-success="onUploadImg"
      >
        <el-button class="btn-block" type="primary">上传背景图</el-button>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>
      <div><el-button class="btn-block" type="primary" @click="handleClearBgImage" style="margin-left: 30px">清除背景图</el-button></div>
    </div>
    <div class="title">
      添加商品套餐
    </div>
    <div class="block">
      <div><el-button class="btn-block" type="primary" @click="handleAddMeal">添加套餐</el-button></div>
      <!-- <div style="margin-left: 30px">
        <el-upload
          class="upload-demo"
          style="display: inline-block"
          :show-file-list="false"
          :limit="1"
          :http-request="handleUploadGoogsExcelFile"
        >
          <el-button size="small" class="el-button el-button--primary">批量添加套餐</el-button>
        </el-upload>
      </div>
      <div class="download" @click="handleDownDemo">
        下载模版
      </div> -->
    </div>
    <div>
      <el-table :data="content.setMealList" :lazy="true" size="mini" @cell-click="handleCell">
        <el-table-column type="index" width="30"></el-table-column>
        <el-table-column label="套餐ID">
          <template slot-scope="scope">
            <el-autocomplete
              style="width: 100%"
              class="inline-input"
              v-model="scope.row.id"
              :fetch-suggestions="querySearchMealId"
              placeholder="请输入套餐ID"
              @select="handleSelectMeal"
            ></el-autocomplete>
          </template>
        </el-table-column>
        <el-table-column label="套餐标题">
          <template slot-scope="scope">
            <el-input v-model="scope.row.name" placeholder="套餐标题" disabled></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button class="btn-block" type="primary" @click="handleDeleteMeal(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import base from "views/apps/topic-prop-components/base.vue";
import api from "api";
import { AppWebsite } from "config";

export default {
  name: 'SetMeal',
  extends: base,
  contentDefault: {
    timevalue: '',
    borderColor: '#f77a00',
    bgColor: '#ffffff',
    scrollColor: '#ffffff',
    scrollActiveColor: '#ffffff',
    bgImage: '',
    setMealList: [],
  },
  data() {
    return {
      currentMealIndex: -1,
      index: 0,
      pickerOptions0: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
    }
  },
  mounted () {
    this.index = this.content.setMealList.length;
  },
  methods: {
    handleClearBgImage() {
      this.content.bgImage = false;
    },
    handleDownDemo() {

    },
    async handleUploadGoogsExcelFile(file) {
      let fileFormData;
      if (file && file.name!="") {
        fileFormData = new FormData();
        fileFormData.append("uploadFile", file.file, file.file.name);
        const pms = {
          url: AppWebsite + '/cms/importActivityPackage',
          dataType: "json",
          data: fileFormData,
          head: {
            "Content-Type": "application/x-www-form-urlencoded",
            terminalType: 1,
            isadmin: true,
          }
        };
        const res = await api.topic.uplodaSetMeal(fileFormData);
        const { data } = res;
        if (res.success && Object.keys(data).length) {
          
        }
      } else {
        this.$message.warning('请选择上传文件!');
      }
    },
    handleDeleteMeal(index) {
      this.content.setMealList.splice(index, 1);
      this.index = this.content.setMealList.length;
      this.content.setMealList.forEach((item, index) => {
        item.index = index;
      });((item) => {})
      this.currentMealIndex = -1;
    },
    handleCell(row) {
      this.currentMealIndex = row.index;
    },
    handleAddMeal() {
      this.index += 1;
      this.content.setMealList.push({
        id: '',
        name: '',
        index: this.index,
      });
    },
    handleSelectMeal(item) {
      // this.currentCrowd = item;
      this.content.setMealList[this.currentMealIndex - 1].id = item.id;
      this.content.setMealList[this.currentMealIndex - 1].name = item.value;
    },
    async querySearchMealId(queryString, cb) {
      const pms = {
        url: AppWebsite + `/app/sku/cms/getActivityPackageSkus?branchCode=${this.topic.branchCode}`,
        dataType: "json",
        data: {
          marketingIds: [+queryString],
        },
        head: {
          "Content-Type": "application/json;charset=UTF-8",
          terminalType: 1,
          isadmin: true,
        }
      };
      const res = await api.proxy.post(pms);
      const { data } = res;
      if (res.success && Object.keys(data).length) {
        const { activityPackageInfo } = data;
        cb([
          {
            id: queryString,
            value: activityPackageInfo[0].name || ""
          }
        ]);
        return false;
      }
      // cb([]);
    },
    async onUploadImg(res, file) {
      //  = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.bgImage = res.data.url;
    }
  }
}
</script>

<style lang="scss" scoped>
  .setMeal {
    margin-left: -10px;
    margin-right: -10px;
    .title {
      text-align: left;
      line-height: 35px;
      background-color: #f2f2f2;
      padding-left: 15px;
    }
    .block {
      display: flex;
      padding: 20px 0;
      padding-left: 15px;
      padding-right: 15px;
      align-items: center;
      .download {
        text-decoration: underline;
        color: blue;
        margin-left: 20px;
      }
    }
    .jist {
      justify-content: space-between;
    }
  }
</style>