<template>
  <header class="topbar">
    <div @click="onLogoClick" class="logo">
      <i class="iconfont icon-cms-c"></i>
      <span>CMS内容管理系统</span>
    </div>

    <ul
      class="el-menu top-menu-list el-menu--horizontal"
      v-if="hasTab"
    >
      <li
        v-for="(item,index) in ['APP端日志', 'PC端日志']"
        :key="index"
        role="menuitem"
        @click="navChange(item, index)"
        class="el-menu-item"
        :class="active_index === index ? 'is-active':''"
      >{{ item }}</li>
    </ul>

    <ul
      class="el-menu top-menu-list el-menu--horizontal"
      v-else
    >
      <li
        role="menuitem"
        @click="navChange"
        class="el-menu-item"
        :class="isShow ? 'is-active':''"
      >{{menu_now}}</li>
    </ul>

    <div class="new-float-nav">
      <el-dropdown class="user-info-wrap" @command="handleCommand">
        <span class="el-dropdown-link">
          {{currentAccount.userName}}
          <i class="el-icon-caret-bottom el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command class="user-name-wrap">
            {{currentAccount.userName}}
            <div class="user-name">{{currentAccount.roleNames}}</div>
          </el-dropdown-item>
          <el-dropdown-item :command="menu.actionUrl" v-for="menu in menuList" :key="menu.menuId">
            <i class="iconfont" :class="menu.icon"></i>
            {{menu.menuName}}
          </el-dropdown-item>
          <el-dropdown-item command="logout">
            <i class="iconfont icon-circle-left"></i>退出
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <!-- <el-dropdown v-if="this.type.includes('other-')" class="area-info-wrap" @command="handleBranch">
        <span class="el-dropdown-link">
          {{ currentBranch.branchName }}
          <i class="el-icon-caret-bottom el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item, index) in branchs" :key="index" :command="item">
            {{ item.branchName }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->

      <div v-if="this.type.includes('other-')" class="department-info-wrap" @command="handleDeparment">
        <span class="el-dropdown-link">
          {{ currentDepartment.name }}
          <!-- <i class="el-icon-caret-bottom el-icon--right"></i> -->
        </span>
        <!-- <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item, index) in departmentList" :key="index" :command="item">
            {{ item.name }}
          </el-dropdown-item>
        </el-dropdown-menu> -->
      </div>
    </div>

  </header>
</template>

<script>
import bus from "utils/eventbus";
import api from "api";

const hasTabName = ['日志管理'];

export default {
  name: "Topbar",

  props: {
    // branchs: Array,
    departmentList: Array,
    type: String
  },

  data() {
    return {
      active_url: location.hash
        .substr(1)
        .split("/")
        .splice(0, 3)
        .join("/"),
      activeIndex: 1,
      active_index: 0,
      hasTab: false,
      menuWidth: window.innerWidth - 200 + "px",
      navList: [
        {
          id: 1,
          name: "WORK GO总平台"
        }
      ],
      menu_now: "CMS总台",
      currentBranch: {},
      currentDepartment: {},
    };
  },

  created () {
    bus.$on("topbarold_name", (type = "CMS总台") => {
      let sts = type.split("-");
      this.hasTab = false;
      if (hasTabName.includes(sts[1])) {
        this.hasTab = true
      } 
      if (sts && sts.length > 1) {
        this.menu_now = sts[1];
      }
    });
  },

  watch: {
    isShow() {
      this.activeIndex = this.isShow ? "1" : "2";
    },
    // branchs() {
    //   this.setCurrentBranch()
    // }
  },

  computed: {
    isShow() {
      return this.$store.getters["sideBar/isShow"];
    },
    currentAccount() {
      return this.$store.getters["sys/currentAccount"];
    },
    menuList() {
      return this.$store.getters["sideBar/accountMenu"];
    },
    corp() {
      return this.$store.getters["corp/corp"];
    }
  },

  async mounted() {
    const menuList = (await api.menu.list()).data;
    for (let v in menuList) {
      if (menuList[v].actionUrl == this.active_url) {
        this.menu_now = menuList[v].menuName;
        if (hasTabName.includes(this.menu_now)) {
          this.hasTab = true
        }
      }
    }
    // if (this.active_url.includes('topicPc')) {
    //   this.menu_now = 'pc端-活动页'
    // }
    setTimeout(() => {
      this.setCurrentDepartment()
      // if (this.branchs && this.branchs.length) this.setCurrentBranch()
    }, 100)
  },

  methods: {
    // setCurrentBranch() {
    //   if (this.branchs) {
    //     if (this.currentAccount && this.currentAccount.defaultBranch) {
    //       const current = this.branchs.find((item) => {
    //         return item.branchCode === this.currentAccount.defaultBranch.branchCode
    //       })
    //       if (current) {
    //         this.currentBranch = current;
    //         this.hasTab && bus.$emit("change_branch", this.currentBranch);
    //       }
    //     } else {
    //       this.currentBranch = {
    //         branchName: "全国",
    //         branchCode: ""
    //       };
    //       this.hasTab && bus.$emit("change_branch", this.currentBranch);
    //     }
    //   }
    // },

    setCurrentDepartment() {
      if (this.departmentList) {
        this.currentDepartment = this.departmentList.find((item) => {
          return item.name === '药帮忙';
        })
        this.hasTab && bus.$emit("change_depearment", this.currentDepartment);
      }
    },

    /*
     * 处理菜单跳转
     * */
    handleCommand(command) {
      if (!command) {
        return;
      }
      if (this.active_url.indexOf("/apps/topic") == -1) {
        switch (command) {
          case "logout":
            localStorage.clear();
            this.$router.replace("/auth/login");
            break;
          default:
            this.$router.replace(command);
        }
      } else {
        this.$confirm("请确定是否已进行保存？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(async () => {
          switch (command) {
            case "logout":
              localStorage.clear();
              this.$router.replace("/auth/login");
              break;
            default:
              this.$router.replace(command);
          }
        });
      }
    },

    handleDeparment(command) {
      this.currentDepartment = command;
      this.hasTab && bus.$emit("change_depearment", this.currentDepartment);
    },

    handleBranch(command) {
      this.currentBranch = command;
      bus.$emit("change_branch", this.currentBranch);
    },
    /**
     * 处理 logo 的点击
     */
    onLogoClick() {
      // this.$router.push('home')
    },

    async navChange(item, index) {
      if (this.hasTab) {
        this['active_index'] = index
        bus.$emit("change_log_type", item);
        return false
      }
      if (this.menu_now == "CMS总台" || this.active_url.includes('topicPc')) {
        this.$confirm("请确定是否已进行保存？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(async () => {
          // if (this.active_url.includes('topicPc')) {
          //   localStorage.setItem('backFrom', 'pc')
          // } else {
          //   localStorage.setItem('backFrom', '')
          // }
          this.$router.replace(this.active_url.includes('topicPc') ? '/apps/topic?category=pc' : '/apps/topic?category=app');
          bus.$emit("menu_type", this.active_url.includes('topicPc') ? 'pc' : 'app');
        });
      }
    }
  },

  destroyed() {
    bus.$off('topbarold_name')
  }

};
</script>


<style lang="scss" rel="stylesheet/scss" scoped>
.topbar {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid $border-base;
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 200px;
    height: 50px;
    font-size: 14px;
    color: #fff;
    background-color: $color-light-silver;
    cursor: pointer;
    .iconfont {
      font-size: 30px;
      margin-right: 10px;
    }
  }
  .top-menu-list {
    border-bottom: none;
    .el-menu-item {
      padding: 0;
      margin-left: 20px;
      height: 50px;
      line-height: 50px;
      border-width: 3px;
      &.is-active {
        color: $color-primary;
      }
    }
  }
  
  .new-float-nav {
    @include middle-center-y();
    right: 30px;
    display: flex;
    flex-direction: row-reverse;
  }
  .user-info-wrap {
    cursor: pointer;
  }
  .area-info-wrap {
    cursor: pointer;
    margin-right: 15px;
  }

  .department-info-wrap {
    cursor: pointer;
    margin-right: 15px;
  }

}
</style>
