<template>
    <div class="images-box">

        <!--模块背景设置-->
        <el-row :gutter="20">
            <div class="title">模块背景设置</div>
            <el-col :span="8">
                <div class="block">
                    <span class="demonstration">背景色</span>
                    <el-color-picker v-model="content.color" size="mini" @change="onSelect"></el-color-picker>
                </div>
            </el-col>
            <el-col :span="8">
                <div class="block">
                    <div>
                        <el-button @click="imgOnclick">清除背景图</el-button>
                    </div>
                </div>
            </el-col>
            <el-col :span="8">
                <div class="block">
                    <div>
                        <el-upload
                            class="topic-image-upload"
                            ref="upload"
                            accept="image/jpeg,image/jpg,image/png,image/gif"
                            :show-file-list="false"
                            :before-upload="() => {loading = true; return true;}"
                            :on-success="onUploadImg">
                            <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
                            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                        </el-upload>
                    </div>
                </div>
            </el-col>
        </el-row>

        <!--上传图片设置和编辑-->
        <el-row :gutter="20">
            <div class="title">上传图片设置和编辑</div>
            <el-col :span="24">
                <el-table :data="list" size="mini"  :row-key="getRowKeys"   style="width: 100%">
                    <el-table-column label="图片">
                        <template slot-scope="scope">
                            <div class="container">
                                <div class="img">
                                    <img v-if="scope.row.image" :src="scope.row.image" alt="图" class="title-image"/>
                                    <i v-else class="el-icon-circle-plus-outline no-img"></i>
                                </div>
                                <div class="button-list">
                                    <el-button v-if="scope.row.operation =='2'" size="mini"
                                               @click="toEdit(scope.row, scope.$index, '2')" type="primary">编辑
                                    </el-button>
                                    <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑
                                    </el-button>
                                </div>
                            </div>
                            <div class="link-desc">{{scope.row.link | link}}</div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>
        <!--上传图片弹框-->
        <el-dialog class="banner-dialog" title="修改图片" :visible.sync="addDialog">
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="onUploadImage">
                <img v-if="dataForm.image" :src="dataForm.image" class="image">
                <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>

            <div class="topic-image-picker">
                <el-input placeholder="链接地址" v-model.trim="dataForm.link.meta.page_url">
                    <template slot="prepend">跳转链接</template>
                </el-input>
            </div>
            <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeAddDialog">取 消</el-button>
                <el-button size="small" type="primary" @click="confirm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import base from '../../base'
    import api from 'api';
    import { getUrlParam } from "config";
    export default {
        extends: base,
        data() {
            return {
                loading: false,
                addDialog: false,
                dataForm: {
                    image: '',
                    link: {
                        meta: {
                            page_url: ''
                        }
                    },
                },
                pickerOptions2: {
                    shortcuts: [{
                        text: '未来一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来六个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来一年',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                value4: [new Date(2000, 10, 10, 10, 10), new Date(2000, 10, 11, 10, 10)],
                value5: '',
            }
        },
        computed: {

            list() {
                var list = _.get(this, 'content.list')
                if (list) {
                    if(list[0].link.meta){
                        this.$nextTick(function () {
                            this.setSort()
                        })
                    }
                    return list
                } else {
                    return [];
                }
            },
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return data.meta.page_url;
            }
        },
        methods: {
            closeAddDialog() {
                this.addDialog = false;
            },
            toEdit(data, index) {
                if (status == '1') {
                    //新建时图片清空
                    this.dataForm.image = '';
                }
                this.currentData = data;
                this.currentIndex = index;
                // this.dataForm = Object.assign({}, data);
                this.dataForm = JSON.parse(JSON.stringify(data));
                this.isEdit = true;
                this.addDialog = true;
            },
            toAdd() {
                this.isEdit = false;
                this.dataForm = {
                    image: '',
                    link: {
                        meta: {
                            page_url: ''
                        }
                    },
                };
                this.addDialog = true;
            },
            onSetLink(link) {
                this.dataForm.link = link;
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.dataForm.image = res.data.url
            },
            async confirm() {
                if (!this.dataForm.image) {
                    this.$message.warning('请上传图片');
                    return false;
                }
                let linkErrMsg = '';
                if ((this.dataForm.link.meta || {}).page_url) {
                    if (!new RegExp("^ybmpage://commonh5activity.*$").test((this.dataForm.link.meta || {}).page_url)) {
                    linkErrMsg = '跳转链接格式不正确，请检查';
                    } else {
                    let linkPageUrl = getUrlParam((this.dataForm.link.meta || {}).page_url, 'url');
                    const result = await api.topic.checkPageUrl({ url: linkPageUrl });
                    if (((result || {}).data || {}).status != 200) {
                        linkErrMsg = '跳转链接不存在，请检查';
                    }
                    }
                }
                if (linkErrMsg) {
                    this.$message.error(linkErrMsg);
                    return false;
                }

                this.closeAddDialog();
                if (this.isEdit) {
                    this.psData = {
                        image: this.dataForm.image,
                        link: this.dataForm.link
                    }
                    this.list.splice(this.currentIndex, 1, this.psData);
                    this.content.list.splice(this.currentIndex, 1, this.psData);
                } else {
                    this.list.push(Object.assign({}, this.list));
                    this.content.list.push(Object.assign({}, this.list));
                }
            },
            onSelect(val) {
                if(val){
                    this.content.bgRes = val;
                }else {
                    this.content.bgRes = "#fff";
                }
            },
            async onUploadImg(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.image = res.data.url;
                this.content.bgRes = res.data.url;
            },
            imgOnclick() {
                this.content.bgRes = '';
                this.content.color = '#ffffff',
                    this.content.image = '';
            },
            async onUploadImg(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.bgRes = res.data.url;
            },
            onSelect(val) {
                this.content.color = val;
                this.content.bgRes = this.toColor16(val);
            },
            imgOnclick() {
                this.content.bgRes = '';
                this.content.color = '#ffffff';
            },
            toColor16(str) {
                if (/^(rgb|RGB)/.test(str)) {
                    var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
                    var strHex = "#";
                    for (var i = 0; i < aColor.length; i++) {
                        var hex = Number(aColor[i]).toString(16);
                        if (hex === "0") {
                            hex += hex;
                        }
                        strHex += hex;
                    }

                    if (strHex.length !== 7) {
                        strHex = str;
                    }
                    return strHex.toUpperCase();
                } else {
                    return str;
                }
            }
        },
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .images-box {
        .container {
            display: flex;
            align-items: center;
            .img {
                width: 78%;
                img {
                    display: block;
                    max-width: 300px;
                    height: 100px;
                }
            }
            .button-list {
                margin-left: 10px;
            }
        }
        .content-setting {
            color: #fff;
            background-color: #13c2c2;
            padding: 10px;
            text-align: center;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .el-icon-circle-plus-outline {
            font-size: 35px;
            color: #c7bdbd;
        }
        .topic-image-upload {
            .image {
                display: block;
                width: 100%;
            }
            .uploader-icon {
                width: 200px;
                height: 200px;
                line-height: 200px;
                border: 1px solid $border-base;
                font-size: 50px;
            }
        }
        .topic-image-picker {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }

    .el-row {
        text-align: center;

        .title {
            text-align: left;
            line-height: 30px;
            background-color: #f2f2f2;
            margin: 10px 0;
            padding-left: 10px;
        }
    }
</style>
<style lang="scss" rel="stylesheet/scss">
    .topic-banner {
        .banner-dialog {
            .el-dialog__body {
                padding-top: 10px;
            }
        }
    }
</style>
