<template>
	<div>
		<el-dialog @close="afterClose" :visible.sync="visible" :title="title">
			<el-form :model="params.data" :rules="validate" :ref="validRef" label-position="right" size="small" label-width="100px" label-suffix="：">
				<el-form-item label="图片" prop="imgs">
					<el-upload
							:limit="1"
							:auto-upload="false"
							:file-list="params.data.imgs"
							:before-upload="f => (sending = true)"
							:on-change="(f, fs) => {params.data.imgs = fs; params.imgUp = true;}"
							:on-remove="(f, fs) => params.data.imgs = fs"
							:on-exceed="uploadExceed"
							:on-error="uploadErr"
							:on-success="uploadSuccess"
							ref="uploadImg"
							class="upload-demo"
							list-type="picture-card"
							accept="image/jpeg,image/jpg,image/png,image/bmp,image/gif">
						<i v-if="!params.data.imgs || !params.data.imgs.length" class="el-icon-plus"></i>
						<div slot="tip" class="el-upload__tip">
							<i>只能上传jpeg/jpg/png/bmp/gif文件，且不超过1M</i>
						</div>
					</el-upload>
				</el-form-item>
				<el-form-item label="链接" >
					<el-input v-model.trim="params.data.link" placeholder="跳转链接" clearable>
						<el-button slot="append" @click="linkShow = !linkShow" icon="el-icon-d-caret"></el-button>
						<a slot="append" target="_blank" :href="params.data.link">
							<el-button icon="el-icon-share"></el-button>
						</a>
					</el-input>
				</el-form-item>
			</el-form>
			<div v-show="linkShow">
				<page-link @select="seLink" :params="{
							branchCode: params.branchCode
						}"></page-link>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-button size="small" @click="visible = false">取消</el-button>
				<el-button size="small" type="primary" :loading="sending" :disabled="sending" @click="save">提交</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
	export default {
		name: 'activityTipsEdit',
		props: {
			params: {
				type: Object,
				default() {
					return {
						data: {
							imgs: []
						}
					};
				}
			}
		},
		data() {
			return {
				visible: false,
				sending: false,
				linkShow: false,
				title: '活动提醒',
				validRef: 'form',   //验证ref属性值
				validate: {
					imgs: [
						{ required: true, type: 'array', message: '请上传图片', trigger: 'blur' }
					],
					link: [
						{ required: true, message: '请选择或填写跳转链接', trigger: 'blur' },
						{ min: 16, max: 500, message: '长度在16 - 500之间', trigger: 'blur' },
						{ validator: (rule, val, callback) => {
								if (!/^[A-Za-z]+:\/\/.+$/.test(val))
									var e = new Error('链接格式错误');
								return callback(e);
							}, trigger: 'blur' }
					]
				}
			};
		},
		methods: {
			show() {
				this.visible = true;
			},
			save() {
				this.sending = true;
				this.$refs[this.validRef].validate(async (ok) => {
					if (!ok) {
						this.sending = !this.sending;
						this.$message.error('验证失败！');
						return;
					}
					this.params.imgUp ? this.handUpload() : this.afterSave();  //上传图片
				});
			},
			afterSave() {
				let data = Object.assign({}, this.params.data);
				data.imgs = data.imgs.map(f => f.url);
				this.$emit('commit', {
					data,
					index: this.params.index
				});
				this.visible = this.sending = false;
			},
			afterClose() {
				this.$emit('close');
				this.$refs[this.validRef].resetFields();
			},
			handUpload(ref) {
				this.$refs[ref || 'uploadImg'].submit();
			},
			uploadExceed(f, fs) {
				this.$notify.warning(`最多允许上传 ${ fs.length } 个文件`);
			},
			uploadSuccess(res, f, fs) {
				this.sending = false;
				if (res.code != 200) {
					this.$message.warning(`[${ res.code }]${ res.msg }`);
					return;
				}
				this.params.data.imgs = fs.map(f => f.response.data);
				this.afterSave();
			},
			uploadErr(e, f, fs) {
				this.sending = false;
				this.$notify.error(`文件“${ f.name }”上传出错`);
			},
			seLink(data) {
				this.linkShow = false;
				if (!data)
					return;
				this.$set(this.params.data, 'link', data.meta.page_url);
			}
		}
	}
</script>
<style scoped>
</style>
