<template>
    <div>
        <div class="container">
            <el-row :gutter="10">
                <el-col :span="12">
                    <el-input placeholder="请输入选项卡名称" v-model="tabName" size="mini"></el-input>
                </el-col>
                <el-col :span="12">
                    <el-button size="mini" type="primary" @click="add_tab">添加选项卡</el-button>
                </el-col>
            </el-row>

            <el-row :gutter="10">
                <el-col :span="8">
                    tab的字体颜色:
                    <el-color-picker v-model="content.tabs.color" size="mini" @change="change_color"></el-color-picker>
                </el-col>
                <el-col :span="8">
                    tab的横线颜色:
                    <el-color-picker v-model="content.tabs.lineColor" size="mini"
                                     @change="change_lineColor"></el-color-picker>
                </el-col>
                <el-col :span="8">
                    tab的背景颜色:
                    <el-color-picker v-model="content.tabs.bg_color" size="mini"
                                     @change="change_bgColor"></el-color-picker>
                </el-col>
            </el-row>

            <el-row :gutter="10">
                <el-col :span="8">
                    列表的背景颜色:
                    <el-color-picker v-model="content.tabs.body_color" size="mini" @change="change_body_color"></el-color-picker>
                </el-col>
            </el-row>

        </div>
    </div>
</template>

<script>

    import {common} from 'api'

    export default {
        props: ['content'],
        data() {
            return {
                tabName: ""
            }
        },
        computed: {},

        watch: {},
        methods: {
            change_bgColor(color){
                this.$emit("change_color", "bg", color)
            },
            change_color(color) {
                this.$emit("change_color", "font", color)
            },
            change_lineColor(color) {
                this.$emit("change_color", "line", color)
            },
            change_body_color(color){
                this.$emit("change_color", "body", color)
            },
            add_tab() {
                if (!this.tabName) {
                    this.$message('请输入选项卡名称')
                    return
                }
                if (this.content.goods_list.length > 0) {
                    const nameIndex = common.getRepeatResult('name', this.tabName, this.content.goods_list);
                    if (nameIndex >= 0) {
                        this.$message.warning('您所添加的选项卡名称已经存在啦,请重新添加')
                        return
                    }
                }
                let obj = {
                    name: this.tabName,
                    goods_group: [],
                    list: [],
                    type: 0
                };
                this.$emit("add_list", obj)
            },
        }

    }
</script>
