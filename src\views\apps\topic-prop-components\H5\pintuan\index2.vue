<template>
  <div class="topic-menu-list">
    <div>
      <!-- <el-row :gutter="20">
        <div class="title">列表背景颜色</div>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">列表背景的颜色</span>
            <div>
              <el-color-picker v-model="content.list_bgColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
      </el-row> -->
      <!--按钮设置-->
      <!-- <el-row :gutter="20">
        <div class="title">按钮颜色设置</div>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">拼团中文字颜色</span>
            <div>
              <el-color-picker v-model="content.btnColorList.progress_default_textColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">拼团中背景颜色</span>
            <div>
              <el-color-picker v-model="content.btnColorList.progress_default_bgColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">未开始文字颜色</span>
            <div>
              <el-color-picker v-model="content.btnColorList.before_default_textColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">未开始背景颜色</span>
            <div>
              <el-color-picker v-model="content.btnColorList.before_default_bgColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">已结束文字颜色</span>
            <div>
              <el-color-picker v-model="content.btnColorList.end_default_textColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">已结束背景颜色</span>
            <div>
              <el-color-picker v-model="content.btnColorList.end_default_bgColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
      </el-row> -->
      <!--进度设置-->
      <!-- <el-row :gutter="20">
        <div class="title">进度条设置</div>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">拼团中进度1-50%颜色</span>
            <div>
              <el-color-picker v-model="content.progressColorList.progressColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">拼团中进度>50%颜色</span>
            <div>
              <el-color-picker v-model="content.progressColorList.half_progressColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">拼团中进度≥90%颜色</span>
            <div>
              <el-color-picker v-model="content.progressColorList.beforeOver_progressColor"
                               size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">拼团进度100%颜色</span>
            <div>
              <el-color-picker v-model="content.progressColorList.over_progressColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">拼团已结束颜色</span>
            <div>
              <el-color-picker v-model="content.progressColorList.end_progressColor" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
      </el-row> -->
      <!-- <el-row :gutter="24">
        <div class="title">拼团商品数据模式</div>
        <el-col :span="8">
          <span>系统自动聚合：</span>
          <el-switch v-model="content.isSystemAggre" />
        </el-col>
      </el-row> -->
      <!-- <el-row :gutter="24" v-show="!content.isSystemAggre">
        <div class="title">拼团商品列表</div>
        <el-col :span="10">
          <span>区域：</span>
          <el-select
            v-model="branchCode"
            placeholder="请选择"
            size="mini"
          >
            <el-option
              v-for="item in branchs"
              :value="item.branchCode"
              :label="item.branchName"
              :key="item.branchCode"
            ></el-option>
          </el-select>
        </el-col>
        <el-col :span="11">
          <span>活动状态：</span>
          <el-select
            v-model="activityStatus"
            placeholder="请选择"
            size="mini"
          >
            <el-option label="全部" :value="0" />
            <el-option label="未开始" :value="1" />
            <el-option label="进行中" :value="2" />
          </el-select>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" @click="searchList" size="mini">查询</el-button>
        </el-col>
        <el-col :span="6" v-if="branchCode === 'XS000000'" style="marginTop: 15px">
          <span>仅看已选：</span>
          <el-switch v-model="onlySelected" />
        </el-col>
        <el-col :span="24" v-if="showOrderBtn" class="btnCol">
          <el-button style="marginTop: 15px" type="primary" @click="updatOrder" size="mini">更新排序</el-button>
          (调整排序后，请点此按钮更新拼团列表展示顺序)
        </el-col>
        <el-col :span="24" style="marginTop: 20px">
          <el-table
            ref="multipleTable"
            :data="tableData"
            v-loading="loading"
            :row-key="row => row.marketingId"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              :reserve-selection="true"
              width="55">
            </el-table-column>
            <el-table-column
              label="活动ID"
              prop="marketingId"
              width="80">
            </el-table-column>
            <el-table-column
              label="商品名称"
              width="120">
              <template slot-scope="scope">{{ scope.row.groupBuyingSku.skuName }}</template>
            </el-table-column>
            <el-table-column
              label="活动状态"
              width="80">
              <template slot-scope="scope">{{ scope.row.status|get_status}}</template>
            </el-table-column>

            <el-table-column
              width="200"
              label="开始时间/结束时间">
              <template slot-scope="scope">
                {{ [scope.row.startTime,scope.row.endTime]|dateFilter }}
              </template>
            </el-table-column>

            <el-table-column
              label="拼团价"
              width="120">
              <template slot-scope="scope">{{ scope.row.groupBuyingSku.skuPrice }}</template>
            </el-table-column>

            <el-table-column
              label="起售数"
              width="120">
              <template slot-scope="scope">{{ scope.row.groupBuyingSku.skuStartNum }}</template>
            </el-table-column>

            <el-table-column
              label="成团数"
              width="120">
              <template slot-scope="scope">{{ scope.row.groupBuyingSku.skuStartNum }}</template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row> -->
    </div>
  </div>
</template>

<script>
  import base from "../../base";
  import api from 'api';
  import Sortable from 'sortablejs';
  export default {
    extends: base,
    // contentDefault: {
    //   btnColorList: {
    //     progress_default_textColor: "#ffffff",
    //     progress_default_bgColor: "#00B377",
    //     before_default_textColor: "#ffffff",
    //     before_default_bgColor: "#00B377",
    //     end_default_textColor: "#ffffff",
    //     end_default_bgColor: "#A9AEB7",
    //   },
    //   progressColorList: {
    //     progressColor: "#00B377",
    //     half_progressColor: "#00B377",
    //     beforeOver_progressColor: "#00B377",
    //     over_progressColor: "#00B377",
    //     end_progressColor: "#00B377",
    //   },
    //   list_bgColor:"#F7F7F8",
    //   multipleSelection: [],
    //   isSystemAggre: true,
    // },
    data() {
      return {
        tableData: [],
        activityStatus: 0,
        branchCode: "XS000000",
        branchs: [],
        loading: false,
        onlySelected: false,
        showOrderBtn: false,
        // isSystemAggre: true,
      }
    },
    computed: {},
    mounted () { 
      // this.rowDrop();
    },
    created() {
      // this.getBranchs();
    },
    filters:{
      get_status(val){
        switch (val) {
          case 1:
            return "未开始";
          case 2:
            return "拼团中";
          case 3:
            return "已结束";
        }
      },
      dateFilter(date) {
        function formatDate(date) {
          let year = date.getFullYear();
          let month = date.getMonth() + 1;
          let day = date.getDate();
          let hour = date.getHours();
          let minute = date.getMinutes();
          let second = date.getSeconds();
          return year + '-' + (String(month).length > 1 ? month : '0' + month) + '-' +
            (String(day).length > 1 ? day : '0' + day) + ' ' + (String(hour).length > 1 ? hour : '0' + hour) + ':' + (String(minute).length > 1 ? minute : '0' + minute)
            + ':' + (String(second).length > 1 ? second : '0' + second)
        }

        if (date) {
          let date1 = formatDate(new Date(date[0]));
          let date2 = formatDate(new Date(date[1]));
          // const nS=new Date(date).getTime()
          return date1 + "至" + date2
        } else {
          return " "
        }
      },
    },
    methods: {
      rowDrop() {
        const _this = this;
        const tbody = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0];
        Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
          ghostClass: "sortable-ghost",
          onMove: (evt, originalEvent) => {
            if (!this.showOrderBtn) {
              return false
            }
          },
          onEnd: evt => {
            const currRow = (_this.tableData || []).splice(evt.oldIndex, 1)[0];
            (_this.tableData || []).splice(evt.newIndex, 0, currRow);
          }
        });
      },
      async getBranchs() {
        // const accountResult = await api.user.current();
        let bho = await api.dict.branchHasOpen();
        if (bho.code == 200) {
          this.$nextTick(() => {
            bho.data.unshift({
              branchName: "全国",
              branchCode: "XS000000"
            })
            this.branchs = bho.data || [];
            this.branchCode = 'XS000000';
            this.get_GroupBuyingInfos();
          })
        };
      },
      // handle_sort(item,type){
      //   const index=this.tableData.indexOf(item)
      //   if(type==='up'){
      //     this.tableData.splice(index,1);
      //     this.tableData.splice(index-1,0,item)
      //   }else {
      //     this.tableData.splice(index,1);
      //     this.tableData.splice(index+1,0,item)
      //   }
        // let arr = [];
        // this.tableData.forEach(item => {
        //   for (let i = 0; i < this.content.multipleSelection.length; i++) {
        //     if (item.marketingId === this.content.multipleSelection[i].marketingId) {
        //       arr.push(item)
        //     }
        //   }
        // });
        // this.content.multipleSelection=arr;
      // },
      toggleSelection(rows) {
        let arr = [];
        rows.forEach(item => {
          for (let i = 0; i < this.tableData.length; i++) {
            if (item.marketingId === this.tableData[i].marketingId) {
              arr.push(i)
            }
          }
        });
        if (arr.length) {
          arr.forEach(val => {
            this.$refs.multipleTable.toggleRowSelection(this.tableData[val], true);
          });
        }
      },
      handleSelectionChange(val) {
        this.content.multipleSelection = val;
      },
      searchList() {
        this.get_GroupBuyingInfos();
      },
      updatOrder() {
        let arr = [];
        this.tableData.forEach(item => {
          for (let i = 0; i < this.content.multipleSelection.length; i++) {
            if (item.marketingId === this.content.multipleSelection[i].marketingId) {
              arr.push(item)
            }
          }
        });
        this.content.multipleSelection = arr;
      },
      async get_GroupBuyingInfos(){
        this.loading = true;
        const marketingIds = this.content.multipleSelection.map(i => i.marketingId);
        let params={
          branchCode: this.branchCode,
          activityStatus: this.activityStatus,
        };
        if (this.onlySelected && this.branchCode === 'XS000000') {
          params.marketingIds = marketingIds.join();
          if (marketingIds.length == 0) {
            this.loading = false;
            this.tableData = [];
            return false;
          }
        }
        if (this.branchCode === 'XS000000' && this.activityStatus == 0) {
          this.showOrderBtn = true;
        } else {
          this.showOrderBtn = false;
        }
        const GroupBuyingInfos=await api.specific.get_GroupBuyingInfos(params);

        let list=[];
        if (GroupBuyingInfos.data&&GroupBuyingInfos.data.status === 'success') {
          list=GroupBuyingInfos.data.data.groupBuyingInfoCmsList;
        } else {
          this.$message.error("业务接口错误");
        }
        this.loading = false;
        let arr = [];
        this.content.multipleSelection.forEach((item) => {
          list.forEach((child,child_index)=>{
           if( item.marketingId===child.marketingId){
             arr.push(child);
             list.splice(child_index,1,{Is_delete:true});
           }
          });
        });
        this.tableData=[...list.filter(item=>{
          if(!item.Is_delete){
            return item
          }
        }),...arr];
        this.$nextTick(()=>{
          this.toggleSelection(this.content.multipleSelection)
        })
      }
    }
  }
</script>
<style scoped lang="scss">
  .el-row {
    text-align: center;
    img {
      width: 100%;
    }
    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
  }
  .btnCol {
    text-align: left;
  }
</style>
