<template>
    <div class="rich-text-editor" :disabled="disabled" v-loading="loading">
        <quill-editor v-model="content" ref="editor" :options="options" :style="{height:`${height}px`}"></quill-editor>
    </div>
</template>

<script>
    import hljs from 'highlight.js'
    import Quill from 'quill'
    import Delta from 'quill-delta'
    import './box-blot.js'
    import './box-blot.scss'
    import { UPLOAD_SERVER } from 'config'

    Quill.register(Quill.import('attributors/style/align'), true)
    Quill.register(_.merge(Quill.import('formats/header'), {
        tagName: 'p'
    }), true)
    export default {
        props: {
            value: {
                type: String
            },
            disabled: {
                type: Boolean,
                default: false
            },
            height: {
                type: Number,
                default: 400
            },
        },
        data() {
            return {
                loading: false,
            }
        },
        computed: {
            content: {
                get() {
                    return this.value;
                },
                set(value) {
                    this.$emit('input', value);
                }
            },
            quill() {
                return this.$refs.editor.quill;
            },
            options() {
                return {
                    placeholder: '请输入',
                    modules: {
                        toolbar: {
                            container: [
                                // [ 'bold', 'italic', 'underline', 'strike' ],
                                // [ 'blockquote', 'code-block' ],
                                // [ { 'indent': '-1' }, { 'indent': '+1' } ],
                                // [ { 'size': [ 'small', false, 'large', 'huge' ] } ],
                                // [ { 'color': [] }, { 'background': [] } ],
                                // [ { 'align': [] } ],
                                // [ 'clean' ],
                                // [ 'link', 'image' ],
                                [ 'bold', 'underline', 'strike' ],
                                [ { 'color': [] }, { 'background': [] } ],
                                [ 'clean' ],
                            ],
                            syntax: {
                                highlight: text => hljs.highlightAuto(text).value
                            },
                            handlers: {
                                image: this.onInsertImage,
                                clearAll: this.onClearAll
                            }
                        }
                    }
                }
            }
        },
        watch: {
            disabled(value) {
                this.quill.enable(!value);
            }
        },
        methods: {
            onClearAll() {
                this.content = '';
            },
            onInsertImage() {
                let fileUpload = _.assign(document.createElement('input'), {
                    type: 'file',
                    accept: 'image/jpeg,image/png,image/gif',
                    multiple: true,
                    onchange: () => {
                        this.loading = true;
                        Promise
                        .all(_.map(fileUpload.files, file => this.uploadImage(file)))
                        .then(
                            images => Promise.all(
                                _.map(images, image => new Promise(
                                    resolve => {
                                        var bitmap = new Image();
                                        bitmap.onload = () => resolve(bitmap)
                                        bitmap.src = image;
                                    }
                                ))
                            )
                        )
                        .then(images => _.forEach(images, image => {
                            if (!image) return;
                            let cellHeight = 400;
                            let count = Math.ceil(image.height / cellHeight);
                            let range = this.quill.getSelection(true);
                            let delta = new Delta()
                            .retain(range.index)
                            .delete(range.length)
                            for (var i = 0; i < count; i++) {
                                delta = delta.insert({
                                    image: image.src + `?x-oss-process=image/indexcrop,y_${cellHeight},i_${i}`
                                }, {
                                    width: '100%'
                                })
                            }
                            this.quill.updateContents(delta, 'user');
                            this.quill.setSelection(range.index + count, 0, 'user');
                            this.loading = false;
                        }))
                    }
                })
                fileUpload.click();
            },
            async uploadImage(file) {
                return new Promise(async resolve => {
                    let formData = new FormData();
                    formData.append('file', file);
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', UPLOAD_SERVER)
                    xhr.send(formData)
                    xhr.onload = function () {
                        const result = JSON.parse(xhr.responseText);
                        if (result.code === 10000) {
                            resolve(result.data.url)
                        } else {
                            resolve(null);
                        }
                    }
                })
            }
        },
        mounted() {
            this.quill.enable(!this.disabled);
        },
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .rich-text-editor {
        .quill-editor {
            padding-bottom: 42px;
            line-height: normal;
        }
        .ql-toolbar {
            background: #fff;
        }

        /*字号*/
        .ql-snow .ql-picker.ql-size {
            .ql-picker-label::before,
            .ql-picker-item::before {
                content: '字号13px';
            }
            .ql-picker-label[data-value]::before,
            .ql-picker-item[data-value]::before {
                content: '字号 ' attr(data-value);
            }
        }

        /*disabled*/
        &[disabled] {
            .ql-toolbar {
                background: #ddd;
                pointer-events: none;

                button, span {
                    color: #999;
                }
                .ql-stroke, .ql-fill {
                    stroke: #999;
                }
            }
        }

        .ql-editor {
            img {
                display: block;
                margin: 0;
            }
        }

        p {
            line-height: 1.5
        }
        strong {
            font-weight: bold
        }
    }

    .ql-formats button {
        outline: none;
    }

    .ql-box::before {
        content: '万';
    }

    .ql-clearAll::before {
        content: '\E612';
        font-family: element-icons;
    }
</style>
