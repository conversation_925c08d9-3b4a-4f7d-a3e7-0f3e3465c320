/**
 * 项目基本配置
 * @type {Object}
 */
export const API_SERVER = 'API_DOMAIN'
export const TOPIC_BASE_URL = 'TOPIC_FRAME'
export const BUSINESS_API_SERVER = 'BUSINESS_DOMAIN'
export const AppWebsite = 'APP_WEBSITE'    //App外部接口域名
export const getUrlParam = (url, name) => {
  //debugger
  let urlStr = url || window.location.href;
  const urlArr = urlStr.split('?');
  if (urlArr.length < 2) {
    return '';
  }
  const tempArr = urlArr[1].split('&');
  for (let i = 0; i < tempArr.length; i++) {
    const item = tempArr[i].split('=');
    if (item[0].trim() === name) {
      return item[1];
    }
  }
  return '';
}
