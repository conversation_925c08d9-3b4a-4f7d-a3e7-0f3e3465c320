<template>
  <div>
    <el-row :gutter="20">
      <div class="title">模块背景色</div>
      <el-col :span="12">
        <div class="block">
          <span class="demonstration">背景色</span>
          <el-color-picker v-model="content.bgRes" size="mini"></el-color-picker>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="block">
          <div>
            <el-button @click="reset_bgRes">恢复默认背景色</el-button>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">icon上传</div>
      <el-col :span="12">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="UploadIcon">
              <el-button class="btn-block" type="primary" :loading="loading">上传icon</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="block">
          <div>
            <img width="50"
                 height="50"
                 :src="content.Icon_url" alt="">
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">分享文案编辑</div>
      <el-col :span="12">
        <div class="block">
          <el-input placeholder="请输入内容" v-model="content.share_title">
            <template slot="prepend">分享标题</template>
          </el-input>
        </div>
      </el-col>

      <el-col :span="12">
        <div class="block">
          <el-input placeholder="请输入内容" v-model="content.share_desc">
            <template slot="prepend">分享描述文案</template>
          </el-input>
        </div>
      </el-col>

    </el-row>
    <el-row :gutter="20">
      <div class="title">商店名称编辑</div>
      <el-col :span="12">
        <div class="block">
          <span>最多10个字</span>
          <el-input placeholder="请输入内容"
                    maxlength="10"
                    v-model="content.store_title">
            <template slot="prepend">商店名称</template>
          </el-input>
        </div>
      </el-col>

      <el-col :span="12">
        <div class="block">
          <span class="demonstration">商店名颜色</span>
          <el-color-picker v-model="content.store_title_color" size="mini"></el-color-picker>
        </div>
      </el-col>

    </el-row>
    <el-row :gutter="20">
      <div class="title">分享按钮颜色</div>
      <el-col :span="12">
        <div class="block">
          <span class="demonstration">按钮背景色</span>
          <el-color-picker v-model="content.share_bgColor" size="mini"></el-color-picker>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="block">
          <span class="demonstration">按钮字体色</span>
          <el-color-picker v-model="content.share_fontColor" size="mini"></el-color-picker>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">商店标签(最多展示3个，无标签内容不展示)</div>
      <div v-for="(item,index) in content.tag_list">
          <div class="float">
            <el-input placeholder="请输入内容" v-model="content.tag_list[index].tag_content">
              <template slot="prepend">标签内容</template>
            </el-input>
          </div>
          <div class="float">
            <span class="demonstration">标签背景色</span>
            <el-color-picker v-model="content.tag_list[index].tag_bgRes" size="mini"></el-color-picker>
          </div>
          <div class="float">
            <span class="demonstration">标签字体色</span>
            <el-color-picker v-model="content.tag_list[index].tag_fontRes" size="mini"></el-color-picker>
          </div>
      </div>
    </el-row>

  </div>
</template>

<script>
  import base from "views/apps/topic-prop-components/base.vue";  export default {
    name: "index",
    extends: base,
    contentDefault: {
      tag_list: [
        {
          tag_bgRes:"#5670F0",
          tag_content:"",
          tag_fontRes:"#ffffff"
        },
        {
          tag_bgRes:"#5670F0",
          tag_content:"",
          tag_fontRes:"#ffffff"
        },
        {
          tag_bgRes:"#5670F0",
          tag_content:"",
          tag_fontRes:"#ffffff"
        }
      ],
      bgRes: '#292933',
      store_title_color: '#ffffff',
      store_title: "",
      Icon_url: '',
      share_bgColor: "#00B377",
      share_fontColor: "#ffffff",
      share_title:"",
      share_desc:""
    },
    data() {
      return {
        loading: false,
      }
    },
    methods: {
      reset_bgRes() {
        this.content.bgRes = '#292933';
      },
      UploadIcon(res) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.content.Icon_url = res.data.url;
      }
    }
  }
</script>

<style scoped lang="scss">
  .el-row {
    text-align: center;

    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
    .float{
      float: left;
      width: 33%;
      height: 60px;
    }
  }
</style>
