<template>
  <div class="main-content" v-if="(search_obj || {}).page_type!='floatWindow'">
    <el-row class="mb-10">
      <el-col :span="24">
        <el-row class="search-wrap" type="flex" :gutter="10">
          <el-popover placement="top" trigger="hover">
            <el-button @click="cancel()" :loading="loading" type="info" icon="el-icon-refresh" size="mini" plain>清空</el-button>
            <el-button @click="changeSize(pageSize)" :loading="loading" slot="reference" type="primary" icon="el-icon-search" size="mini" plain>查询</el-button>
          </el-popover>
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              结束时间
              <b>不能小于</b>开始时间
            </div>
            <el-date-picker class="input-class" v-model="searchParam.searchEndDate" size="mini" type="datetime" placeholder="<=结束时间" default-time="23:59:59"></el-date-picker>
          </el-tooltip>
          <el-date-picker class="input-class" v-model="searchParam.searchStartDate" size="mini" type="datetime" placeholder=">=开始时间" default-time="00:00:00"></el-date-picker>
          <el-select v-model.number="searchParam.state" placeholder="状态" size="mini" clearable>
            <el-option v-for="(item, i) in state" :value="Number(i)" :label="item" :key="i"></el-option>
          </el-select>
          <el-select v-model.number="searchParam.usingState" v-if="usingStateShowType == 1" placeholder="使用状态" size="mini" clearable>
            <el-option v-for="(item, i) in usingState" :value="Number(i)" :label="item" :key="i"></el-option>
          </el-select>
          <el-select v-model="searchParam.branchCode" v-if="(search_obj || {}).page_type==='KAHome' || (search_obj || {}).page_type==='KActivity'" placeholder="区域" size="mini" default-first-option
            filterable clearable>
            <el-option v-for="(item, i) in branchs" :value="item.branchCode" :label="item.branchName" :key="i"></el-option>
          </el-select>
          <el-input v-model="searchParam.page_name" placeholder="搜索页面名称" size="mini" clearable></el-input>
          <el-input v-model="searchParam.page_id" placeholder="页面ID" size="mini" clearable></el-input>
          <el-col :span="12">
            <el-button type="primary" size="mini" icon="el-icon-plus" @click="showModal()">添加</el-button>
            <el-button type="danger" size="mini" @click="synchronous()">全部同步</el-button>
            <el-button type="" size="mini" @click="deleteMore()"  v-if="['start','diaNewLog'].includes(search_obj.page_type)">删除</el-button>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <el-table @selection-change="handleSelectionChange" :key="search_obj.page_type" :data="dataList" v-loading="loading" :row-class-name="tabRowCla" @row-dblclick="edit" class="custom-table" size="mini" border @sort-change="handleSortChange">
      <div slot="empty" class="empty-wrap">
        <i class="iconfont icon-tishi"></i>
        <span>尚未添加页面</span>
      </div>
      <el-table-column
      v-if="['start','diaNewLog'].includes(search_obj.page_type)"
      type="selection"
      width="55">
    </el-table-column>
      <el-table-column label="页面ID" :show-overflow-tooltip="true" width="100%" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="edit(scope.row)">{{ scope.row.page_id }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="page_name" :show-overflow-tooltip="true" label="页面名称" width="120%" align="center"></el-table-column>
      <el-table-column prop="category" :show-overflow-tooltip="true" label="客户端 / 类型" width="110%" align="center">
        <template slot-scope="scope">
          <span>{{ $options.filters.stateText(scope.row.category, category) }}</span>
          <span>/</span>
          <span>{{ $options.filters.stateText(scope.row.page_type, pageType) }}</span>
        </template>
      </el-table-column>

      <el-table-column v-if="categoryType === 'app' && search_obj.page_type === 'h5'" prop="categoryList" :show-overflow-tooltip="true" label="页面模板" width="110%" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.categoryList[0].title}}</span>
        </template>
      </el-table-column>

      <el-table-column prop="url" :show-overflow-tooltip="true" label="页面链接" width="150%">
        <template slot-scope="scope">
          <el-button type="text" v-clipboard:copy="`${categoryType !== 'app' ? scope.row.url : `ybmpage://commonh5activity?cache=0&url=${scope.row.url}`}`" v-clipboard:success="copy_url" size="mini">
            {{ categoryType !== 'app' ? scope.row.url : `ybmpage://commonh5activity?cache=0&url=${scope.row.url}` }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="startDate" label="开始时间" width="140">
        <template slot-scope="scope">
          <span v-bind:class="{active: (new Date(scope.row.startDate) <= new Date() && new Date(scope.row.endDate) >= new Date())}">{{ scope.row.startDate | dateFmt }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="endDate" label="结束时间" width="140">
        <template slot-scope="scope">
          <span v-bind:class="{active: (new Date(scope.row.endDate) >= new Date())}">{{ scope.row.endDate | dateFmt }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="endDate" label="优先级" width="140" align="center">
        <template slot-scope="scope">
         <el-input v-model="scope.row.priority"  v-if="scope.row.focus" @blur="scope.row.focus==unFocus(scope.row)"></el-input>
        <div style="width: 100%;height: 40px;" @click="focus(scope.row)" v-if="!scope.row.focus"> {{ scope.row.priority }}</div>
        {{scope.row.focus}}
        </template>
      </el-table-column> -->
      <el-table-column prop="dialogType" label="弹窗内容" width="140" v-if="(search_obj || {}).page_type==='diaNewLog'">
        <template slot-scope="scope">
          <span>{{ scope.row.dialogType == 1 ? '自定义' : scope.row.dialogType == 2 ? '优惠券' : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="occasion" label="提醒时机" width="140" v-if="(search_obj || {}).page_type==='diaNewLog'">
        <template slot-scope="scope">
          <span>{{ scope.row.occasion == 1 ? '仅一次' : scope.row.occasion == 2 ? '每日一次' : scope.row.occasion == 3 ? '不限' : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级" width="100" v-if="(search_obj || {}).page_type==='diaNewLog'||(search_obj || {}).page_type==='start'" show-overflow-tooltip sortable='custom'>
        <template slot-scope="scope">
          <div ref="closepopover" >
            <el-popover placement="top" trigger="click"  >
              <div class="priorityCheck">
                <el-input size="mini" maxlength="11" @input="popoverValue = popoverValue.replace(/[^\d]/g, '')" v-model="popoverValue" />
                <el-button style="width: 150px;" type="primary" icon="el-icon-check" size="mini" @click="changePriority(scope.$index,scope.row)"></el-button>
                <el-button style="width: 150px;" type="info" icon="el-icon-close" size="mini" @click="hidePriority()"></el-button>
              </div>
              <el-button type="text" style="width: 150px;" size="mini" slot="reference" @click="setPriority(scope.row.priority)">{{ scope.row.priority }}</el-button>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="crowdType" label="场景" width="140" v-if="(search_obj || {}).page_type==='diaNewLog' || (search_obj || {}).page_type==='exhibitionPosition'">
        <template slot-scope="scope">
          <span>{{ {1: '首页', 3: '个人中心', 4: '排行榜', 6: '支付成功页', 7: '大搜启动页',8:'订单页'}[scope.row.sceneType] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="crowdType" label="指定人群" width="140" v-if="(search_obj || {}).page_type==='diaNewLog' || (search_obj || {}).page_type==='exhibitionPosition'">
        <template slot-scope="scope">
          <span>{{ scope.row.crowdId || '全部' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="branchCode" label="区域" width="90%" align="center" v-if="(search_obj || {}).page_type==='KAHome' || (search_obj || {}).page_type==='KActivity'">
        <template slot-scope="scope">{{ $options.filters.getBranchName(scope.row.branchCode, branchs) }}</template>
      </el-table-column>

      <el-table-column v-if="categoryType === 'app' &&(search_obj.page_type === 'index'||search_obj.page_type === 'start') " prop="crowdId" label="人群范围" width="90%" align="center">
        <template slot-scope="scope">{{scope.row.crowdId || '全部'}}</template>
      </el-table-column>

      <el-table-column prop="state" label="状态" width="50%">
        <template slot-scope="scope">
          <span v-bind:class="{active: (scope.row.state === 1)}">{{ $options.filters.stateText(scope.row.state, state) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="使用状态" width="90%" align="center" v-if="usingStateShowType == 1">
        <template slot-scope="scope">
          <span v-bind:class="{active: (scope.row.usingState === 1),
      overtime : (scope.row.usingState === 4),
      nouser : (scope.row.usingState === 3)}">{{ $options.filters.stateText(scope.row.usingState, usingState) }}</span>
        </template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="创建人">
        <template slot-scope="scope">{{scope.row.operator.create && scope.row.operator.create.userName}}</template>
      </el-table-column>
      <el-table-column prop="create_time" label="创建时间" width="140">
        <template slot-scope="scope">{{ scope.row.create_time | dateFmt }}</template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="更新人">
        <template slot-scope="scope">{{scope.row.operator.edit && scope.row.operator.edit.userName}}</template>
      </el-table-column>
      <el-table-column prop="update_time" label="更新时间" width="140">
        <template slot-scope="scope">{{ scope.row.update_time | dateFmt }}</template>
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" label="发布人">
        <template slot-scope="scope">{{scope.row.operator.publish && scope.row.operator.publish.userName}}</template>
      </el-table-column>
      <el-table-column prop="update_time" label="发布时间" width="140">
        <template slot-scope="scope">{{ scope.row.publish_time | dateFmt }}</template>
      </el-table-column>
      <el-table-column prop="versions" label="版本号" width="150">
        <template slot-scope="scope">
          <div @click="showMore(scope.row)" class="moreVersions">
            {{ scope.row.versions && (scope.row.versions[scope.row.versions.length - 1] || {}).mtime }}
            <i v-if="scope.row.versions.length" class="el-icon-arrow-down"></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" width="300" label="操作" align="center">
        <template slot-scope="scope">
          <el-popover :disabled="!isAdmin" placement="left" trigger="hover" :open-delay="800" popper-class="myHover">
            <el-button type="danger" :plain="true" size="mini" v-if="isAdmin" @click="remove(scope.row)">删除</el-button>
            <div slot="reference">
              <el-button type="primary" :plain="true" size="mini" @click="edit(scope.row)">编辑</el-button>
              <el-button type="primary" :plain="true" size="mini" @click="showModal(scope.row, 'modal', true)">复制</el-button>
              <el-button type="primary" :plain="true" size="mini" @click="fetch(scope.row)">同步</el-button>
              <el-button v-if="categoryType !== 'pc'" type="primary" :plain="true" size="mini" @click="online(scope.row)">二维码</el-button>
            </div>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :visible.sync="dialogVisible" width="30%">
      <img width="100%" :src="qrCode" alt />
    </el-dialog>

    <el-pagination background :current-page.sync="currentPage" v-show="totalSize > 10" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" @size-change="changeSize" @current-change="changePage"
      layout="total, sizes, slot, jumper, prev, pager, next" :total="totalSize"></el-pagination>

    <label style="color: red">注：状态、开始时间、结束时间同时为红色字体为正在使用中</label>

    <edit-modal ref="modal" @changePriority="changePriority" :current="curTopic" :origin="origin" :categoryType="categoryType" @close="close" @save-done="loadData"></edit-modal>

    <el-dialog @close="afterClose" :visible.sync="showMoreVisible" title="历史版本号">
      <p v-for="item in rowVersions" :key="item.mtime">
        {{ item.mtime }}
      </p>
    </el-dialog>
  </div>
  <div v-else>
    <newIndex></newIndex>
  </div>
</template>
<script>
import api from "api";
import topic from "api/topic";
import EditModal from "./edit-modal";
import { fetch, getDate } from "../../../utils/time-format";
import bus from "utils/eventbus";
import { AppWebsite } from "config";
import newIndex from "./newIndex.vue";
export default {
  name: "Topic",

  data() {
    return {
      handleSelectionVal:[],//选中数据
      popoverValue: '',
      sord: '',//排序字段
      sidx: '',//排序字段
      qrCode: "",
      dialogVisible: false,
      currentPage: 1,
      pageFrom: 1,
      pageSize: 10,
      totalSize: 10,
      loading: false,
      isAdmin: false,
      loginUser: {},
      searchParam: {},
      dataList: [],
      curTopic: {},
      origin: {
        copy: {}
      },
      state: null,
      usingState: null,
      usingStateShow: ["index", "start", "dialog", "KAHome"],
      usingStateShowType: 1,
      category: null,
      categoryType: "app",
      pageType: null,
      branchs: null,
      showMoreVisible: false,
      rowVersions: [],
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      search_obj: {
        category: "app",
        pageFrom: 1,
        pageSize: 10,
        page_type: "index"
        // department: 0,
      }
    };
  },

  components: {
    EditModal,newIndex
  },

  created() {
    // bus.$on("change_department", this.changeDepartment);
    // bus.$on("change_branch", this.changeBranch);
    bus.$on("send_category", value => {
      this.categoryType = value;
      this.sendDict(value);
    });
    bus.$on("change_type", this.change_type);
  },

  computed: {
    /**
     * 组装客户端类型"options"参数
     * @returns {*}
     */
    clientType() {
      let clientType = [];
      if (!this.category) return clientType;
      if (this.pageType) {
        var child = [];
        for (let k in this.pageType)
          child.push({
            value: k,
            label: this.pageType[k]
          });
      }
      for (let k in this.category)
        clientType.push({
          value: k,
          label: this.category[k],
          children: child
        });
      return clientType;
    }
  },

  watch: {
    searchParam: {
      deep: true,
      handler(val, oldVal) {
        {
          /* 客户端类型 */
          val.category = !val.clientType ? null : val.clientType[0];
          val.page_type = !val.clientType ? null : val.clientType[1];
        }
        /* 时间段 */
        if (
          val.searchStartDate &&
          !(!val.searchStartDate ^ !val.searchEndDate)
        ) {
          //两时间段都有值
          if (val.searchEndDate.getTime() < val.searchStartDate.getTime())
            val.searchEndDate = val.searchStartDate;
        }
        this.searchParam.startDate = !val.searchStartDate
          ? null
          : { _$gte: val.searchStartDate };
        this.searchParam.endDate = !val.searchEndDate
          ? null
          : { _$lte: val.searchEndDate };
      }
    }
  },

  mounted() { },

  filters: {
    stateText(k, dict) {
      return (dict || {})[k] || "未知";
    },

    dateFmt(date) {
      return date ? getDate(date) : "";
    },

    getBranchName(code, branchs) {
      let branchName = "";
      if (!code || !branchs || !branchs.length) return branchName;
      for (let i = 0, len = branchs.length; i < len; i++) {
        let branch = branchs[i];
        if (branch.branchCode == code) {
          branchName = branch.branchName;
          break;
        }
      }
      return branchName;
    }
  },

  methods: {
    focus(val){
      console.log(1)
val.focus=true
    },
    unFocus(){
      val.focus=false
    },
    //删除
    deleteMore(){
      console.log(this.handleSelectionVal)
      if(this.handleSelectionVal.length<1){
        this.$message.error("请选择")
        return
      }
      this.$confirm("是否删除该页面?删除后将永不能恢复，后果很严重！", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        let data=this.handleSelectionVal.map(item=>item.page_id)
        console.log(data)
        const result = await api.topic.removeMore(data);
        if (result.code == 200) {
          this.$message.success("删除成功");
          this.loadData();
        } else {
          this.$message.error(result.msg);
        }
      });
    },
    //多选
    handleSelectionChange(val){
      this.handleSelectionVal=val
console.log(val)
    },
    //排序
    handleSortChange({ column, prop, order }) {
      if (order && prop) {
        this.sord = order.replace('ending', '')
        this.sidx = prop
      } else {
        this.sord = ''
        this.sidx = ''
      }
      this.loadData();
    },
    setPriority(value) {
      this.popoverValue = value
    },
    hidePriority() {
      document.body.click()
    },
    async changePriority(index, row,newData) {
      //若为启动页则走新排序
      if(row.page_type=="start"){
        
        console.log(newData)
        this.loading = true
        let popoverValue =  newData?newData:this.popoverValue ? Number(this.popoverValue):1
        if(Number(popoverValue)<=0||Number(popoverValue)>99999){
          this.$message.error("优先级范围为1-99999")
          this.loading = false
            return
        }
         let ts = await api.topic.updatePriority([{ "id": row.page_id, "newPriority": popoverValue,ordPriority: row.priority||0,page_type:this.search_obj.page_type}])
         if (ts.code == 200) {
        this.hidePriority()
        this.loadData();
        this.loading = false
      }
      else { this.$message.error(ts.msg);this.loading = false }
        return
      }
      let popoverValue = this.popoverValue ? Number(this.popoverValue) : 1
      let ts = await api.topic.batchUpdatePriority([{ "id": row.page_id, "priority": popoverValue }])
      if (ts.code == 200) {
        this.$set(this.dataList[index], 'priority', popoverValue)
        this.hidePriority()
      }
      else { this.$message.error(ts.msg); }
    },
    logInsert(params) {
      topic.logInsert(params).then(res => {
        // console.log('2323', res)
      });
    },
    change_type(type = "app") {
      this.handleSelectionVal=[]
      setTimeout(() => {
        let sts = type.split("-");
        if (this.usingStateShow.indexOf(sts[1]) != -1) {
          this.usingStateShowType = 1; //展示
        } else {
          this.usingStateShowType = -1;
        }
        this.categoryType = sts[0];
        this.search_obj.category = sts[0];
        this.search_obj.page_type = sts[1];
        this.pageFrom = 1;
        this.currentPage = 1;
        this.sendDict(sts[0]);
        bus.$emit("menu_type", sts[0]);
      });
    },
    sendDict(value) {
      this.dict(value).then(data => {
        let locationUrl = location.hash.substr(1);
        let tempCategory = value;
        if (locationUrl.indexOf("=") > 0) {
          tempCategory = locationUrl.split("=")[1];
        }
        bus.$emit("menu_type", tempCategory);
        this.search_obj.category = tempCategory;
        this.editDefaultBranch();
        this.loadData();
        this.$store.dispatch("sideBar/setSideBarState", true);
        this.$store.dispatch("breadcrumb/clearPath");
        this.$store.dispatch("breadcrumb/addPath", {
          title: "页面管理",
          subTitle: "页面管理",
          action: "topic"
        });
        return Promise.resolve(this.search_obj);
      });
    },

    // changeDepartment(item) {
    // this.search_obj.department = item.id;
    // this.pageFrom = 1;
    // this.pageSize = 10;
    // this.loadData();
    // },

    // changeBranch(item) {
    //   this.pageFrom = 1;
    //   this.pageSize = 10;
    //   this.searchParam.branchCode = item.branchCode;
    //   this.loadData();
    // },

    async editDefaultBranch() {
      let branchs = [
        { branchName: "全国", branchCode: "" },
        ...this.origin.branchs
      ];
      let num = branchs.findIndex(val => {
        return val.branchCode == this.searchParam.branchCode;
      });
      if (num < 0) {
        this.$set(this.searchParam, "branchCode", "");
        let dfb = {
          branchName: "",
          branchCode: ""
        };
        const result = await api.user.editDefaultBranch(this.loginUser.id, dfb);
        if (result.code === 200) {
          const res = await api.user.current();
          if (res.code === 200) {
            this.$store.dispatch("sys/updateCurrentMember", res.data);
          } else {
            this.$message.error(res.msg);
          }
        } else {
          this.$message.error(result.msg);
        }
      }
    },

    copy_url() {
      this.$message({
        message: `复制成功`,
        type: "info"
      });
    },

    async loadData() {
      this.loading = true;
      let params = JSON.parse(JSON.stringify(this.search_obj));
      let pms = Object.assign(this.searchParam);
      for (let k in pms) {
        if (
          !(pms[k] == null || (typeof pms[k] == "string" && !pms[k].trim()))
        ) {
          params[k] = pms[k];
        }
      }
      params.pageFrom = this.pageFrom;
      params.pageSize = this.pageSize;
      if (this.sord && this.sidx) {
        params.selfSortFields = { priority: this.sord == 'desc' ? - 1 : 1 }
      }
      delete params.searchStartDate;
      delete params.searchEndDate;
      if (params.usingState && params.usingState == 1) {
        delete this.searchParam.state;
      }
      if (this.usingStateShowType !== 1) {
        delete params.usingState;
      }
      const result = await api.topic.list(params);
      this.loading = false;
      if (result.code == 200) {
        this.$nextTick(() => {
          this.dataList = result.data.rows;
          this.totalSize = result.data.total;
        });
      } else {
        this.$notify({
          message: result.msg,
          type: "error",
          dangerouslyUseHTMLString: true, //允许html
          offset: 100, //偏移
          duration: 60000
        });
      }
    },

    changePage(pageNo) {
      this.pageFrom = pageNo;
      this.loadData();
    },

    changeSize(pageSize) {
      this.currentPage = 1;
      this.pageSize = pageSize || this.pageSize;
      this.pageFrom = 1;
      this.loadData();
    },

    cancel() {
      this.searchParam = {
        branchCode: !this.isAdmin ? this.searchParam.branchCode : undefined
      };
      this.changeSize();
    },

    dict(value) {
      let prs = new Array(6);
      prs[0] = new Promise(res => res(api.user.current())).then(user => {
        if (user.code == 200)
          this.$nextTick(() => {
            this.origin.loginUser = this.loginUser = user.data;
            this.$set(
              this.searchParam,
              "branchCode",
              user.data.defaultBranch && user.data.defaultBranch.branchCode
                ? user.data.defaultBranch.branchCode
                : ""
            );
            this.isAdmin = this.loginUser.userName == "admin";
            /*if (!this.isAdmin)
                this.searchParam.branchCode = this.loginUser.branch.branchCode;*/
          });
        else this.$message.error(user.msg);
        return user;
      });
      prs[1] = new Promise(res => res(api.topic.topicState())).then(ts => {
        if (ts.code == 200)
          this.$nextTick(() => (this.origin.state = this.state = ts.data));
        else this.$message.error(ts.msg);
        return ts;
      });
      prs[2] = new Promise(res => res(api.topic.topicUsingState())).then(ts => {
        if (ts.code == 200)
          this.$nextTick(
            () => (this.origin.usingState = this.usingState = ts.data)
          );
        else this.$message.error(ts.msg);
        return ts;
      });
      prs[3] = new Promise(res =>
        res(
          api.topic.topicCategory({
            type: value
          })
        )
      ).then(tc => {
        if (tc.code == 200)
          this.$nextTick(
            () => (this.origin.category = this.category = tc.data)
          );
        else this.$message.error(tc.msg);
        return tc;
      });
      prs[4] = new Promise(res =>
        res(
          api.topic.topicPageType({
            type: value
          })
        )
      ).then(pt => {
        if (pt.code == 200)
          this.$nextTick(
            () => (this.origin.pageType = this.pageType = pt.data)
          );
        else this.$message.error(pt.msg);
        return pt;
      });
      prs[5] = new Promise(res => res(api.dict.branchHasOpen())).then(bho => {
        if (bho.code == 200)
          this.$nextTick(() => (this.origin.branchs = this.branchs = bho.data));
        else this.$message.error(bho.msg);
        return bho;
      });
      return Promise.all(prs.map(item => item.catch(e => e))) //并发请求
        .then(data => (this.origin.clientType = this.clientType)); //客户端 / 类型
    },

    close() {
      this.origin.copy = {};
      this.curTopic = {};
    },

    showModal(data, refName = "modal", isCopy) {
      data = _.cloneDeep(data || {});
      this.origin.clientTypeNow = [
        this.search_obj.category,
        this.search_obj.page_type
      ];
      this.origin.isActive =
        this.search_obj.page_type == "h5" ||
        this.search_obj.page_type == "KActivity";
      if (isCopy) {
        this.origin.copy = data;
        this.origin.isActive = false;
        this.curTopic.page_name = data.page_name + "-copy";
      } else {
        this.origin.copy = {};
        this.curTopic = data;
      }
      this.$refs[refName].show(true);
    },

    edit(data) {
      if (data.page_type && data.page_type == "start") {
        window.open(
          `#/apps/topic${this.categoryType !== "app" ? "Pc" : ""}/start/${data.page_id
          }`
        );
      } else {
        window.open(
          `#/apps/topic${this.categoryType !== "app" ? "Pc" : ""}/${data.page_id
          }`
        );
      }
    },

    showMore(data) {
      if (data.versions) {
        this.showMoreVisible = true;
        this.rowVersions = data.versions;
      }
    },

    afterClose() {
      this.showMoreVisible = false;
    },

    online(data) {
      if (data.url.indexOf("http") > -1) {
        this.qrCode = AppWebsite + "/cms/getQrCodeByUrl?url=" + data.url;
        this.dialogVisible = true;
        /*window.open(data.url);*/
      }
    },

    async fetch(data) {
      const result = await api.topic.fetch(data.page_id);
      if (result.code == 200) {
        this.$message.success("同步成功");
        this.logInsert(
          Object.assign(
            {
              type: "页面同步"
            },
            data
          )
        );
      } else {
        this.$message.error(result.msg);
      }
    },

    remove(data) {
      this.$confirm("是否删除该页面?删除后将永不能恢复，后果很严重！", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        const result = await api.topic.remove(data.page_id);
        if (result.code == 200) {
          this.$message.success("删除成功");
          this.loadData();
        } else {
          this.$message.error(result.msg);
        }
      });
    },

    synchronous() {
      this.$confirm("是否要同步所有活动页数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        const result = await api.topic.synchronous();
        if (result.code == 200) {
          this.$message.success("同步成功");
          this.loadData();
          // this.logInsert({
          //   type: '页面全部同步',
          // })
        } else {
          this.$message.error(result.msg);
        }
      });
    },

    tabRowCla({ row, i }) {
      /*if(row.state == 1)
            return 'bgc-safe';*/
      if (row.state == -1) return "bgc-warn";
      return "";
    }
  },

  destroyed() {
    // bus.$off("change_department", this.changeDepartment);
    bus.$off("send_category", this.sendDict);
    // bus.$off("change_branch", this.changeBranch);
    bus.$off("change_type", this.change_type);
  }
};
</script>
<style lang="scss" rel="stylesheet/scss">
// .main-content {
// }

.el-table .bgc-warn {
  background: oldlace;
}

.el-table .bgc-safe {
  background: #eeffee;
}

.input-class {
  .el-input__inner {
    min-width: 127px;
  }
}

.active {
  color: red;
}

.overtime {
  color: #b5bcc2;
}

.nouser {
  color: #8b8b8b;
}

.myHover {
  min-width: 50px;
}

.moreVersions {
  cursor: pointer;
}
.priorityCheck {
  display: flex;
  justify-content: space-around;
  .el-input {
    margin-right: 10px;
  }
}
</style>
