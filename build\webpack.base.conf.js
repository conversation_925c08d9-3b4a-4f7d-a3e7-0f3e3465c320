var path = require('path')
var utils = require('./utils')
var config = require('../config')
var vueLoaderConfig = require('./vue-loader.conf')
var env = process.env.BUILD_ENV;

function resolve(dir) {
    return path.join(__dirname, '..', dir);
}

module.exports = {
    entry: {
        main: './src/main',
        vendor: [
            'vue',
            'lodash',
            'axios',
            'vue-router',
            'element-ui'
        ]
    },
    output: {
        path: config[ env ].assetsRoot,
        publicPath: config[ env ].assetsPublicPath,
        filename: '[name].js'
    },
    resolve: {
        modules: [ resolve('src'), resolve('node_modules'), resolve('node_modules/vue-echarts') ],
        extensions: [ '.js', '.vue', '.json', '.scss' ],
        alias: {
            '@': resolve('src'),
            'vue$': 'vue/dist/vue.esm.js',
            'axios$': 'axios/dist/axios',
            'api': resolve('src/api'),
            'components': resolve('src/components'),
            'config': resolve('src/config'),
            'utils': resolve('src/utils'),
            'router': resolve('src/router'),
            'views': resolve('src/views'),
            'store': resolve('src/store'),
            'styles': resolve('src/styles'),
        }
    },
    module: {
        rules: [
            {
                test: /\.vue$/,
                loader: 'vue-loader',
                options: vueLoaderConfig
            },
            {
                test: /\.js$/,
                loader: 'babel-loader?cacheDirectory',
                include: [ resolve('src'), resolve('test') ]
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                loader: 'url-loader',
                options: {
                    limit: 10000,
                    name: utils.assetsPath('img/[name].[hash:7].[ext]')
                }
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                loader: 'url-loader',
                options: {
                    limit: 10000,
                    name: utils.assetsPath('fonts/[name].[hash:7].[ext]')
                }
            },
            {
                test: /\.(js|vue)$/,
                loader: resolve('build/replace-loader'),
                options: {
                    replace: [{
                        from: /API_DOMAIN/g,
                        to: config[ env ].apiDomain
                    },{
                        from: /TOPIC_FRAME/g,
                        to: config[ env ].topicFrameUrl
                    },{
                        from: /ASSET_PUBLIC_PATH/g,
                        to: config[ env ].assetsPublicPath
                    },{
                        from: /BUSINESS_DOMAIN/g,
                        to: config[ env ].businessDomain
                    },{
                        from: /APP_WEBSITE/g,   //App外部接口域名
                        to: config[ env ].appWebsite
                    }],
                }
            },
        ]
    }
}
