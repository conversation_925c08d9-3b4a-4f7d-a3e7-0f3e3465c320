<template>
  <div class="topic-image">
    <el-row :gutter="20">
      <div class="title">活动类型</div>
      <el-col :span="24">
        <el-select v-model="content.activityType" placeholder="请选择" disabled>
          <el-option
            label="拼团活动"
            :value="1"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">模块背景</div>
      <el-col :span="8">
        <div class="block">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg,image/png,image/gif"
            :show-file-list="false"
            :on-success="onUploadBgImg"
          >
            <el-button size="small" class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </div>
      </el-col>
      <el-col :span="8">
        <el-button size="small" @click="clearBg">清除背景图</el-button>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">背景色</span>
          <div>
            <el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">活动配置</div>
      <el-button type="primary" class="btn-block" @click="addActivity">添加活动</el-button>
    </el-row>
    <el-table :data="list" size="mini" :row-key="row => row.activityName" style="width: 100%">
      <!-- <el-table-column type="index" width="50"></el-table-column> -->
      <el-table-column prop="activityName" width="80px" label="活动名称">
        <template slot-scope="scope">
          <span v-html="scope.row.activityName"></span>
        </template>
      </el-table-column>
      <el-table-column label="人群名称" width="100px">
        <template slot-scope="scope">
          <span>{{scope.row.crowdValue || '全部人群'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="timevalue" width="80px" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.mode == 1" v-html="format_text(1, scope.row.timevalue)"></span>
          <span v-else v-html="format_text(2, scope.row.weekData)"></span>
        </template>
      </el-table-column>
      <el-table-column prop="timevalue" width="80px" label="展示类型">
        <template slot-scope="scope">
          <span>{{ {1: '固定时段', 2: '周期循环'}[scope.row.mode]}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="timevalue" label="展示时间">
        <template slot-scope="scope">
          <span v-if="scope.row.mode == 1 && scope.row.timevalue.length">
            {{format_date(scope.row.timevalue[0])}}~{{format_date(scope.row.timevalue[1])}}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="120px">
        <template slot-scope="scope">
          <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑</el-button>
          <el-button size="mini" @click="handleCancle(scope.row, scope.$index)" type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <activity-dialog
      v-if="add_editContent"
      :visible="add_editContent"
      :adItemData="adItemData"
      :editIndex="editIndex"
      @saveDialog="saveDialog"
    ></activity-dialog>
  </div>
</template>
<script>
  import base from "../../base";
  import api from "api";
  import activityDialog from './activityDialog';
  export default {
    name: 'groupTimeActivity',
    extends: base,
    components: {
      activityDialog
    },
    props: {
      core: Object,
    },
    contentDefault: {
      list: [],
      bgRes: "",
      // selectProducts: [],
      bgUrl: '',
      // mainTitleUrl: '',
      // subtitleUrl: '',
      jumpLink: 'ybmpage://commonh5activity?cache=0&url=https://app.ybm100.com/public/2021/6/16401.html'
    },
    data() {
      return {
        loading: false,
        add_editContent: false,
        adItemData: {},
        editIndex: '',
      }
    },
    computed: {
      list() {
        var list = _.get(this, "content.list");
        if (list) {
          this.$nextTick(function() {
            this.setSort();
          });
          return list;
        } else {
          return [];
        }
      }
    },
    created() {
      this.initData();
    },
    methods: {
      initData() {
        if (!this.content.componentVersion) {
          this.$set(this.content, 'componentVersion', 2);
          this.content.list = [{
            activityName: '活动名称',
            entryName: this.content.entryName || '',
            jumpLink: this.content.jumpLink || '',
            mainTitleUrl: this.content.mainTitleUrl || '//upload.ybm100.com/ybm/app/layout/cmsimages/2022-3/1e5040fd3138993a621060e1d4503947.png',
            subtitleUrl: this.content.subtitleUrl || '',
            selectProductType: this.content.selectProductType,
            selectProductGroupId: this.content.selectProductGroupId || '',
            selectProducts: this.content.selectProducts || [],
            crowdType: 1,
            crowdValue: '',
            crowdId: '',
            mode: 1,
            timevalue: [],
            weekData: [],
          }]
        }
        // this.productsArr = _.get(this, "content.selectProducts");
        // this.productsArr = JSON.parse(JSON.stringify(this.content.selectProducts || []));
        // this.productGroupId = this.content.selectProductGroupId;
      },
      
      clearBg() {
        this.content.bgUrl = '//upload.ybm100.com/ybm/app/layout/cmsimages/2021-10/46893b5ea8af28595fda37f58794673f.png';
        this.content.bgColor = '#FFFFFF';
      },
      format_text(type, data) {
        const _date = new Date().getTime();
        let start = '', end = '';
        if (type == 1) {
          if (data.length == 0) {
            return "<b style='color: red'>请设置时间</b>";
          }
          start = new Date(data[0]).getTime();
          end = new Date(data[1]).getTime();
        } else {
          let pageTimevalue = this.core.timevalue || [];
          start = new Date(pageTimevalue[0]).getTime();
          end = new Date(pageTimevalue[1]).getTime();
        }
        if (!start && !end) {
          return `<b style='color: #000000'>未开始</b>`;
        } else if (_date <= end && _date >= start) {
          return "<b style='color: #67C23A'>展示中</b>";
        } else if (_date < start) {
          return `<b style='color: #000000'>未开始</b>`;
        } else {
          return "<b style='color: #000000'>已过期</b>";
        }
      },
      addTamp(m) {
        return m < 10 ? '0' + m : m
      },
      format_date(timestamp) {
        if (timestamp === '') {
          return ''
        }
        var time = new Date(timestamp);
        var year = time.getFullYear();
        var month = time.getMonth() + 1;
        var date = time.getDate();
        var hours = time.getHours();
        var minutes = time.getMinutes();
        var seconds = time.getSeconds();
        return year + '-' + this.addTamp(month) + '-' + this.addTamp(date) + ' ' + this.addTamp(hours) + ':' +
          this.addTamp(minutes) + ':' + this.addTamp(seconds);
      },
      async onUploadBgImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.content.bgUrl = res.data.url;
      },
      addActivity() {
        this.add_editContent = true;
        this.adItemData = {};
        this.editIndex = '';
      },
      //编辑
      toEdit(data, index) {
        this.add_editContent = true;
        this.adItemData = JSON.parse(JSON.stringify(data));
        this.editIndex = index;
      },
      //删除
      handleCancle(row,index) {
        let _self = this;
        return function () {
          const index = _self.content.list.indexOf(row)
          _self.content.list.splice(index, 1)
          _self.$message({
              type: 'success',
              message: '删除成功!'
          });
        }.confirm(_self)()
      },
      closeEditContent() {
        this.add_editContent = false;
      },
      saveDialog(type, psData, index) {
        this.closeEditContent();
        if (type == 'edit') {
          this.content.list.splice(index, 1, psData)
        } else {
          let list = _.get(this, "content.list");
          if (list) {
            this.content.list.push(psData);
          } else {
            this.$set(this.content, 'list', []);
            this.content.list.push(psData);
          }
        }
      }, 
    }
  }
</script>

<style lang="scss" scoped>
  .el-row {
    text-align: center;
    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
    .entryNameBox {
      margin: 10px 0;
    }
  }
  .confirmIcon {
    color: #fff;
  }
  .productsDataBox {
    margin: 10px 0;
  }
  .btnBox {
    text-align: right;
    margin-right: 30px !important;
    margin-top: 20px;
  }
  .btn-block {
    display: block;
    width: 90%;
    // margin:10px auto;
  }
  .el-button--mini {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
  }
</style>