// see http://vuejs-templates.github.io/webpack for documentation.
const path = require('path')
module.exports = {
  prod: {
    env: require('./prod.env'),
    port: 9090,
    apiDomain: '/cms/',
    topicFrameUrl: '/public/static/',
    businessDomain: 'http://api.yunbaiplus.com/router?v=1.0&appKey=100001',
    assetsRoot: path.resolve(__dirname, '../online'),
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {},
    productionSourceMap: false,
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],
    cssSourceMap: false,
    bundleAnalyzerReport: false,
    appWebsite: 'https://app.ybm100.com/'   //App外部接口域名
  },
  staging: {
    env: require('./staging.env'),
    port: 9090,
    autoOpenBrowser: true,
    apiDomain: '/cms/',
    topicFrameUrl: '/public/static/',
    businessDomain: 'http://api.yunbaiplus.com/router?v=1.0&appKey=100001',
    assetsSubDirectory: 'static',
    assetsRoot: path.resolve(__dirname, '../online'),
    assetsPublicPath: '/',
    proxyTable: {},
    cssSourceMap: false,
    bundleAnalyzerReport: false,
    appWebsite: 'https://app-new.stage.ybm100.com/'    //App外部接口域名
  },
  dev: {
    env: require('./dev.env'),
    port: 9090,
    autoOpenBrowser: true,
    // apiDomain: '/cms/',
    // topicFrameUrl: '/public/static/',
    apiDomain: 'https://admin-cms.test.ybm100.com/cms/',
    topicFrameUrl: 'https://new-app.test.ybm100.com/public/static/',
    // apiDomain: 'http://127.0.0.1:7001/',
    // topicFrameUrl: 'http://127.0.0.1:9091/',
    businessDomain: 'http://api.yunbaiplus.com/router?v=1.0&appKey=100001',
    assetsSubDirectory: 'static',
    assetsRoot: path.resolve(__dirname, '../online'),
    assetsPublicPath: '/',
    proxyTable: {},
    cssSourceMap: false,
    bundleAnalyzerReport: false,
    appWebsite: 'https://new-app.test.ybm100.com/',   //App外部接口域名
    // appWebsite: 'https://new-app.dev.ybm100.com/',   //App外部接口域名
  },
  test: {
    env: require('./test.env'),
    port: 9090,
    autoOpenBrowser: true,
    apiDomain: '/cms/',
    topicFrameUrl: '/public/static/',
    businessDomain: 'http://api.yunbaiplus.com/router?v=1.0&appKey=100001',
    assetsSubDirectory: 'static',
    assetsRoot: path.resolve(__dirname, '../online'),
    assetsPublicPath: '/',
    proxyTable: {},
    cssSourceMap: false,
    bundleAnalyzerReport: false,
    appWebsite: 'https://new-app.test.ybm100.com/'    //App外部接口域名
  },
  test2: {
    env: require('./test2.env'),
    port: 9090,
    autoOpenBrowser: true,
    apiDomain: '/cms/',
    topicFrameUrl: '/public/static/',
    businessDomain: 'http://api.yunbaiplus.com/router?v=1.0&appKey=100001',
    assetsSubDirectory: 'static',
    assetsRoot: path.resolve(__dirname, '../online'),
    assetsPublicPath: '/',
    proxyTable: {},
    cssSourceMap: false,
    bundleAnalyzerReport: false,
    appWebsite: 'https://app2.test.ybm100.com/'    //App外部接口域名
  },
  bench: {
    env: require('./bench.env'),
    port: 9090,
    autoOpenBrowser: true,
    apiDomain: '/cms/',
    topicFrameUrl: '/public/static/',
    businessDomain: 'http://api.yunbaiplus.com/router?v=1.0&appKey=100001',
    assetsSubDirectory: 'static',
    assetsRoot: path.resolve(__dirname, '../online'),
    assetsPublicPath: '/',
    proxyTable: {},
    cssSourceMap: false,
    bundleAnalyzerReport: false,
    appWebsite: 'https://app.bench.ybm100.com/'    //App外部接口域名
  },
  singed: {
    env: require('./singed.env'),
    port: 9090,
    autoOpenBrowser: true,
    apiDomain: '/cms/',
    topicFrameUrl: '/public/static/',
    businessDomain: 'http://api.yunbaiplus.com/router?v=1.0&appKey=100001',
    assetsSubDirectory: 'static',
    assetsRoot: path.resolve(__dirname, '../online'),
    assetsPublicPath: '/',
    proxyTable: {},
    cssSourceMap: false,
    bundleAnalyzerReport: false,
    appWebsite: 'https://xyy-app.ybm100.com/'    //App外部接口域名
  },
}
