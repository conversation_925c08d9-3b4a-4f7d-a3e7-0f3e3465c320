<template>
  <header class="topbar">
    <div @click="onLogoClick" class="logo">
      <i class="iconfont icon-cms-c"></i>
      <span>CMS内容管理系统</span>
    </div>
    <ul class="el-menu top-menu-list el-menu--horizontal">
      <li
        v-for="(item,index) in tab_list"
        :key="index"
        role="menuitem"
        @click="navChange(index)"
        class="el-menu-item"
        :class="active_index === index ? 'is-active':''"
      >
        {{item.title}}
      </li>
    </ul>

    <el-dropdown class="user-info-wrap" @command="handleCommand">
      <span class="el-dropdown-link">
        {{currentAccount.userName}}
        <i class="el-icon-caret-bottom el-icon--right"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command class="user-name-wrap">
          {{currentAccount.userName}}
          <div class="user-name">{{currentAccount.roleNames}}</div>
        </el-dropdown-item>
        <el-dropdown-item :command="menu.actionUrl" v-for="menu in menuList" :key="menu.menuId">
          <i class="iconfont" :class="menu.icon"></i>
          {{menu.menuName}}
        </el-dropdown-item>
        <el-dropdown-item command="logout">
          <i class="iconfont icon-circle-left"></i>退出
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </header>
</template>

<script>
import bus from "utils/eventbus";
export default {
  name: "Topbar",
  data() {
    return {
      activeIndex: 1,
      active_index: 0,
      menuWidth: window.innerWidth - 200 + "px",
      navList: [
        {
          id: 1,
          name: "WORK GO总平台"
        }
      ],
      tab_list: [
        {
          title: "App管理"
        },
        {
          title: "PC管理"
        },
        {
          title: "H5管理"
        }
      ]
    };
  },
  watch: {
    isShow() {
      this.activeIndex = this.isShow ? "1" : "2";
    }
  },
  computed: {
    isShow() {
      return this.$store.getters["sideBar/isShow"];
    },
    currentAccount() {
      return this.$store.getters["sys/currentAccount"];
    },
    menuList() {
      return this.$store.getters["sideBar/accountMenu"];
    },
    corp() {
      return this.$store.getters["corp/corp"];
    }
  },
  methods: {
    /*
     * 处理菜单跳转
     * */
    handleCommand(command) {
      if (!command) {
        return;
      }
      switch (command) {
        case "logout":
          localStorage.clear();
          this.$router.replace("/auth/login");
          break;
        default:
          this.$router.replace(command);
      }
    },
    /**
     * 处理 logo 的点击
     */
    onLogoClick() {
      // this.$router.push('home')
    },
    navChange(index) {
      // 老逻辑
      this.$store.dispatch("sideBar/setSideBarState", true);
      const navList = this.$store.getters["sideBar/navList"];
      this.$router.replace(navList[0].actionUrl);
      //新逻辑
      this.active_index = index;
      setTimeout(() => {
        switch (index) {
          case 0:
            bus.$emit("change_type", "app");
            break;
          case 1:
            bus.$emit("change_type", "pc");
            break;
          case 2:
            bus.$emit("change_type", "h5");
            break;
        }
      }, 500);
    }
  }
};
</script>


<style lang="scss" rel="stylesheet/scss" scoped>
.topbar {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #fff;
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 200px;
    height: 50px;
    font-size: 14px;
    color: #fff;
    background-color: $color-light-silver;
    cursor: pointer;
    .iconfont {
      font-size: 30px;
      margin-right: 10px;
    }
  }
  .top-menu-list {
    border-bottom: none;
    .el-menu-item {
      padding: 0;
      margin-left: 20px;
      height: 50px;
      line-height: 50px;
      border-width: 3px;
      &.is-active {
        color: $color-primary;
      }
    }
  }
  .user-info-wrap {
    @include middle-center-y();
    right: 30px;
    cursor: pointer;
  }
}
</style>
