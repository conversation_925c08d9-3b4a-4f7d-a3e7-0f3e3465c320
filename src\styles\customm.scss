@import "config";

$--color-primary: $color-primary;

$--font-path: '../../node_modules/element-ui/lib/theme-chalk/fonts';

@import "../../node_modules/element-ui/packages/theme-chalk/src/index";

.el-form-item.is-success .el-input__inner,
.el-form-item .el-input__inner:focus,
.el-form-item.is-success .el-input__inner:focus,
.el-form-item .el-textarea__inner:focus,
.el-form-item.is-success .el-textarea__inner,
.el-form-item.is-success .el-textarea__inner:focus {
	border-color: #ccc !important;
}

.el-table__header {
	th {
		background-color: #eee;
	}
}

.el-button--text {
	padding: 0;
	font-weight: 400;
	max-width: 100%;
	@include ellipsis();
	box-sizing: border-box;
	text-align: left;
}

.el-pagination {
	margin-top: 10px;
	font-weight: normal;
	text-align: right;
}

.custom-table {
	width: 100%;
	min-height: 70px;
}

.empty-wrap {
	display: flex;
	align-items: center;
	color: $black;
	justify-content: center;
	.iconfont {
		margin-right: 10px;
		font-size: 36px;
		color: #666;
	}
}

.search-wrap {
	display: flex;
	flex-direction: row-reverse;
	padding-right: 5px;
	&.search-wrap-left {
		flex-direction: row;
	}
	.el-select, .el-date-editor {
		margin-left: 10px;
	}
	& > .el-input {
		margin-left: 10px;
		width: 210px;
	}
}

.active-color {
	color: colors(green);
}

.el-button i {
	font-weight: bold;
}

.el-form-item__label {
	font-size: 13px;
}

.pb-5 {
	padding-bottom: 5px;
}

.pt-5 {
	padding-top: 5px;
}

.pr-5 {
	padding-right: 5px;
}

.pl-5 {
	padding-left: 5px;
}

.pb-10 {
	padding-bottom: 10px;
}

.pt-10 {
	padding-top: 10px;
}

.pr-10 {
	padding-right: 10px;
}

.pl-10 {
	padding-left: 10px;
}

.pb-20 {
	padding-bottom: 20px;
}

.pt-20 {
	padding-top: 20px;
}

.pr-20 {
	padding-right: 20px;
}

.pl-20 {
	padding-left: 20px;
}

.mb-5 {
	margin-bottom: 5px;
}

.mt-5 {
	margin-top: 5px;
}

.mr-5 {
	margin-right: 5px;
}

.ml-5 {
	margin-left: 5px;
}

.mb-10 {
	margin-bottom: 10px;
}

.mt-10 {
	margin-top: 10px;
}

.mr-10 {
	margin-right: 10px;
}

.ml-10 {
	margin-left: 10px;
}

.mb-20 {
	margin-bottom: 20px;
}

.mt-20 {
	margin-top: 20px;
}

.mr-20 {
	margin-right: 20px;
}

.ml-20 {
	margin-left: 20px;
}

.ptb-5 {
	padding-top: 5px;
	padding-bottom: 5px;
}

.ptb-10 {
	padding-top: 10px;
	padding-bottom: 10px;
}

.ptb-20 {
	padding-top: 20px;
	padding-bottom: 20px;
}

.plr-5 {
	padding-left: 5px;
	padding-right: 5px;
}

.plr-10 {
	padding-left: 10px;
	padding-right: 10px;
}

.plr-20 {
	padding-left: 20px;
	padding-right: 20px;
}

.mtb-5 {
	margin-top: 5px;
	margin-bottom: 5px;
}

.mtb-10 {
	margin-top: 10px;
	margin-bottom: 10px;
}

.mtb-20 {
	margin-top: 20px;
	margin-bottom: 20px;
}

.mlr-5 {
	margin-left: 5px;
	margin-right: 5px;
}

.mlr-10 {
	margin-left: 10px;
	margin-right: 10px;
}

.mlr-20 {
	margin-left: 20px;
	margin-right: 20px;
}

.p-5 {
	padding: 5px;
}

.p-10 {
	padding: 10px;
}

.p-20 {
	padding: 20px;
}

.m-5 {
	margin: 5px;
}

.m-10 {
	margin: 10px;
}

.m-20 {
	margin: 20px;
}

.main-content {
	padding: 20px;
}

.el-tabs {
	padding-top: 20px;
}

.content-title {
	padding-top: 5px;
	padding-bottom: 15px;
	margin-bottom: 30px;
	border-bottom: 1px solid $extra-black;
}

.data-form {
	//width: 440px;
	padding-left: 30px;
	.btn-save {
		width: 105px;
	}
	.el-input, .el-textarea, .el-select {
		width: 305px;
		&.mini {
			width: 140px !important;
		}
		&.short {
			width: 200px !important;;
		}
		&.middle {
			width: 400px !important;;
		}
		&.long {
			width: 600px !important;;
		}
	}
	.el-form-item__content {
		display: flex;
		align-items: center;
	}
	.el-form-item__error {
		position: relative;
		display: inline-block;
		margin-left: 10px;
	}
	.el-date-editor--daterange, .el-date-editor--timerange {
		width: 305px !important;
	}
}

.el-input .el-input__inner:focus,
.el-input .el-textarea__inner:focus {
	border-color: #ccc;
}

.info-form {
	width: 705px;
	//padding-left: 20px;
	//padding-right: 20px;
	margin-right: 20px;
	margin-top: 20px;
	padding-bottom: 50px;
	.module-title {
		position: relative;
		font-size: 22px;
		padding-top: 0 !important;
		border-bottom: none !important;
		padding-bottom: 0 !important;
		.el-input {
			width: auto;
		}
		.el-icon-edit-outline {
			color: #999;
			cursor: pointer;
		}
		.status {
			@include middle-center-y();
			right: 0;
			width: 78px;
			height: 25px;
			line-height: 25px;
			font-size: 12px;
			color: #AEAEAE;
			text-align: center;
			background-color: rgba(246, 246, 246, 1);
			border: 1px solid rgba(188, 188, 188, 1);
			cursor: pointer;
			.icon-chuyidong {
				color: #f00;
			}
			.el-icon-check {
				color: #67c23a;
				font-weight: bold;
				margin-right: 5px;
			}
		}
	}
	.grid-content {
		padding-top: 20px;
		padding-left: 10px;
		padding-bottom: 8px;
		border-bottom: 1px solid $extra-black;
		.grid-content-title {
			padding-bottom: 5px;
			color: #999;
		}
	}
}

.el-tabs__nav {
	//border: none!important;
	.el-tabs__item {
		//border-left: none !important;
		//border-right: none !important;
		height: 32px !important;
		line-height: 32px !important;
		font-size: 13px !important;
		&.is-active {
			//border-top: 1px solid $extra-black !important;
			//border-left: 1px solid $extra-black !important;
			//border-right: 1px solid $extra-black !important;
			//color: $black !important;
		}
	}
}

.el-switch {
	position: relative;
	.el-switch__core {
		width: 55px !important;
	}
	&.is-active {
		.el-switch__button {
			left: auto;
			right: 22px;
		}
	}
	.el-switch__label {
		@include middle-center-y();
		right: 10px;
		display: none;
		z-index: 200;
		span {
			font-size: 12px;
			color: #fff;
		}
		&.is-active {
			display: block;
		}
		&.el-switch__label--left {
			right: 0;
		}
		&.el-switch__label--right {
			left: 0;
		}
	}
	&.is-checked {
		.el-switch__button {
			right: 22px;
			left: auto;
		}
	}
}

.dialog-form {
	padding-left: 30px;
	padding-right: 30px;
	.el-input, .el-select {
		width: 100%;
	}
}

.el-checkbox__label {
	font-size: 13px;
}

.dialog-footer {
	.el-checkbox {
		float: left;
		margin-left: 40px;
	}
}

.el-tabs__content {
	padding-top: 10px;
}

.el-icon-search {
	cursor: pointer;
}

.el-form-item.is-required .el-form-item__label:before {
	margin-right: -4px;
	position: relative;
	left: -8px;
	top: 3px;
}

.border-b {
	border-bottom: 1px solid $extra-black;
}

.input-with-select {
	width: 280px !important;
	.el-input-group__prepend {
		padding-left: 0;
		width: 80px;
		.el-select {
			margin-left: 0;
		}
	}
}

.el-button--small {
	min-width: 105px;
	text-align: center;
}

.el-button--text {
	min-width: inherit;
}

//.el-button--default {
//  &:hover {
//    color: #5a5e66;
//    background-color: #fff;
//    border-color: #d8dce5;
//  }
//}
.btn-link {
	display: inline-block;
	padding-left: 15px;
	padding-right: 15px;
	font-size: 12px;
	height: 28px;
	line-height: 28px;
	border: 1px solid #d8dce5;
	border-radius: 3px;
	box-sizing: border-box;
}

.pic-desc {
	display: flex;
	align-items: center;
	line-height: 1.5;
	width: 400px;
	font-size: 13px;
	color: #999;
}

.uploader-wrap, .uploader-wrap2 {
	width: 100px;
	margin-right: 10px;
	.uploader {
		position: relative;
		height: 100px;
		&:hover {
			.close-wrap {
				opacity: 1;
			}
		}
		.preview-img {
			width: 100px;
			height: 100px;
			background: center no-repeat;
			background-size: cover;
		}
		.el-icon-plus-wrap {
			position: relative;
			width: 100px;
			height: 100px;
			border: 1px solid #ddd;
			font-size: 12px;
			color: $black;
			.icon-content {
				@include middle-center();
				width: 100%;
			}
			.el-icon-plus {
				display: inline-block;
				margin-top: 5px;
				font-size: 25px;
			}
		}
		.close-wrap {
			position: absolute;
			top: 20px;
			right: 20px;
			.iconfont {
				@include middle-center();
				font-size: 25px;
				font-weight: bold;
				color: $black;
			}
		}
	}
}

.uploader-wrap2 {
	width: 180px;
	.uploader {
		height: 120px;
		.preview-img {
			width: 180px;
			height: 120px;
		}
		.el-icon-plus-wrap {
			width: 180px;
			height: 120px;
			border: 1px solid #ddd;
		}
		.icon-content {
			@include middle-center();
			width: 100%;
		}
		.el-icon-plus {
			display: inline-block;
			margin-top: 5px;
			font-size: 25px;
		}
	}
}

.table-header {
	background-color: #F2F2F2;
	border: 1px solid $extra-black;
	padding: 8px 20px;
}

.footer-btn {
	float: right;
	margin-top: 10px;
	&.footer-btn-left {
		float: left;
	}
}

.no-padding {
	padding: 0;
}

.disabled {
	color: #999;
}

.el-date-editor--daterange {
	display: inline-block;
	width: 230px !important;
}

.el-select {
	.el-input {
		width: 100% !important;
	}
}

.form-short {
	&.el-select {
		width: 120px;
	}
	&.el-input {
		width: 200px;
	}
}

.form-mini {
	&.el-input {
		width: 120px;
	}
}

.flex-column {
	.el-form-item__content {
		flex-flow: column;
		align-items: baseline;
	}
}

.border-dashed {
	padding-bottom: 15px;
	border-bottom: 1px dashed #ddd;
}

.el-select {
	&.short {
		width: 100px;
	}
	&.mini {
		width: 80px;
	}
}

.el-input {
	&.short {
		width: 150px;
	}
	&.mini {
		width: 80px;
	}
}

.el-pagination__sizes {
	.el-select {
		width: 90px;
	}
}

.align-r {
	display: block;
	margin-left: auto;
}

.border-t {
	border-top: 1px solid #ddd;
}

.el-date-editor {
	display: flex;
}

.el-date-editor-inline {
	display: inline-block !important;
}

.flex {
	display: flex;
	align-items: center;
}

.dialog-footer {
	text-align: right;
}

.btn-block {
	display: block;
	width: 100%;
}