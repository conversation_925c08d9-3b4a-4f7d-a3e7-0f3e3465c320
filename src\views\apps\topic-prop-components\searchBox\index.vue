
<template>
  <div class="topic-image">
    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">左侧滑动颜色</span>
          <div>
            <el-color-picker v-model="content.tab_bg_color1" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">右侧滑动颜色</span>
          <div>
            <el-color-picker v-model="content.tab_bg_color2" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">刷新区域颜色</span>
          <div>
            <el-color-picker v-model="content.refresh_bgRes" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">默认icon颜色</span>
          <div>
            <el-select v-model="content.default_icon_color" size="mini" placeholder="请选择">
              <el-option v-for="item in icon_color_list" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">滑动icon颜色</span>
          <div>
            <el-select v-model="content.active_icon_color" size="mini" placeholder="请选择">
              <el-option v-for="item in icon_color_list" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">搜索标题文字配置</span>
          <div>
            <el-input placeholder="搜索标题文字配置" v-model="content.search_text" size="mini"></el-input>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">头部搜索区域背景图</span>
          <div>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="UploadTopSearchBg">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">热词区域背景图</span>
          <div>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="UploadhotWord_bgRes">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">banner主体背景图</span>
          <div>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="UploadMeddleSearchBg">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">banner底部区域背景图</span>
          <div>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="UploadBottomSearchBg">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>

      <el-col :span="8">
        <div class="block">
          <span class="demonstration">清除背景图</span>
          <div>
            <el-button size="small" type="primary" @click="clear_bgs">清除背景图</el-button>
          </div>
        </div>
      </el-col>

    </el-row>

    <swiper-point :content="content"></swiper-point>
    <!--上传轮播图片和编辑设置-->
    <el-row :gutter="20">
      <div class="title">上传轮播和轮播编辑</div>
      <el-col :span="24">
        <el-button @click="toAdd" class="btn-block mb-10" type="primary">上传banner</el-button>
      </el-col>
    </el-row>
    <!--轮播位置选择-->
    <el-row :gutter="20">
      <div class="title">轮播列表</div>
      <div class="tabBox">
        <div :class="activeTab === 'notInvalid' ? 'activeTab' : ''" @click="changeTab('notInvalid')">有效/未失效</div>
        <div :class="activeTab === 'invalid' ? 'activeTab' : ''" @click="changeTab('invalid')">已失效</div>
      </div>
      <el-row class="carouselFlexBox">
        <el-col :span="10" class="carouselFlex">
          <span>轮播位：</span>
          <el-select v-model="carouselList.bannerLocation" placeholder="请选择" size="mini" clearable>
            <el-option value="" label="全部"></el-option>
            <el-option v-for="item in bannerLocationList" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col :span="11" class="carouselFlex">
          <span>人群名称：</span>
          <el-input v-model="carouselList.crowdValue" size="mini" clearable></el-input>
        </el-col>
        <el-col :span="10" class="carouselFlex">
          <span>状态：</span>
          <el-select v-model="carouselList.status" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in statusList.filter(i=> { return activeTab === 'notInvalid' ? i.id != 3 : i.id == 3 })" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col :span="4" class="carouselButton">
          <el-button type="primary" @click="searchList" size="mini">查询</el-button>
        </el-col>
      </el-row>
      <el-table :data="dataList" size="mini" class="tableBox" style="margin: 0 0 20px" ref="tableBox" :row-key="row => row.id">
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column label="帧位">
          <template slot-scope="scope">
            <div class="img">
              {{scope.row.bannerLocations}}帧
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2">展示中</div>
            <div v-else>
              {{getStatusName(scope.row.timevalue)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="图片">
          <template slot-scope="scope">
            <div class="img">
              <img :src="scope.row.image" alt='' />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="开始结束时间" min-width="120px" show-overflow-tooltip>
      
          <template slot-scope="scope" >
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
             
           
            </div>
          <div v-else>  {{scope.row.timevalue|dateFilter}}</div>
          </template>
        </el-table-column>
        <el-table-column label="人群" show-overflow-tooltip>
          <template slot-scope="scope">
            <p>
              {{scope.row.crowdValue || '全部人群'}}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="页面链接" prop="link" show-overflow-tooltip>
          <template slot-scope="scope">
            <p>
              {{scope.row.link | link}}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="150px">
          <template slot-scope="scope">
            <el-button size="mini" @click="toEdit(scope.row, scope.$index)">编辑
            </el-button>
            <el-button size="mini" @click="toRemove(scope.row)" type="danger">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <!--轮播的-->
    <el-dialog class="banner-dialog" title="banner设置" :visible.sync="addDialog">
      <el-form label-position="right" size="small" label-width="100px" label-suffix="：">
        <el-form-item label="轮播位置" prop="bannerLocation">
          <el-select v-model="dataForm.bannerLocation" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in bannerLocationList" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="指定人群">
          <el-radio-group v-model="dataForm.crowdType" @change="changeCrowdType">
            <el-radio :label="1">全部人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="dataForm.crowdType===2" label="指定人群">
          <el-select
            v-model.trim="dataForm.crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-autocomplete style="width: 100%" class="inline-input" v-model.trim="dataForm.crowdValue" :fetch-suggestions="querySearchCrowd" placeholder="请输入人群id" :trigger-on-focus="false"
            @select="handleSelectCrowd" @input="changeCrowdValue"></el-autocomplete> -->
        </el-form-item>
            <el-form-item label="展示时间" >
              <el-radio  v-model="dataForm.timeType" label="1">固定时段</el-radio> <el-date-picker v-model="dataForm.timevalue"  type="datetimerange" :picker-options="pickerOptions1" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="left">
          </el-date-picker><br>
              <el-radio  v-model="dataForm.timeType" label="2">周期循环</el-radio>  <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
       
      </el-form-item>
     
      <br>
       
        
      </el-form>

      <el-upload class="topic-image-upload" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
        :on-success="onUploadImage">
        <img v-if="dataForm.image" :src="dataForm.image" class="image">
        <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>
      <br>
      <!-- <el-row :gutter="20">
        <el-col :span="24">
          <el-date-picker v-model="dataForm.timevalue" type="datetimerange" :picker-options="pickerOptions1" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
          </el-date-picker>
        </el-col>
      </el-row>
      <br> -->
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">banner和头部整体背景色</span>
            <div>
              <el-color-picker v-model="dataForm.bgRes" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="block">
            <span class="demonstration">banner对应底部颜色</span>
            <div>
              <el-color-picker v-model="dataForm.rest_bgRes" size="mini"></el-color-picker>
            </div>
          </div>
        </el-col>
      </el-row>
      <br>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-radio v-model="dataForm.bannerType" label="inLink">内部跳转</el-radio>
          <el-radio v-model="dataForm.bannerType" label="outLink">外部跳转</el-radio>
        </el-col>
      </el-row>
      <br>
      <el-row :gutter="20">
        <el-col :span="24">
          <!-- 跳转链接<span>({{dataForm.link | link}})</span>-->
          <el-input placeholder="链接地址" v-model="dataForm.link.meta.page_url" @input="urlChange">
            <template slot="prepend">跳转链接</template>
          </el-input>
          <div v-show="!dataForm.bannerType||dataForm.bannerType==='inLink'">
            <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>
    <loopcirculateTime ref="loopcirculateTime" @loopcirculateTimeBack="loopcirculateTimeBack"></loopcirculateTime>
  </div>
</template>
<script>
import loopcirculateTime from '../../components/loopcirculateTime.vue';
import base from "../base";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import Sortable from 'sortablejs';
let sortableObject = {}
export default {
  name: 'searchBox',
  extends: base,
  components: { swiperPoint,loopcirculateTime },
  contentDefault: {
    tab_bg_color1: "",
    tab_bg_color2: "",
    pro_obj: {
      pro_type: "longBar",
      pro_auto: 0,
      pro_align_type: "center",
      default_color: "#ffffff",
      default_opacity: 30,
      active_color: "#555555",
      active_opacity: 100,
      component_name: "searchBox", //区分模块的标识
    },
    list: [],
    search_text: "您常搜",
    active_icon_color: "#ffffff",
    default_icon_color: "#ffffff",
    // top_bgRes: "#00B377",
    // hotWord_bgRes: "#00B377",
    // meddle_bgRes: "#00B377",
    // bottom_bgRes: "#00B377",
    // refresh_bgRes: "#00B377"
    top_bgRes: "#fff",
    hotWord_bgRes: "#fff",
    meddle_bgRes: "#fff",
    bottom_bgRes: "#fff",
    refresh_bgRes: "#fff"
  },
  data() {
    return {
      keys: 'id',
      dataList: [], // 查询完的列表
      carouselList: {
        bannerLocation: '',
        crowdValue: '',
        status: ''
      },
      currentData: {},
      currentDataIndex: 0,
      currentIndex: 0,
      activeTab: 'notInvalid',
      selectLoading: false,
      options: [],
      bannerLocationList: [{
        id: 1,
        name: '第一帧'
      }, {
        id: 2,
        name: '第二帧'
      }, {
        id: 3,
        name: '第三帧'
      }, {
        id: 4,
        name: '第四帧'
      }, {
        id: 5,
        name: '第五帧'
      }, {
        id: 6,
        name: '第六帧'
      }, {
        id: 7,
        name: '第七帧'
      }, {
        id: 8,
        name: '第八帧'
      }],
      statusList: [{
        id: '',
        name: '全部'
      }, {
        id: 1,
        name: '生效中'
      }, {
        id: 2,
        name: '待生效'
      }, {
        id: 3,
        name: '已失效'
      }, {
        id: 4,
        name: '未设置时间'
      }],
      icon_color_list: [
        {
          label: "黑色",
          value: "#000000"
        },
        {
          label: "白色",
          value: "#ffffff"
        },
      ],
      pickerOptions0: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      pickerOptions1: {
        shortcuts: [{
          text: '未来一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来六个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来一年',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      loading: false,
      addDialog: false,
      dataForm: {
        bannerLocation: '',
        bannerType: "inLink",
        image: '',
        link: {
          meta: {
            id: 0,
            page_url: ''
          }
        },
        timevalue: '',
        // bgRes: "#00B377",
        bgRes: "#fff",
        rest_bgRes: "#ffffff",
        crowdType: 1,
        crowdValue: '',
        crowdId: '',
        timeType:'1',
        circulateTime:{}
      },
    }
  },
  filters: {
    link(data) {
      return data.meta.page_url;
    },
    dateFilter(date) {
      function formatDate(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return year + '-' + (String(month).length > 1 ? month : '0' + month) + '-' +
          (String(day).length > 1 ? day : '0' + day) + ' ' + (String(hour).length > 1 ? hour : '0' + hour) + ':' + (String(minute).length > 1 ? minute : '0' + minute)
          + ':' + (String(second).length > 1 ? second : '0' + second)
      }

      if (date) {
        let date1 = formatDate(new Date(date[0]));
        let date2 = formatDate(new Date(date[1]));
        // const nS=new Date(date).getTime()
        return date1 + "至" + date2
      } else {
        return " "
      }
    },
    jumpText(val) {
      if (!val) {
        return "app内部跳转"
      } else {
        if (val === "inLink") {
          return "app内部跳转"
        }
        return "跳转至外部"
      }
    }
  },
  mounted() {
    this.initData();
    this.rowDrop()
    this.changeTab('notInvalid')
    // this.searchList()
  },
  computed: {
    /**
     *   获取列的状态名称
     */
    getStatusName() {
      return function (timevalue, type) {
        let item = {}
        if (!timevalue) {
          item = {
            id: 4,
            name: '未设置时间'
          }
        } else {
          const _date = new Date().getTime();
          const start = new Date(timevalue[0]).getTime();
          const end = new Date(timevalue[1]).getTime();
          if (_date <= end && _date >= start) {
            item = {
              id: 1,
              name: '生效中'
            }
          } else if (_date > end) {
            item = {
              id: 3,
              name: '已失效'
            }
          } else if (_date < start) {
            item = {
              id: 2,
              name: '待生效'
            }
          }
        }
        if (type == 'id') {
          return item.id
        } else {
          return item.name
        }
      }
    },
    // list() {
    //   let list = _.get(this, 'content.list')
    //   if (list) {
    //     if (list.length > 0 && list[0].link.meta) {
    //       this.$nextTick(function () {
    //         this.setSort()
    //       })
    //     }
    //     return list
    //   } else {
    //     return [];
    //   }
    // }
  },
  methods: {
   
    //打开时间循环
    toloopcirculateTime(){
      this.$refs.loopcirculateTime.showVisible=true
    },
    //循环时间回调
    loopcirculateTimeBack(data){
      this.dataForm.circulateTime=data
console.log(data)
    },
    //链接去掉空格
    urlChange(){
      this.dataForm.link.meta.page_url=this.dataForm.link.meta.page_url.trim()
    },
    initData() {
      let notInvalidArr = [];
      let invalidArr = [];
      notInvalidArr = this.content.list.filter((item) => {
        return this.getStatusName(item.timevalue) !== '已失效'
      })
      invalidArr = this.content.list.filter((item) => {
        return this.getStatusName(item.timevalue) === '已失效'
      })
      this.content.list = notInvalidArr.concat(invalidArr);
    },
    rowDrop() {
      const _this = this;
      const tbody = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0];
      sortableObject = Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        ghostClass: "sortable-ghost",
        onEnd: evt => {
          const currRow = (_this.dataList || []).splice(evt.oldIndex, 1)[0];
          (_this.dataList || []).splice(evt.newIndex, 0, currRow);
          const currRowData = (_this.content.list || []).splice(evt.oldIndex, 1)[0];
          (_this.content.list || []).splice(evt.newIndex, 0, currRowData);
        }
      });
    },
    changeCrowdValue(e) {
      if (!e) {
        this.dataForm.crowdId = '';
      }
      this.$forceUpdate();
    },
    clear_bgs() {
      this.content.top_bgRes = "#fff";
      this.content.meddle_bgRes = "#fff";
      this.content.bottom_bgRes = "#fff";
      this.content.hotWord_bgRes = "#fff"
    },

    // 设置轮播链接
    onSetLink(link) {
      this.dataForm.link = link
    },

    // 上传轮播图片
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.dataForm.image = res.data.url
    },

    // 上传banner对应的头部区域背景图片
    async UploadTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.content.top_bgRes = res.data.url
    },
    UploadhotWord_bgRes(res) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.content.hotWord_bgRes = res.data.url
    },
    // 上传banner对应的中间区域背景图片
    async UploadMeddleSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.content.meddle_bgRes = res.data.url
    },
    // 上传banner对应的底部区域背景图片
    async UploadBottomSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.content.bottom_bgRes = res.data.url
    },
    changeCrowdType() {
      this.dataForm.crowdId = '';
      this.dataForm.crowdValue = '';
    },
    // 确定上传
    async confirm() {
      if (!this.dataForm.bannerLocation) {
        this.$message.warning("请选择轮播位置");
        return false;
      }
      if (!this.dataForm.crowdType || (this.dataForm.crowdType === 2 && !this.dataForm.crowdId)) {
        this.$message.warning("请选择正确的人群");
        return false;
      }if (!this.dataForm.timevalue&&this.dataForm.timeType!=2) {
          this.$message.warning("请选择有效时间");
          return false;
        }
        if (this.dataForm.timeType==2&&(!this.dataForm.circulateTime||Object.keys(this.dataForm.circulateTime).length==0||!this.dataForm.circulateTime.circulateList||this.dataForm.circulateTime.circulateList.length==0)) {
          this.$message.warning("请添加【周期循环】 时间段。");
          return false;
        }
      // this.dataForm.crowdId = (this.currentCrowd || {}).id;
      if (!this.dataForm.image) {
        this.$message.warning('请上传图片');
        return false;
      }
      if (!this.dataForm.timevalue) {
        this.dataForm.timevalue = ""
      }
      let linkErrMsg = '';
      if (this.dataForm.bannerType === 'inLink' && ((this.dataForm.link || {}).meta || {}).page_url) {
        if (!new RegExp("^ybmpage://commonh5activity.*$").test(((this.dataForm.link || {}).meta || {}).page_url)) {
          linkErrMsg = '跳转链接格式不正确，请检查';
        } else {
          let linkPageUrl = getUrlParam(((this.dataForm.link || {}).meta || {}).page_url, 'url')
          const result = await api.topic.checkPageUrl({ url: linkPageUrl });
          if (((result || {}).data || {}).status != 200) {
            linkErrMsg = '跳转链接不存在，请检查';
          }
        }
      }
      if (linkErrMsg) {
        this.$message.error(linkErrMsg);
        return false;
      }
      this.addDialog = false;
      if (this.isEdit) {
        // this.currentData = Object.assign(this.currentData, this.dataForm);
        // this.content.list.splice(this.currentIndex, 1, this.currentData);
        // this.$set(this.dataList, this.currentDataIndex, this.currentData)

        this.currentData = Object.assign(this.currentData, this.dataForm);
        this.content.list.splice(this.currentIndex, 1);
        this.sortByRule(this.currentData);
      } else {
        let data = {
          ...this.dataForm,
          id: this.genID(12)
        }
        this.sortByRule(data);
      }
    },

    // 按照规则排序--排序规则优先级：人群 > 帧位 > 生效时间（生效中>待生效>未设置）
    sortByRule(data) {
      const samePeople = this.content.list.filter((item,index) => {
        return Number(item.crowdId) === Number(this.dataForm.crowdId)
      });
      // 相同人群的逻辑
      if (samePeople.length) {
        const sameLocation = samePeople.filter((item, index) => {
          return Number(item.bannerLocation) === Number(this.dataForm.bannerLocation)
        });
        // 相同人群下，相同帧位的逻辑
        if (sameLocation.length) {
          const sameStatus = sameLocation.filter((item, index) => {
            return this.getStatusName(this.dataForm.timevalue) === this.getStatusName(item.timevalue);
          });
          let tempIndex = undefined;
          if (sameStatus.length) {
            this.content.list.forEach((item, index) => {
              if(item.id === sameStatus[sameStatus.length-1].id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，相同状态，插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === '生效中') {
            this.content.list.forEach((item, index) => {
              if (sameLocation[0].id === item.id) {
                tempIndex = index;
              }
            })
            // 相同人群，相同帧位，生效中插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === '待生效') {
            this.content.list.map((item, index) => {
              if(this.getStatusName(item.timevalue) === '生效中') {
                tempIndex = index + 1
              }
            })
            if (!tempIndex) {
              // 说明没有生效中，找未设置的
              this.content.list.map((item, index) => {
                if(this.getStatusName(item.timevalue) === '未设置时间') {
                  tempIndex = index
                }
              })
            }
            if (!tempIndex) {
              // 说明没有生效中未设置的，插到帧位最后面
              this.content.list.map((item, index) => {
                if (sameLocation[sameLocation.length - 1].id === item.id) {
                  tempIndex = index + 1;
                }
              })
            }
            // 相同人群，相同帧位，待生效插到生效中后面或未设置时间的前面或同帧位最后面
            this.content.list.splice(tempIndex, 0, data);
          } else {
            this.content.list.map((item, index) => {
              if (sameLocation[sameLocation.length - 1].id === item.id) {
                tempIndex = index;
              }
            })
            // 相同人群，相同帧位，未设置插到后面
            this.content.list.splice(tempIndex + 1, 0, data);
          }
        } else {
          // 相同人群下，不同帧位，比较帧位大小
          let tempIndex = undefined;
          // 找到第一个大于新增帧位的项，有则插入到前面，没有则插入到同人群下最后一位
          let maxItem = samePeople.find((item, index) => {
            return item.bannerLocation > this.dataForm.bannerLocation
          });
          if (maxItem) {
            this.content.list.map((item, index) => {
              if (item.id === maxItem.id) {
                tempIndex = index
              }
            })
          } else {
            this.content.list.map((item, index) => {
              if (item.id === samePeople[samePeople.length-1].id) {
                tempIndex = index + 1
              }
            })
          }
          // 新增帧位小插到前面；新增帧位大插到后面
          this.content.list.splice(tempIndex, 0, data);
        }
      } else { // 新人群，直接添加
        let tempIndex = undefined;
        tempIndex = this.content.list.filter((item) => {
          return this.getStatusName(item.timevalue) !== '已失效'
        }).length;
        this.content.list.splice(tempIndex, 0, data)
      }
      this.$nextTick(() => {
        this.searchList();
      })
      // this.content.list.push(Object.assign({}, data));
      // this.$set(this.dataList, this.dataList.length, data)
    },

    // 上传轮播按钮
    toAdd() {
      this.dataForm = {
        bannerType: "inLink",
        image: '',
        link: {
          meta: {
            id: new Date().getTime(),
            page_url: ''
          }
        },
        timevalue: "",
        // bgRes: "#00B377",
        bgRes: "#fff",
        rest_bgRes: "#ffffff",
        crowdType: 1,
        bannerLocation: '',
        crowdValue: '',
        crowdId: '',
        circulateTime:{},
        timeType:"1"
      };
      this.$refs.loopcirculateTime.circulateTime={}
      this.addDialog = true;
      this.isEdit = false;
    },
    changeTab(type) {
      this.activeTab = type;
      this.carouselList.status = '';
      this.searchList();
    },
    //生成唯一id
    genID(length) {
      return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36);
    },
    //查询
    searchList() {
      //只有查询全部的时候允许拖拽
      if (this.carouselList.status || this.carouselList.crowdValue || this.carouselList.bannerLocation) {
        sortableObject.option('disabled', true)
      } else {
        sortableObject.option('disabled', false)
      }
      // 查询操作
      this.dataList = this.content.list.filter((item, index) => {
        item.id = this.genID(12);
        // 状态tab查询条件
        let statusTab = this.activeTab === 'invalid' ? this.getStatusName(item.timevalue, 'id') === 3 : this.getStatusName(item.timevalue, 'id') !== 3;
        if (!item.bannerLocation) {
          item.bannerLocation = index > 7 ? 1 : (index + 1);
        }
        //当查询的是全部人群的时候
        if (this.carouselList.crowdValue == '全部人群' && !item.crowdValue && statusTab) {
          item.crowdValue = '全部人群'
        }
        //查询全部
        if (!this.carouselList.status && !this.carouselList.crowdValue && !this.carouselList.bannerLocation && statusTab) {
          return item
        }
        //查询轮播位
        if (!this.carouselList.status && !this.carouselList.crowdValue && item.bannerLocation === this.carouselList.bannerLocation && statusTab) {
          return item
        }
        //查询人群
        if (!this.carouselList.status && !this.carouselList.bannerLocation && item.crowdValue === this.carouselList.crowdValue && statusTab) {
          return item
        }
        //查询状态
        if (!this.carouselList.bannerLocation && !this.carouselList.crowdValue && this.getStatusName(item.timevalue, 'id') === this.carouselList.status && statusTab) {
          return item
        }
        //查询轮播位，人群
        if (!this.carouselList.status && item.crowdValue === this.carouselList.crowdValue && item.bannerLocation === this.carouselList.bannerLocation && statusTab) {
          return item
        }
        //查询轮播位，状态
        if (!this.carouselList.crowdValue && item.bannerLocation === this.carouselList.bannerLocation && this.getStatusName(item.timevalue, 'id') === this.carouselList.status && statusTab) {
          return item
        }
        //查询轮人群，状态
        if (!this.carouselList.bannerLocation && item.crowdValue === this.carouselList.crowdValue && this.getStatusName(item.timevalue, 'id') === this.carouselList.status && statusTab) {
          return item
        }
        //查询轮轮播位，人群，状态
        if (item.crowdValue === this.carouselList.crowdValue && item.bannerLocation === this.carouselList.bannerLocation && this.getStatusName(item.timevalue, 'id') === this.carouselList.status && statusTab) {
          return item
        }
      })
       //对列表进行排序
       let contentList=[]
      for(let i=0;i<8;i++){
        let arr=this.dataList.filter(item=>{return item.bannerLocation==i+1})
          arr.forEach((item,index)=>{
            item.bannerLocations= item.bannerLocation+"-"+(index+1)
          })
       contentList.push( ...arr)
      }
      console.log(this.dataList)
      this.dataList=contentList
     //
     let contentLists=[]
      for(let i=0;i<8;i++){
        let arr=this.content.list.filter(item=>{return item.bannerLocation==i+1})
         
       contentLists.push( ...arr)
      }
   

      this.content.list=contentLists
      this.initData()
      console.log( this.content.list,12123)
    },
    toEdit(data, index) {
      this.currentData = data;
      this.currentDataIndex = index;
      this.currentIndex = this.content.list.findIndex((item) => item.id == data.id);
      if(!data.timeType){
        data.timeType="1"
      }
      
      this.dataForm = JSON.parse(JSON.stringify(data));
      if(this.dataForm.timeType==2&&this.dataForm.circulateTime){
        this.$refs.loopcirculateTime.circulateTime=this.dataForm.circulateTime
        this.$refs.loopcirculateTime.editInit()

      }
      console.log( this.dataForm)
      this.isEdit = true;
      this.addDialog = true;
    },
    toRemove(data) {
      let _self = this;
      return function () {
        _self.content.list.splice(_self.content.list.findIndex((item) => item.id == data.id), 1)
        _self.dataList.splice(_self.dataList.findIndex((item) => item.id == data.id), 1)
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [{
          label: data.name,
          value: val,
        }]
      } else {
        this.selectLoading = false;
        this.options = []
      }
    },
    selectCrowd(e) {
      if (e) {
        this.dataForm.crowdId = Number(this.options[0].value.trim());
        this.dataForm.crowdValue = this.options[0].label;
      } else {
        this.dataForm.crowdId = '';
        this.dataForm.crowdValue = '';
      }
      this.$forceUpdate();
    },
    // async querySearchCrowd(queryString, cb) {
    //   const pms = {
    //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
    //     dataType: "json",
    //     data: {},
    //     head: {
    //       "Content-Type": "application/json;charset=UTF-8"
    //     }
    //   };
    //   const res = await api.proxy.post(pms);
    //   if (res.success) {
    //     const { data } = res;
    //     cb([{
    //       id: queryString,
    //       value: data.name || ""
    //     }]);
    //     return false;
    //   }
    // },
    // handleSelectCrowd(item) {
    //   this.dataForm.crowdId = item.id;
    // },
  },
  watch:{
    // "dataForm.timeType"(newdata,ordData){
     
    //   if(newdata==2){
    //     this.dataForm.timevalue=""
    //   }
    //   if(newdata==1){
    //     this.dataForm.circulateTime={}
    //   }
    // },
  }
 
}
  ;
</script>
<style lang="scss" scoped>
.tableBox {
  width: 100%;
}
.carouselFlexBox {
  padding-right: 10px;
  .carouselFlex {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    padding-right: 20px;
    span {
      min-width: 100px;
      text-align: right;
    }
  }
  .carouselButton {
    text-align: right;
  }
}

.container-table {
  margin: 10px auto;
  padding-bottom: 10px;
  display: flex;
  justify-content: space-around;

  .img {
    width: 50%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .button-list {
    width: 45%;
  }
}

.topic-image-upload {
  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 200px;
    height: 200px;
    line-height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    font-size: 50px;
  }
}

.topic-image-upload .el-upload {
  width: 100%;
}

.el-row {
  text-align: center;

  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
  .tabBox {
    display: flex;
    margin: 20px;
    border-bottom: 1px solid #F1F1F4;
    cursor: pointer;
    div {
      border: 1px solid #F1F1F4;
      border-bottom: none;
      padding: 5px 10px;
    }
    .activeTab {
      color: #13c2c2;
    }
  }
}
</style>

