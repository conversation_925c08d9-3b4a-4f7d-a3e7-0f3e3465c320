<template>
    <div class="navbar">

        <el-input size="small"  v-model="content.color" placeholder="#ffffff" style="margin-bottom:5px;">
            <p slot="prepend">高亮色调</p>
        </el-input>

        <div class="navbar-link">
            <div class="navbar-link-title">楼层锚点</div>
            <div class="navbar-link-item" v-for="(item, index) in content.area" :key="index">
                <div class="navbar-link-item-index">{{index + 1}}</div>

                <div class="navbar-link-item-input">
                    <el-input v-model="content.area[index].link.text">
                        <p slot="prepend">文案</p>
                    </el-input>
                </div>

                <div class="navbar-link-item-input">
                    <el-input v-model="content.area[index].link.name" placeholder="name">
                        <p slot="prepend">锚点</p>
                    </el-input>
                </div>

                <el-button icon="delete" @click="content.area.splice(index, 1)"></el-button>
            </div>

            <el-button class="navbar-link-add" icon="plus" @click="addArea()"></el-button>
        </div>
    </div>
</template>

<script>
    import base from '../base'

    export default {
        extends: base,
        contentDefault: {
            image: '',
            color: '#ffffff',
            area: []
        },
        data() {
            return {
                selectedAreaIndex: -1
            }
        },
        computed: {
            image() {
                var url = _.get(this, 'content.image')
                if (url) {
                    return `url(${url})`
                } else {
                    return '';
                }
            }
        },
        methods: {
            addArea() {
                this.content.area.push({
                    link: {
                        type: 'anchor',
                        text: '',
                        name: ''
                    }
                })
            },
            async onUploadImage(res, file) {
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.image = res.data.url
            }
        }
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .navbar {

        .navbar-bg {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            border: 2px dashed $border-color-base;
            box-sizing: border-box;
            border-radius: 4px;
            color: $color-extra-light-silver;
            background: no-repeat center / contain;
        }

        .navbar-link {
            padding: 0 2px;
            border: 1px solid $color-base-gray;

            .navbar-link-title {
                margin: 0 -2px;
                line-height: 40px;
                text-align: center;
                background: $color-base-gray;
                color: $border-color-hover;
            }

            .navbar-link-item {
                display: flex;
                justify-content: space-between;
                margin: 2px 0;

                .navbar-link-item-index {
                    border: 1px solid rgb(191, 217, 215);
                    border-radius: 4px;
                    min-width: 42px;
                    line-height: 34px;
                    text-align: center;
                    color: rgb(151, 190, 190)
                }
                .navbar-link-item-input {
                    flex: 1;
                    margin: 0 2px;
                }
                .el-button {
                }
            }

            .navbar-link-add {
                display: block;
                width: 100%;
                margin: 2px 0;
            }
        }
    }
</style>
