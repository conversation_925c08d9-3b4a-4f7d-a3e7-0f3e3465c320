<template>
  <header class="topbar">
    <div @click="onLogoClick" class="logo">
      <i class="iconfont icon-cms-c"></i>
      <span>CMS内容管理系统</span>
    </div>
    <ul class="el-menu top-menu-list el-menu--horizontal">
      <li
        v-for="(item,index) in tab_list"
        :key="index"
        role="menuitem"
        @click="navChange(item.value, index)"
        class="el-menu-item"
        :class="active_index===index ? 'is-active':''"
      >{{item.name}}</li>
    </ul>

    <div class="new-float-nav">
      <el-dropdown class="user-info-wrap" @command="handleCommand">
        <span class="el-dropdown-link">
          {{currentAccount.userName}}
          <i class="el-icon-caret-bottom el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command class="user-name-wrap">
            {{currentAccount.userName}}
            <div class="user-name">{{currentAccount.roleNames}}</div>
          </el-dropdown-item>
          <el-dropdown-item :command="menu.actionUrl" v-for="menu in menuList" :key="menu.menuId">
            <i class="iconfont" :class="menu.icon"></i>
            {{menu.menuName}}
          </el-dropdown-item>
          <el-dropdown-item command="logout">
            <i class="iconfont icon-circle-left"></i>退出
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <!-- <el-dropdown class="area-info-wrap" @command="handleBranch">
        <span class="el-dropdown-link">
          {{ currentBranch.branchName }}
          <i class="el-icon-caret-bottom el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="(item, index) in branchs"
            :key="index"
            :command="item"
          >{{ item.branchName }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->

      <div class="department-info-wrap" @command="handleDeparment">
        <span class="el-dropdown-link">
          {{ currentDepartment.name }}
          <!-- <i class="el-icon-caret-bottom el-icon--right"></i> -->
        </span>
        <!-- <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item, index) in departmentList" :key="index" :command="item">
            {{ item.name }}
          </el-dropdown-item>
        </el-dropdown-menu>-->
      </div>
    </div>
  </header>
</template>

<script>
import bus from "utils/eventbus";
import topic from "api/topic";
export default {
  name: "Topbar",

  props: {
    // branchs: Array,
    departmentList: Array
  },

  data() {
    return {
      activeIndex: 1,
      active_index: 0,
      menuWidth: window.innerWidth - 200 + "px",
      navList: [
        {
          id: 1,
          name: "WORK GO总平台"
        }
      ],
      tab_list: [
        // {
        //   title: "首页"
        // },
        // {
        //   title: "活动页"
        // },
        // {
        //   title: "频道页"
        // },
        // {
        //   title: '静态页'
        // },
        // {
        //   title: "弹窗"
        // }
      ],
      currentBranch: {},
      currentDepartment: {}
    };
  },

  watch: {
    isShow() {
      this.activeIndex = this.isShow ? "1" : "2";
    },
    // branchs(new_val) {
    //   this.setCurrentBranch();
    // }
  },

  created() {
    bus.$emit("send_category", "pc");
  },

  computed: {
    isShow() {
      return this.$store.getters["sideBar/isShow"];
    },

    currentAccount() {
      return this.$store.getters["sys/currentAccount"];
    },

    menuList() {
      return this.$store.getters["sideBar/accountMenu"];
    },

    corp() {
      return this.$store.getters["corp/corp"];
    }
  },

  mounted() {
    setTimeout(() => {
      this.setCurrentDepartment();
      // if (this.branchs && this.branchs.length) this.setCurrentBranch();
    }, 100);
    topic
      .getTabList({
        category: "pc"
      })
      .then(res => {
        const arr = [];
        if (res.code === 200) {
          res.data.forEach(item => {
            item.menuList.forEach(item1 => {
              arr.push(item1);
            });
          });
          res = res.data[0];
          this.tab_list = arr;
          this.navChange(arr[0]["value"], 0);
        }
      });
  },

  methods: {
    // setCurrentBranch() {
    //   if (this.branchs) {
    //     if (this.currentAccount && this.currentAccount.defaultBranch) {
    //       const current = this.branchs.find(item => {
    //         return (
    //           item.branchCode === this.currentAccount.defaultBranch.branchCode
    //         );
    //       });
    //       if (current) {
    //         this.currentBranch = current;
    //       }
    //     } else {
    //       this.currentBranch = {
    //         branchName: "全国",
    //         branchCode: ""
    //       };
    //     }
    //   }
    // },

    setCurrentDepartment() {
      if (this.departmentList) {
        this.currentDepartment = this.departmentList.find(item => {
          return item.name === "药帮忙";
        });
        bus.$emit("change_department", this.currentDepartment);
      }
    },

    /*
     * 处理菜单跳转
     * */
    handleCommand(command) {
      if (!command) {
        return;
      }
      switch (command) {
        case "logout":
          localStorage.clear();
          this.$router.replace("/auth/login");
          break;
        default:
          this.$router.replace(command);
      }
    },

    handleDeparment(command) {
      this.currentDepartment = command;
      bus.$emit("change_department", this.currentDepartment);
    },

    // handleBranch(command) {
    //   this.currentBranch = command;
    //   const obj = JSON.parse(JSON.stringify(this.currentAccount));
    //   obj.defaultBranch = this.currentBranch;
    //   this.$store.dispatch("sys/updateCurrentMember", obj);
    //   bus.$emit("change_branch", this.currentBranch);
    // },

    /**
     * 处理 logo 的点击
     */
    onLogoClick() {
      // this.$router.push('home')
    },

    navChange(value, index) {
      // 老逻辑
      this.$store.dispatch("sideBar/setSideBarState", true);
      /*const navList = this.$store.getters[ 'sideBar/navList' ];
                this.$router.replace(navList[1].actionUrl)*/
      //新逻辑
      this.active_index = index;
      setTimeout(() => {
        bus.$emit("change_type", value);
      }, 500);
    }
  }
};
</script>


<style lang="scss" rel="stylesheet/scss" scoped>
.topbar {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid $border-base;
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 200px;
    height: 50px;
    font-size: 14px;
    color: #fff;
    background-color: $color-light-silver;
    cursor: pointer;
    .iconfont {
      font-size: 30px;
      margin-right: 10px;
    }
  }
  .top-menu-list {
    border-bottom: none;
    .el-menu-item {
      padding: 0;
      margin-left: 20px;
      height: 50px;
      line-height: 50px;
      border-width: 3px;
      &.is-active {
        color: $color-primary;
      }
    }
  }
  .new-float-nav {
    @include middle-center-y();
    right: 30px;
    display: flex;
    flex-direction: row-reverse;
  }
  .user-info-wrap {
    cursor: pointer;
  }
  .area-info-wrap {
    cursor: pointer;
    margin-right: 15px;
  }

  .department-info-wrap {
    cursor: pointer;
    margin-right: 15px;
  }
}
</style>
