<template>
  <el-dialog class="banner-dialog" :title="isEdit ? '编辑优惠券' : '添加优惠券'" :visible="true" :before-close="closeDialog">
    <el-row :gutter="10" style="marginBottom: 5px;display: flex;align-items: center;">
      <el-col :span="121" class="attr-right">关联优惠券ID：</el-col>
      <el-col :span="12">
        <el-select
          v-model="editData.couponTitle"
          :loading="selectLoading"
          filterable
          :filter-method="optionFilter"
          placeholder="请输入优惠券id"
          clearable
          @clear="options = []"
          @change="selectCoupon"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <!-- <el-autocomplete
          size="small"
          v-model.trim="editData.couponTitle"
          :fetch-suggestions="querySearchCoupon"
          placeholder="请输入优惠券id"
          :trigger-on-focus="false"
          @select="handleSelectCoupon"
        ></el-autocomplete> -->
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="closeDialog">取 消</el-button>
      <el-button size="small" type="primary" @click="confirmAdd">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
  import { AppWebsite } from "config";
  import api from 'api';
  export default {
    name: 'couponSettings',
    props:["dataForm", "editIndex", "isEdit", "orient"],
    data() {
      return {
        loading: false,
        defImgloading: false,
        editData: {},
        options: [],
        selectLoading: false,
        couponObj: {},
      };
    },
    mounted () {
      this.editData = JSON.parse(JSON.stringify(this.dataForm));
    },
    methods: {
      closeDialog() {
        this.$parent.closeAddDialog();
      },
      async optionFilter(val) {
        this.selectLoading = true;
        const res = await api.stores.searchCoupon([val]);
        const { data } = res;
        if (data && data.success) {
          this.selectLoading = false;
          if (!Object.keys(data.data).length) {
            return false;
          }
          const { list } = data.data;
          if (list && list.length) {
            this.couponObj = list[0];
            this.options = [{
              label: list[0].voucherTitle || list[0].templateName,
              value: val,
            }]
          } else {
            this.options = []
          }
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCoupon(e) {
        if (e) {
          this.editData = Object.assign(this.editData, this.couponObj);
          this.editData.couponTitle = this.couponObj.voucherTitle || this.couponObj.templateName;
          this.editData.couponText = this.couponObj.couponText || '';
          this.editData.couponState = this.couponObj.couponState;
          this.editData.couponId = this.couponObj.templateId;
          this.$set(this.editData, 'customerGroupName', this.couponObj.customerGroupName);
        } else {
          this.editData.couponTitle = '';
          this.editData.couponId = '';
          this.editData.couponState = '';
          this.editData.customerGroupName = '';
        }
      },
      //确定添加优惠券
      confirmAdd() {
        if(!this.editData.couponId) {
          this.$message.error('请选择优惠券');
          return
        }
        this.psData = {
          couponId: this.editData.couponId,
          couponTitle: this.editData.couponTitle,
          couponText: this.editData.couponText,
          customerGroupName: this.editData.customerGroupName,
          couponState: this.editData.couponState,
          };
        if (this.isEdit) {
          this.$emit('saveDialog','edit', this.psData, this.editIndex )
        } else {
          this.$emit("saveDialog", 'add', this.psData);
        }
      },

    }
  };
 
</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .topic-image-upload {
    margin: 20px 0;
    .image {
      display: block;
      width: 100%;
    }
    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }
  .this.editData.couponId{
    text-align: center;
  }
</style>
