<template>
    <div class="topic-menu-list">
        <div style="height: 30px">
            <el-radio-group v-model="content.type">
                <el-radio :label="index" v-for="(item,index) in typeList" :key="index">{{item}}
                </el-radio>
            </el-radio-group>
        </div>
        <div style="margin: 10px 0">
           列表的背景色:
            <el-color-picker v-model="content.list_body_color" size="mini"></el-color-picker>
        </div>
        <div v-if="list.length>0" style="border: 1px solid #3c763d;margin-bottom: 10px">
            <el-table :data="list" style="width: 100%;margin-top: 5px" height="300"
                      ref="multipleTable">
                <el-table-column fixed label="图片" width="80">
                    <template slot-scope="scope">
                        <img :src="scope.row.imageUrl" :alt="scope.row.productName" style="width:100%;max-height:50px;">
                    </template>
                </el-table-column>
                <el-table-column prop="productName" label="药名">
                    <template slot-scope="scope">
                        <span v-if="scope.row.productName">{{scope.row.productName}}</span>
                        <span v-else>{{scope.row.showName}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="规格" width="80">
                    <template slot-scope="scope">
                        {{scope.row.mediumPackageTitle}}
                    </template>
                </el-table-column>
                <el-table-column prop="fob" label="价格" width="80">
                    <template slot-scope="scope">
                        {{scope.row.fob}}
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="80">
                    <template slot-scope="scope">
                        <div class="edit-button">
                            <el-button @click="handleDelete(scope.row)" type="warning" size="mini">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div>
            <el-table :data="goods_group" style="width: 100%;margin-top: 5px" height="150"
                      ref="multipleTable">
                <el-table-column fixed label="商品组名称">
                    <template slot-scope="scope">
                        {{scope.row.name}}
                    </template>
                </el-table-column>

                <el-table-column fixed="right" label="操作">
                    <template slot-scope="scope">
                        <div class="edit-button">
                            <el-button @click="group_delete(scope.row)" type="warning" size="mini">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!--选择商品-->
        <all-link @select="onSetLink" :tabs="tabs" :params="{
                productlink: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                importGoods: {
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                goodsGroup: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
            }"></all-link>
    </div>
</template>

<script>
    import base from "../base";
    import api from 'api'

    export default {
        name: "hotRecommend",
        extends: base,
        contentDefault: {
            list: [],
            goods_group: [],
            type:0,
            list_body_color:"#f1f1f1"
        },
        data() {
            return {
                loading: false,
                tabs: [
                    {label: '商品', value: 'productlink'},
                    {label: '导入商品', value: 'importGoods'},
                    {label: '商品组', value: 'goodsGroup'}
                ],
                typeList: ['列表模式', '大图模式'],

            }
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            },
            moreLink(data) {
                if (!data || !data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            }
        },
        computed: {
            list() {
                let list = _.get(this, 'content.list');
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
            goods_group() {
                let list = _.get(this, 'content.goods_group');
                if (list) {
                    return list
                } else {
                    return [];
                }
            }
        },
        methods: {
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: "warning"
                    });
                    return;
                }
                this.content.image = res.data.url;
            },
            onSetLink(link) {
                function handle_arr(arr = []) {
                    return arr.map((item) => {
                        let obj = {};
                        obj.init_img_url = item.init_img_url;
                        obj.imageUrl = item.imageUrl;
                        obj.productName = item.productName;
                        obj.showName = item.showName;
                        obj.mediumPackageTitle = item.mediumPackageTitle;
                        obj.fob = item.fob;
                        obj.id = item.id;
                        return obj
                    });
                }
                if (link.tag === "goods" || link.tag === "importGoods") {
                    let _self_arr = handle_arr(link.data);
                    if (this.content.list.length > 0) {
                        this.content.list = [...api.common.removeRepeat(this.content.list, _self_arr)]
                    } else {
                        this.content.list = [..._self_arr]
                    }
                } else if (link.tag === "goodsGroup") {
                    let obj = {};
                    obj.name = link.data.name;
                    obj.ids = link.data.goods;
                    obj.code = link.data.code;
                    this.content.goods_group.splice(0,1,obj);
                }
            },
            group_delete(row) {
                const index = this.goods_group.indexOf(row);
                this.goods_group.splice(index, 1)
            },
            handleDelete(row) {
                const index = this.list.indexOf(row);
                this.list.splice(index, 1)

            }

        }
    }
</script>
<style scoped lang="scss">
    /*.topic-pic-upload{*/
    /*display:block;*/
    /*width:100% ;*/
    /*}*/
    /*.el-upload--text{width:100%}*/

</style>
