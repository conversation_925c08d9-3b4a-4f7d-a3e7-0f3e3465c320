<template>
  <div class="topic-menu-list">
    <div
      style="margin: 10px 0"
    >
      <!-- <el-row :gutter="20">
        <el-col :span="24">
          <el-radio-group v-model="content.activeKey">
            <el-radio
              :label="index"
              v-for="(item, index) in menu"
              :key="index"
              :disabled="index > 1"
              >{{ item }}</el-radio
            >
          </el-radio-group>
        </el-col>
      </el-row> -->
      <el-row :gutter="20">
        <div class="panel-common-header">模块有效时间设置</div>
        <div class="timevalue">
          <el-date-picker
            v-model="content.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions2"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          >
          </el-date-picker>
        </div>
      </el-row>
      <el-header
        height="50px"
        style="
          background-color: rgb(19, 194, 194);
          text-align: center;
          padding-top: 15px;
          margin-top: 5px;
        "
      >
        <el-row :gutter="20">
          <label class="demonstration">间隔颜色:</label>
          <el-color-picker
            v-model="content.floorSpacingBgRes"
            size="mini"
            @active-change="onSelect"
          ></el-color-picker>
          <span style="margin-left: 50px"></span>
          楼层间隔高度：</el-row
        >
      </el-header>
      <el-main>
        <el-row :gutter="20">
          <el-input
            class="space-height"
            v-model="content.floorSpaceHeight"
            placeholder="请输入高度"
            @change="reviseValue"
          ></el-input
          >px
        </el-row>
      </el-main>
    </div>
  </div>
</template>

<script>
import base from "../base";

export default {
  extends: base,
  contentDefault: {
    floorSpacingBgRes: "",
    floorSpaceHeight: "",
    timevalue: "",
    activeKey: 1,
  },
  data() {
    return {
      pickerOptions2: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  computed: {
    contentBg() {
      var url = _.get(this, "content.");
      if (url) {
        return `url(${url})`;
      } else {
        return "";
      }
    },
  },
  methods: {
    onSelect(val) {
      this.content.floorSpacingBgRes = val;
    },
    reviseValue(val) {
      this.content.floorSpaceHeight = val;
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss">
.floor-spacing {
  .space-height {
    width: 70%;
    display: inline-block;
    margin-right: 10px;
  }
}
.el-row {
  text-align: center;
  .title {
    text-align: left;
    line-height: 30px;
    color: #13c2c2;
    padding-left: 10px;
    margin: 10px;
  }
}
.topic-menu-list {
  .panel-common-header {
    height: 35px;
    line-height: 35px;
    background: #f2f2f2;
    padding: 0 0 0 15px;
    text-align: left;
  }
  .timevalue,
  .background-img {
    padding: 20px 0;
  }
  .background-img {
    text-align: left;
    padding-left: 20px;
    .topic-image-upload {
      display: inline-block;
    }
    .btn-block {
      display: inline-block;
      width: auto;
    }
    .btn-block:last-child {
      margin-left: 20px;
    }
  }
}
</style>
