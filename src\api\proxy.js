import * as http from 'utils/http'

/**
 * 基础数据接口
 */
export default {
	/**
	 * 跨域代理请求
	 * @param pms {@link Object}：代理请求参数，示例：
	 * pm = {
	 *  url: 'http://localhost/',
	 *  param: { k: v }
	 *  dataType: 'json',
	 *  head: {
	 *          contentType: 'application/json; charset=utf-8'
	 *      }
	 * }；
	 * @param method {@link String}：请求方式；
	 * @param head {@link Object}：代理请求header头；
	 * @returns {*}：响应结果。
	 */
	req(pms, method, head) {
		method = !method ? 'post' : String(method).toLowerCase();
		if (pms.url && http[method])
			return http[method]('/proxy', pms, head);
		return {
			code: 500,
			msg: '参数异常'
		};
	},
	get(pms, head) {
		return this.req(pms, 'get', head);
	},
	post(pms, head) {
		return this.req(pms, null, head);
	}
}
