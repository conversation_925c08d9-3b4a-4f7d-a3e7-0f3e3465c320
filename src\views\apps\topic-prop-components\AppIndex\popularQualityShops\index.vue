<template>
  <div class="selectedShops">
    <el-row :gutter="20">
      <div class="title">模块设置</div>
      <el-col :span="24">
        <el-input placeholder="请输入内容" maxlength="6" v-model="content.main_title">
          <template slot="prepend">模块标题</template>
        </el-input>
      </el-col>
      <el-col :span="24" style="marginTop: 10px">
        <el-input placeholder="请输入内容" maxlength="10" v-model="content.leading_words">
          <template slot="prepend">链接引导语</template>
        </el-input>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">关联店铺</div>
      <el-col :span="8">
        <div class="block">
          <el-button class="btn-block" type="primary" @click="toEdit('add')">关联店铺</el-button>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">关联店铺列表</div>
      <el-col :span="24">
        <el-table :data="content.list" :lazy="true" size="mini" :row-key="(row) => row.groupShopsName">
          <el-table-column label="序号" width="50" type="index" />
          <el-table-column label="店铺组名称">
            <template slot-scope="scope">
              <span>{{ scope.row.groupShopsName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="指定人群">
            <template slot-scope="scope">
              <span>{{ scope.row.crowdValue || '全部人群' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="关联店铺数">
            <template slot-scope="scope">
              <span>{{ (scope.row.shopsList || []).length }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="toEdit('edit', scope.row, scope.$index)">编辑</el-button>
              <el-popover
                placement="top"
                v-model="scope.row.visible"
              >
                <p>确定删除吗？</p>
                <div style="text-align: right; margin: 0">
                  <el-button size="mini" type="text" @click="scope.row.visible = false">取消</el-button>
                  <el-button type="primary" size="mini" @click="toDelete(scope.row, scope.$index)">确定</el-button>
                </div>
                <el-button slot="reference" @click="scope.row.visible = true" type="danger" size="mini">删除</el-button>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <shop-dialog
      v-if="add_editContent"
      :shopItemData="shopItemData"
      :content="content"
      :topic="topic"
      :visible="add_editContent"
      :hideDisplayGoods="true"
      @confirmEditContent="confirmEditContent"
    />
  </div>
</template>

<script>
import base from 'views/apps/topic-prop-components/base.vue';
import shopDialog from '../carefullySelectedShops/shopDialog.vue';

export default {
  components: { shopDialog },
  extends: base,
  contentDefault: {
    list: [],
    bgRes: "",
  },
  components: {
    shopDialog
  },
  data() {
    return {
      add_editContent: false,
      shopItemData: {
        groupShopsName: '',
        crowdType: 1,
        crowdValue: '',
        shopsList: [],
        visible: false,
      }
    };
  },
  computed: {
    list() {
      var list = _.get(this, "content.list");
      if (list) {
        return list;
      } else {
        return [];
      }
    }
  },
  methods: {
    toEdit(type, row, index) {
      this.add_editContent = true;
      this.editType = type;
      if(type == 'edit') {
        this.shopItemData = JSON.parse(JSON.stringify(row));
        this.nowIndex = index;
      } else {
        this.shopItemData = {
          groupShopsName: '',
          crowdType: 1,
          crowdValue: '',
          shopsList: [],
          visible: false,
        };
      }
    },
    toDelete(row, index) {
      this.content.list.splice(index, 1);
      row.visible = false;
    },
    closeEditContent() {
      this.add_editContent = false;
    },
    confirmEditContent(data) {
      if(this.editType == 'add') {
        this.content.list.push(data);
      } else {
        this.content.list.splice(this.nowIndex, 1, data);
      }
    },
  }
};
</script>

<style lang="scss" rel="stylesheet/scss">
  .selectedShops {
    position: relative;
    .el-table {
      .cell {
        text-align: center;
        padding: 0;
      }
      th .cell {
        color: #606266;
      }
    }
  }
  .el-loading-spinner {
    top: auto !important;
    margin-top: auto !important;
  }
  .el-row {
    text-align: center;
    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
  }
</style>
