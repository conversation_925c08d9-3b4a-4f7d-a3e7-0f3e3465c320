import Vue from 'vue'
import Vuex from 'vuex'
import sideBar from './modules/side-bar'
import createLogger from 'vuex/dist/logger'
import sys from './modules/sys'
import breadcrumb from './modules/breadcrumb';
import page from './modules/page';
import * as actions from './actions'
import getters from './getters'


Vue.use(Vuex)

const debug = process.env.NODE_ENV !== 'prod'

export default new Vuex.Store({
  actions,
  modules: {
    sideBar,
    sys,
    breadcrumb,
    page
  },
  getters,
  strict: debug,
  plugins: debug ? [createLogger()] : []
})
