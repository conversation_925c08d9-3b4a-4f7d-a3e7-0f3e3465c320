<template>
  <div class="topic-search">
    <el-row :gutter="20">
      <div class="title">Feed轮播图</div>
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="活动id:">
            <el-input
              placeholder="请输入内容"
              v-model="feedScrollQueryParams.activityId"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="活动名称:">
            <el-input
              placeholder="请输入内容"
              v-model="feedScrollQueryParams.activityName"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="展示时间:">
            <el-date-picker
              v-model="feedScrollQueryParams.validityTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="帧数:">
            <el-select
              v-model="feedScrollQueryParams.bannerLocation"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in bannerLocationList"
                :value="item.id"
                :label="item.name"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="顺序号:">
            <el-input v-model="feedScrollQueryParams.sort"></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="人群id:">
            <el-select
              style="margin-left: 10px"
              v-model.trim="feedScrollQueryParams.crowdValue"
              :loading="scrollSelectLoading"
              filterable
              :filter-method="scrollOptionFilter"
              placeholder="请输入人群id"
              clearable
              @clear="feedScrollOptions = []"
              @change="scrollSelectCrowd"
            >
              <el-option
                v-for="item in feedScrollOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <!-- <el-input v-model="feedScrollQueryParams.crowdValue"></el-input> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态:">
            <el-select
              v-model.number="feedScrollQueryParams.status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="item in status"
                :label="item.name"
                :key="'scroll' + item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <div class="three-button">
      <el-button type="primary" @click="feedScrollSearchList" size="mini"
        >查询</el-button
      >
      <el-button type="primary" @click="feedScrollResetList" size="mini"
        >重置</el-button
      >
      <el-button type="primary" @click="addListScroll" size="mini"
        >新建</el-button
      >
    </div>
    <el-table
      :data="feedScrollDataList"
      size="mini"
      class="tableBox"
      style="margin: 0 0 20px"
      ref="tableBox"
    >
      <el-table-column
        label="id"
        prop="activityId"
        width="100"
      ></el-table-column>
      <el-table-column label="活动名称" prop="activityName"></el-table-column>
      <el-table-column label="帧数">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.bannerLocation"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            @blur="changeLocationSort(scope,feedScrollDataList)"
            @keyup.enter.native="changeLocationSort(scope,feedScrollDataList)"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="顺序">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.sort"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            @blur="changeSort(scope,feedScrollDataList)"
            @keyup.enter.native="changeSort(scope,feedScrollDataList)"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="人群" show-overflow-tooltip>
        <template slot-scope="scope">
          <p>
            {{ scope.row.crowdValue || "该页面已选人群" }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="展示时间" width="200">
        <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
            </div>
            <div v-else> 
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <div>
            {{
              ["未开始", "上线", "已结束", "下线"][scope.row.status - 1] || "-"
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="预览">
        <template slot-scope="scope">
          <div style="width: 40px; height: 40px">
            <img :src="scope.row.bannerImg" style="width: 40px; height: 40px" alt="" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="150px">
        <template slot-scope="scope">
          <div style="display: flex;">
            <el-button
            size="mini"
            type="text"
            @click="toScrollEdit(scope.row, scope.$index)" v-if="scope.row.status == 1 ||scope.row.status == 3 || scope.row.status == 4"
            >编辑
          </el-button>
          <el-button size="mini" type="text" @click="toScrollRemove(scope.row)"  v-if="scope.row.status == 4"
            >删除</el-button
          >
          </div>
          <div style="display: flex;margin-top: 10px;">
            <el-button size="mini" type="text" @click="online(scope, 'scroll')"  v-if="scope.row.status == 4"
            >上线</el-button
          >
          <el-button size="mini" type="text" @click="outline(scope, 'scroll')" v-if="scope.row.status == 2"
            >下线</el-button
          >
           <el-button size="mini" v-if="scope.row.status == 2" @click="toScrollEdit(scope.row, scope.$index,true)" type="text">详情</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <feedScrollAlert
      ref="feedScrollAlert"
      @done="feedScrollAlertCallback"
      :topic="topic"
      :isInfo="isInfo"
    ></feedScrollAlert>

    <!--广告信息配置-->
    <el-row :gutter="20">
      <div class="title">广告信息配置</div>
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="活动id:">
            <el-input
              placeholder="请输入内容"
              v-model="goodAdverqueryParams.activityId"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="活动名称:">
            <el-input
              placeholder="请输入内容"
              v-model="goodAdverqueryParams.activityName"
            >
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="人群id:">
            <el-select
              style="margin-left: 10px"
              v-model.trim="goodAdverqueryParams.crowdValue"
              :loading="adverSelectLoading"
              filterable
              :filter-method="adverOptionFilter"
              placeholder="请输入人群id"
              clearable
              @clear="feedAdverOptions = []"
              @change="adverSelectCrowd"
            >
              <el-option
                v-for="item in feedAdverOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <!-- <el-input v-model="goodAdverqueryParams.crowdValue"></el-input> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态:">
            <el-select
              v-model="goodAdverqueryParams.status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="item in status"
                :label="item.name"
                :key="'adverti' + item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="展示时间:">
            <el-date-picker
              v-model="goodAdverqueryParams.validityTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="位置:">
            <el-radio v-model="goodAdverqueryParams.positionType" :label="0">
              全部
            </el-radio>
            <el-radio v-model="goodAdverqueryParams.positionType" :label="1">
              左侧
            </el-radio>
            <el-radio v-model="goodAdverqueryParams.positionType" :label="2">
              右侧
            </el-radio>
            
            <el-select v-model="goodAdverqueryParams.position">
              <el-option value="1" label="1"></el-option>
              <el-option value="2" label="2"></el-option>
              <el-option value="3" label="3"></el-option>
              <el-option value="4" label="4"></el-option>
              <el-option value="5" label="5"></el-option>
              <el-option value="6" label="6"></el-option>
              <el-option value="7" label="7"></el-option>
              <el-option value="8" label="8"></el-option>
              <el-option value="9" label="9"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <div class="three-button">
      <el-button type="primary" @click="advertisementSearchList" size="mini"
        >查询</el-button
      >
      <el-button type="primary" @click="advertResetList" size="mini"
        >重置</el-button
      >
      <el-button
        type="primary"
        @click="openFeedGoodsAdvertisementAlert"
        size="mini"
        >新建</el-button
      >
    </div>
    <el-table
      :data="dataList"
      size="mini"
      class="tableBox"
      style="margin-top: 10px"
      ref="tableBox"
      :row-key="(row) => row.id"
    >
      <el-table-column
        label="id"
        width="100"
        prop="activityId"
      ></el-table-column>
      <el-table-column label="活动名称" prop="activityName"></el-table-column>
      <!-- <el-table-column label="帧数" prop="position"></el-table-column> -->
      <!-- <el-table-column label="顺序号" prop="sort">
        <template slot-scope="scope">
          <el-input v-model=" scope.row.sort"  @blur="changeSort(scope,dataList)"
            @keyup.enter.native="changeSort(scope,dataList)"></el-input>
        </template>
      </el-table-column> -->
      <el-table-column label="位置" prop="position">
        <template slot-scope="scope">
          {{ {1:"左侧",2:"右侧"}[scope.row.positionType]}}{{ scope.row.position }}
        </template>
      </el-table-column>
      <el-table-column label="人群" show-overflow-tooltip>
        <template slot-scope="scope">
          <p>
            {{ scope.row.crowdValue || "该页面已选人群" }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="展示时间" width="200">
        <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
            </div>
            <div v-else> 
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <div>
            {{
              ["未开始", "上线", "已结束", "下线"][scope.row.status - 1] || "-"
            }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="预览">
        <template slot-scope="scope">
          <div style="width: 40px; height: 40px">
            <img :src="scope.row.url" alt="" />
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" min-width="150px">
        <template slot-scope="scope">
          <div style="display: flex;">
            <el-button
            size="mini"
            @click="toEditAdvertisement(scope.row, scope.$index)" v-if="scope.row.status == 1||scope.row.status == 3 || scope.row.status == 4"
            >编辑
          </el-button>
          <el-button size="mini" @click="toAdverRemove(scope.row)" type="danger"  v-if="scope.row.status == 4"
            >删除</el-button
          >
          </div>
         <div style="display: flex;margin-top: 10px;">
          <el-button size="mini" @click="online(scope, 'advert')" type="danger" v-if="scope.row.status == 4"
            >上线</el-button
          >
          <el-button size="mini" @click="outline(scope, 'advert')" type="danger" v-if=" scope.row.status == 2"
            >下线</el-button
          >
          <el-button size="mini" v-if="scope.row.status == 2" @click="toEditAdvertisement(scope.row, scope.$index,true)" >详情</el-button>
         </div>
        </template>
      </el-table-column>
    </el-table>
    <feedGoodsAdvertisementAlert
      style="margin-top: 5px"
      ref="feedGoodsAdvertisementAlert"
      :topic="topic"
      :isInfo="isInfo"
      @done="feedGoodsAdvertisementAlertCallBack"
    ></feedGoodsAdvertisementAlert>

    <!--广告信息配置结束-->
    <!-- 商品信息配置 -->
    <el-row :gutter="20">
      <div class="title">商品信息配置</div>
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="活动id:">
            <el-input
              placeholder="请输入内容"
              v-model="goodsInfoParams.activityId"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="活动名称:">
            <el-input
              placeholder="请输入内容"
              v-model="goodsInfoParams.activityName"
            >
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="人群id:">
            <el-select
              style="margin-left: 10px"
              v-model.trim="goodsInfoParams.crowdValue"
              :loading="goodsInfoSelectLoading"
              filterable
              :filter-method="goodsInfoOptionFilter"
              placeholder="请输入人群id"
              clearable
              @clear="goodsInfoOptions = []"
              @change="goodsInfoSelectCrowd"
            >
              <el-option
                v-for="item in goodsInfoOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态:">
            <el-select
              v-model="goodsInfoParams.status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="item in status"
                :label="item.name"
                :key="'adverti' + item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="展示时间:">
             <el-date-picker
              v-model="goodsInfoParams.validityTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <div class="three-button">
      <el-button type="primary" @click="goodsInfoSearchList" size="mini"
        >查询</el-button
      >
      <el-button type="primary" @click="goodsInfoResetList" size="mini"
        >重置</el-button
      >
      <el-button type="primary" @click="openfeedGoodsInfoAlert" size="mini"
        >新建</el-button
      >
    </div>
    <el-table
      :data="goodInfoDataList"
      size="mini"
      class="tableBox"
      style="margin: 0 0 20px"
      ref="tableBox"
      :row-key="(row) => row.id"
    >
      <el-table-column
        label="id"
        width="100"
        prop="activityId"
      ></el-table-column>
      <el-table-column label="活动名称" prop="activityName"></el-table-column>
      <el-table-column label="人群" show-overflow-tooltip>
        <template slot-scope="scope">
          <p>
            {{ scope.row.crowdValue || "该页面已选人群" }}
          </p>
        </template>
      </el-table-column>

      <el-table-column label="展示时间" width="200">
        <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
            </div>
            <div v-else> 
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <div>
            {{
              ["未开始", "上线", "已结束", "下线"][scope.row.status - 1] || "-"
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="150px">
        <template slot-scope="scope">
         <div style="display: flex;">
          <el-button
            size="mini"
            @click="toEditGoodsInfo(scope.row, scope.$index)"  v-if="scope.row.status == 1||scope.row.status == 3 || scope.row.status == 4"
            >编辑
          </el-button>
          <el-button size="mini" @click="toRemove(scope.row)" type="danger" v-if="scope.row.status == 4"
            >删除</el-button
          >
           <el-button size="mini" v-if="scope.row.status == 2" @click="toEditGoodsInfo(scope.row, scope.$index,true)" >详情</el-button>
         </div>
         <div style="display: flex;margin-top: 10px;">
          <el-button
            size="mini"
            @click="online(scope, 'goodsInfo')" v-if="scope.row.status == 4"
            type="danger"
            >上线</el-button
          >
          <el-button
            size="mini"
            @click="outline(scope, 'goodsInfo')" v-if="scope.row.status == 2"
            type="danger"
            >下线</el-button
          >
         </div>
        </template>
      </el-table-column>
    </el-table>
    <feedGoodsInfoAlert
      ref="feedGoodsInfoAlert"
      :topic="topic"
      @done="feedGoodsInfoAlertDone"
      :isInfo="isInfo"
    ></feedGoodsInfoAlert>
    <!-- 商品信息配置结束 -->
  </div>
</template>
<script>
import feedGoodsAdvertisementAlert from "./components/feed_goods_advertisement_alert.vue";
import feedGoodsInfoAlert from "./components/feed_goods_info_alert.vue";
import feedScrollAlert from "./components/feed_scroll_alert.vue";
import base from "../../base";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import Sortable from "sortablejs";
let sortableObject = {};
export default {
  name: "feedGoods",
  extends: base,
  components: {
    swiperPoint,
    feedGoodsAdvertisementAlert,
    feedGoodsInfoAlert,
    feedScrollAlert,
  },
  contentDefault: {
    list: [],
    goodInfoList: [],
    feedScrollList: [],
  },
  props: {
    core: Object,
  },
  data() {
    return {
      isInfo:false,
      isEditScroll: false,
      isEditAdvertisement: false,
      isEditGoodsInfo: false,

      isShowHrefDialog: false,
      status: [
        { id: "", name: "全部" },
        { id: 1, name: "未开始" },
        { id: 2, name: "上线" },
        { id: 3, name: "已结束" },
        { id: 4, name: "下线" },
      ],
      goodInfoStatus: [],
      addDialog: false,

      feedScrollQueryParams: {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        sort: "",
        crowdValue: "",
        crowdId: "",
        status: "", //状态
        bannerLocation: "",
      },

      feedScrollOptions: [],
      scrollSelectLoading: false,

      goodAdverqueryParams: {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        crowdValue: "",
        crowdId: "",
        status: "", //状态
        positionType: 0,
        postion: "",
      },
      feedAdverOptions: [],
      adverSelectLoading: false,

      goodsInfoParams: {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        crowdValue: "",
        crowdId: "",
        status: "", //状态
      },

      goodsInfoOptions: [],
      goodsInfoSelectLoading: false,

      dataList: [],
      goodInfoDataList: [],
      feedScrollDataList: [],
      currentIndexScroll: undefined,
      currentIndexAdvertisement: undefined,
      currentIndexGoodsInfo: undefined,
      carouselList: {
        bannerLocation: "",
        crowdValue: "",
        status: "",
      },
      bannerLocationList: [
        {
          id: 1,
          name: "第一帧",
        },
        {
          id: 2,
          name: "第二帧",
        },
        {
          id: 3,
          name: "第三帧",
        },
        {
          id: 4,
          name: "第四帧",
        },
        {
          id: 5,
          name: "第五帧",
        },
        {
          id: 6,
          name: "第六帧",
        },
        {
          id: 7,
          name: "第七帧",
        },
        {
          id: 8,
          name: "第八帧",
        },
      ],
      // 时间不能大于当前时间
      disabledDate: (time) => {
        return time.getTime() > Date.now();
      },
    };
  },
  filters: {
    link(data) {
      return data.meta.page_url;
    },
    dateFilter(date) {
      function formatDate(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return (
          year +
          "-" +
          (String(month).length > 1 ? month : "0" + month) +
          "-" +
          (String(day).length > 1 ? day : "0" + day) +
          " " +
          (String(hour).length > 1 ? hour : "0" + hour) +
          ":" +
          (String(minute).length > 1 ? minute : "0" + minute) +
          ":" +
          (String(second).length > 1 ? second : "0" + second)
        );
      }

      if (date) {
        let date1 = formatDate(new Date(date[0]));
        let date2 = formatDate(new Date(date[1]));
        // const nS=new Date(date).getTime()
        return date1 + "至" + date2;
      } else {
        return " ";
      }
    },
    jumpText(val) {
      if (!val) {
        return "app内部跳转";
      } else {
        if (val === "inLink") {
          return "app内部跳转";
        }
        return "跳转至外部";
      }
    },
  },
  mounted() {
    this.initData();
    this.iniFeedScrollDataStatus();
    this.iniGoodInfoDataStatus();
    this.iniDataStatus();

   // this.rowDrop();
    // this.changeTab("notInvalid");
    this.getDict();
    // this.searchList()
  },
  computed: {
    /**
     *   获取列的状态名称
     */
    getStatusName() {
      return function (timevalue, type) {
        let item = {};
        if (!timevalue) {
          item = {
            id: 4,
            name: "未设置时间",
          };
        } else {
          const _date = new Date().getTime();
          const start = new Date(timevalue[0]).getTime();
          const end = new Date(timevalue[1]).getTime();
          if (_date <= end && _date >= start) {
            item = {
              id: 1,
              name: "生效中",
            };
          } else if (_date > end) {
            item = {
              id: 3,
              name: "已失效",
            };
          } else if (_date < start) {
            item = {
              id: 2,
              name: "待生效",
            };
          }
        }
        if (type == "id") {
          return item.id;
        } else {
          return item.name;
        }
      };
    },
  },
  methods: {
    async getDict() {
      // let status = await api.goods.status();
      // if (status.code == 200) this.$nextTick(() => (this.status = status.data));
      // else this.$message.error(status.msg);
    },
    iniFeedScrollDataStatus() {
      this.feedScrollDataList = this.setBannerStatusInitList(this.feedScrollDataList,true);
    },
    iniGoodInfoDataStatus() {
      this.goodInfoDataList = this.setStatusInitList(this.goodInfoDataList,true);
    },
    iniDataStatus() {
      this.dataList = this.setStatusInitList(this.dataList,true);
    },

    feedScrollSearchList() {
      //只有查询全部的时候允许拖拽
      this.feedScrollDataList = this.content.feedScrollList;
      if (Array.isArray(this.feedScrollQueryParams.validityTime)&& this.feedScrollQueryParams.validityTime.length) {
        this.feedScrollDataList = this.feedScrollDataList.filter(
          (item, index) => {
            return (
              new Date(this.feedScrollQueryParams.validityTime[0]) * 1 >=
                new Date(item.validityTime[0]) * 1 &&
              new Date(this.feedScrollQueryParams.validityTime[1]) * 1 <=
                new Date(item.validityTime[1]) * 1
            );
          }
        );
      }
      if (this.feedScrollQueryParams.activityId) {
        this.feedScrollDataList = this.feedScrollDataList.filter(
          (item, index) => {
            return this.feedScrollQueryParams.activityId == item.activityId.toString();
          }
        );
      }
      if (this.feedScrollQueryParams.activityName) {
        this.feedScrollDataList = this.feedScrollDataList.filter(
          (item, index) => {
            return (
              // this.feedScrollQueryParams.activityName === item.activityName
               new RegExp(this.feedScrollQueryParams.activityName).test(item.activityName)
            );
          }
        );
      }
      if (this.feedScrollQueryParams.crowdValue) {
        this.feedScrollDataList = this.feedScrollDataList.filter(
          (item, index) => {
            return this.feedScrollQueryParams.crowdValue == item.crowdValue;
          }
        );
      }
      if (this.feedScrollQueryParams.status) {
        this.feedScrollDataList = this.feedScrollDataList.filter(
          (item, index) => {
            return this.feedScrollQueryParams.status == item.status;
          }
        );
      }

      if (this.feedScrollQueryParams.bannerLocation) {
        this.feedScrollDataList = this.feedScrollDataList.filter(
          (item, index) => {
            return (
              this.feedScrollQueryParams.bannerLocation == item.bannerLocation
            );
          }
        );
      }

      if (this.feedScrollQueryParams.sort) {
        this.feedScrollDataList = this.feedScrollDataList.filter(
          (item, index) => {
            return this.feedScrollQueryParams.sort == item.sort.toString();
          }
        );
      }
    },

    feedScrollResetList() {
      this.feedScrollDataList = this.content.feedScrollList;
      this.iniFeedScrollDataStatus()
      this.resetScrollQueryParams();
    },

    advertResetList() {
      this.dataList = this.content.list;
      this.iniDataStatus()
      this.resetAdvertisementQueryParams();
    },

    goodsInfoResetList() {
      
      this.goodInfoDataList = this.content.goodInfoList;
      this.iniGoodInfoDataStatus()
      this.resetGoodsInfoQueryParams();
    },

    addListScroll() {
      this.isEditScroll = false;
      this.isInfo=false
      this.$refs.feedScrollAlert.open();
    },

    toScrollRemove(data) {
      let _self = this;
      return function () {
        _self.content.feedScrollList.splice(
          _self.content.feedScrollList.findIndex(
            (item) => item.activityId == data.activityId
          ),
          1
        );

        _self.feedScrollDataList = _self.content.feedScrollList;
        _self.iniFeedScrollDataStatus();
        _self.$message({
          type: "success",
          message: "删除成功!",
        });
      }.confirm(_self)();
    },

    advertisementSearchList() {
      //只有查询全部的时候允许拖拽
      this.dataList = this.content.list;
      if (Array.isArray(this.goodAdverqueryParams.validityTime)&& this.goodAdverqueryParams.validityTime.length) {
        this.dataList = this.dataList.filter((item, index) => {
          return (
            new Date(this.goodAdverqueryParams.validityTime[0]) * 1 >=
              new Date(item.validityTime[0]) * 1 &&
            new Date(this.goodAdverqueryParams.validityTime[1]) * 1 <=
              new Date(item.validityTime[1]) * 1
          );
        });
      }

      if (this.goodAdverqueryParams.activityId) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.goodAdverqueryParams.activityId === item.activityId.toString();
        });
      }

      if (this.goodAdverqueryParams.activityName) {
        this.dataList = this.dataList.filter((item, index) => {
          //return this.goodAdverqueryParams.activityName === item.activityName;
          return   new RegExp(this.goodAdverqueryParams.activityName).test(item.activityName)
        });
      }

      if (this.goodAdverqueryParams.positionType) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.goodAdverqueryParams.positionType === item.positionType;
        });
      }

      if (this.goodAdverqueryParams.position) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.goodAdverqueryParams.position === item.position.toString();
        });
      }

      if (this.goodAdverqueryParams.crowdValue) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.goodAdverqueryParams.crowdValue === item.crowdValue;
        });
      }

      if (this.goodAdverqueryParams.status) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.goodAdverqueryParams.status === item.status;
        });
      }
    },

        // 商品信息配置部分
    goodsInfoSearchList() {
      //只有查询全部的时候允许拖拽
      this.goodInfoDataList = this.content.goodInfoList;
      if (Array.isArray(this.goodsInfoParams.validityTime)&&this.goodsInfoParams.validityTime.length) {
        this.goodInfoDataList = this.goodInfoDataList.filter((item, index) => {
          return (
            new Date(this.goodsInfoParams.validityTime[0]) * 1 >=
              new Date(item.validityTime[0]) * 1 &&
            new Date(this.goodsInfoParams.validityTime[1]) * 1 <=
              new Date(item.validityTime[1]) * 1
          );
        });
      }
      if (this.goodsInfoParams.activityId) {
        this.goodInfoDataList = this.goodInfoDataList.filter((item, index) => {
          return this.goodsInfoParams.activityId === item.activityId.toString();
        });
      }
      if (this.goodsInfoParams.activityName) {
        this.goodInfoDataList = this.goodInfoDataList.filter((item, index) => {
         // return this.goodsInfoParams.activityName === item.activityName;
         new RegExp(this.goodsInfoParams.activityName).test(item.activityName)
        });
      }

      if (this.goodsInfoParams.crowdValue) {
        this.goodInfoDataList = this.goodInfoDataList.filter((item, index) => {
          return this.goodsInfoParams.crowdValue === item.crowdValue;
        });
      }
      if (this.goodsInfoParams.status) {
        this.goodInfoDataList = this.goodInfoDataList.filter((item, index) => {
          return this.goodsInfoParams.status === item.status;
        });
      }
    },



    toAdverRemove(data) {
      let _self = this;
      return function () {
        _self.content.list.splice(
          _self.content.list.findIndex(
            (item) => item.activityId == data.activityId
          ),
          1
        );

        _self.dataList = _self.content.list;
        _self.iniDataStatus();
        _self.$message({
          type: "success",
          message: "删除成功!",
        });
      }.confirm(_self)();
    },

    online(scope, type) {
      this.$confirm("确定要执行上线操作吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        if (type === "scroll") {
         // this.content.feedScrollList[scope.$index].status = 2;
        //  this.content.feedScrollList.find(item=>item.activityId==scope.row.activityId).status=2
        //  this.content.feedScrollDataList.find(item=>item.activityId==scope.row.activityId).status=2
         if(this.content.feedScrollList) {
          this.content.feedScrollList.find(item=>item.activityId==scope.row.activityId).status=2
         }
         if(this.feedScrollDataList) {
          this.feedScrollDataList.find(item=>item.activityId==scope.row.activityId).status=2
         }
          this.$message.success("操作成功！");
          // this.feedScrollDataList = this.content.feedScrollList;
        } else if (type === "advert") {
         // this.content.list[scope.$index].status = 2;
         this.content.list.find(item=>item.activityId==scope.row.activityId).status=2
          this.$message.success("操作成功！");
          this.dataList = this.content.list;
        }else if (type === "goodsInfo") {
          //this.content.goodInfoList[scope.$index].status = 2;
          this.content.goodInfoList.find(item=>item.activityId==scope.row.activityId).status=2
          this.$message.success("操作成功！");
          this.goodInfoDataList = this.content.goodInfoList;
        }
      });
    },

    outline(scope, type) {
      this.$confirm("确定要执行下线操作吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        if (type === "scroll") {
         // this.content.feedScrollList[scope.$index].status = 4;
         if(this.content.feedScrollList) {
          this.content.feedScrollList.find(item=>item.activityId==scope.row.activityId).status=4
         }
         if(this.feedScrollDataList) {
          this.feedScrollDataList.find(item=>item.activityId==scope.row.activityId).status=4
         }
          this.$message.success("操作成功！");
          // this.feedScrollDataList = this.content.feedScrollList;
        } else if (type === "advert") {
          //this.content.list[scope.$index].status = 4;
          this.content.list.find(item=>item.activityId==scope.row.activityId).status=4
          this.$message.success("操作成功！");
          this.dataList = this.content.list;
        }else if (type === "goodsInfo") {
          //this.content.goodInfoList[scope.$index].status = 4;
          this.content.goodInfoList.find(item=>item.activityId==scope.row.activityId).status=4
          this.$message.success("操作成功！");
          this.goodInfoDataList = this.content.goodInfoList;
        }
      });
    },
    sortChange(){
      this.content.feedScrollList.sort((a, b) => a.bannerLocation - b.bannerLocation)
      this.feedScrollDataList = this.content.feedScrollList;
    },
    changeLocationSort(scope,val) {
      let dataLists= this.content.feedScrollList
      let ind = scope.$index;
      let nowIndex = this.content.feedScrollList.findIndex(i => i.activityId == val[ind].activityId);

      if (dataLists[nowIndex].bannerLocation <= 0) {
        this.$message.warning("请输入大于0的数字！");
        return;
      }
      if (dataLists[nowIndex].bannerLocation > 9) {
        let index = 0;
        index = dataLists.findIndex(i => i.bannerLocation == dataLists[dataLists.length - 1].bannerLocation && i.sort == dataLists[ind].sort);
        dataLists[nowIndex].bannerLocation = 9;
        this.content.feedScrollList.splice(index, 0, dataLists[nowIndex]);
        this.content.feedScrollList.splice(nowIndex, 1);
      } else {
        let index = 0;
        if (val[ind].sort < dataLists.filter(item => item.bannerLocation == val[ind].bannerLocation).length) {
          index = dataLists.findIndex(i => i.activityId != val[ind].activityId && i.bannerLocation == val[ind].bannerLocation && i.sort == val[ind].sort);
        } else {
          for(let i=0;i<dataLists.length;i++) {
            if (dataLists[i].bannerLocation == val[ind].bannerLocation) {
              index = i + 1;
            }
          }
        }
        if (index > -1) {
          if (nowIndex > index) { // 后去前
            this.content.feedScrollList.splice(index, 0, dataLists[nowIndex]);
            this.content.feedScrollList.splice(nowIndex + 1, 1);
          } else { // 前去后
            this.content.feedScrollList.splice(index, 0, dataLists[nowIndex]);
            this.content.feedScrollList.splice(nowIndex, 1);
          }
        }
      }

      dataLists.sort((a, b) => {
        return a.bannerLocation - b.bannerLocation;
      })
      let startIndex = 0;
      for(let i=0;i<this.content.feedScrollList.length;i++) {
        startIndex += 1;
        this.$set(this.content.feedScrollList[i], "sort", startIndex);
        if (i < this.content.feedScrollList.length - 1) {
          if (this.content.feedScrollList[i].bannerLocation != this.content.feedScrollList[i+1].bannerLocation) {
            startIndex = 0;
          }
        }
      }
      this.feedScrollSearchList();
    },
    changeEditLocationSort(scope,val) {
      let dataLists= this.content.feedScrollList
      let ind = this.currentIndexScroll;
      let nowIndex = this.content.feedScrollList.findIndex(i => i.activityId == val[ind].activityId);

      if (dataLists[nowIndex].bannerLocation <= 0) {
        this.$message.warning("请输入大于0的数字！");
        return;
      }
      if (scope.bannerLocation > 9) {
        let index = 0;
        index = dataLists.findIndex(i => i.bannerLocation == dataLists[dataLists.length - 1].bannerLocation && i.sort == scope.sort);
        dataLists[nowIndex].bannerLocation = 9;
        this.content.feedScrollList.splice(index, 0, dataLists[nowIndex]);
        this.content.feedScrollList.splice(nowIndex, 1);
      } else {
        let index = 0;
        if (scope.sort < dataLists.filter(item => item.bannerLocation == scope.bannerLocation).length) {
          index = dataLists.findIndex(i => i.activityId != scope.activityId && i.bannerLocation == scope.bannerLocation && i.sort == scope.sort);
        } else {
          for(let i=0;i<dataLists.length;i++) {
            if (dataLists[i].bannerLocation == scope.bannerLocation) {
              index = i + 1;
            }
          }
        }
        if (index > -1) {
          if (nowIndex > index) { // 后去前
            this.content.feedScrollList.splice(index, 0, dataLists[nowIndex]);
            this.content.feedScrollList.splice(nowIndex + 1, 1);
          } else { // 前去后
            this.content.feedScrollList.splice(index, 0, dataLists[nowIndex]);
            this.content.feedScrollList.splice(nowIndex, 1);
          }
        }
      }

      dataLists.sort((a, b) => {
        return a.bannerLocation - b.bannerLocation;
      })
      let startIndex = 0;
      for(let i=0;i<this.content.feedScrollList.length;i++) {
        startIndex += 1;
        this.$set(this.content.feedScrollList[i], "sort", startIndex);
        if (i < this.content.feedScrollList.length - 1) {
          if (this.content.feedScrollList[i].bannerLocation != this.content.feedScrollList[i+1].bannerLocation) {
            startIndex = 0;
          }
        }
      }
      this.feedScrollSearchList();
    },
    changeSort(scope,val) {
      let dataLists= this.content.feedScrollList
      let ind = scope.$index;
      if (dataLists[ind].sort <= 0) {
        this.$message.warning("请输入大于0的数字！");
        return;
      }
      
      let nowIndex = this.content.feedScrollList.findIndex(i => i.activityId == val[ind].activityId);


      if (dataLists[ind].sort > this.content.feedScrollList.filter(i => i.bannerLocation == this.content.feedScrollList[nowIndex].bannerLocation).length) {
        let index = 0;
        for(let i=0;i<this.content.feedScrollList.length;i++) {
          if (this.content.feedScrollList[i].bannerLocation == this.content.feedScrollList[nowIndex].bannerLocation) {
            index = i;
          }
        }
        this.content.feedScrollList.splice(index + 1, 0, this.content.feedScrollList[ind]);
        this.content.feedScrollList.splice(ind, 1);
      } else {
        // let isIndex = dataLists.filter(i => i.bannerLocation == val[ind].bannerLocation)
        let updateIndex = 1;
        for(let i=0;i<this.content.feedScrollList.length;i++) {
          if (this.content.feedScrollList[i].activityId == val[ind].activityId) {
            break;
          }
          updateIndex += 1;
          if (i < dataLists.length - 1) {
            if (dataLists[i].bannerLocation != dataLists[i+1].bannerLocation) {
              updateIndex = 1;
            }
          }
        }
        let locationIndex = 0;
        for(let i=0;i<dataLists.length;i++) {
          if (dataLists[i].bannerLocation < val[ind].bannerLocation) {
            locationIndex += 1;
          } else {
            break;
          }
        }
        if (this.content.feedScrollList[nowIndex].sort > updateIndex) {
          this.content.feedScrollList.splice(this.content.feedScrollList[nowIndex].sort * 1 + locationIndex * 1, 0, this.content.feedScrollList[nowIndex]);
          this.content.feedScrollList.splice(nowIndex, 1);
        } else {
          this.content.feedScrollList.splice(
            this.content.feedScrollList[nowIndex].sort * 1 + locationIndex * 1 - 1,
            0,
            this.content.feedScrollList[nowIndex]
          );
          this.content.feedScrollList.splice(nowIndex + 1, 1);
        }
      }



      // if (dataLists[ind].sort > dataLists.filter(i => i.bannerLocation == val[ind].bannerLocation).length) {
      //   let index = 0;
      //   for(let i=0;i<dataLists.length;i++) {
      //     if (dataLists[i].bannerLocation == dataLists[ind].bannerLocation) {
      //       index = i;
      //     }
      //   }
      //   dataLists.splice(index + 1, 0, dataLists[ind]);
      //   dataLists.splice(ind, 1);
      // } else {
      //   // let isIndex = dataLists.filter(i => i.bannerLocation == val[ind].bannerLocation)
      //   let updateIndex = 1;
      //   for(let i=0;i<dataLists.length;i++) {
      //     if (dataLists[i].activityId == val[ind].activityId) {
      //       break;
      //     }
      //     updateIndex += 1;
      //     if (i < dataLists.length - 1) {
      //       if (dataLists[i].bannerLocation != dataLists[i+1].bannerLocation) {
      //         updateIndex = 1;
      //       }
      //     }
      //   }
      //   let locationIndex = 0;
      //   for(let i=0;i<dataLists.length;i++) {
      //     if (dataLists[i].bannerLocation < val[ind].bannerLocation) {
      //       locationIndex += 1;
      //     } else {
      //       break;
      //     }
      //   }
      //   if (dataLists[ind].sort > updateIndex) {
      //     dataLists.splice(dataLists[ind].sort * 1 + locationIndex * 1, 0, dataLists[ind]);
      //     dataLists.splice(ind, 1);
      //   } else {
      //     dataLists.splice(
      //       dataLists[ind].sort * 1 + locationIndex * 1 - 1,
      //       0,
      //       dataLists[ind]
      //     );
      //     dataLists.splice(ind + 1, 1);
      //   }
      // }

      dataLists.sort((a, b) => {
        return a.bannerLocation - b.bannerLocation;
      })

      let startIndex = 0;
      for(let i=0;i<this.content.feedScrollList.length;i++) {
        startIndex += 1;
        this.$set(this.content.feedScrollList[i], "sort", startIndex);
        if (i < this.content.feedScrollList.length - 1) {
          if (this.content.feedScrollList[i].bannerLocation != this.content.feedScrollList[i+1].bannerLocation) {
            startIndex = 0;
          }
        }
      }

      this.feedScrollSearchList();

      // let startIndex = 0;
      // for(let i=0;i<dataLists.length;i++) {
      //   startIndex += 1;
      //   this.$set(dataLists[i], "sort", startIndex);
      //   if (i < dataLists.length - 1) {
      //     if (dataLists[i].bannerLocation != dataLists[i+1].bannerLocation) {
      //       startIndex = 0;
      //     }
      //   }
      // }
      //  this.content.feedScrollList = dataLists;
    },
    openFeedGoodsAdvertisementAlert() {
      this.isEditAdvertisement=false
      this.isInfo=false
      this.$refs.feedGoodsAdvertisementAlert.open();
    },

    openfeedGoodsInfoAlert() {
      this.isEditGoodInfo=false
      this.isInfo=false
      this.$refs.feedGoodsInfoAlert.open();
    },


    resetScrollQueryParams() {
      this.feedScrollQueryParams = {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        sort: "",
        crowdValue: "",
        crowdId: "",
        status: "", //状态
        bannerLocation: "",
      };
    },
    resetAdvertisementQueryParams() {
      this.goodAdverqueryParams = {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        crowdValue: "",
        crowdId: "",
        status: "", //状态
        positionType: 0,
        postion: "",
      };
    },
    resetGoodsInfoQueryParams() {
      this.goodsInfoParams = {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        crowdValue: "",
        crowdId: "",
        status: "", //状态
      };
    },

    initData() {
      debugger
      let list = this.content.list.filter(item => item !== null);
      this.$set(this.content, "list", list);
      this.sortChange()
      this.dataList = this.content.list;
      this.goodInfoDataList = this.content.goodInfoList;
      this.feedScrollDataList = this.content.feedScrollList;
      
    },
    rowDrop() {
      const _this = this;
      const tbody = document.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      sortableObject = Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        ghostClass: "sortable-ghost",
        onEnd: (evt) => {
          const currRow = (_this.dataList || []).splice(evt.oldIndex, 1)[0];
          (_this.dataList || []).splice(evt.newIndex, 0, currRow);
          const currRowData = (_this.content.list || []).splice(
            evt.oldIndex,
            1
          )[0];
          (_this.content.list || []).splice(evt.newIndex, 0, currRowData);
        },
      });
    },
    //生成唯一id
    genID(length) {
      return Number(
        Math.random().toString().substr(3, length) + Date.now()
      ).toString(36);
    },
    toScrollEdit(data, index,isInfo) {
        if(isInfo){
        this.isInfo=true
      }else{
         this.isInfo=false
      }

      this.currentIndexScroll = index;
      this.isEditScroll = true;
      this.$refs.feedScrollAlert.open(data, true);
    },

    toRemove(data) {
      let _self = this;
      return function () {
        _self.content.goodInfoList.splice(
          _self.content.goodInfoList.findIndex(
            (item) => item.activityId == data.activityId
          ),
          1
        );

        _self.goodInfoDataList = _self.content.goodInfoList;
        _self.iniFeedScrollDataStatus();
        _self.$message({
          type: "success",
          message: "删除成功!",
        });
      }.confirm(_self)();
    },

    async scrollOptionFilter(val) {
      this.scrollSelectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.scrollSelectLoading = false;
        this.feedScrollOptions = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.scrollSelectLoading = false;
        this.feedScrollOptions = [];
      }
    },

    scrollSelectCrowd(e) {
      if (e) {
        this.feedScrollQueryParams.crowdId = Number(
          this.feedScrollOptions[0].value.trim()
        );
        this.feedScrollQueryParams.crowdValue = this.feedScrollOptions[0].label;
      } else {
        this.feedScrollQueryParams.crowdId = "";
        this.feedScrollQueryParams.crowdValue = "";
      }
      this.$forceUpdate();
    },
    async adverOptionFilter(val) {
      this.adverSelectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.adverSelectLoading = false;
        this.feedAdverOptions = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.adverSelectLoading = false;
        this.feedAdverOptions = [];
      }
    },
    adverSelectCrowd(e) {
      if (e) {
        this.goodAdverqueryParams.crowdId = Number(
          this.feedAdverOptions[0].value.trim()
        );
        this.goodAdverqueryParams.crowdValue = this.feedAdverOptions[0].label;
      } else {
        this.goodAdverqueryParams.crowdId = "";
        this.goodAdverqueryParams.crowdValue = "";
      }
      this.$forceUpdate();
    },

    async goodsInfoOptionFilter(val) {
      this.adverSelectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.goodsInfoSelectLoading = false;
        this.goodsInfoOptions = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.goodsInfoSelectLoading = false;
        this.goodsInfoOptions = [];
      }
    },
    goodsInfoSelectCrowd(e) {
      if (e) {
        this.goodsInfoParams.crowdId = Number(
          this.goodsInfoOptions[0].value.trim()
        );
        this.goodsInfoParams.crowdValue = this.goodsInfoOptions[0].label;
      } else {
        this.goodsInfoParams.crowdId = "";
        this.goodsInfoParams.crowdValue = "";
      }
      this.$forceUpdate();
    },

    async querySearchCrowd() {
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        this.crowdName = data.name;
      }
    },

    feedScrollAlertCallback(val) {
      let arr = [...this.content.feedScrollList];
    //   //校验重复
    //  if(this.isEditScroll){
    //     arr.splice(arr.findIndex(item => item.activityId == val.activityId),1)
       
    //   }
    //  let classFlag = false;
    //  let id;
      
    //     if (val.timeType == 1) {
    //       const start_form = new Date(val.validityTime[0]).getTime();          
    //       const end_form = new Date(val.validityTime[1]).getTime();          
    //       arr.forEach(item => {
    //         if(item.timeType==2){
    //           return
    //         }
    //         const start = new Date(item.validityTime[0]).getTime();
    //         const end = new Date(item.validityTime[1]).getTime();
    //         if (start_form <= start && end_form >= end) {
    //           if(item.bannerLocation==val.bannerLocation){
    //             if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
    //                       classFlag = true;
    //                       id=item.activityId
    //                     }
    //                   }
    //         } else if ((start_form >= start && start_form <= end) || (end_form >= start && end_form <= end)) {
    //           if(item.bannerLocation==val.bannerLocation){
    //             if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
    //                       classFlag = true;
    //                       id=item.activityId
    //                     }
    //                   }
    //         }
    //       })
    //     } else if (val.timeType == 2) {
    //       // 1:周 2:月 3:日
    //       arr.forEach(item => {
    //         if(item.timeType==1){
    //           return
    //         }
    //         if(Array.isArray(val.circulateTime.circulateList)){
    //           // let _date =  new Date().toLocaleTimeString('en-US', {hour12: false});  
    //           if (item.timeType == 2) {
    //             let _date = val.circulateTime.circulateList[0].selectTimeData;     
    //             if(val.circulateTime.circulateType==3){
    //               item.circulateTime.circulateList.forEach(element => {
    //                 if ((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1])) {
    //                   // classFlag = true;
    //                   if(item.bannerLocation==val.bannerLocation){
    //                     if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
    //                       classFlag = true;
    //                       id=item.activityId
    //                     }
    //                   }
    //                 }
    //               });
    //             }
    //             if(item.circulateTime.circulateType==1 || item.circulateTime.circulateType==2){
    //               item.circulateTime.circulateList.forEach(element => {
    //                 if (val.circulateTime.circulateList[0].weekOrday==element.weekOrday&&((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1]))) {
    //                   if(item.bannerLocation==val.bannerLocation){
    //                     if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
    //                       classFlag = true;
    //                       id=item.activityId
    //                     }
    //                   }
    //                 }
    //               });
    //             }
    //           }
    //         }
    //       })
    //     }
      
      // if (classFlag) {
      //   this.$message.error("同时段、同人群、位置已有该活动id"+id);
      //   return;
      // }
      if (this.isEditScroll) {
       // this.content.feedScrollList[this.content.feedScrollList.findIndex(item=>item.activityId==val.activityId)]=val
       this.$set(this.content.feedScrollList,this.content.feedScrollList.findIndex(item=>item.activityId==val.activityId),val)
        //this.$set(this.content.feedScrollList, this.currentIndexScroll, val);
      } else {
        let id = 0;
        id = Math.floor(Math.random() * 90000) + 10000;
        this.$set(val, "activityId", id);
        if (
          this.content.feedScrollList.findIndex((item) => item.activityId == id) > -1||!val.activityId ) {
          this.$message("id错误，请重新添加！");
          return;
        }
        arr.splice(0, 0, val);
        this.$set(this.content, "feedScrollList", arr);
        this.$refs.feedScrollAlert.addDialogCancel()
      }
      this.$refs.feedScrollAlert.addDialogCancel()
      this.$message.success(
        `${this.isEditScroll ? "编辑" : "添加"}成功！`
      );
      if (!this.isEditScroll) {
        this.sortChange()
        this.feedScrollResetList()
      } else {
        this.changeEditLocationSort(val, this.feedScrollDataList);
      }
      this.iniFeedScrollDataStatus()
      this.isEditScroll = false;
      this.currentIndexScroll = undefined;
    },
    toEditAdvertisement(data, index,isInfo) {
       if(isInfo){
        this.isInfo=true
      }else{
         this.isInfo=false
      }
      this.isEditAdvertisement = true;
      this.currentIndexAdvertisement = index;
      this.$refs.feedGoodsAdvertisementAlert.open(data, true);
    },
    feedGoodsAdvertisementAlertCallBack(val) {
      let arr = [...this.content.list];
      //校验重复
      if(this.isEditAdvertisement){
        arr.splice(arr.findIndex(item => item.activityId == val.activityId),1)
      }
     let classFlag = false;
     let id;
     
        if (val.timeType == 1) {
          const start_form = new Date(val.validityTime[0]).getTime();          
          const end_form = new Date(val.validityTime[1]).getTime();          
          arr.forEach(item => {
            if(item.timeType==2){
              return
            }
            const start = new Date(item.validityTime[0]).getTime();
            const end = new Date(item.validityTime[1]).getTime();
            if (start_form <= start && end_form >= end) {
              if(item.positionType==val.positionType){
              if(item.position==val.position){
                if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
              }
                           
              }
            } else if ((start_form >= start && start_form <= end) || (end_form >= start && end_form <= end)) {
              if(item.positionType==val.positionType){
                if(item.position==val.position){
                  if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                           }
                      }
            }
          })
        } else if (val.timeType == 2) {
          // 1:周 2:月 3:日
          arr.forEach(item => {
            if(item.timeType==1){
              return
            }
            if(Array.isArray(val.circulateTime.circulateList)){
              // let _date =  new Date().toLocaleTimeString('en-US', {hour12: false});  
              if (item.timeType == 2) {
                let _date = val.circulateTime.circulateList[0].selectTimeData;     
                if(val.circulateTime.circulateType==3){
                  item.circulateTime.circulateList.forEach(element => {
                    if ((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1])) {
                      // classFlag = true;
                      if(item.positionType==val.positionType){
                        if(item.position==val.position){
                          if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                           }
                      }
                    }
                  });
                }
                if(item.circulateTime.circulateType==1 || item.circulateTime.circulateType==2){
                  item.circulateTime.circulateList.forEach(element => {
                    if (val.circulateTime.circulateList[0].weekOrday==element.weekOrday&&((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1]))) {
                      if(item.positionType==val.positionType){
                        if(item.position==val.position){
                          if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                           }
                      }
                    }
                  });
                }
              }
            }
          })
        }
      
      if (classFlag) {
        this.$message.error("同时段、同人群、位置已有该活动id"+id);
        return;
      }
      if (this.isEditAdvertisement) {
        //this.content.list[this.content.list.findIndex(item=>item.activityId==val.activityId)]=val
        this.$set(this.content.list,this.content.list.findIndex(item=>item.activityId==val.activityId),val)
       // this.$set(this.content.list, this.currentIndexAdvertisement, val);
      } else {
        let id = 0;
        id = Math.floor(Math.random() * 90000) + 10000;
        this.$set(val, "activityId", id);
        if (this.content.list.findIndex((item) => item.activityId == id) > -1||!val.activityId) {
          this.$message("id错误，请重新添加！");
          return;
        }
        arr.splice(0, 0, val);
        this.$set(this.content, "list", arr);
      }
      this.$message.success(
        `${this.isEditAdvertisement ? "编辑" : "添加"}成功！`
      );
    
      this.isEditAdvertisement = false;
      this.$refs.feedGoodsAdvertisementAlert.addDialogCancel()
      this.currentIndexAdvertisement = undefined;
      this.iniDataStatus()
      this.advertResetList();
    
    },

    toEditGoodsInfo(data, index,isInfo) {
        if(isInfo){
        this.isInfo=true
      }else{
         this.isInfo=false
      }

      this.currentIndexGoodsInfo = index;
      this.isEditGoodInfo = true;
      this.$refs.feedGoodsInfoAlert.open(data, true);
    },
    // 校验绑定商品
 async checkBindCsuOrProductGroup(addForm) {
  return true
      let canSave = true;
      (addForm.selectProducts || []).forEach((item) => {
        if (isNaN(item) || item < 0) {
          canSave = false;
        }
      });
      if (!canSave) {
        this.$message.error("指定商品ID只能输入数字");
        return;
      }
      const params = {
        type: addForm.selectProductType === "appointProduct" ? 1 : 2,
        exhibitionId: addForm.selectProductGroupId,
        // csuIds: this.addForm.selectProducts.filter(i => i).map(Number),
      };
      const result = await api.topic.checkBindCsuOrProductGroup(params);
      if ((result.data.data || {}).checkResult) {
        this.$message.success("绑定成功");
       return true
      } else {
        if (addForm.selectProductType === "appointProduct") {
          this.$message.error(
            `以下商品id绑定失败：${(
              (result.data.data || {}).failureCsuIds || []
            ).join()}`
          );
          return false
        } else {
          this.$message.error(result.data.msg);
          return false
        }
      }
    },
   async feedGoodsInfoAlertDone(val) {
   
      let arr = [...this.content.goodInfoList];
      //校验重复
      
       if(this.isEditGoodInfo){
        arr.splice(arr.findIndex(item => item.activityId == val.activityId),1)
      }
       let classFlag = false;
       let id;
        if (val.timeType == 1) {
          const start_form = new Date(val.validityTime[0]).getTime();          
          const end_form = new Date(val.validityTime[1]).getTime();          
          arr.forEach(item => {
            if(item.timeType==2){
              return
            }
            const start = new Date(item.validityTime[0]).getTime();
            const end = new Date(item.validityTime[1]).getTime();
            if (start_form <= start && end_form >= end) {
              if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
            } else if ((start_form >= start && start_form <= end) || (end_form >= start && end_form <= end)) {
              if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
            }
          })
        } else if (val.timeType == 2) {
          // 1:周 2:月 3:日
          arr.forEach(item => {
            if(item.timeType==1){
              return
            }
            if(Array.isArray(val.circulateTime.circulateList)){
              // let _date =  new Date().toLocaleTimeString('en-US', {hour12: false});  
              if (item.timeType == 2) {
                let _date = val.circulateTime.circulateList[0].selectTimeData;     
                if(val.circulateTime.circulateType==3){
                  item.circulateTime.circulateList.forEach(element => {
                    if ((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1])) {
                      if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                    }
                  });
                }
                if(item.circulateTime.circulateType==1 || item.circulateTime.circulateType==2){
                  item.circulateTime.circulateList.forEach(element => {
                    if (val.circulateTime.circulateList[0].weekOrday==element.weekOrday&&((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1]))) {
                      if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                    }
                  });
                }
              }
            }
          })
        }
      
      if (classFlag) {
        this.$message.error("同时段、同人群已有该活动id"+id);
        return;
      }
      if (this.isEditGoodInfo) {
        
       // this.content.goodInfoList[this.content.goodInfoList.findIndex(item=>item.activityId==val.activityId)]=val
       // this.$set(this.content.goodInfoList, this.currentIndexGoodsInfo, val);
       this.$set(this.content.goodInfoList,this.content.goodInfoList.findIndex(item=>item.activityId==val.activityId),val)
      } else {
        let id = 0;
        
        id = Math.floor(Math.random() * 90000) + 10000;
        
        this.$set(val, "activityId", id);
        if (this.content.goodInfoList.findIndex((item) => item.activityId == id) > -1||!val.activityId) {
          this.$message("id错误，请重新添加！");
          return;
        }
        arr.splice(0, 0, val);
        this.$set(this.content, "goodInfoList", arr);
      }
      this.$message.success(`${this.isEditGoodInfo ? "编辑" : "添加"}成功！`);
      this.isEditGoodInfo = false;
      this.currentIndexGoodsInfo = undefined;
      this.$refs.feedGoodsInfoAlert.addDialogCancel()
      this.goodsInfoResetList();
      this.iniGoodInfoDataStatus()
    },

    //公用的方法
    setStatusInitList(data,isInit) {
      if (!data) {
        return;
      }
      data.forEach((item, index) => {
      
        // item.sort = index + 1;
        this.$set(item, "sort", index + 1);
        // 1:"月 ",2:"周 ",3:"日 "
        // if (item.status != 4) {
          if (item.timeType == 2) {
            // if (Array.isArray(item.circulateTime.circulateList)) {
            //   let _date = new Date().toLocaleTimeString("en-US", {
            //     hour12: false,
            //   });
            //   let dateTime = new Date().getTime();
            //   if (item.circulateTime.circulateType == 3) {
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       }
            //       else if (_date >= element.selectTimeData[1]) {
            //         item.status = 3; //已结束
            //       }
            //       else if(_date<element.selectTimeData[0]) {
            //         item.status = 1; //未开始
            //       }
            //     });
            //   }
            //   if (item.circulateTime.circulateType == 1) {
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         new Date().getDate() == element.weekOrday &&
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       } else if (
            //         dateTime > new Date(element.selectTimeData[1]).getTime()
            //       ) {
            //         item.status = 3; //已结束
            //       } else if(_date<element.selectTimeData[0]) {
            //         item.status = 1; //未开始
            //       }
            //     });
            //   }
            //   if (item.circulateTime.circulateType == 2) {
            //     const dayOfWeek = [
            //       "周日",
            //       "周一",
            //       "周二",
            //       "周三",
            //       "周四",
            //       "周五",
            //       "周六",
            //     ][new Date().getDay()];
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         dayOfWeek == element.weekOrday &&
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       } else if (
            //         dateTime > new Date(element.selectTimeData[1]).getTime()
            //       ) {
            //         item.status = 3; //已结束
            //       } else if(_date<element.selectTimeData[0]) {
            //         item.status = 1; //未开始
            //       }
            //     });
            //   }
            // }
            if(item.status!=4){
              item.status=2
            }
          } else {
            if (new Date() * 1 < new Date(item.validityTime[0]) * 1) {
              item.status = 1; // 未开始
            } else if (
              new Date() * 1 > new Date(item.validityTime[0]) * 1 &&
              new Date() * 1 < new Date(item.validityTime[1]) * 1&&item.status!=4
            ) {
              item.status = 2; //上线
            } else if (new Date() * 1 > new Date(item.validityTime[1]) * 1) {
              item.status = 3;
            } else {
              item.status = 4;
            }
          }
        // }
      });
      return data;
    },
    setBannerStatusInitList(data, isInit) {
      if (!data) {
        return;
      }
      let startIndex = 0;
      for(let i=0;i<data.length;i++) {
        startIndex += 1;
        this.$set(data[i], "sort", startIndex);
        if (i < data.length - 1) {
          if (data[i].bannerLocation != data[i+1].bannerLocation) {
            startIndex = 0;
          }
        }
          if (data[i].timeType == 2) {
          if(data[i].status!=4){
            data[i].status=2
          }
        } else {
          if (new Date() * 1 < new Date(data[i].validityTime[0]) * 1) {
            data[i].status = 1; // 未开始
          } else if (
            new Date() * 1 > new Date(data[i].validityTime[0]) * 1 &&
            new Date() * 1 < new Date(data[i].validityTime[1]) * 1&&data[i].status!=4
          ) {
            data[i].status = 2; //上线
          } else if (new Date() * 1 > new Date(data[i].validityTime[1]) * 1) {
            data[i].status = 3;
          } else {
            data[i].status = 4;
          }
        }
      }
      // return;
      //   startIndex = i + 1;
      //   this.$set(data.length, "sort", startIndex);
      //   if (i < data.length && (data[i].bannerLocation != data[i+1].bannerLocation)) {
      //     startIndex = 0;
      //   }

      
      return data;
    }
  },
};
</script>
<style lang="scss" scoped>
.title {
  text-align: left;
  line-height: 30px;
  background-color: #f2f2f2;
  margin: 10px 0;
  padding-left: 10px;
}
.three-button {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
.tableBox {
  width: 100%;
}
.dialog-activity-sort {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.goods_info_crowd_value {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
