<template>
    <div>
        <el-input size="small" v-if="!params.customItemDisabled" v-model="url" @keyup.native.enter="useCustomItem()"
                  placeholder="自定义链接" style="margin-bottom:5px">
            <el-button slot="append" @click="useCustomItem()">使用</el-button>
        </el-input>

        <el-input size="small" icon="search" v-model="text" placeholder="请输入名称/ID">
            <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
        </el-input>

        <el-table size="mini" :data="list"  highlight-current-row   @current-change="onSelect" :row-style="getRowStyle" style="margin:5px 0" v-loading="loading">
            <el-table-column label="名称/ID">
                <template slot-scope="scope">
                    <p>
                        <a :href="'#/topic/edit/' + scope.row.page_id" target="_blank" @click.stop style="color:#333">{{scope.row.page_name}}</a>
                    </p>
                    <p>
                        <small style="color:#999">{{scope.row.page_id}}</small>
                    </p>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination
                small
                layout="pager"
                :current-page="pagination.current"
                :page-size="pagination.size"
                :total="pagination.total"
                @current-change="getList">
        </el-pagination>
    </div>
</template>

<script>
    import api from 'api'

    export default {
        props: {
            params: Object,
            default() {
                return {
                    customItemDisabled: false
                }
            }
        },
        data() {
            return {
                url: '',
                text: '',
                pagination: {
                    size: 5,
                    current: 1,
                    total: 0
                },
                list: [],
                loading: false
            }
        },
        methods: {
            async getList(page = 1) {
                this.pagination.current = page;
                this.loading = true;
                const result = await api.topic.list({
                    branchCode : this.params.branchCode,
                    status: 'published',
                    keyword: this.text,
                    pageFrom: this.pagination.current,
                    pageSize: this.pagination.size
                })
                this.loading = false;
                if (result.code === 200) {
                    this.list = result.data.rows;
                    this.pagination.total = result.data.total;
                }
            },
            onSelect(row) {
                this.$emit('select', {
                    type: 'topic',
                    label: '页面',
                    id: row.page_id,
                    desc: row.page_name,
                    meta: row
                })
            },
            getRowStyle(row) {
                return {
                    height: '60px',
                    cursor: 'pointer',
                }
            },
            useCustomItem() {
                this.$emit('select', {
                    type: 'url',
                    label: '自定义',
                    desc: '自定义',
                    url: this.url
                })
            }
        },
        mounted() {
            this.getList();
        }
    }
</script>
