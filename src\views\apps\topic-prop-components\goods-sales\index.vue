<template>
    <div class="topic-menu-list">
        <!--公共配置-->
        <div class="container">
            <el-row :gutter="10">
                <el-col :span="12">
                    <el-input placeholder="请输入选项卡名称" v-model="tabName" size="mini"></el-input>
                </el-col>
                <el-col :span="12">
                    <el-button size="mini" type="primary" @click="add_tab">添加选项卡</el-button>
                </el-col>
            </el-row>

            <el-row :gutter="10">
                <el-col :span="8">
                    tab的字体颜色:
                    <el-color-picker v-model="content.tab_color" size="mini"></el-color-picker>
                </el-col>
                <el-col :span="8">
                    tab的横线颜色:
                    <el-color-picker v-model="content.tab_line_color" size="mini"></el-color-picker>
                </el-col>
                <el-col :span="8">
                    tab的背景颜色:
                    <el-color-picker v-model="content.tab_bg_color" size="mini"></el-color-picker>
                </el-col>
            </el-row>

        </div>
        <div class="container">
            <el-tabs v-model="activeName" @tab-click="tab_click" closable @tab-remove="remove_tab">
                <el-tab-pane v-for="item  in  list" :label="item.name" :name="item.name" :key="item.name"
                             v-if="list.length>0">
                    <div class="container">
                        <el-radio v-model="type" label="banner">给banner配置商品</el-radio>
                        <el-radio v-model="type" label="list">给列表配置商品</el-radio>
                    </div>

                    <!--给banner配置-->
                    <div class="container" v-if="type==='banner'">
                        <div style="height: 30px">
                            <el-radio-group v-model="item.is_init_banner">
                                <el-radio :label="index" v-for="(item,index) in banner_type" :key="index">{{item}}
                                </el-radio>
                            </el-radio-group>
                        </div>
                        <div style="margin: 10px 0">
                            设置banner的背景色:
                            <el-color-picker v-model="item.bg_color" size="mini" @change="clear_self_bg_img"></el-color-picker>
                            <el-button @click="banner_bg_dialog=true"  type="primary">上传banner的背景图片</el-button>
                            <img :src="item.bg_img" alt="" width="40" height="40">
                        </div>
                        <div v-if="item.is_init_banner===0">
                            <h3>banner的商品</h3>
                            <el-table :data="item.banner_list" style="width: 100%;margin-top: 5px" height="250"
                                      v-if="item.banner_list.length>0"
                                      ref="multipleTable">
                                <el-table-column fixed label="图片" width="80">
                                    <template slot-scope="scope">
                                        <img :src="scope.row.init_img_url" :alt="scope.row.productName"
                                             style="width:100%;max-height:50px;" v-if="scope.row.init_img_url">
                                        <img :src="scope.row.imageUrl" :alt="scope.row.productName"
                                             style="width:100%;max-height:50px;"
                                             v-else>
                                    </template>
                                </el-table-column>

                                <el-table-column prop="productName" label="药名">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.productName">{{scope.row.productName}}</span>
                                        <span v-else>{{scope.row.showName}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="规格">
                                    <template slot-scope="scope">
                                        {{scope.row.mediumPackageTitle}}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="fob" label="价格" width="80">
                                    <template slot-scope="scope">
                                        {{scope.row.fob}}
                                    </template>
                                </el-table-column>
                                <el-table-column fixed="right" label="操作" width="100">
                                    <template slot-scope="scope">
                                        <div class="edit-button">
                                            <el-button @click="handleDelete(scope.row)" type="warning" size="mini">删除
                                            </el-button>
                                        </div>
                                        <div class="edit-button" style="margin-top: 5px">
                                            <el-button @click="toAdd(scope.row)" type="primary" size="mini">上传图片
                                            </el-button>
                                        </div>

                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <div v-else class="init_topic-banner">
                            <el-button @click="init_toAdd" class="btn-block mb-10" type="primary">上传图片</el-button>
                            <el-table :data="item.init_banner_list" size="mini" style="width: 100%">
                                <el-table-column label="图片">
                                    <template slot-scope="scope">
                                        <div class="init_container">
                                            <div class="img">
                                                <img :src="scope.row.image">
                                            </div>
                                            <div class="button-list">
                                                <el-row style="margin-top: 5px" :gutter="10">
                                                    <el-col :span="15">
                                                        <div class="grid-content bg-purple">
                                                            <el-tooltip placement="top" style="width: 100px">
                                                                <div slot="content" style="width: 250px">
                                                                    <div class="demonstration">
                                                                        商品名称:{{scope.row.goods_title}}
                                                                    </div>
                                                                    <div class="demonstration"
                                                                         v-if="scope.row.timevalue">
                                                                        开始时间:{{scope.row.timevalue[0]}}
                                                                    </div>
                                                                    <div class="demonstration"
                                                                         v-if="scope.row.timevalue">
                                                                        结束时间:{{scope.row.timevalue[1]}}
                                                                    </div>
                                                                    <div class="demonstration">活动页链接:
                                                                        <p>
                                                                            {{scope.row.link|link}}
                                                                        </p>
                                                                    </div>
                                                                    <div class="demonstration">
                                                                        商品介绍:{{scope.row.goods_introduce}}
                                                                    </div>
                                                                    <div class="demonstration">
                                                                        商品说明1:{{scope.row.first_explain}}
                                                                    </div>
                                                                    <div class="demonstration">
                                                                        商品说明2:{{scope.row.second_explain}}
                                                                    </div>
                                                                </div>
                                                                <el-button>配置详情</el-button>
                                                            </el-tooltip>
                                                        </div>
                                                    </el-col>
                                                    <el-col :span="9">
                                                        <div style="margin: 5px 0">
                                                            <el-button size="mini"
                                                                       @click="init_toEdit(scope.row, scope.$index)"
                                                                       type="primary">编辑
                                                            </el-button>
                                                        </div>
                                                        <div style="margin: 5px 0">
                                                            <el-button size="mini" @click="init_toRemove(scope.row)"
                                                                       type="danger">删除
                                                            </el-button>
                                                        </div>

                                                    </el-col>
                                                </el-row>

                                            </div>
                                        </div>
                                        <!--<div class="link-desc">{{scope.row.link | link}}</div>-->
                                    </template>
                                </el-table-column>
                            </el-table>
                            <el-dialog class="banner-dialog" title="添加图片" :visible.sync="init_addDialog">
                                <el-upload
                                        class="topic-image-upload"
                                        ref="upload"
                                        accept="image/jpeg,image/jpg,image/png,image/gif"
                                        :show-file-list="false"
                                        :before-upload="() => {loading = true; return true;}"
                                        :on-success="init_onUploadImage">
                                    <img v-if="dataForm.image" :src="dataForm.image" class="image">
                                    <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                                    <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                                </el-upload>

                                <el-row style="margin-top: 5px" :gutter="10">
                                    <el-col :span="24">
                                        <div class="grid-content bg-purple">
                                            <el-input placeholder="商品名称" v-model="dataForm.goods_title">
                                                <template slot="prepend">商品名称</template>
                                            </el-input>

                                        </div>
                                    </el-col>

                                </el-row>

                                <el-row style="margin-top: 15px" :gutter="10">
                                    <el-col :span="24">
                                        <div class="grid-content bg-purple">
                                            <el-input placeholder="商品介绍" v-model="dataForm.goods_introduce">
                                                <template slot="prepend">商品介绍</template>
                                            </el-input>

                                        </div>
                                    </el-col>

                                </el-row>


                                <el-row style="margin-top: 15px" :gutter="10">
                                    <el-col :span="12">
                                        <div class="grid-content bg-purple">
                                            <el-input placeholder="底部说明1" v-model="dataForm.first_explain">
                                                <template slot="prepend">底部说明1</template>
                                            </el-input>
                                        </div>
                                    </el-col>
                                    <el-col :span="12">
                                        <div class="grid-content bg-purple">
                                            <el-input placeholder="底部说明2" v-model="dataForm.second_explain">
                                                <template slot="prepend">底部说明2</template>
                                            </el-input>
                                        </div>
                                    </el-col>
                                </el-row>

                                <div style="margin-top: 15px">
                                    <!-- 跳转链接<span>({{dataForm.link | link}})</span>-->
                                    <el-input placeholder="链接地址" v-model="dataForm.link.meta.page_url">
                                        <template slot="prepend">跳转链接</template>
                                    </el-input>
                                </div>
                                <page-link @select="init_onSetLink"
                                           :params="{branchCode: topic.branchCode}"></page-link>
                                <div slot="footer" class="dialog-footer">
                                    <el-button size="small" @click="init_closeAddDialog">取 消</el-button>
                                    <el-button size="small" type="primary" @click="init_confirm">确定</el-button>
                                </div>
                            </el-dialog>
                        </div>
                    </div>
                    <!--给列表配置-->
                    <div class="container" v-else>
                        <div style="height: 30px">
                            <el-radio-group v-model="item.goods_list.type">
                                <el-radio :label="index" v-for="(item,index) in typeList" :key="index">{{item}}
                                </el-radio>
                            </el-radio-group>
                        </div>
                        <h3>列表的商品</h3>
                        <div v-if="item.goods_list.list.length>0" style="border: 1px solid #3c763d;margin-bottom: 10px">
                            <el-table :data="item.goods_list.list" style="width: 100%;margin-top: 5px" height="300"
                                      ref="multipleTable">
                                <el-table-column fixed label="图片" width="80">
                                    <template slot-scope="scope">
                                        <img :src="scope.row.imageUrl" :alt="scope.row.productName"
                                             style="width:100%;max-height:50px;">
                                    </template>
                                </el-table-column>
                                <el-table-column prop="productName" label="药名">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.productName">{{scope.row.productName}}</span>
                                        <span v-else>{{scope.row.showName}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="规格" width="80">
                                    <template slot-scope="scope">
                                        {{scope.row.mediumPackageTitle}}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="fob" label="价格" width="80">
                                    <template slot-scope="scope">
                                        {{scope.row.fob}}
                                    </template>
                                </el-table-column>
                                <el-table-column fixed="right" label="操作" width="80">
                                    <template slot-scope="scope">
                                        <div class="edit-button">
                                            <el-button @click="handleDelete(scope.row)" type="warning" size="mini">删除
                                            </el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <div>
                            <el-table :data="item.goods_list.goods_group" style="width: 100%;margin-top: 5px"
                                      height="150"
                                      ref="multipleTable">
                                <el-table-column label="商品组名称">
                                    <template slot-scope="scope">
                                        {{scope.row.name}}
                                    </template>
                                </el-table-column>


                                <el-table-column label="操作">
                                    <template slot-scope="scope">
                                        <el-button @click="group_delete(scope.row)" type="warning" size="mini">
                                            删除{{scope.row.name}}
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <div class="container">
            <!--选择商品-->
            <all-link ref="all_link" @select="onSetLink" :tabs="tabs" :params="{
                    productlink: {
                        seledShow: false,
                        minSel: 1,
                        search: {
                            status: 1,
                            branchCode: topic.branchCode
                        }
                    },
                    importGoods: {
                        minSel: 1,
                        search: {
                            status: 1,
                            branchCode: topic.branchCode
                        }
                    },
                    goodsGroup: {
                        seledShow: false,
                        minSel: 1,
                        search: {
                          state: 1,
                            branchCode: topic.branchCode
                        },
                        data: {
                            ids: undefined
                        }
                    }
                }"></all-link>
        </div>
        <!--选择商品图片-->
        <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addDialog">
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="onUploadImage">
                <img v-if="content.list.length
                &&content.list[currentTabIndex].banner_list[cur_index
                ]&&content.list[currentTabIndex].banner_list[cur_index].init_img_url"
                     :src="content.list[currentTabIndex].banner_list[cur_index].init_img_url" class="image">
                <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeAddDialog">取 消</el-button>
                <el-button size="small" type="primary" @click="confirm">确定</el-button>
            </div>
        </el-dialog>
        <!--添加banner的背景图片      -->
        <el-dialog class="banner-dialog" title="添加图片" :visible.sync="banner_bg_dialog">
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="upload_bg_img">
                <img v-if="content.list.length
                &&content.list[currentTabIndex].bg_img"
                     :src="content.list[currentTabIndex].bg_img" class="image">
                <i v-loading="loading" v-else  class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="banner_bg_dialog=false">取 消</el-button>
                <el-button size="small" type="primary" @click="banner_bg_dialog=false">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import base from "../base";
    import {common} from 'api'

    export default {
        extends: base,
        contentDefault: {
            list: [
                {
                    name: "精品推荐",
                    bg_color: "#FC4340",
                    bg_img:"",
                    banner_list: [],
                    is_init_banner: 0,
                    init_banner_list: [],
                    goods_list: {
                        list: [],
                        goods_group: [],
                        type: 0
                    }
                },
                {
                    name: "优惠<10%",
                    bg_color: "#FC4340",
                    bg_img:"",
                    banner_list: [],
                    is_init_banner: 0,
                    init_banner_list: [],
                    goods_list: {
                        list: [],
                        goods_group: [],
                        type: 0
                    }
                },
                {
                    name: "优惠10%-20%",
                    bg_color: "#FC4340",
                    bg_img:"",
                    banner_list: [],
                    is_init_banner: 0,
                    init_banner_list: [],
                    goods_list: {
                        list: [],
                        goods_group: [],
                        type: 0
                    }
                },
                {
                    name: "优惠>20%",
                    bg_color: "#FC4340",
                    bg_img:"",
                    banner_list: [],
                    is_init_banner: 0,
                    init_banner_list: [],
                    goods_list: {
                        list: [],
                        goods_group: [],
                        type: 0
                    }
                },
            ],
            tab_color: "#ffffff",
            tab_line_color: "#ffffff",
            tab_bg_color: '#fc4340',
            active_name: ""
        },
        data() {
            return {
                tabName: "",
                activeName: "",
                type: "banner",
                addDialog: false,
                init_addDialog: false,
                banner_bg_dialog:false,
                loading: false,
                dataForm: {
                    first_explain: "",
                    second_explain: "",
                    goods_introduce: "",
                    goods_title: "",
                    image: '',
                    link: {
                        meta: {
                            page_url: ''
                        }
                    },
                    timevalue: '',
                },
                tabs: [
                    {label: '商品', value: 'productlink'},
                    {label: '导入商品', value: 'importGoods'},
                    {label: '商品组', value: 'goodsGroup'}
                ],
                typeList: ['列表模式', '大图模式'],
                banner_type: ["商品模式", "自定义模式"],
                cur_index: 0,
                currentTabIndex: 0,
            }
        },
        computed: {
            list() {
                let list = _.get(this, 'content.list');
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
        },
        methods: {
            clear_self_bg_img(){
                this.content.list[this.currentTabIndex].bg_img=""
            },
            init_closeAddDialog() {
                this.init_addDialog = false;
            },
            closeAddDialog() {
                this.addDialog = false;
            },
            init_toRemove(data) {
                const index = this.list[this.currentTabIndex].init_banner_list.indexOf(data)
                this.list[this.currentTabIndex].init_banner_list.splice(index, 1)
            },
            init_toEdit(data, index) {
                this.currentData = data;
                this.currentIndex = index;
                this.dataForm = Object.assign({}, data);
                this.isEdit = true;
                this.init_addDialog = true;
            },
            init_toAdd() {
                this.isEdit = false;
                this.dataForm = {
                    image: '',
                    link: {
                        meta: {
                            page_url: ''
                        }
                    },
                };
                this.init_addDialog = true;
            },
            init_onSetLink(link) {
                this.dataForm.link = link;
            },
            async init_onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.dataForm.image = res.data.url
            },
            async upload_bg_img(res){
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    });
                    return;
                }
                this.$set(this.content.list[this.currentTabIndex], "bg_img", res.data.url)
                this.$set(this.content.list[this.currentTabIndex], "bg_color", "")
            },
            init_confirm() {
                if (!this.dataForm.image) {
                    this.$message.warning('请上传图片');
                    return false;
                }
                this.init_closeAddDialog();
                if (this.isEdit) {
                    this.currentData = Object.assign(this.currentData, this.dataForm);
                    this.content.list[this.currentTabIndex].init_banner_list.splice(this.currentIndex, 1, this.currentData);
                } else {
                    this.content.list[this.currentTabIndex].init_banner_list.push(Object.assign({}, this.dataForm));
                }
            },
            toAdd(row) {
                const index = this.content.list[this.currentTabIndex].banner_list.indexOf(row);
                this.cur_index = index;
                this.addDialog = true;
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: "warning"
                    });
                    return;
                }
                this.$set(this.content.list[this.currentTabIndex].banner_list[this.cur_index], "init_img_url", res.data.url)
            },
            onSetLink(link) {
                function handle_arr(arr=[]){
                    return arr.map((item)=>{
                        let obj={};
                        obj.init_img_url=item.init_img_url;
                        obj.imageUrl=item.imageUrl;
                        obj.productName=item.productName;
                        obj.showName=item.showName;
                        obj.mediumPackageTitle=item.mediumPackageTitle;
                        obj.fob=item.fob;
                        obj.id=item.id;
                        return obj
                    });
                }

                if (this.type === "list") {
                    if (link.tag === "goods" || link.tag === "importGoods") {
                        let _self_arr= handle_arr(link.data);
                        if (this.content.list[this.currentTabIndex].goods_list.list.length > 0) {
                            this.content.list[this.currentTabIndex].goods_list.list =
                                [...common.removeRepeat(this.content.list[this.currentTabIndex].goods_list.list, _self_arr)]
                        } else {
                            this.content.list[this.currentTabIndex].goods_list.list = [..._self_arr]
                        }
                    } else if (link.tag === "goodsGroup") {
                        let obj = {};
                        obj.name = link.data.name;
                        obj.ids = link.data.goods;
                        obj.code = link.data.code;
                        this.content.list[this.currentTabIndex].goods_list.goods_group.splice(0, 1, obj);
                    }
                }
                else {
                    if (link.tag === "goods" || link.tag === "importGoods") {
                        let _self_arr= handle_arr(link.data);
                        if (this.content.list[this.currentTabIndex].banner_list.length > 0) {

                            this.content.list[this.currentTabIndex].banner_list =
                                [...common.removeRepeat(this.content.list[this.currentTabIndex].banner_list, _self_arr)]
                        } else {
                            this.content.list[this.currentTabIndex].banner_list = [..._self_arr]
                        }
                    } else if (link.tag === "goodsGroup") {
                        let _self_arr= handle_arr(link.data._goods);
                        this.content.list[this.currentTabIndex].banner_list = [..._self_arr]
                    }
                }
            },
            confirm() {
                this.closeAddDialog();
            },
            handleDelete(row) {
                if (this.type === "banner") {
                    const index = this.list[this.currentTabIndex].banner_list.indexOf(row)
                    this.list[this.currentTabIndex].banner_list.splice(index, 1)
                } else {
                    const index = this.list[this.currentTabIndex].goods_list.list.indexOf(row)
                    this.list[this.currentTabIndex].goods_list.list.splice(index, 1)
                }
            },
            group_delete(row) {
                if (this.type === "list") {
                    const index = this.list[this.currentTabIndex].goods_list.goods_group.indexOf(row);
                    this.list[this.currentTabIndex].goods_list.goods_group.splice(index, 1)
                }
            },
            add_tab() {
                if (!this.tabName) {
                    this.$message('请输入选项卡名称')
                    return
                }
                if (this.content.list.length > 0) {
                    const nameIndex = common.getRepeatResult('name', this.tabName, this.content.list);
                    if (nameIndex >= 0) {
                        this.$message.warning('您所添加的选项卡名称已经存在啦,请重新添加')
                        return
                    }
                }
                this.content.list.push(
                    {
                        name: this.tabName,
                        bg_color: "#FC4340",
                        bg_img:"",
                        banner_list: [],
                        is_init_banner: 0,
                        init_banner_list: [],
                        goods_list: {
                            list: [],
                            goods_group: [],
                            type: 0
                        }
                    }
                )
            },
            tab_click() {
                this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.list);
                //太暴力了
                this.$refs.all_link.$children[0].$children[1].$children[0].closeAddGoods()
            },
            remove_tab(targetName) {
                const index = common.getRepeatResult('name', targetName, this.content.list);
                this.content.list.splice(index, 1)
            },
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return data.meta.page_url;
            }
        },
        watch: {
            type(){
                //太暴力了
                this.$refs.all_link.$children[0].$children[1].$children[0].closeAddGoods()
            },
            activeName(new_val, old_val) {
                if (new_val) {
                    this.content.active_name = new_val
                }
            }
        }
    }
</script>
<style scoped lang="scss">
    .init_topic-banner {
        .banner-dialog {
            .el-dialog__body {
                padding-top: 10px;
            }
        }

        .init_container {
            display: flex;
            align-items: center;

            .img {
                width: 65%;

                img {
                    display: block;
                    width: 100%;
                }
            }

            .button-list {
                margin-left: 10px;
            }
        }

        .link-desc {
        }

        .topic-image-upload {
            .image {
                display: block;
                width: 100%;
            }

            .uploader-icon {
                width: 200px;
                height: 200px;
                line-height: 200px;
                border: 1px solid #8b8b8b;
                font-size: 50px;
            }
        }

        .topic-image-picker {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }

    .container {
        margin: 10px auto;
        padding-bottom: 10px;
    }

    .topic-image-upload {
        .image {
            display: block;
            width: 100%;
        }

        .uploader-icon {
            width: 200px;
            height: 200px;
            line-height: 200px;
            border: 1px solid #8b8b8b;
            font-size: 50px;
        }
    }


</style>
