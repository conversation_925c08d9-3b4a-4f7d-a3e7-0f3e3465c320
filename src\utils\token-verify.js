import api from 'api'

const INTERVAL = 5 * 60 * 1000

function _verify () {
  return setInterval(async () => {
    try {
      await api.user.current();
    } catch (error) {
      console.log(error)
    }
  }, INTERVAL)
}

class TokenVerify {
  constructor () {
    this.descriptor = 0
  }
  start () {
    this.descriptor = _verify()
  }
  cancel () {
    clearInterval(this.descriptor)
  }
}

export default TokenVerify
