<template>
  <div class="categoryFloorBox">
    <el-row :gutter="20">
      <div class="title">有效时间设置</div>
      <el-col :span="24">
        <el-date-picker
            v-model="content.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right">
        </el-date-picker>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">上传背景图</span>
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :on-success="onUploadImg">
              <el-button class="btn-block" type="primary" size="mini">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">标题背景色</span>
          <div>
            <el-color-picker
              v-model="content.titleBgRes"
              size="mini"
              @change="change_bgRes"
            />
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">商品栏背景色</span>
          <div>
            <el-color-picker v-model="content.productsBgRes" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">清除背景</span>
          <div>
            <el-button class="btn-block" size="mini" @click="clearBg">清除背景</el-button>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">模块标题设置</div>
      <el-col :span="24">
        <div class="block">
          <div>
            <el-input placeholder="请输入内容" v-model="content.moduleTitle" class="inputItem">
              <template slot="prepend">模块title</template>
            </el-input>
          </div>
        </div>
      </el-col>
      <el-col :span="24" style="margin: 5px 0">
        <div class="block">
          <div>
            <el-input placeholder="请输入内容" v-model="content.moduleSlogan" class="inputItem">
              <template slot="prepend">模块标语</template>
            </el-input>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <span>链接类型：</span>
        <el-select v-model="content.linkType" placeholder="请选择">
          <el-option
            v-for="item in linkOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
      <el-col :span="24">
        <!-- 跳转链接-->
        <el-input placeholder="链接地址" v-model.trim="content.page_url" v-if="content.linkType !== 'dynamic'" style="margin: 10px 0">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </el-col>
      <el-col :span="24">
        <div v-if="content.linkType === 'dynamic'">
          <div class="topic-image-picker">
            <el-input style="width: 300px; margin: 10px 0" placeholder="输入跳转id" v-model="content.dynamicId">
              <template slot="prepend">跳转id</template>
            </el-input>
            <el-button type="primary" @click="putDynamicLink()">生成链接</el-button>
          </div>
          <el-input placeholder="链接地址" v-model.trim="content.page_url">
            <template slot="prepend">跳转链接</template>
          </el-input>
        </div>
        <div v-if="content.linkType==='topic'">
          <page-link @select="onSetLink" :params="{branchCode: content.branchCode}"></page-link>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">商品设置</div>
      <el-col :span="24" style="marginBottom: 10px">
        <span>商品数设置</span>
        <el-select v-model="content.productsTotal" placeholder="请选择">
          <el-option
            v-for="item in pageCount_option"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-col>
    </el-row>
    <br>
    <all-link
      @select="onSetGroup"
      :tabs="tabs"
      :params="{
        goodsGroup: {
          seledShow: false,
          minSel: 1,
          search: {
            state: 1,
            branchCode: topic.branchCode
          }
        }
      }"
    ></all-link>
  </div>
</template>
<script>
  import base from "../../../base";
  import api from "api";
  import { getUrlParam } from "config";
  export default {
    extends: base,
    contentDefault: {
      list: [],
      productsBgRes: "#fff",
      titleBgRes: "#fff",
      moduleTitle: "",
      moduleSlogan: "",
      branchCode:null,
      exhibitionId:null,
      productsTotal: '',
      page_url: '',
    },
    created() {
      this.content.branchCode=this.content.branchCode?this.content.branchCode:null;
      this.content.exhibitionId=this.content.exhibitionId?this.content.exhibitionId:null;
      this.content.productsTotal=this.content.productsTotal?this.content.productsTotal:""
      this.debounce = _.debounce(this.changeLink, 1000);
    },
    data() {
      return {
        loading: false,
        tabs: [{ label: '商品组', value: 'goodsGroup' }],
        linkOptions: [{
          value: "topic",
          label: "专题页链接"
        }, {
          value: "stores",
          label: "店铺页链接"
        }, {
          value: "dynamic",
          label: "动态商品链接"
        }],
        pageCount_option:[{
          value: '3',
          label: '3个'
        }, {
          value: '4',
          label: '4个'
        }, {
          value: '5',
          label: '5个'
        }, {
          value: '6',
          label: '6个'
        }, {
          value: '7',
          label: '7个'
        }, {
          value: '8',
          label: '8个'
        }, {
          value: '9',
          label: '9个'
        }],
        pickerOptions: {
          shortcuts: [{
            text: '未来一周',
            onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                picker.$emit('pick', [start, end]);
            }
          }, {
            text: '未来一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '未来三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '未来六个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '未来一年',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit('pick', [start, end]);
            }
          }]
				},
      }
    },
    computed: {
      list() {
        let list = _.get(this, 'content.list')
        if (list) {
          return list
        } else {
          return [];
        }
      }
    },
    methods: {
      change_bgRes(val) {
        this.content.titleBgRes = val;
      },
      async onUploadImg(res, file) {
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          });
          return;
        }
        this.content.titleBgRes = res.data.url;
      },
      clearBg() {
        this.content.titleBgRes = '';
      },
      onSetLink(link) {
        this.$set(this.content, 'page_url', link.meta.page_url)
      },
      onSetGroup(link) {
        this.content.branchCode=link.data.branchCode;
        this.content.exhibitionId=link.data.code;
        this.content.list=[];
      },
      putDynamicLink() {
        if (!this.content.dynamicId) {
          this.$message({
            message: "请输入跳转id再点击生成链接",
            type: "warning"
          });
          return false;
        };
        this.$set(this.content, 'page_url', `ybmpage://homeSteadyChannel?strategyId=${this.content.dynamicId}&title=${this.content.moduleTitle}`)
      },
      async changeLink() {
        if (this.content.page_url) {
          if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.content.page_url)) {
            this.$message.error('跳转链接格式不正确');
            this.content.page_url = '';
          } else {
            let linkPageUrl = getUrlParam(this.content.page_url, 'url');
            const result = await api.topic.checkPageUrl({ url: linkPageUrl });
            if (((result || {}).data || {}).status != 200) {
              this.$message.error('跳转链接不存在');
              this.content.page_url = '';
            }
          }
        }
      }
    },
    //监听input输入值变化
    watch:{
      'content.page_url': {
        handler(val, oldVal) {
          if (val && this.content.linkType === 'topic') {
            this.debounce();
          }
        }
      },
      'content.linkType': {
        handler(val, oldVal) {
          if (val === 'topic' && this.content.page_url) {
            this.debounce();
          }
        }
      }
    }
  }
</script>

<style scoped lang="scss">
  .categoryFloorBox {
    text-align: center;
    img {
      width: 100%;
    }
    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
  }
</style>
