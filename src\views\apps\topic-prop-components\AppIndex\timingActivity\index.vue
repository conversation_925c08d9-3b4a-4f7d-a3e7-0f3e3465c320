<template>
  <div class="topic-image">
    <el-row :gutter="20">
      <div class="title">活动类型</div>
      <el-col :span="24">
        <el-select v-model="content.activityType" placeholder="请选择" disabled>
          <el-option
            label="秒杀活动"
            :value="1"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">模块背景</div>
      <el-col :span="8">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :on-success="onUploadBgImg"
            >
              <el-button size="small" class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <el-button size="small" @click="clearBg">清除背景图</el-button>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">背景色</span>
          <div>
            <el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="8">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :on-success="uploadMainTitleImg"
            >
              <el-button size="small" class="btn-block" type="primary" :loading="loading">上传主标题</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :on-success="uploadSubtitleImg"
            >
              <el-button size="small" class="btn-block" type="primary" :loading="loading">上传副标题</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <el-button size="small" @click="clearSubtitle">清除副标题</el-button>
      </el-col>
      <el-col :span="24" class="headline">
        <el-input class="entryNameBox" placeholder="请输入内容" v-model="content.entryName" :maxlength="4">
          <template slot="prepend">入口名称</template>
        </el-input>
      </el-col>
      <el-col :span="24" class="headline">
        <el-input placeholder="请输入内容" v-model.trim="content.jumpLink">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">商品配置</div>
      <el-col :span="24" style="marginBottom: 20px">
        选品方式：
        <el-select size="small" @change="changeProductsType" v-model="content.selectProductType" placeholder="请选择">
          <el-option label="指定商品" value="appointProduct" />
          <el-option label="指定商品组" value="appointProductGroup" />
          <el-option label="系统自动" value="systemAuto" />
        </el-select>
      </el-col>
      <el-col :span="24" v-if="content.selectProductType === 'appointProductGroup'">
        商品组ID：
        <el-input style="width: 200px" size="small" placeholder="请输入内容" v-model="productGroupId" />
      </el-col>
      <el-table
        v-if="content.selectProductType === 'appointProduct'"
        :data="productsArr"
        size="mini"
      >
        <el-table-column label="显示序号" type="index" width="100" />
        <el-table-column label="指定商品ID">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row"
              size="mini"
              placeholder="请输入指定商品ID"
              clearable
              style="width: 200px"
              @input="changeInput($event, scope.$index)"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <el-row class="btnBox" :gutter="20">
      <el-button v-if="content.selectProductType !== 'systemAuto'" type="primary" size="mini" @click="checkBindCsuOrProductGroup">确认</el-button>
    </el-row>
  </div>
</template>
<script>
  import base from "../../base";
  import api from "api";
  import { getUrlParam } from "config";
  export default {
    name: 'timingActivity',
    extends: base,
    contentDefault: {
      list: [],
      bgRes: "",
      selectProducts: [],
      bgUrl: '//upload.ybm100.com/ybm/app/layout/cmsimages/2021-10/46893b5ea8af28595fda37f58794673f.png',
      mainTitleUrl: '//upload.ybm100.com/ybm/app/layout/cmsimages/2022-1/4a4087cfa8aefbcf9d2243dccd6d455d.png',
      subtitleUrl: '',
      entryName: '即将开抢',
      jumpLink: 'ybmpage://commonh5activity?cache=0&url=https://app.ybm100.com/public/2022/1/18426.html',
      activityType: 1,
    },
    data() {
      return {
        loading: false,
        productsArr: [],
        productGroupId: '',
      }
    },
    computed: {},
    created() {
      this.initData();
      this.debounce = _.debounce(this.changeLink, 1000);
    },
    methods: {
      initData() {
        // this.productsArr = _.get(this, "content.selectProducts");
        this.productsArr = JSON.parse(JSON.stringify(this.content.selectProducts || []));
        this.productGroupId = this.content.selectProductGroupId;
      },
      changeInput(val, index) {
        this.$set(this.productsArr, index, Number(val.trim()));
      },
      changeProductsType() {
        this.productsArr = Array.from({length: 6}, v => '');
        this.content.selectProductGroupId = '';
        this.productGroupId = '';
      },
      clearBg() {
        // this.content.bgUrl = '';
        this.content.bgUrl = '//upload.ybm100.com/ybm/app/layout/cmsimages/2021-10/46893b5ea8af28595fda37f58794673f.png';
        this.content.bgColor = '#FFFFFF';
      },
      clearSubtitle() {
        this.content.subtitleUrl = '';
      },
      async onUploadBgImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.$set(this.content, 'bgUrl', res.data.url)
        // this.content.bgUrl = res.data.url;
      },
      async uploadMainTitleImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.$set(this.content, 'mainTitleUrl', res.data.url)
        // this.content.mainTitleUrl = res.data.url;
      },
      async uploadSubtitleImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.$set(this.content, 'subtitleUrl', res.data.url)
      },
      async checkBindCsuOrProductGroup() {
        let canSave = true;
        this.productsArr.forEach((item) => {
          if (isNaN(item) || item < 0) {
            canSave = false;
          }
        });
        if (!canSave) {
          this.$message.error('指定商品ID只能输入数字');
          return;
        }
        const params = {
          type: this.content.selectProductType === 'appointProduct' ? 1: 2,
          exhibitionId: this.productGroupId,
          csuIds: this.productsArr.filter(i => i).map(Number),
        }
        const result = await api.topic.checkBindCsuOrProductGroup(params);
        if ((result.data.data || {}).checkResult) {
          if (this.content.selectProductType === 'appointProduct' ) {
            this.$set(this.content, 'selectProducts', this.productsArr)
          } else {
            this.content.selectProductGroupId = this.productGroupId;
          }
          this.$message.success('绑定成功')
        } else {
          this.content.selectProductGroupId = '';
          if (this.content.selectProductType === 'appointProduct' ) {
            this.$message.error(`以下商品id绑定失败：${((result.data.data || {}).failureCsuIds || []).join()}`)
          } else {
            this.$message.error(result.data.msg)
          }
        }
      },
      async changeLink() {
        if (this.content.jumpLink) {
          if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.content.jumpLink)) {
            this.$message.error('跳转链接格式不正确');
            this.content.jumpLink = '';
          } else {
            let linkPageUrl = getUrlParam(this.content.jumpLink, 'url');
            const result = await api.topic.checkPageUrl({ url: linkPageUrl });
            if (((result || {}).data || {}).status != 200) {
              this.$message.error('跳转链接不存在');
              this.content.jumpLink = '';
            }
          }
        }
      }
    },

    //监听input输入值变化
    watch:{
      'content.jumpLink': {
        handler(val, oldVal) {
          if (val) {
            this.debounce();
          }
        }
      }
    }

  }
</script>

<style lang="scss" scoped>
  .el-row {
    text-align: center;
    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
    .entryNameBox {
      margin: 10px 0;
    }
  }
  .confirmIcon {
    color: #fff;
  }
  .productsDataBox {
    margin: 10px 0;
  }
  .btnBox {
    text-align: right;
    margin-right: 30px !important;
    margin-top: 20px;
  }
</style>