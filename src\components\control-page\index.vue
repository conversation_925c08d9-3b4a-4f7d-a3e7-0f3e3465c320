<template>
  <div class="page-link">
    <el-input size="mini" class="mb-10" v-model="key" @keyup.enter.native="getList()" placeholder="关键字" clearable>
      <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
    </el-input>

    <el-table size="mini" :data="list" highlight-current-row @current-change="onSelect" style="margin-bottom:5px"
              v-loading="loading">
      <el-table-column label="模板名称" width="100">
        <template slot-scope="scope">
          <p>{{scope.row.page_name}}</p>
        </template>
      </el-table-column>
      <el-table-column label="用户可见名称" width="100">
        <template slot-scope="scope">
          <p>{{scope.row.page_title?scope.row.page_title:scope.row.page_name}}</p>

        </template>
      </el-table-column>
      <el-table-column label="路径" prop="item_name">
        <template slot-scope="scope">
          <span>{{ (params || {}).category === 'pc' ? '' : 'ybmpage://commonh5activity?cache=0&url=' }}{{scope.row.url}}</span>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      small
      layout="pager"
      :current-page="pagination.current"
      :page-size="pagination.size"
      :total="pagination.total"
      @current-change="getList">
    </el-pagination>
  </div>
</template>

<script>
  import api from 'api';

  export default {
    props: {
      params: Object
    },
    data() {
      return {
        id: '',
        key: '',
        sku_key: '',
        list: [],
        pagination: {
          size: 5,
          current: 1,
          total: 0
        },
        loading: false,
        manualId: ''
      }
    },
    methods: {
      async getList(page = 1) {
        this.pagination.current = page;
        this.pagination.size = 5;
        this.loading = true;
        const params = {
          pageFrom: this.pagination.current,
          pageSize: this.pagination.size,
          state: 1,
          branchCode: this.params.branchCode,
          page_type: this.params.page_type || "controlMall",
          category: this.params.category || 'app',
        };
        const searchParam = {
          page_name: this.key || '',
        };
        let pms = Object.assign(params, searchParam);
        const result = await api.topic.list(pms);
        this.loading = false;
        if (result.code === 200) {
          this.$nextTick(() => {
            this.list = result.data.rows;
            this.pagination.total = result.data.total;
          })
        } else {
          this.$message.error(result.msg);
        }
      },
      onSelect(row) {
        if (!row)
          return;
        this.$emit('select', {
          type: 'item',
          label: '活动页',
          id: row.productSid,
          desc: row.productName,
          meta: {
            'id': row.id,
            'page_url': `${(this.params || {}).category == 'pc' ? '' : 'ybmpage://commonh5activity?cache=0&url='}${row.url}`,
            'page_name': row.page_title?row.page_title:row.page_name
          }
        })
      }
    },
    mounted() {
      this.getList();
    },
    filters: {
      tradeType(value) {
        return {
          1: '国内贸易',
          2: '跨境贸易',
        }[value] || ''
      }
    }
  }
</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .page-link {
    border: 1px solid #0cdcdc;
    padding: 3px;
  }
</style>
