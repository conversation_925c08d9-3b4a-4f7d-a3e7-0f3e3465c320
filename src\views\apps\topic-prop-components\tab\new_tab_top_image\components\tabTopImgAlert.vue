<template>
  <div>
    <!-- 新增 -->
    <el-dialog
      class="tab-top-dialog"
      :title="`${isEdit ? '编辑' : '新建'}`"
      :before-close="addDialogCancel"
      :visible.sync="addDialog"
    >
      <el-form
        label-position="right"
        ref="addRuleForm"
        :rules="addRuleForm"
        :model="addForm"
        size="small"
        label-width="100px"
        label-suffix="："
      >
        <el-form-item label="活动名称" prop="activityName">
          <el-input
            v-model="addForm.activityName"
            size="mini"
            placeholder="请输入活动名称"
            clearable
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="展示类型" prop="classType">
          <el-select
            style="margin-left: 10px"
            v-model.trim="addForm.classType"
            placeholder="请选择展示类型"
            clearable
          >
            <el-option label="一行一个" :value="1"> </el-option>
            <el-option label="一行两个" :value="2"> </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="展示时间">
          <el-radio v-model="addForm.timeType" :label="1">固定时段</el-radio>
          <el-date-picker
            v-model="addForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :picker-options="{
              disabledDate: (time) => {
                const times =
                  new Date(new Date().toLocaleDateString()).getTime() +
                  1095 * 8.64e7 -
                  1;
                return (
                  time.getTime() < Date.now() - 8.64e7 || time.getTime() > times
                ); // 如果没有后面的-8.64e7就是不可以选择今天的
              },
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker
          ><br />
          <el-radio v-model="addForm.timeType" :label="2">周期循环</el-radio>
          <el-button
            style="margintop: 10px"
            @click="toloopcirculateTime"
            type="primary"
            size="mini"
            >配置</el-button
          >
          <br>
          <div v-for="(item,index) in addForm.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[addForm.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{addForm.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
        </el-form-item>
        <el-form-item label="人群范围" prop="crowdType">
          <el-radio-group
            v-model="addForm.crowdType"
            @change="changeCrowdType"
            style="width: 100%"
          >
            <el-radio :label="1" style="width: 100%"
              >该页面已选中人群</el-radio
            >
            <el-radio
              :label="2"
              style="width: 100%; margin-top: 10px"
              class="cowdtype-radio"
            >
              <div class="cowdtype-radio">
                <div>指定人群</div>
                <el-select
                  v-if="addForm.crowdType === 2"
                  style="margin-left: 10px"
                  v-model.trim="addForm.crowdValue"
                  :loading="selectLoading"
                  filterable
                  :filter-method="optionFilter"
                  placeholder="请输入人群id"
                  clearable
                  @clear="options = []"
                  @change="selectCrowd"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="图片配置" prop="imgUrl">
          <div class="background-color-img-config">
            <el-upload
              class="topic-image-upload"
              :show-file-list="false"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :on-preview="handlePreview"
              :on-success="uploadSucces"
            >
              <el-button size="small" type="primary">图片点击</el-button>
            </el-upload>
            <el-button
              type="text"
              @click="clearBgColors()"
              style="margin-left: 20px"
              >清空重置</el-button
            >
            <img
              :src="addForm.imgUrl"
              alt=""
              style="width: 100px; height: 40px; margin-left: 5px"
            />
          </div>
        </el-form-item>

        <el-form-item label="转跳链接" prop="hrefUrl">
          <el-input v-model="addForm.hrefUrl" size="mini" clearable></el-input>
          <el-button
            type="primary"
            size="small"
            @click="isShowHrefDialog = true"
            >more</el-button
          >
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addDialogConfirm"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="跳转链接配置"
      :visible.sync="isShowHrefDialog"
      width="50%"
      append-to-body
    >
      <page-link
        @select="onSetLink"
        :params="{ branchCode: topic.branchCode }"
      ></page-link>

      <span slot="footer" class="dialog-footer">
        <el-button @click="hrefCancel">取 消</el-button>
        <el-button type="primary" @click="hrefConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <loopcirculateTime
      ref="loopcirculateTime"
      @loopcirculateTimeBack="loopcirculateTimeBack"
    ></loopcirculateTime>
  </div>
</template>


<script>
import loopcirculateTime from "../../../../components/loopcirculateTime.vue";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
export default {
  components: { swiperPoint, loopcirculateTime },
  props: {
    value: Object,
    topic: Object,
    categoryList: Array,
  },
  data() {
    return {
      isEdit: false,
      addDialog: false,
      options: [],
      selectLoading: false,
      isShowHrefDialog: false,
      addForm: {
        activityName: "",
        timeType: 1,
        validityTime: [],
        circulateTime: [],
        crowdType: 1,
        crowdId: "",
        crowdValue: "",
        hrefUrl: "",
        classType: 1,
        imgUrl: "",
        status: "",

        // bg_color: "",
      },
      imgShowList: [
        {
          bannerLocation: 1,
          linkUrl: "",
          image: "",
        },
      ],
      currentRow: undefined,
      addFormSelectLink: "",
      bannerLocationList: [
        {
          id: 1,
          name: "第一帧",
        },
        {
          id: 2,
          name: "第二帧",
        },
        {
          id: 3,
          name: "第三帧",
        },
        {
          id: 4,
          name: "第四帧",
        },
        {
          id: 5,
          name: "第五帧",
        },
        {
          id: 6,
          name: "第六帧",
        },
        {
          id: 7,
          name: "第七帧",
        },
        {
          id: 8,
          name: "第八帧",
        },
      ],
      addRuleForm: {
        activityName: [
          { required: true, message: "请填写活动名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" },
        ],
        crowdType: [
          { required: true, message: "请选择指定人群", trigger: "change" },
        ],
        crowdValue: [
          { required: true, message: "请填写人群名称", trigger: "blur" },
        ],
        hrefUrl: [
          { required: true, message: "请填写转跳链接", trigger: "blur" },
        ],
        // validityTime: [
        //   { required: true, message: "展示时间不能为空", trigger: "change" },
        // ],
        // circulateTime: [
        //   { required: true, message: "展示时间不能为空", trigger: "change" },
        // ],
        imgUrl: [
          { required: true, message: "图片不能为空", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    open(val, isEdit) {
      this.isEdit = isEdit;
      if (this.isEdit) {
        let keys = Object.keys(val);
        for (let index = 0; index < keys.length; index++) {
          const key = keys[index];
          this.addForm[key] = val[key];
        }
      }
      this.addDialog = true;
    },

    openLink(row) {
      this.currentRow = row;
      this.isShowHrefDialog = true;
    },

    selectCrowd(e) {
      if (e) {
        this.addForm.crowdId = Number(this.options[0].value.trim());
        this.addForm.crowdValue = this.options[0].label;
      } else {
        this.addForm.crowdId = "";
        this.addForm.crowdValue = "";
      }
      this.$forceUpdate();
    },

    onSetLink(link) {
      this.addFormSelectLink = link.meta.page_url;
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    //打开时间循环
    toloopcirculateTime() {
      
          this.$refs.loopcirculateTime.circulateTime=this.addForm.circulateTime
          this.$refs.loopcirculateTime.editInit()
   
      this.$refs.loopcirculateTime.showVisible = true;
    },
    changeCrowdType() {
      this.addForm.crowdId = "";
      this.addForm.crowdValue = "";
    },
    //循环时间回调
    loopcirculateTimeBack(data) {
      this.addForm.circulateTime = data;
    },
    addDialogCancel() {
      this.resetAddForm();
      this.$refs['addRuleForm'].clearValidate()
      this.addDialog = false;
    },

    hrefCancel() {
      this.addFormSelectLink = "";
      this.isShowHrefDialog = false;
    },
    hrefConfirm() {
      this.addForm.hrefUrl = this.addFormSelectLink;
      this.isShowHrefDialog = false;
    },
    resetAddForm() {
      this.addForm = {
        activityName: "",
        timeType: 1,
        validityTime: [],
        circulateTime: [],
        crowdType: 1,
        crowdId: "",
        crowdValue: "",
        hrefUrl: "",
        classType: 1,
        imgUrl: "",
        status: "",
      };
    },
    handlePreview(val) {
      console.log(val);
    },
    uploadSucces(res) {
      if (res.code != 200) {
        this.$message.warning(`[${res.code}]${res.msg}`);
        return;
      }
      this.addForm.imgUrl = res.data.url;
    },
    clearBgColors() {
      this.addForm.imgUrl = "";
    },
    delImageListRow(val) {
      console.log(val);
    },
    addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        if (this.addForm.timeType==2&&(!this.addForm.circulateTime||Object.keys(this.addForm.circulateTime).length==0||!this.addForm.circulateTime.circulateList||this.addForm.circulateTime.circulateList.length==0)) {
          this.$message.warning("请添加[周期循环] 时间段。");
          return false;
        }
        if(this.addForm.timeType==1&&(!this.addForm.validityTime||this.addForm.validityTime.length==0)){
          this.$message.warning("请添加时段");
          return false;
        }
        if(this.addForm.crowdType==2&&!this.addForm.crowdId){
          this.$message.warning("请添人群ID");
          return false;
        }
         if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.addForm.hrefUrl)) {
          this.$message.error("跳转链接格式不正确，请检查");
          return false;
        }
        let linkErrMsg = '';
        
            let linkPageUrl = getUrlParam(this.addForm.hrefUrl, 'url')
             const loading = this.$loading({
                lock: true,
                text: '校验中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
            const result = await api.topic.checkPageUrl({ url: linkPageUrl });
             loading.close()
            if (((result || {}).data || {}).status != 200) {
              linkErrMsg = '跳转链接不存在，请检查';
            }
        
        if (linkErrMsg) {
          this.$message.error(linkErrMsg);
          return false;
        }

        this.$emit("done", this.addForm);
        
        // this.$message.success("添加成功！");
        // this.addDialogCancel();
      });
    },
  },
};
</script>

<style scoped>
.cowdtype-radio {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.background-color-img-config {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.tableBox {
  width: 100%;
}
.banner-url-type {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
</style>