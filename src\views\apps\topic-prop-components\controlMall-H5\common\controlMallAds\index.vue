<template>
  <div class="images-box">

    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块图片上传</div>
      <el-col :span="24">
        <div class="block">
          <div>
            <p style="color: red">点击热区预览图</p>
            <img width="100%" :src="`/static/images/${content.main_type}.jpg`" alt="">
          </div>
          <div>
            <img width="100%" :src="content.bgRes" alt="">
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadImg">
              <el-button class="btn-block" type="primary" :loading="loading"> 上传模板图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
    </el-row>

    <!--上传图片设置和编辑-->
    <el-row :gutter="20">
      <div class="title">上传图片设置和编辑</div>
      <el-col :span="24">

        <el-table :data="list" size="mini" style="width: 100%">
          <el-table-column
            type="index"
            width="50">
          </el-table-column>
          <el-table-column label="热区信息（热区宽度累加起来应该等于100）,1+n系列不支持配置宽度">
            <template slot-scope="scope">
              <div v-if="scope.row.link.meta.page_width">
                热区宽度：
                <span style="color: red">
                 {{scope.row.link.meta.page_width}}
                </span>
              </div>
              <!--<div>-->
                <!--是否分享：-->
                <!--{{scope.row.link.meta.is_share|is_no}}-->
              <!--</div>-->
              <div >
                埋点名称（用户可见名称）：
                {{!scope.row.link.meta.is_share?scope.row.link.meta.page_name:""}}
              </div>
              <div>
                链接：
                {{!scope.row.link.meta.is_share?scope.row.link.meta.page_url :""}}
              </div>
              <div>
                <el-button size="mini"
                           @click="toEdit(scope.row, scope.$index)"
                           type="primary">编辑
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <!--上传图片弹框-->
    <el-dialog class="banner-dialog" title="设置信息" :visible.sync="addDialog">
     <div class="level" v-if="dataForm.link.meta.page_width">
       热区宽度（百分比）
       <el-input-number v-model="dataForm.link.meta.page_width" :max="100" label="描述文字"></el-input-number>
     </div>

      <!--<div class="level">-->
        <!--<el-radio v-model="is_share" :label="false">无分享功能</el-radio>-->
        <!--<el-radio v-model="is_share" :label="true">此图具有分享功能</el-radio>-->
      <!--</div>-->

      <div class="level" v-if="!is_share">
        <div class="topic-image-picker">
          <span>此字段用于：埋点统计的名称，默认赋予（用户可见名称） 可修改！</span>
          <el-input placeholder="页面名称" v-model="dataForm.link.meta.page_name">
            <template slot="prepend">用户可见名称</template>
          </el-input>
        </div>

        <div class="topic-image-picker">
          <el-input placeholder="链接地址" v-model.trim="dataForm.link.meta.page_url">
            <template slot="prepend">跳转链接</template>
          </el-input>
        </div>
        <control-page @select="onSetLink" :params="{page_type: topic.page_type === 'h5' ? 'h5' : '', branchCode: topic.branchCode}"></control-page>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeAddDialog">取 消</el-button>
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import base from "views/apps/topic-prop-components/base.vue";
  import api from "api";
  import { getUrlParam } from "config";
  export default {
    extends: base,
    data() {
      return {
        currentIndex:0,
        is_share:false,
        loading: false,
        addDialog: false,
        dataForm: {
          link: {
            meta: {
              page_url: '',
              page_name: "",
              is_share:false,
              page_width:""
            }
          },
        },
      }
    },
    computed: {
      list() {
        let list = _.get(this, 'content.list');
        if (list.length) {
          if(this.content.main_type==="twoone"){
            return [list[1],list[2],list[0]]
          }
          return list
        } else {
          return [];
        }
      },
    },
    filters: {
      is_no(val){
        if (val)return "是";
        return "否"
      }
    },
    methods: {
      closeAddDialog() {
        this.addDialog = false;
      },
      toEdit(data, index) {
        this.currentIndex = index;
        this.dataForm = Object.assign(this.dataForm,JSON.parse(JSON.stringify(data)));
        this.is_share = this.dataForm.link.meta.is_share ? this.dataForm.link.meta.is_share : false;
        this.addDialog = true;
      },
      onSetLink(link) {
        this.dataForm.link.meta.page_url = link.meta.page_url;
        this.dataForm.link.meta.page_name = link.meta.page_name;
      },
      async confirm() {
        if (this.dataForm.link.meta.page_url) {
          if (!this.dataForm.link.meta.page_name) {
            this.$message({
              message: '埋点名称不能为空',
              type: 'warning',
              time: 3000
            });
            return;
          }
        }
        if (this.dataForm.link.meta.page_name) {
          if (!this.dataForm.link.meta.page_url) {
            this.$message({
              message: '链接不能为空',
              type: 'warning',
              time: 3000
            });
            return;
          }
        }
        let linkErrMsg = '';
        if (this.dataForm.link.meta.page_url) {
          if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.dataForm.link.meta.page_url)) {
            linkErrMsg = '跳转链接格式不正确，请检查';
          } else {
            let linkPageUrl = getUrlParam(this.dataForm.link.meta.page_url, 'url');
            const result = await api.topic.checkPageUrl({ url: linkPageUrl });
            if (((result || {}).data || {}).status != 200) {
              linkErrMsg = '跳转链接不存在，请检查';
            }
          }
        }
        if (linkErrMsg) {
          this.$message.error(linkErrMsg);
          return false;
        }
        if (this.content.main_type === "twoone") {
          switch (this.currentIndex) {
            case (0):
              this.currentIndex = 1;
              break;
            case (1):
              this.currentIndex = 2;
              break;
            case (2):
              this.currentIndex = 0;
              break;
          }
        }
        this.content.list.splice(this.currentIndex, 1, this.dataForm);
        this.closeAddDialog();
      },
      async onUploadImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.content.bgRes = res.data.url;
      },
    },
    watch:{
      is_share(new_val){
        this.dataForm.link.meta.is_share=new_val
      }
    }
  }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


  .images-box {
    .container {
      display: flex;
      align-items: center;

      .img {
        width: 78%;

        img {
          display: block;
          max-width: 300px;
          height: 100px;
        }
      }

      .button-list {
        margin-left: 10px;
      }
    }

    .content-setting {
      color: #fff;
      background-color: #13c2c2;
      padding: 10px;
      text-align: center;
      font-size: 16px;
      margin-bottom: 10px;
    }

    .el-icon-circle-plus-outline {
      font-size: 35px;
      color: #c7bdbd;
    }

    .topic-image-upload {
      .image {
        display: block;
        width: 100%;
      }

      .uploader-icon {
        width: 200px;
        height: 200px;
        line-height: 200px;
        border: 1px solid $border-base;
        font-size: 50px;
      }
    }

    .topic-image-picker {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .el-row {
    text-align: center;

    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
  }

  .level{
    margin-top: 10px;
  }
</style>
<style lang="scss" rel="stylesheet/scss">
  .topic-banner {
    .banner-dialog {
      .el-dialog__body {
        padding-top: 10px;
      }
    }
  }
</style>
