<template>
    <div class="topic-banner">
        <div class="block" style="margin-bottom: 4px">
            <el-date-picker
                    v-model="content.timevalue"
                    type="datetimerange"
                    :picker-options="pickerOptions2"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    align="right">
            </el-date-picker>
        </div>
        <div class="bg-img">
            <el-form class="data-form" size="mini" label-width="60px" label-suffix=" : "
                     style="display:inline-block;margin-bottom: 5px;margin-top: 5px;">
                <label class="demonstration">背景色:</label>
                <el-color-picker v-model="content.color" size="mini" @active-change="onSelect"></el-color-picker>
            </el-form>
        </div>
        <el-button @click="toAdd" class="btn-block mb-10" type="primary">上传宣传商品</el-button>
        <el-table :data="list" :row-key="getRowKeys" size="mini" style="width: 100%">
            <el-table-column label="图片">
                <template slot-scope="scope">
                    <div class="container">
                        <div class="img"><img :src="scope.row.image"></div>
                        <div class="button-list">
                            <el-row style="margin-top: 5px" :gutter="10">
                                <el-col :span="15">
                                    <div class="grid-content bg-purple">
                                        <el-tooltip placement="top" style="width: 100px">
                                            <div slot="content" style="width: 250px">
                                                <div class="demonstration">商品名称:{{scope.row.goods_title}}</div>
                                                <div class="demonstration" v-if="scope.row.timevalue">开始时间:{{scope.row.timevalue[0]}}</div>
                                                <div class="demonstration" v-if="scope.row.timevalue">结束时间:{{scope.row.timevalue[1]}}</div>
                                                <div class="demonstration">商品原价:{{scope.row.goods_original_price}}</div>
                                                <div class="demonstration">商品现价:{{scope.row.goods_present_price}}</div>
                                                <div class="demonstration">商品介绍:{{scope.row.goods_introduce}}</div>
                                                <div class="demonstration">商品说明1:{{scope.row.goods_explain}}</div>
                                            </div>
                                            <el-button>配置详情</el-button>
                                        </el-tooltip>
                                    </div>
                                </el-col>
                                <el-col :span="9">
                                    <div style="margin: 5px 0">
                                        <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑</el-button>
                                    </div>
                                    <div style="margin: 5px 0">
                                        <el-button size="mini" @click="toRemove(scope.row)" type="danger">删除</el-button>
                                    </div>

                                </el-col>
                            </el-row>

                        </div>
                    </div>
                    <!--<div class="link-desc">{{scope.row.link | link}}</div>-->
                </template>
            </el-table-column>
        </el-table>
        <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addDialog">
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="onUploadImage">
                <img v-if="dataForm.image" :src="dataForm.image" class="image">
                <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
            <div class="block" style="margin-top: 10px">
                <span class="demonstration">开始结束时间:</span>
                <el-date-picker
                        v-model="dataForm.timevalue"
                        type="datetimerange"
                        :picker-options="pickerOptions2"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        align="right">
                </el-date-picker>
            </div>
            <el-row style="margin-top: 5px" :gutter="10">
                <el-col :span="18">
                    <div class="grid-content bg-purple">
                        <el-input placeholder="商品名称" v-model="dataForm.goods_title">
                            <template slot="prepend">商品名称</template>
                        </el-input>

                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-light">
                        <label class="demonstration">名称的颜色:</label>
                        <el-color-picker v-model="title_color" size="mini"></el-color-picker>
                    </div>
                </el-col>
            </el-row>

            <el-row style="margin-top: 15px" :gutter="10">
                <el-col :span="18">
                    <div class="grid-content bg-purple">
                        <el-input placeholder="商品介绍" v-model="dataForm.goods_introduce">
                            <template slot="prepend">商品介绍</template>
                        </el-input>

                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-light">
                        <label class="demonstration">介绍的颜色:</label>
                        <el-color-picker v-model="introduce_color" size="mini"></el-color-picker>
                    </div>
                </el-col>
            </el-row>

            <el-row style="margin-top: 15px" :gutter="10">
                <el-col :span="9">
                    <div class="grid-content bg-purple">
                        <el-input placeholder="商品原价" v-model="dataForm.goods_original_price" type="number">
                            <template slot="prepend">商品原价</template>
                        </el-input>
                    </div>
                </el-col>
                <el-col :span="9">
                    <div class="grid-content bg-purple">
                        <el-input placeholder="商品现价" v-model="dataForm.goods_present_price" type="number">
                            <template slot="prepend">商品现价</template>
                        </el-input>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="grid-content bg-purple-light">
                        <label class="demonstration">价格的颜色:</label>
                        <el-color-picker v-model="price_color" size="mini"></el-color-picker>
                    </div>
                </el-col>
            </el-row>

            <el-row style="margin-top: 15px" :gutter="10">
                <el-col :span="18">
                    <div class="grid-content bg-purple">
                        <el-input placeholder="产品说明" v-model="dataForm.goods_explain">
                            <template slot="prepend">产品说明</template>
                        </el-input>
                    </div>
                </el-col>

                <el-col :span="6">
                    <div class="grid-content bg-purple-light">
                        <label class="demonstration">说明的颜色:</label>
                        <el-color-picker v-model="goods_explain_color" size="mini"></el-color-picker>
                    </div>
                </el-col>
            </el-row>

            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeAddDialog">取 消</el-button>
                <el-button size="small" type="primary" @click="confirm">确定</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
    import base from '../base'

    export default {
        extends: base,
        contentDefault: {
            list: [],
            color: '#ffffff',
            image: '',
            goods_title:"",
            timevalue:''
        },
        data() {
            return {
                loading: false,
                addDialog: false,
                goods_explain_color: null,
                price_color: null,
                title_color: null,
                introduce_color: null,
                dataForm: {
                    goods_title: "",
                    title_color: null,
                    goods_explain:"",
                    goods_explain_color: null,
                    goods_introduce: "",
                    introduce_color: null,
                    goods_original_price: null,
                    goods_present_price: null,
                    price_color: null,
                    image: '',
                    link: {
                        meta: {
                            page_url: ''
                        }
                    },
                    timevalue: '',
                },
                pickerOptions2: {
                    shortcuts: [{
                        text: '未来一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来六个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来一年',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                value4: [new Date(2000, 10, 10, 10, 10), new Date(2000, 10, 11, 10, 10)],
                value5: ''
            }
        },
        computed: {
            goods_list(){
                var list = _.get(this, 'content.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
            list() {
                var list = _.get(this, 'content.list')
                if (list) {
                    if (list.length > 0 && list[0].link.meta) {
                        this.$nextTick(function () {
                            this.setSort()
                        })
                    }
                    console.log(list);
                    return list
                } else {
                    return [];
                }
            },
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return data.meta.page_url;
                // return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '') + (data.desc ? ',' : '') + data.desc;
            }
        },
        methods: {
            handleDelete(row) {
                const index = this.list.indexOf(row)
                this.list.splice(index, 1)
            },
            goods_onSetLink(link) {
                if (this.content.list.length > 0) {
                    this.content.list = [...api.common.removeRepeat(this.content.list, link.data)]
                } else {
                    this.content.list = [...link.data]
                }

            },
            closeAddDialog() {
                this.addDialog = false;
            },
            toRemove(data) {
                let _self = this;
                return function () {
                    _self.list.splice(_self.list.indexOf(data), 1)
                    _self.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }.confirm(_self)()
            },
            toEdit(data, index) {
                this.currentData = data;
                this.currentIndex = index;
                this.dataForm = Object.assign({}, data);
                this.isEdit = true;
                this.addDialog = true;
            },
            toAdd() {
                this.isEdit = false;
                this.dataForm = {
                    image: '',
                    link: {
                        meta: {
                            page_url: ''
                        }
                    },
                };
                this.addDialog = true;
            },
            onSetLink(link) {
                this.dataForm.link = link
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.dataForm.image = res.data.url
            },
            confirm() {
                if (!this.dataForm.image) {
                    this.$message.warning('请上传图片');
                    return false;
                }
                this.closeAddDialog();
                if (this.isEdit) {
                    this.currentData = Object.assign(this.currentData, this.dataForm);
                    this.list.splice(this.currentIndex, 1, this.currentData);
                } else {
                    this.list.push(Object.assign({}, this.dataForm));
                }
            },
            onSelect(val) {
                this.content.bgRes = this.toColor16(val);
            },
            setlink(e) {
                this.content.link.meta.page_url = e.target.value
            },
            toColor16(str) {
                if (/^(rgb|RGB)/.test(str)) {
                    var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
                    var strHex = "#";
                    for (var i = 0; i < aColor.length; i++) {
                        var hex = Number(aColor[i]).toString(16);
                        if (hex === "0") {
                            hex += hex;
                        }
                        strHex += hex;
                    }

                    if (strHex.length !== 7) {
                        strHex = str;
                    }
                    return strHex.toUpperCase();
                } else {
                    return str;
                }
            }
        },
        watch: {
            radio(new_val) {
                this.dataForm.goods_sales_type = new_val
            },
            goods_explain_color(new_val) {
                this.dataForm.goods_explain_color = new_val
            },
            price_color(new_val) {
                this.dataForm.price_color = new_val
            },
            title_color(new_val) {
                this.dataForm.title_color = new_val
            },
            introduce_color(new_val) {
                this.dataForm.introduce_color = new_val
            },
            is_from_self(new_val){
                this.dataForm.is_from_self = new_val
            }

        },
        created() {
            this.radio="单品";
            this.price_color="#FF5B5B";
            this.title_color="#333333";
            this.introduce_color="#939393";
            this.goods_explain_color="#6470B0";
        }
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .topic-banner {
        .container {
            display: flex;
            align-items: center;

            .img {
                width: 65%;

                img {
                    display: block;
                    width: 100%;
                }
            }

            .button-list {
                margin-left: 10px;
            }
        }

        .link-desc {
        }

        .topic-image-upload {
            .image {
                display: block;
                width: 100%;
            }

            .uploader-icon {
                width: 200px;
                height: 200px;
                line-height: 200px;
                border: 1px solid $border-base;
                font-size: 50px;
            }
        }

        .topic-image-picker {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }

</style>
<style lang="scss" rel="stylesheet/scss">
    .topic-banner {
        .banner-dialog {
            .el-dialog__body {
                padding-top: 10px;
            }
        }
    }
</style>
