<template>
  <div>
    <!-- 新增 -->
    <el-dialog
      class="feed-goods-advertisememt-alert"
      :title="`${isEdit ? '编辑' : '新建'}`"
      :before-close="addDialogCancel"
      :visible.sync="addDialog"
    >
      <el-form
        label-position="right"
        ref="addRuleForm"
        :model="addForm"
        :rules="addRules"
        :disabled="isInfo"
        size="small"
        label-width="100px"
        label-suffix="："
      >
        <el-form-item label="活动名称" prop="activityName">
          <el-input
            v-model="addForm.activityName"
            maxlength="20"
            size="mini"
            placeholder="请输入热词组名称，20个字符以内"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="位置" prop="positionType">
          <el-radio v-model="addForm.positionType" :label="1"> 左侧 </el-radio>
          <el-radio v-model="addForm.positionType" :label="2"> 右侧 </el-radio>
          <el-select v-model="addForm.position">
            <el-option v-if="addForm.positionType==2" value="1" label="1"></el-option>
            <el-option value="2" label="2"></el-option>
            <el-option value="3" label="3"></el-option>
            <el-option value="4" label="4"></el-option>
            <el-option value="5" label="5"></el-option>
            <el-option value="6" label="6"></el-option>
            <el-option value="7" label="7"></el-option>
            <el-option value="8" label="8"></el-option>
            <el-option value="9" label="9"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="指定人群" prop="crowdType">
          <el-radio-group
            v-model="addForm.crowdType"
            @change="changeCrowdType"
            style="width: 100%"
          >
            <el-radio :label="1" style="width: 100%"
              >该页面已选中人群</el-radio
            >
            <el-radio
              :label="2"
              style="width: 100%; margin-top: 10px"
              class="cowdtype-radio"
            >
              <div class="cowdtype-radio">
                <div>指定人群</div>
                <el-select
                  v-if="addForm.crowdType === 2"
                  style="margin-left: 10px"
                  v-model.trim="addForm.crowdValue"
                  :loading="selectLoading"
                  filterable
                  :filter-method="optionFilter"
                  placeholder="请输入人群id"
                  clearable
                  @clear="options = []"
                  @change="selectCrowd"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="展示时间">
          <el-radio v-model="addForm.timeType" :label="1">固定时段</el-radio>
          <el-date-picker
            v-model="addForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :picker-options="{
              disabledDate: (time) => {
                const times =
                  new Date(new Date().toLocaleDateString()).getTime() +
                  1095 * 8.64e7 -
                  1;
                return (
                  time.getTime() < Date.now() - 8.64e7 || time.getTime() > times
                ); // 如果没有后面的-8.64e7就是不可以选择今天的
              },
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker
          ><br />
          <el-radio v-model="addForm.timeType" :label="2">周期循环</el-radio>
          <el-button
            style="margintop: 10px"
            @click="toloopcirculateTime"
            type="primary"
            size="mini"
            >配置</el-button
          >
          <br>
          <div v-for="(item,index) in addForm.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[addForm.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{addForm.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
        </el-form-item>
        <el-form-item label="展示机制" style="margin-top: 20px" prop="showType">
          <el-radio v-model="addForm.showType" :label="1">
            每次刷新按顺序展示
          </el-radio>
          <el-radio v-model="addForm.showType" :label="2"> 随机 </el-radio>
        </el-form-item>
      
      <el-button type="primary" style="margin-bottom: 10px;" @click="addImageListRow" :disabled="imgShowList.length>=9" >添加</el-button>
      <el-table :data="imgShowList" size="mini" class="tableBox" ref="tableBox">
        <el-table-column
          label="帧数"
          prop="position"
          width="50px"
        ></el-table-column>

        <el-table-column label="图片">
          <template slot-scope="scope">
            <el-upload
              class="topic-image-upload"
              :show-file-list="false"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :on-preview="handlePreview"
              :on-success="uploadSucces"
            >
              <el-button
                size="small"
                type="primary"
                @click="currentRow = scope.$index"
                >点击上传</el-button
              >
            </el-upload>
          </template>
        </el-table-column>
        <el-table-column label="链接按钮">
          <template slot-scope="scope">
            <el-button @click="openLink(scope)">链接按钮</el-button>
          </template>
        </el-table-column>
        <el-table-column label="展示url" prop="hrefUrl">
          <template slot-scope="scope">
            <el-tooltip
              :content="scope.row.hrefUrl"
              placement="top"
              :open-delay="600"
            >
              <span
                style="
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
                >{{ scope.row.hrefUrl }}</span
              >
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="预览" prop="url">
          <template slot-scope="scope">
            <div style="width: 40px; height: 40px" v-show="scope.row.url">
              <img
                :src="scope.row.url"
                alt=""
                style="width: 40px; height: 40px"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="delImageListRow(scope)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
</el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addDialogConfirm"
        v-if="!isInfo"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="跳转链接配置"
      :visible.sync="isShowHrefDialog"
      width="50%"
      append-to-body
    >
      <div class="banner-url-type">
        <el-radio
          v-model="addFormSelectRadio"
          label="inLink"
          @change="inLinkChange"
          >内部跳转</el-radio
        >
        <el-radio
          v-model="addFormSelectRadio"
          label="outLink"
          @change="inLinkChange"
          >外部跳转</el-radio
        >
      </div>
      <el-row :gutter="20" style="margin-top: 10px">
        <el-col :span="24">
          <!-- 跳转链接<span>({{dataForm.link | link}})</span>-->
          <el-input
            v-show="addFormSelectRadio !== 'inLink'"
            placeholder="链接地址"
            v-model="addFormSelectLink"
            @input="urlChange"
          >
            <template slot="prepend">跳转链接</template>
          </el-input>
          <div v-if="!addFormSelectRadio || addFormSelectRadio === 'inLink'">
            <page-link
              @select="onSetLink"
              :params="{ branchCode: topic.branchCode }"
            ></page-link>
          </div>
        </el-col>
      </el-row>

      <span slot="footer" class="dialog-footer">
        <el-button @click="hrefCancel">取 消</el-button>
        <el-button type="primary" @click="hrefConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <loopcirculateTime
      ref="loopcirculateTime"
      @loopcirculateTimeBack="loopcirculateTimeBack"
    ></loopcirculateTime>
  </div>
</template>


<script>
import loopcirculateTime from "../../../../components/loopcirculateTime.vue";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import twoVue from "../../../AppIndex/ads-list/two.vue";
export default {
  components: { swiperPoint, loopcirculateTime },
  props: {
    value: Object,
    topic: Object,
    categoryList: Array,
    isInfo:Boolean
  },
  data() {
    return {
      addRules: {
        activityName: [
          { required: true, message: "请填写活动名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" },
        ],
        crowdType: [
          { required: true, message: "请选择指定人群", trigger: "change" },
        ],
        // crowdValue: [
        //   { required: true, message: "请填写人群名称", trigger: "blur" },
        // ],
        positionType:[
          { required: true, message: "请选择轮播位置", trigger: "blur" },
        ],
        showType:[
          { required: true, message: "选择展示机制", trigger: "blur" },
        ],
        
      },
      imgShowType: "",
      imgShowList: [
        {
          position: 1,
          hrefType: 1,
          hrefUrl: "",
          url: "",
        }
      ],
      isEdit: false,
      addDialog: false,
      options: [],
      selectLoading: false,
      isShowHrefDialog: false,
      currentRow: undefined,

      addForm: {
        positionType: 1,
        position: "",
        activityName: "",
        activitySort: "",
        crowdType: 1,
        crowdId: "",
        crowdValue: "",
        timeType: 1,
        validityTime: "",
        circulateTime: "",
        showType: "",
      },
      addFormSelectRadio: "inLink",
      addFormSelectLink: "",
      bannerLocationList: [
        {
          id: 1,
          name: "第一帧",
        },
        {
          id: 2,
          name: "第二帧",
        },
        {
          id: 3,
          name: "第三帧",
        },
        {
          id: 4,
          name: "第四帧",
        },
        {
          id: 5,
          name: "第五帧",
        },
        {
          id: 6,
          name: "第六帧",
        },
        {
          id: 7,
          name: "第七帧",
        },
        {
          id: 8,
          name: "第八帧",
        },
      ],
    };
  },
  methods: {
    open(row, isEdit) {
      this.imgShowList= [
      {
          position: 1,
          hrefUrl: "",
          url: "",
        }
      ]
      this.isEdit = isEdit;
      if (this.isEdit) {
        let keys = Object.keys(row);
        for (let index = 0; index < keys.length; index++) {
          const key = keys[index];
          this.addForm[key] = row[key];
          if(key === 'showList'){
            this.imgShowList = JSON.parse(JSON.stringify(row[key]));
          }
        }
      }
      this.addDialog = true;
    },
    
    selectCrowd(e) {
      if (e) {
        this.addForm.crowdId = Number(this.options[0].value.trim());
        this.addForm.crowdValue = this.options[0].label;
      } else {
        this.addForm.crowdId = "";
        this.addForm.crowdValue = "";
      }
      this.$forceUpdate();
    },

    delImageListRow(val) {
      this.imgShowList.splice(val.$index, 1);
      for (let index = 0; index < this.imgShowList.length; index++) {
        const element = this.imgShowList[index];
        if (index >= val.$index) {
          element.position -= 1;
        }
      }
    },
    addImageListRow(){
     
     this.imgShowList.push(
       {
         hrefUrl: "",
         url: "",
         position:  this.imgShowList.length+1,
       }
     )
   },
    openLink(row) {
      this.currentRow = row.$index;
      this.isShowHrefDialog = true;
    },
    handlePreview(val) {
      console.log(val);
    },
    uploadSucces(res) {
      if (res.code != 200) {
        this.$message.warning(`[${res.code}]${res.msg}`);
        return;
      }
      console.log(res);
      this.imgShowList[this.currentRow].url = res.data.url;
    },

    onSetLink(link) {
      this.addFormSelectLink = link.meta.page_url;
      this.imgShowList[this.currentRow].hrefUrl = link.meta.page_url;
    },

    urlChange(link) {
      this.addFormSelectLink = link;
      this.imgShowList[this.currentRow].hrefUrl = link;
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    //打开时间循环
    toloopcirculateTime() {
      this.$refs.loopcirculateTime.circulateTime=this.addForm.circulateTime
      this.$refs.loopcirculateTime.editInit()
      this.$refs.loopcirculateTime.showVisible = true;
    },
    changeCrowdType() {
      this.addForm.crowdId = "";
      this.addForm.crowdValue = "";
    },
    //循环时间回调
    loopcirculateTimeBack(data) {
      this.addForm.circulateTime = data;
    },
    addDialogCancel() {
      this.resetAddForm();
      this.$refs['addRuleForm'].clearValidate()
      this.addDialog = false;
    },

    inLinkChange(val) {
      this.imgShowList[this.currentRow].hrefType = val;
    },

    hrefCancel() {
      this.addFormSelectLink = "";
      this.isShowHrefDialog = false;
    },
    hrefConfirm() {
      this.isShowHrefDialog = false;
    },
    resetAddForm() {
      this.addForm = {
        positionType: 1,
        position: "",
        activityName: "",
        activitySort: "",
        crowdType: 1,
        crowdId: "",
        crowdValue: "",
        timeType: 1,
        validityTime: "",
        circulateTime: "",
        showType: "",
      };
    },
    async addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        if(!this.addForm.position){
          this.$message.warning("请选择轮播位");
          return false;
        }
        if (this.addForm.timeType==2&&(!this.addForm.circulateTime||Object.keys(this.addForm.circulateTime).length==0||!this.addForm.circulateTime.circulateList||this.addForm.circulateTime.circulateList.length==0)) {
          this.$message.warning("请添加[周期循环] 时间段。");
          return false;
        }
        if(this.addForm.timeType==1&&(!this.addForm.validityTime||this.addForm.validityTime.length==0)){
          this.$message.warning("请添加时段");
          return false;
        }
        if(this.addForm.crowdType==2&&!this.addForm.crowdId){
          this.$message.warning("请添人群ID");
          return false;
        }
      
      
        let next=true
        let msg=""
      
        this.imgShowList.forEach((item,index)=>{
          if(!item.url||!item.hrefUrl){
          next=false
          msg=msg?msg:`第${index+1}帧，信息不完整`
         
        }
        })
        if(!next){
          this.$message.warning(msg);
          return
        }
        this.addForm['showList'] = this.imgShowList;
        this.$emit('done', this.addForm);
        // this.$message.success("添加成功！");
       // this.addDialogCancel();
      });
    },
  },
};
</script>

<style scoped>
.cowdtype-radio {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.tableBox {
  width: 100%;
}
.banner-url-type {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
</style>