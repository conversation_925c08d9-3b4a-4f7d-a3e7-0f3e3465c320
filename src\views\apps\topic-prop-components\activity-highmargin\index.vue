<template>
	<div>
		<tab-switch
			  :page-data="content.switch"
			  label="switch"
			  @listenData="getData"
			  @listenCurrentRow="getEditRow"
			  :componentsList="componentsList"></tab-switch>
		<el-dialog title="编辑当前页" :visible.sync="dialogVisible" width="40%">
			<p class="blank_20">选择组件:</p>
			<el-checkbox-group v-model="selectComponent" @change="setComponent">
				<el-checkbox :label="item.name" v-for="(item,index) in componentsList" :key="index">{{item.label}}
				</el-checkbox>
			</el-checkbox-group>
			<p class="blank_20">编辑组件:</p>
			<el-radio-group v-model="currentComponent">
				<el-radio :label="item.name" v-for="(item,index) in selectComponentList" :key="index">{{item.label}}
				</el-radio>
			</el-radio-group>
			<p class="blank_20"></p>
			<header class="edit-header">{{textArr(currentComponent)}}</header>
			<high-sort
				  v-if="isShow.highSort"
				  v-show="currentComponent==='highSort'"
				  :page-data="content.highSort"
				  label="highSort"
				  :clearNum="clearNum"
				  @listenData="getData"></high-sort>
			<banner
				  v-if="isShow.banner"
				  v-show="currentComponent==='banner'"
				  :page-data="content.banner"
				  :branchCode="topic.branchCode"
				  label="banner"
				  :clearNum="clearNum"
				  @listenData="getData"></banner>
			<streamer
				  v-if="isShow.streamer"
				  v-show="currentComponent==='streamer'"
				  :page-data="content.streamer"
				  :branchCode="topic.branchCode"
				  label="streamer"
				  :clearNum="clearNum"
				  @listenData="getData"></streamer>

			<promotion
				  v-if="isShow.promotion1"
				  v-show="currentComponent==='promotion1'"
				  :page-data="content.promotion1"
				  :branchCode="topic.branchCode"
				  label="promotion1"
				  :clearNum="clearNum"
				  @listenData="getData"></promotion>
			<promotion
				  v-if="isShow.promotion2"
				  v-show="currentComponent==='promotion2'"
				  :page-data="content.promotion2"
				  :branchCode="topic.branchCode"
				  label="promotion2"
				  :clearNum="clearNum"
				  @listenData="getData"></promotion>
			<swiper-goods
				  v-if="isShow.swiper"
				  v-show="currentComponent==='swiper'"
				  :page-data="content.swiper"
				  :branchCode="topic.branchCode"
				  label="swiper"
				  :clearNum="clearNum"
				  @listenData="getData"></swiper-goods>
			<special-area
				  v-if="isShow.special"
				  v-show="currentComponent==='special'"
				  :page-data="content.special"
				  :branchCode="topic.branchCode"
				  label="special"
				  :clearNum="clearNum"
				  @listenData="getData"></special-area>
			<high-title
				  v-if="isShow.title"
				  v-show="currentComponent==='title'"
				  :page-data="content.title"
				  label="title"
				  :clearNum="clearNum"
				  @listenData="getData"></high-title>
			<top-list
				  v-if="isShow.top"
				  v-show="currentComponent==='top'"
				  :page-data="content.top"
				  :branchCode="topic.branchCode"
				  label="top"
				  :clearNum="clearNum"
				  @listenData="getData"></top-list>
		</el-dialog>
	</div>
</template>

<script>
	import tabSwitch from '../../components/public/tab-switch'
	import banner from '../../components/public/banner'
	import streamer from '../../components/public/streamer'
	import promotion from '../../components/public/main-promotion'
	import swiperGoods from '../../components/public/brand-promotion'
	import highTitle from '../../components/public/highmargin-title'
	import specialArea from '../../components/public/special-area'
	import highSort from '../../components/public/sort'


	import base from "../base";
	import TopList from "../../components/public/top-list";
	import activityPage from '../../mixins/activityPage'
	import {common} from 'api'

	export default {
		name: "activityMargin",
		extends: base,
		mixins: [activityPage],
		contentDefault: {
			switch: {
				color: '#2A2A2A',
				hoverColor: '#333',
				lineColor: '#00DC82',
				bgColor: '#EBEBEB',
				isBtn: true,
				list: [
					{
						"name": "高毛专区",
						"content": [
							{
								"title": "高毛首页",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "心脑血管",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "抗菌消炎",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "消化系统",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "五官用药",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "皮肤外用",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "感冒用药",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "滋补保健",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "妇科用药",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "风湿骨痛",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "辅助器材",
								"componentsName": [],
								"data": {}
							},
							{
								"title": "其他用药",
								"componentsName": [],
								"data": {}
							}
						]
					}
				],
			},
			currentRow: [],
			list: [],
			banner: {
				list: [],
				bgRes: '',
				color: '#000000',
				rotationpointcolor: '#000000',
				image: '',
			},
			streamer: {
				image: '',
				bgRes: '',
				color: '#000000',
				link: {
					page_url: '',
					page_name: ''
				},
				timevalue: ''
			},
			promotion1: {
				type: 0,
				code: 0,
				searchNum: 1,
				list: [],
				count: 3,
				image: '',
				link: '',
				isBtn: false, //添加购物车按钮
				styleNum: 3,
				isTitle: false,
				bgImage: 'http://upload.ybm100.com/ybm/applayoutbanner/6e41f728-4f27-4b57-8f16-c883a17f1158.png',
				flag: 'http://upload.ybm100.com/ybm/applayoutbanner/f2e8af0f-268b-4481-bf2d-a33b94879bd1.png'
			},
			promotion2: {
				type: 0,
				code: 0,
				searchNum: 1,
				list: [],
				count: 3,
				image: '',
				link: '',
				isBtn: false, //添加购物车按钮
				styleNum: 3,
				isTitle: false,
				bgImage: 'http://upload.ybm100.com/ybm/applayoutbanner/6e41f728-4f27-4b57-8f16-c883a17f1158.png',
				flag: 'http://upload.ybm100.com/ybm/applayoutbanner/f2e8af0f-268b-4481-bf2d-a33b94879bd1.png'
			},
			swiper: {
				list: [],
				count: 3
			},
			title: {
				text: '高毛新品，每半月上不停',
				size: 16,
				color: '#000000',
				bg_color: '#ffffff',
				align: 'left'
			},
			top: {
				list: [],
				color: '#999', //文字颜色
				hoverColor: '#00DC82', //文字选中颜色,
				bgColor: '#ccc', //背景颜色,
				type: 0,
				isBtn: false //添加购物车按钮
			},
			special: {
				list: [],
				image: '',
				bgImage: '',
				goodsIds: [],
				link: {
					page_url: '',
					page_name: ''
				}
			},
			highSort: {
				category: [
					{name: 'smsr.sale_num', title: '综合', isBtn: false},
					{name: 'fob', title: '价格', isBtn: true},
					{name: 'grossProfile', title: '毛利', isBtn: true},
					{name: 'isPromotion', title: '有促销', isBtn: false}
				],
				color: '#333',
				hoverColor: '#00DC82',
				bgColor: '#fff'
			}

		},
		data() {
			return {
				init: {},
				isShow: {
					banner: false,
					streamer: false,
					promotion1: false,
					promotiom2: false,
					swiper: false,
					title: false,
					top: false,
					special: false,
					highSort: false
				},
				clearNum: 1,
				componentsList: [
					{name: 'banner', label: '轮播'},
					{name: 'streamer', label: '横幅广告'},
					{name: 'promotion1', label: '商品组1'},
					{name: 'promotion2', label: '商品组2'},
					{name: 'swiper', label: '横向滑动商品组'},
					{name: 'special', label: '专区'},
					{name: 'title', label: '标题'},
					{name: 'top', label: '商品列表'},
					{name: 'highSort', label: '排序'},
				]
			}
		},
		created() {
			for (let key in this.content) {
				if (key !== 'switch' && key !== 'currentRow' && key !== 'list' && key !== 'branchCode' && this.content.hasOwnProperty(key)) {
					this.init[key] = _.cloneDeep(this.content[key])
				}
			}
		},
		components: {
			TopList,
			tabSwitch, banner, streamer, promotion, swiperGoods, highTitle, specialArea, highSort
		},
		computed: {
			list() {
				var list = _.get(this, 'content.list')
				if (list) {
					return list
				} else {
					return []

				}
			},

		},
		methods: {
			getData(data) {
				// 返回组件的数据
				data = _.cloneDeep(data);
				if (this.content.currentRow.length == 0) return;
				//填入选项卡数据
				if (data.key === 'switch') {
					this.$set(this.content, 'switch', data.data)
					return
				}

				if (!this.content.list[this.content.currentRow[0]]) {
					this.$set(this.content.list, this.currentRow[0], [])
				}
				if (!this.content.list[this.content.currentRow[0]][this.content.currentRow[1]]) {
					this.$set(this.content.list[this.content.currentRow[0]], this.content.currentRow[1], {})
				}

				if (data.key === 'promotion') {
					this.$set(this.content.list[this.content.currentRow[0]][this.content.currentRow[1]][data.key], data.index, data.data)
					return
				}
				this.$set(this.content.list[this.content.currentRow[0]][this.content.currentRow[1]], data.key, data.data)

			},
			getEditRow(data) {
				// data为选中的当前行
				if (!this.content) return;
				if (data.key === 'tab') {
					this.content.currentRow = data.data;
					return
				}
				this.content.currentRow = data.data;
				//全部隐藏
				for (let key in this.isShow) {
					this.isShow[key] = false
				}
				//清空数据缓存
				this.clearNum++;
				for (let key in this.content) {
					if (key !== 'switch' && key !== 'currentRow' && key !== 'list' && key !== 'branchCode' && this.content.hasOwnProperty(key)) {
						this.content[key] = this.init[key]
					}
				}
				this.selectComponent.splice(0, this.selectComponent.length)
				// 填充组件数据
				if (this.content.list.length > 0 && this.content.list[this.content.currentRow[0]]) {
					if (this.content.list[this.content.currentRow[0]][this.content.currentRow[1]]) {
						let currentData = _.cloneDeep(this.content.list[this.content.currentRow[0]][this.content.currentRow[1]]);
						if (!common.isEmptyObject(currentData)) {
							for (let key in currentData) {
								key !== 'componentsName' ?
									this.content[key] = _.cloneDeep(currentData[key])
									: this.selectComponent = currentData.componentsName;
							}
						}

					}
				}
				this.showComponents();
			}
		}
	}
</script>
<style>
	.edit-header {
		line-height: 40px;
		background: #FBBC05;
		font-size: 16px;
		padding-left: 10px;
		margin-bottom: 10px;
	}

	.edit-dialog {
		left: 22%;
		box-shadow: 0 5px 10px rgba(0, 0, 0, .3);
	}

</style>
