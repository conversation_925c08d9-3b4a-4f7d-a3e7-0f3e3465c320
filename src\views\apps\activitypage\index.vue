<template>
  <div class="main-content">
    <el-row class="mb-10">
      <el-col :span="24">
        <el-row class="search-wrap" type="flex" :gutter="10">
          <el-popover placement="top" trigger="hover">
            <el-button
              @click="cancel()"
              :loading="loading"
              type="info"
              icon="el-icon-refresh"
              size="mini"
              plain
            >清空</el-button>
            <el-button
              @click="changeSize()"
              :loading="loading"
              slot="reference"
              type="primary"
              icon="el-icon-search"
              size="mini"
              plain
            >查询</el-button>
          </el-popover>
          <el-cascader
            v-model="searchParam.clientType"
            :options="clientType"
            placeholder="客户端类型"
            expand-trigger="click"
            size="mini"
            change-on-select
            filterable
            clearable
          ></el-cascader>
          <el-select v-model.number="searchParam.state" placeholder="状态" size="mini" clearable>
            <el-option v-for="(item, i) in state" :value="Number(i)" :label="item"></el-option>
          </el-select>
          <!-- <el-select
            v-model="searchParam.branchCode"
            v-if="true"
            placeholder="区域"
            size="mini"
            default-first-option
            filterable
            clearable
          >
            <el-option
              v-for="(item, i) in branchs"
              :value="item.branchCode"
              :label="item.branchName"
            ></el-option>
          </el-select> -->
          <el-input v-model="searchParam.page_name" placeholder="搜索活动页名称" size="mini" clearable></el-input>
          <el-input v-model="searchParam.page_id" placeholder="活动页ID" size="mini" clearable></el-input>
          <el-col :span="12">
            <el-button type="primary" size="mini" icon="el-icon-plus" @click="showModal()">添加</el-button>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-table
      :data="dataList"
      v-loading="loading"
      :row-class-name="tabRowCla"
      class="custom-table"
      size="mini"
      border
    >
      <div slot="empty" class="empty-wrap">
        <i class="iconfont icon-tishi"></i>
        <span>尚未添加活动页</span>
      </div>
      <el-table-column label="ID" :show-overflow-tooltip="true" width="170%">
        <template slot-scope="scope">{{ scope.row.page_id }}</template>
      </el-table-column>
      <el-table-column
        prop="page_name"
        :show-overflow-tooltip="true"
        label="活动页名称"
        width="200%"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="category"
        :show-overflow-tooltip="true"
        label="客户端 / 类型"
        width="110%"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ $options.filters.stateText(scope.row.category, category) }}</span>
          <span>/</span>
          <span>{{ $options.filters.stateText(scope.row.page_type, pageType) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="url" :show-overflow-tooltip="true" label="活动页链接" min-width="300%">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="online(scope.row)">{{ scope.row.page_url }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="state" label="状态" width="70%">
        <template slot-scope="scope">
          <span
            v-bind:class="{active: (scope.row.state === 1)}"
          >{{ $options.filters.stateText(scope.row.state, state) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" width="100%">
        <template
          slot-scope="scope"
        >{{scope.row.operator.create && scope.row.operator.create.userName}}</template>
      </el-table-column>
      <el-table-column prop="create_time" label="创建时间" width="150">
        <template slot-scope="scope">{{ scope.row.create_time | dateFmt }}</template>
      </el-table-column>
      <el-table-column label="更新人" width="100%">
        <template slot-scope="scope">{{scope.row.operator.edit && scope.row.operator.edit.userName}}</template>
      </el-table-column>
      <el-table-column prop="update_time" label="更新时间" width="150">
        <template slot-scope="scope">{{ scope.row.update_time | dateFmt }}</template>
      </el-table-column>
      <!-- <el-table-column prop="branchCode" label="区域" width="100%" align="center">
        <template
          slot-scope="scope"
        >{{ $options.filters.getBranchName(scope.row.branchCode, branchs) }}</template>
      </el-table-column> -->
      <el-table-column fixed="right" width="100%" label="操作" align="center">
        <template slot-scope="scope">
          <el-popover
            :disabled="!isAdmin"
            placement="left"
            trigger="hover"
            :open-delay="800"
            popper-class="myHover"
          >
            <el-button
              type="danger"
              :plain="true"
              size="mini"
              v-if="isAdmin"
              @click="remove(scope.row)"
            >删除</el-button>
            <div slot="reference">
              <el-button
                type="primary"
                :plain="true"
                size="mini"
                @click="showModal(scope.row, 'editModal', false)"
              >编辑</el-button>
              <!--<el-button type="primary" :plain="true" size="mini" @click="showModal(scope.row, 'editModal', true)">复制</el-button>-->
            </div>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-show="totalSize > 10"
      :current-page.sync="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      @size-change="changeSize"
      @current-change="changePage"
      layout="total, sizes, slot, jumper, prev, pager, next"
      :total="totalSize"
    ></el-pagination>
    <edit-modal
      ref="editModal"
      :current="activityData"
      :origin="origin"
      @close="close"
      @save-done="loadData"
    ></edit-modal>
  </div>
</template>
<script>
import api from "api";
import EditModal from "./edit-modal";
import { fetch, getDate } from "../../../utils/time-format";
import bus from "utils/eventbus";

export default {
  name: "activity",
  data() {
    return {
      currentPage: 1,
      pageFrom: 1,
      pageSize: 10,
      totalSize: 10,
      loading: false,
      isAdmin: false,
      searchParam: {},
      loginUser: {},
      dataList: [],
      activityData: {},
      origin: {
        copy: {}
      },
      maps: {},
      checkedIds: [],
      state: null,
      category: null,
      pageType: null,
      branchs: null
    };
  },
  created() {
    // bus.$on("change_branch", this.changeBranch);
  },
  components: {
    EditModal
  },
  computed: {
    /**
     * 组装客户端类型"options"参数
     * @returns {*}
     */
    clientType() {
      let clientType = [];
      if (!this.category) return clientType;
      if (this.pageType) {
        var child = [];
        for (let k in this.pageType)
          child.push({
            value: k,
            label: this.pageType[k]
          });
      }
      for (let k in this.category)
        clientType.push({
          value: k,
          label: this.category[k],
          children: child
        });
      return clientType;
    }
  },
  watch: {
    /* 客户端类型 */
    "searchParam.clientType": {
      handler(val, oldVal) {
        this.searchParam.category = !val ? null : val[0];
        this.searchParam.page_type = !val ? null : val[1];
      }
    }
  },
  async mounted() {
    bus.$emit("menu_type", "other-活动页管理");
    await this.dict(); //字典接口
    this.loadData();
    this.$store.dispatch("sideBar/setSideBarState", true);
    this.$store.dispatch("breadcrumb/clearPath");
    this.$store.dispatch("breadcrumb/addPath", {
      title: "活动页管理",
      subTitle: "活动页管理",
      action: "activity"
    });
  },
  filters: {
    stateText(k, dict) {
      return dict[k] || "未知";
    },
    dateFmt(date) {
      return date ? getDate(date) : "";
    },
    // getBranchName(code, branchs) {
    //   let branchName = "";
    //   if (!code || !branchs || !branchs.length) return branchName;
    //   for (let i = 0, len = branchs.length; i < len; i++) {
    //     let branch = branchs[i];
    //     if (branch.branchCode == code) {
    //       branchName = branch.branchName;
    //       break;
    //     }
    //   }
    //   return branchName;
    // }
  },
  methods: {
    // changeBranch(item) {
    //   this.searchParam.branchCode = item.branchCode;
    //   this.pageFrom = 1;
    //   this.pageSize = 10;
    //   this.loadData();
    // },
    async loadData() {
      this.loading = true;
      const params = {
        pageFrom: this.pageFrom,
        pageSize: this.pageSize
      };
      let pms = Object.assign(params, this.searchParam);
      delete pms.clientType;
      const result = await api.activity.list(pms);
      this.loading = false;
      if (result.code == 200) {
        this.$nextTick(() => {
          this.dataList = result.data.rows;
          this.totalSize = result.data.total;
        });
      } else {
        this.$message.error(result.msg);
      }
    },
    changePage(pageNo) {
      this.pageFrom = pageNo;
      this.loadData();
    },
    changeSize(pageSize) {
      this.currentPage = 1;
      this.pageSize = pageSize || this.pageSize;
      this.pageFrom = 1;
      this.loadData();
    },
    cancel() {
      // this.searchParam = {
      //   branchCode: !this.isAdmin ? this.searchParam.branchCode : undefined
      // };
      this.changeSize();
    },
    async dict() {
      let prs = new Array(5);
      prs[0] = new Promise(res => res(api.user.current())).then(user => {
        if (user.code == 200)
          this.$nextTick(() => {
            this.origin.loginUser = this.loginUser = user.data;
            this.isAdmin = this.loginUser.userName == "admin";
            /*if (!this.isAdmin)
								this.searchParam.branchCode = this.loginUser.branch.branchCode;*/
          });
        else this.$message.error(user.msg);
        return user;
      });
      prs[1] = new Promise(res => res(api.activity.activityState())).then(
        as => {
          if (as.code == 200)
            this.$nextTick(() => (this.origin.state = this.state = as.data));
          else this.$message.error(as.msg);
          return as;
        }
      );
      prs[2] = new Promise(res => res(api.activity.activityCategory())).then(
        ac => {
          if (ac.code == 200)
            this.$nextTick(
              () => (this.origin.category = this.category = ac.data)
            );
          else this.$message.error(ac.msg);
          return ac;
        }
      );
      prs[3] = new Promise(res => res(api.activity.activityPageType())).then(
        pt => {
          if (pt.code == 200)
            this.$nextTick(
              () => (this.origin.pageType = this.pageType = pt.data)
            );
          else this.$message.error(pt.msg);
          return pt;
        }
      );
      prs[4] = new Promise(res => res(api.dict.branchHasOpen())).then(bho => {
        if (bho.code == 200)
          this.$nextTick(() => (this.origin.branchs = this.branchs = bho.data));
        else this.$message.error(bho.msg);
        return bho;
      });
      return Promise.all(prs.map(item => item.catch(e => e))) //并发请求
        .then(data => (this.origin.clientType = this.clientType)); //客户端 / 类型
    },
    close() {
      this.origin.copy = {};
      this.activityData = {};
    },
    showModal(data, refName = "editModal", isCopy) {
      data = _.cloneDeep(data || {});
      if (isCopy) {
        this.origin.copy = data;
        this.activityData.page_name = data.page_name + "-copy";
      } else {
        this.origin.copy = {};
        this.activityData = data;
      }
      this.$refs[refName].show(true);
    },
    online(data) {
      let url = data.page_url.substring(
        data.page_url.indexOf("http"),
        data.page_url.length
      );
      var iWidth = 350; //弹出窗口的宽度;
      var iHeight = 600; //弹出窗口的高度;
      //window.screen.height获得屏幕的高，window.screen.width获得屏幕的宽
      var iTop = (window.screen.height - 30 - iHeight) / 2; //获得窗口的垂直位置;
      var iLeft = (window.screen.width - 10 - iWidth) / 2; //获得窗口的水平位置;
      window.open(
        url,
        "webcall",
        "height=" +
          iHeight +
          ",,innerHeight=" +
          iHeight +
          ",width=" +
          iWidth +
          ",innerWidth=" +
          iWidth +
          ",top=" +
          iTop +
          ",left=" +
          iLeft +
          ",toolbar=no,menubar=no,scrollbars=auto,resizeable=no,location=no,status=no"
      );
    },
    remove(data) {
      this.$confirm(
        "是否删除该活动页?删除后将永不能恢复，后果很严重！",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        const result = await api.activity.remove(data.id);
        if (result.code == 200) {
          this.$message.success("删除成功");
          this.loadData();
        } else {
          this.$message.error(result.msg);
        }
      });
    },
    tabRowCla({ row, i }) {
      if (row.state == -1) return "bgc-warn";
      return "";
    }
  },
  destroyed() {
    // bus.$off("change_branch", this.changeBranch);
  }
};
</script>
<style lang="scss" rel="stylesheet/scss">
.main-content {
}
.el-table .bgc-warn {
  background: oldlace;
}
.el-table .bgc-safe {
  background: #ccffcc;
}
.active {
  color: red;
}
.myHover {
  min-width: 50px;
}
</style>
