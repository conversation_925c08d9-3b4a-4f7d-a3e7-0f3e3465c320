/*
* 
*/
import axios from 'axios'
import Promise from 'es6-promise'

const _get = axios.get, _post = axios.post;
axios.get = (url, options) => {
    return new Promise(async resolve => {
        const result = await _get(url, options)
        resolve(result.data)
    })
}
axios.post = (url, data, options) => {
    return new Promise(async resolve => {
        const result = await _post(url, data, options)
        resolve(result.data)
    })
}