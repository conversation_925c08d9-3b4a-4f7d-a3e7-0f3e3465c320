<template>
  <div class="topic-menu-list">
    <el-row :gutter="20">
      <div class="title">列表基础配置</div>
      <div class="block btitle">
        <span class="demonstration">标题名称：</span>
        <div>
          <el-input size="mini" v-model="content.title" placeholder="请输入标题"></el-input>
        </div>
      </div>

      <el-col style="width: 100%;display: flex;align-items: center;padding: 10px;padding-left: 20px;">
        <div class="block" style="margin-right: 40px">
          <span class="demonstration">模块背景颜色</span>
          <div>
            <el-color-picker v-model="content.list_body_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="block">
          <el-upload
            style="display: inline-block;margin-right: 10px"
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="() => {loading = true; return true;}"
            :on-success="onUploadImage"
          >
            <el-button size="mini" class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
          <el-button size="mini" @click="() => { content.image = ''}">清除背景图</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import base from "views/apps/topic-prop-components/base.vue";
export default {
  name: "commodity-flow",
  extends: base,
  contentDefault: {
    list: [],
    goods_group: [],
    type: 1,
    list_body_color: "#f1f1f1",
    title: "",
    image: ""
  },
  data() {
    return {
      loading: false,
      tabs: [{ label: "商品组", value: "goodsGroup" }],
      typeList: ["列表模式", "大图模式"]
    };
  },
  filters: {
    link(data) {
      if (!data.type) {
        return "";
      }
      return "已选:" + data.label + (data.id ? "," : "") + (data.id || "");
    },
    moreLink(data) {
      if (!data || !data.type) {
        return "";
      }
      return "已选:" + data.label + (data.id ? "," : "") + (data.id || "");
    }
  },
  computed: {
    list() {
      let list = _.get(this, "content.list");
      if (list) {
        return list;
      } else {
        return [];
      }
    },
    goods_group() {
      let list = _.get(this, "content.goods_group");
      if (list) {
        return list;
      } else {
        return [];
      }
    }
  },
  methods: {
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.image = res.data.url;
    },
    onSetLink(link) {
      function handle_arr(arr = []) {
        return arr.map(item => {
          let obj = {};
          obj.init_img_url = item.init_img_url;
          obj.imageUrl = item.imageUrl;
          obj.productName = item.productName;
          obj.showName = item.showName;
          obj.mediumPackageTitle = item.mediumPackageTitle;
          obj.fob = item.fob;
          obj.id = item.id;
          return obj;
        });
      }

      if (link.tag === "goods" || link.tag === "importGoods") {
        let _self_arr = handle_arr(link.data);
        this.content.list = [..._self_arr];
        this.content.goods_group = [];
      } else if (link.tag === "goodsGroup") {
        let obj = {};
        obj.name = link.data.name;
        obj.branchCode = link.data.branchCode;
        obj.code = link.data.code;
        this.content.goods_group.splice(0, 1, obj);
        this.content.list = [];
      }
    },
    group_delete(row) {
      const index = this.goods_group.indexOf(row);
      this.goods_group.splice(index, 1);
    },
    handleDelete(row) {
      const index = this.list.indexOf(row);
      this.list.splice(index, 1);
    }
  }
};
</script>
<style scoped lang="scss">
.el-row {
  text-align: center;

  .title {
    text-align: left;
    line-height: 30px;
    /*background-color: #f2f2f2;*/
    color: #13c2c2;
    padding-left: 10px;
    margin: 10px;
  }
  .btitle {
    display: flex;
    align-items: center;
    padding: 10px;
    padding-top: 0;
    padding-left: 20px;
  }
}
</style>
