<template>
  <div class="two-images" v-loading="loading">
    
    <el-row :gutter="20">
      <div class="title">内容设置</div>
      <el-button style="width: 300px" type="primary" size="small" @click="toEdit({}, -1, '1')">添加优惠券</el-button>
    </el-row>

    <el-row :gutter="20">
      <el-col :lg="12" :md="12">
        <div class="formClass">
          <div>券ID</div>
          <el-input v-model.trim="formData.couponId" style="width:70%" clearable placeholder="" />
        </div>
      </el-col>
      <el-col :lg="12" :md="12">
        <div class="formClass"> 
          <el-button  type="primary" size="small" @click="search">搜索</el-button>
        </div>
      </el-col>
    </el-row>
    <!--优惠券列表-->
    <el-table :data="tabData" size="mini" :row-key="row => row.couponId" style="width: 100%; marginTop: 10px">
      <el-table-column label="券ID" prop="couponId">
        <template slot-scope="scope">
          <div>{{scope.row.couponId}}</div>
        </template>
      </el-table-column>
      <el-table-column label="券状态" prop="couponState">
        <template slot-scope="scope">
          <div>{{stateStr(scope.row.couponState)}}</div>
        </template>
      </el-table-column>
      <el-table-column label="人群" prop="customerGroupName">
        <template slot-scope="scope">
          <div>{{scope.row.customerGroupName}}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <div class="button-list">
            <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑</el-button>
            <el-button size="mini" @click="handleCancle(scope.row, scope.$index)" type="danger">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <coupon-settings
      v-if="addDialog"
      :visible="addDialog"
      :dataForm="dataForm"
      :isEdit="isEdit"
      :editIndex="editIndex"
      :orient="content.form.orient"
      @saveDialog="saveDialog"
    ></coupon-settings>
  </div>
</template>

<script>
import base from "../../base";
import api from "api";
import { AppWebsite } from "config";
import couponSettings from './couponSettings';
import index from '.';
export default {
  extends: base,
  components: {
    couponSettings
  },
  contentDefault: {
    list: [],
    form: {
      module: 1,
      orient: 1,
      fobFont: "AVGARDD"
    },
    title:""
  },
  watch: {
    list: {
      handler(val) {
        this.tabData = val.filter(item=>item.couponId==this.formData.couponId || this.formData.couponId == '');
      },
      deep: true
    }
  },
  data() {
    return {
      loading: false,
      tabData:[],
      formData:{
        couponId: '',
      },
      addDialog: false,
      dataForm: {},
      editIndex: '',
      isEdit: false,
      dynamicConfig: {
        couponId: '',
        couponTitle: '',
        couponText:'',
      }
    };
  },
  created() {
  },
  computed: {
    list() {
      var list = _.get(this, "content.list");
      if (list) {
        if (list[0]) {
          this.$nextTick(function() {
            // this.setSort();
          });
        }
        return list;
      } else {
        return [];
      }
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.getList();
    });
  },
  methods: {
    /**更新状态 */
    async getList() {
      let params = [];
      this.list.map((item, index)=> {
       params.push(item.couponId) 
      })
      const res = await api.stores.searchCoupon(params);
      const { data } = res;
      if (data && data.success) {
        if (!Object.keys(data.data).length) {
          return false;
        }
        const { list } = data.data;
        if (list && list.length) {
          list.map((item, index)=> {
            this.content.list.forEach((element,eIndex) => {
              element.sort = eIndex + 1; 
              if(element.couponId == item.templateId) {
                element.couponState = item.couponState;
                element.couponText = item.couponText;                element.couponText = item.couponText;
                element.customerGroupName = item.customerGroupName;
              }
            });
            // this.tabData.forEach(element => {
            //   if(element.couponId == item.couponId) {
            //     element.couponState = item.couponState;
            //   }
            // });
          })
        } 
      } 
    },
    /**搜索 */
    search() {
      this.tabData = this.list.filter(item=>item.couponId==this.formData.couponId || this.formData.couponId == '');
    },
    stateStr(state) {
      switch (state) {
          case 1:
              return '未开始';
              break;
          case 2:
              return '使用中';
              break;
          case 3:
              return '已下架';
              break;
          case 4:
              return '已删除';
              break;
          case 5:
              return '领取中';
              break;
          case 6:
              return '领取结束';
              break;
          default:
              return '其他';
              break;
      }
    },
    closeAddDialog() {
      this.addDialog = false;
    },
    toEdit(data, index, status) {
      if (status == "1") {
        if(this.list.length === 30) {
          this.$message.error('超出可配置数量，请删除或再次编辑失效优惠券');
          return;
        }
        //新建时图片清空
        this.isEdit = false;
        this.dataForm = JSON.parse(JSON.stringify(this.dynamicConfig));
        this.editIndex = '';
      } else {
        this.isEdit = true;
        this.editIndex = index;
        this.dataForm = JSON.parse(JSON.stringify(data));
      }
      this.addDialog = true;
    },
    //删除
    handleCancle(row,index) {
      let _self = this;
      return function () {
        const index = _self.content.list.indexOf(row)
        _self.content.list.splice(index, 1)
        _self.content.list.forEach((element,eIndex) => {
         element.sort = eIndex + 1; 
        })
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    async  saveDialog(type, psData, index) {
    //  保存
      if (psData.couponId == '') {
        return;
      }
      //校验券是否可选
      this.loading = true;
      const res = await api.stores.couponCheck({
        couponId: psData.couponId,
        voucherTypes:'8,1,5,9'
      });
      this.loading = false;
      if (!(res.data.code === 1000 && res.data.data.couponCheckInfo.checkFlag)) {
              this.$message.error('优惠券ID校验不通过');
              return
            }
      if (type == 'edit') {
        if(this.content.list.filter(item=>item.couponId == psData.couponId).length >= 1) {
          this.$message.error('请编辑优惠券信息后再保存');
          return;
        }
        this.content.list.splice(index, 1)
        this.content.list.push(psData);
      } else {
        let list = _.get(this, "content.list");
        if (list) {
          if(this.content.list.filter(item=>item.couponId == psData.couponId).length >= 1) {
            this.$message.error('券ID不能重复');
            return;
          }
          this.content.list.push(psData);
          this.content.list.forEach((element,eIndex) => {
            element.sort = eIndex + 1; 
          })
        } else {
          if(this.content.list.filter(item=>item.couponId == psData.couponId).length >= 1) {
            this.$message.error('券ID不能重复');
            return;
          }
          this.$set(this.content, 'list', []);
          this.content.list.push(psData);
        }
      }
      this.closeAddDialog();
      this.content.list.forEach((element,eIndex) => {
        element.sort = eIndex + 1; 
      })
    },
  }
};
</script>

<style lang="scss" scoped rel="stylesheet/scss">
.formClass{
  display: flex;
  align-items: center;
  margin: 10px;
}
.el-row {
  text-align: center;
  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
.file-list {
  @include flexbox($rowRank: flex-start, $wrap: wrap);
  li {
    margin-right: 2px;
  }
}
.two-images {
  .container {
    display: flex;
    align-items: center;
    .img {
      width: 30%;
      img {
        display: block;
        width: 300%;
        height: 60px;
      }
    }
    .button-list {
      margin-left: 10px;
    }
  }
  .el-icon-circle-plus-outline {
    font-size: 35px;
    color: #c7bdbd;
  }
  .topic-image-picker {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}
</style>
