
<template>
  <div class="topic-search">
    <!--table-->
    <el-row :gutter="20">
      <div class="title">分类配置</div>
      <el-row class="carouselFlexBox">
        <el-col :span="10" class="carouselFlex crowdInput">
          <span>分类名称：</span>
          <el-input v-model="queryParams.className" size="mini" clearable></el-input>
        </el-col>
        <el-col :span="10" class="carouselFlex">
          <span>状态：</span>
          <el-select v-model="queryParams.status" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in queryStatusOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col class="carouselFlex">
          <span>有效期：</span>
          <el-date-picker
            v-model="queryParams.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-col>
        <el-col class="carouselButton">
          <el-button type="primary" @click="addList" size="mini">新增</el-button>
          <el-button type="primary" @click="searchList" size="mini">查询</el-button>
          <el-button type="primary" @click="resetQueryParams" size="mini">重置</el-button>
        </el-col>
      </el-row>
      <el-table :data="dataList" size="mini" class="tableBox" style="margin: 0 0 20px" ref="tableBox" :row-key="row => row.id">
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column label="分类名称" prop="className" width="150"></el-table-column>
        <el-table-column label="有效期" width="300">
          <template slot-scope="scope">
            {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div >
              {{ ['未开始', '上线', '已结束', '下线'][scope.row.status - 1] || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button size="mini" v-if="scope.row.status == 3 || scope.row.status == 4" @click="toEdit(scope.row, scope.$index)" type="text">编辑
            </el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="toRemove(scope.row)" type="text">删除</el-button>
            <el-button size="mini" v-if="scope.row.status == 1 || scope.row.status == 4" @click="online(scope)" type="text">上线</el-button>
            <el-button size="mini" @click="offline(scope)" v-if="scope.row.status == 1 || scope.row.status == 2" type="text">下线</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <!-- 新增 -->
    <el-dialog class="banner-dialog" :title="`分类活动${isEdit ? '编辑': '新建'}`" :before-close="addDialogCancel" :visible.sync="addDialog">
      <el-form label-position="right" ref="addRuleForm"  :model="addForm" :rules="addRules" size="small" label-width="100px" label-suffix="：">
        <el-form-item label="分类名称" prop="className">
          <el-input v-model="addForm.className" maxlength="20" size="mini" placeholder="请输入分类名称，20个字符以内" clearable></el-input>
        </el-form-item>
        <el-form-item label="展示时间" :prop="addForm.timeType == 1 ? 'validityTime' : 'circulateTime'">
          <el-radio  v-model="addForm.timeType" :label="1">固定时段</el-radio> 
          <el-date-picker
            v-if="addForm.timeType == 1"
            v-model="addForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :picker-options="{
              disabledDate: (time) => {
                const times = new Date(new Date().toLocaleDateString()).getTime() + 1095 * 8.64e7 - 1
                return time.getTime() < Date.now() - 8.64e7 || time.getTime() > times// 如果没有后面的-8.64e7就是不可以选择今天的
              }
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker><br>
          <el-radio  v-model="addForm.timeType" :label="2">周期循环</el-radio>
          <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
        </el-form-item>
        <el-form-item label="分类背景色" prop="classBackground">
          <el-radio-group v-model="addForm.classBackgroundType">
            <el-radio :label="1">纯色</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group>
          <div class="add-color-item" v-if="addForm.classBackgroundType == 1">
            <span class="demonstration">点击设置纯色</span>
            <div>
              <el-color-picker v-model="addForm.classBackground" size="mini"></el-color-picker>
            </div>
            <div class="block" style="width: 200px;">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider v-model="addForm.classBackgroundTransparency" :format-tooltip="formatTooltip"></el-slider>
              </div>
            </div>
          </div>
          <div class="add-color-back" v-else>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'classBackground')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addForm.classBackgroundType == 2 && addForm.classBackground" :src="addForm.classBackground" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addForm.classBackground = ''">清空重置</el-button>
          </div>
        </el-form-item>
        <el-form-item label="分类按钮" prop="classBtnBackground">
          <el-radio-group v-model="addForm.classBtnBackgroundType">
            <el-radio :label="1">纯色</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group>
          <div class="add-color-item" v-if="addForm.classBtnBackgroundType == 1">
            <span class="demonstration">点击设置纯色</span>
            <div>
              <el-color-picker v-model="addForm.classBtnBackground" size="mini"></el-color-picker>
            </div>
            <div class="block" style="width: 200px;">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider v-model="addForm.classBtnBackgroundTransparency" :format-tooltip="formatTooltip"></el-slider>
              </div>
            </div>
          </div>
          <div class="add-color-back" v-else>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'classBtnBackground')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addForm.classBtnBackgroundType == 2 && addForm.classBtnBackground" :src="addForm.classBtnBackground" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addForm.classBtnBackground = ''">清空重置</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addDialogConfirm">确定</el-button>
      </div>
    </el-dialog>
    <el-row :gutter="20">
      <div class="title">tab背景配置</div>
      <el-row class="carouselFlexBox">
        <el-col :span="10" class="carouselFlex">
          <span>id：</span>
          <el-input v-model="queryBackParams.id" size="mini" clearable></el-input>
        </el-col>
        <el-col :span="20" class="carouselFlex crowdInput">
          <span>人群id：</span>
          <el-input v-model="queryBackParams.crowdValue" @blur="querySearchCrowd" @change="backCrowdName = ''" size="mini" clearable></el-input>
          <div class="crowdNameDiv">{{ backCrowdName }}</div>
        </el-col>
        <el-col :span="10" class="carouselFlex">
          <span>背景名称：</span>
          <el-input v-model="queryBackParams.backgroundName" size="mini" clearable></el-input>
        </el-col>
        <el-col :span="10" class="carouselFlex">
          <span>状态：</span>
          <el-select v-model="queryBackParams.status" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in queryStatusOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col class="carouselFlex">
          <span>有效期：</span>
          <el-date-picker
            v-model="queryBackParams.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-col>
        <el-col class="carouselButton">
          <el-button type="primary" @click="addBackList" size="mini">新增</el-button>
          <el-button type="primary" @click="searchBackList" size="mini">查询</el-button>
          <el-button type="primary" @click="resetQueryBackParams" size="mini">重置</el-button>
        </el-col>
      </el-row>
      <el-table :data="tabList" size="mini" class="tableBox" style="margin: 0 0 20px" ref="tableBox" :row-key="row => row.id">
        <el-table-column label="id" prop="id" width="150"></el-table-column>
        <el-table-column label="背景名称" prop="backgroundName" width="150"></el-table-column>
        <el-table-column label="tab背景" prop="backgroundName" width="150">
          <template slot-scope="scope">
            <img class="preview-img" v-if="scope.row.tabBackType == 2" :src="scope.row.tabBackImg" alt="">
            <span v-else>纯色</span>
          </template>
        </el-table-column>
        <el-table-column label="人群" show-overflow-tooltip width="200">
          <template slot-scope="scope">
            <p v-if="scope.row.crowdType == 2">
              {{scope.row.crowdId + '/' + scope.row.crowdValue || '全部人群'}}
            </p>
            <p v-else>显示该页面已选中人群</p>
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="300">
          <template slot-scope="scope">
            {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div >
              {{ ['未开始', '上线', '已结束', '下线'][scope.row.status - 1] || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button size="mini" v-if="scope.row.status == 3 || scope.row.status == 4" @click="toBackEdit(scope.row, scope.$index)" type="text">编辑
            </el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="toBackRemove(scope.row)" type="text">删除</el-button>
            <el-button size="mini" v-if="scope.row.status == 1 || scope.row.status == 4" @click="onBackline(scope)" type="text">上线</el-button>
            <el-button size="mini" @click="offBackline(scope)" v-if="scope.row.status == 1 || scope.row.status == 2" type="text">下线</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <!-- 新增 -->
    <el-dialog class="banner-dialog" :title="`tab背景色${isBackEdit ? '编辑': '新建'}`" :before-close="addBackDialogCancel" :visible.sync="addBackDialog">
      <el-form label-position="right" ref="addBackRuleForm"  :model="addBackForm" :rules="addBackRules" size="small" label-width="100px" label-suffix="：">
        <el-form-item label="背景名称" prop="backgroundName">
          <el-input v-model="addBackForm.backgroundName" maxlength="20" size="mini" placeholder="请输入背景名称，20个字符以内" clearable></el-input>
        </el-form-item>
        <el-form-item label="展示时间" :prop="addBackForm.timeType == 1 ? 'validityTime' : 'circulateTime'">
          <el-radio  v-model="addBackForm.timeType" :label="1">固定时段</el-radio> 
          <el-date-picker
            v-if="addBackForm.timeType == 1"
            v-model="addBackForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :picker-options="{
              disabledDate: (time) => {
                const times = new Date(new Date().toLocaleDateString()).getTime() + 1095 * 8.64e7 - 1
                return time.getTime() < Date.now() - 8.64e7 || time.getTime() > times// 如果没有后面的-8.64e7就是不可以选择今天的
              }
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker><br>
          <el-radio  v-model="addBackForm.timeType" :label="2">周期循环</el-radio>
          <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
        </el-form-item>
        <el-form-item label="人群圈选" prop="crowdType">
          <el-radio-group v-model="addBackForm.crowdType" @change="changeCrowdType">
            <el-radio :label="1">显示该页面已选中人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="addBackForm.crowdType===2" prop="crowdValue" label="指定人群">
          <el-select
            v-model.trim="addBackForm.crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="tab背景图片" prop="tabBackImg">
          <el-radio-group v-model="addBackForm.tabBackType">
            <el-radio :label="1">纯色</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group>
          <div class="add-color-item" v-if="addBackForm.tabBackType == 1">
            <span class="demonstration">点击设置纯色</span>
            <div>
              <el-color-picker v-model="addBackForm.tabBackImg" size="mini"></el-color-picker>
            </div>
            <div class="block" style="width: 200px;">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider v-model="addBackForm.tabBackImgTransparency" :format-tooltip="formatTooltip"></el-slider>
              </div>
            </div>
          </div>
          <div class="add-color-back" v-else>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadBackTopSearchBg(e, 'tabBackImg')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addBackForm.tabBackType == 2 && addBackForm.tabBackImg" :src="addBackForm.tabBackImg" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addBackForm.tabBackImg = ''">清空重置</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addBackDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addBackDialogConfirm">确定</el-button>
      </div>
    </el-dialog>
    <el-row :gutter="20">
      <div class="title">tab配置</div>
      <el-row class="carouselFlexBox">
        <el-col  :span="10" class="carouselFlex">
          <span>id：</span>
          <el-input v-model="queryTabParams.id" size="mini" clearable></el-input>
        </el-col>
        <el-col  :span="10" class="carouselFlex">
          <span>tab名称：</span>
          <el-input v-model="queryTabParams.tabName" size="mini" clearable></el-input>
        </el-col>
        <el-col  :span="20" class="carouselFlex crowdInput">
          <span>人群id：</span>
          <el-input v-model="queryTabParams.crowdValue" @blur="queryTabSearchCrowd" @change="tabCrowdName = ''" size="mini" clearable></el-input>
          <div class="crowdNameDiv">{{ tabCrowdName }}</div>
        </el-col>
        <el-col  :span="10" class="carouselFlex">
          <span>tab类别：</span>
          <el-select v-model="queryTabParams.class" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in classOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col  :span="10" class="carouselFlex">
          <span>位置：</span>
          <el-select v-model="queryTabParams.position" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in postionOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col :span="10" class="carouselFlex">
          <span>状态：</span>
          <el-select v-model="queryBackParams.status" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in queryStatusOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col class="carouselFlex">
          <span>有效期：</span>
          <el-date-picker
            v-model="queryTabParams.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-col>
        <el-col class="carouselButton">
          <el-button type="primary" @click="addTabList" size="mini">新增</el-button>
          <el-button type="primary" @click="searchTabList" size="mini">查询</el-button>
          <el-button type="primary" @click="resetQueryTabParams" size="mini">重置</el-button>
        </el-col>
      </el-row>
      <el-table :data="tabConfigList" size="mini" class="tableBox" style="margin: 0 0 20px" ref="tableBox" :row-key="row => row.id">
        <el-table-column label="id" prop="id" width="150"></el-table-column>
        <el-table-column label="tab名称" prop="tabName" width="150"></el-table-column>
        <el-table-column label="tab类别" prop="tabType" width="150">
          <template slot-scope="scope">
            <div >
              {{ scope.row.tabType == 1 ? '文字' : '图片' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="预览" width="150">
          <template slot-scope="scope">
            <img class="preview-img" v-if="scope.row.tabType == 2" :src="scope.row.classImg" alt="">
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="顺序" >
          <template slot-scope="scope">
            <el-input v-model="scope.row.sort" onkeyup="value=value.replace(/[^\d]/g,'')" @blur="changeSort(scope)" @keyup.enter.native="changeSort(scope)"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="人群" show-overflow-tooltip width="200">
          <template slot-scope="scope">
            <p v-if="scope.row.crowdType == 2">
              {{scope.row.crowdId + '/' + scope.row.crowdValue || '全部人群'}}
            </p>
            <p v-else>显示该页面已选中人群</p>
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="300">
          <template slot-scope="scope">
            {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div >
              {{ ['未开始', '上线', '已结束', '下线'][scope.row.status - 1] || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button size="mini" v-if="scope.row.status == 3 || scope.row.status == 4" @click="toTabEdit(scope.row, scope.$index)" type="text">编辑
            </el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="toTabRemove(scope.row)" type="text">删除</el-button>
            <el-button size="mini" v-if="scope.row.status == 1 || scope.row.status == 4" @click="onTabline(scope)" type="text">上线</el-button>
            <el-button size="mini" @click="offTabline(scope)" v-if="scope.row.status == 1 || scope.row.status == 2" type="text">下线</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <!-- 新增 -->
    <el-dialog class="banner-dialog" :title="`tab${isTabEdit ? '编辑': '新建'}`" :before-close="addTabDialogCancel" :visible.sync="addTabDialog">
      <el-form label-position="right" ref="addTabRuleForm"  :model="addTabForm" :rules="addTabRules" size="small" label-width="100px" label-suffix="：">
        <el-form-item label="tab名称" prop="tabName">
          <el-input v-model="addTabForm.tabName" maxlength="20" size="mini" placeholder="请输入tab名称，20个字符以内" clearable></el-input>
        </el-form-item>
        <el-form-item label="展示时间" :prop="addTabForm.timeType == 1 ? 'validityTime' : 'circulateTime'">
          <el-radio  v-model="addTabForm.timeType" :label="1">固定时段</el-radio> 
          <el-date-picker
            v-if="addTabForm.timeType == 1"
            v-model="addTabForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :picker-options="{
              disabledDate: (time) => {
                const times = new Date(new Date().toLocaleDateString()).getTime() + 1095 * 8.64e7 - 1
                return time.getTime() < Date.now() - 8.64e7 || time.getTime() > times// 如果没有后面的-8.64e7就是不可以选择今天的
              }
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker><br>
          <el-radio  v-model="addTabForm.timeType" :label="2">周期循环</el-radio>
          <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
        </el-form-item>
        <el-form-item label="人群圈选" prop="crowdType">
          <el-radio-group v-model="addTabForm.crowdType" @change="changeCrowdType">
            <el-radio :label="1">显示该页面已选中人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="addTabForm.crowdType===2" prop="crowdValue" label="指定人群">
          <el-select
            v-model.trim="addTabForm.crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectTabCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item  prop="position" label="位置">
          <el-select
            v-model.trim="addTabForm.position"
            placeholder="请选择位置"
            clearable
          >
            <el-option v-for="item in postionOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类别" :prop="addTabForm.tabType == 1 ? 'class' : 'classImg'">
          <el-radio-group v-model="addTabForm.tabType">
            <el-radio :label="1">文字</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group>
          <div class="add-color-item" v-if="addTabForm.tabType == 1">
            <el-input v-model="addTabForm.class" maxlength="20" size="mini" placeholder="限制6个字，默认居中显示" clearable></el-input>
            <div style="display: flex;align-items:center;margin: 10px 0;">
              <span class="demonstration" style="margin-right: 10px;">点击设置文字纯色</span>
              <el-color-picker v-model="addTabForm.classColor" size="mini"></el-color-picker>
            </div>
            <div class="block" style="width: 200px;">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider v-model="addTabForm.classColorTransparency" :format-tooltip="formatTooltip"></el-slider>
              </div>
            </div>
          </div>
          <div class="add-color-back" v-else>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTabTopSearchBg(e, 'classImg')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addTabForm.tabType == 2 && addTabForm.classImg" :src="addTabForm.classImg" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addTabForm.classImg = ''">清空重置</el-button>
          </div>
        </el-form-item>
        <el-form-item  label="是否加醒目标识">
          <el-switch
            v-model="addTabForm.isEyeType"
          >
          </el-switch>
          <el-upload v-if="addTabForm.isEyeType" class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
            :on-success="(e) => UploadTabTopSearchBg(e, 'isEyeImg')">
            <el-button size="small" type="primary">点击上传</el-button>
            <img v-if="addTabForm.isEyeImg" :src="addTabForm.isEyeImg" alt="">
            <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
          </el-upload>
        </el-form-item>
        <el-form-item label="转跳类型" prop="hrefType">
          <el-radio-group @change="hrefTypeChange" v-model="addTabForm.hrefType">
            <el-radio :label="1">链接</el-radio>
            <el-radio :label="2">tab模板页</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="addTabForm.hrefType == 1" label="路径" prop="hrefUrl">
          <el-input v-model="addTabForm.hrefUrl" size="mini" clearable></el-input>
          <el-button type="primary" size="small" @click="isShowHrefDialog = true">more</el-button>
        </el-form-item>
        <el-form-item v-if="addTabForm.hrefType == 2" label="tab模板id" prop="hrefTemplateId">
          <el-input v-model="addTabForm.hrefTemplateId" @input="searchTemplateId" size="mini" clearable></el-input>
          <span v-if="templateName">{{ templateName }}</span>
        </el-form-item>
      </el-form>
      <el-dialog
        title="跳转链接配置"
        :visible.sync="isShowHrefDialog"
        width="30%"
        append-to-body
      >
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
        <span slot="footer" class="dialog-footer">
          <el-button @click="hrefCancel">取 消</el-button>
          <el-button type="primary" @click="hrefConfirm">确 定</el-button>
        </span>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addTabDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addTabDialogConfirm">确定</el-button>
      </div>
    </el-dialog>
    <loopcirculateTime ref="loopcirculateTime" @loopcirculateTimeBack="loopcirculateTimeBack"></loopcirculateTime>
  </div>
</template>
<script>
import loopcirculateTime from '../../../components/loopcirculateTime.vue';
import base from "../../base";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import Sortable from 'sortablejs';
let sortableObject = {}
export default {
  name: 'searchBox',
  extends: base,
  components: { swiperPoint,loopcirculateTime },
  contentDefault: {
    tab_bg_color1: "",
    tab_bg_color2: "",
    pro_obj: {
      pro_type: "longBar",
      pro_auto: 0,
      pro_align_type: "center",
      default_color: "#ffffff",
      default_opacity: 30,
      active_color: "#555555",
      active_opacity: 100,
      component_name: "searchBox", //区分模块的标识
    },
    list: [],
    search_text: "您常搜",
    active_icon_color: "#ffffff",
    default_icon_color: "#ffffff",
    // top_bgRes: "#00B377",
    // hotWord_bgRes: "#00B377",
    // meddle_bgRes: "#00B377",
    // bottom_bgRes: "#00B377",
    // refresh_bgRes: "#00B377"
    top_bgRes: "#fff",
    hotWord_bgRes: "#fff",
    meddle_bgRes: "#fff",
    bottom_bgRes: "#fff",
    refresh_bgRes: "#fff"
  },
  data() {
    return {
      templateName: '',
      templateData: [],
      isShowHrefDialog: false,
      addBackDialog: false,
      currentBackDataIndex: 0,
      addFormSelectLink: '',
      currentTabDataIndex: 0,
      currentBackIndex: 0,
      currentTabIndex: 0,
      isBackEdit: false,
      dialogType: 'addForm',
      dialogRule: 'addRuleForm',
      isTabEdit: false,
      addTabDialog: false,
      queryBackParams: {
        id: '',
        status: '',
        validityTime: '',
        crowdValue: '',
        backgroundName: '',
      },
      classOption: [
        {id: 1, name: '文字'},
        {id: 2, name: '图片'}
      ],
      postionOption: [
        {id: 1, name: '左1'},
        {id: 2, name: '左2'},
        {id: 3, name: '左3'},
        {id: 4, name: '左4'},
        {id: 5, name: '左5'},
        {id: 6, name: '左6'},
        {id: 7, name: '左7'},
        {id: 8, name: '左8'},
        {id: 9, name: '左9'},
      ],
      addBackForm: {
        backgroundName: '',
        backgroundImg: '',
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        className: '', //分类名称
        circulateTime: '',//周期循环时间
        status: 1,
        timeType: 1,//展示时间type,
        backgroundTabType: 1,
        tabBackImg: '#eee',
        tabBackType: 1,
        tabBackImgTransparency: 100
      },
      addTabForm: {
        tabName: '',
        class: '',
        classImg: '',
        position: '',
        classColor: '#eee',
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        className: '', //分类名称
        circulateTime: '',//周期循环时间
        status: 1,
        timeType: 1,//展示时间type,
        classColorTransparency: 100,
        tabType: 1,
        hrefTemplateId: '',
        hrefType: 1,
        hrefUrl: '',
        isEyeType: 1,
        isEyeImg: "",
      },
      addRules: {
        className: [
          { required: true, message: "请填写热词组名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" }
        ],
        crowdType: [{ required: true, message: "请选择指定人群", trigger: "change" }],
        crowdValue: [
          { required: true, message: "请填写人群名称", trigger: "blur" },
        ],
        classBackground: [
          { required: true, message: "分类背景色不能为空", trigger: "change" }
        ],
        classBtnBackground: [
          { required: true, message: "分类按钮不能为空", trigger: "change" },
        ],
        validityTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        circulateTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
      }, 
      addBackRules: {
        backgroundName: [
          { required: true, message: "请填写背景名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" }
        ],
        crowdType: [{ required: true, message: "请选择指定人群", trigger: "change" }],
        crowdValue: [
          { required: true, message: "请填写人群名称", trigger: "blur" },
        ],
        tabBackImg: [
          { required: true, message: "tab背景不能为空", trigger: "change" }
        ],
        validityTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        circulateTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
      }, 
      addTabRules: {
        tabName: [
          { required: true, message: "请填写背景名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" }
        ],
        crowdType: [{ required: true, message: "请选择指定人群", trigger: "change" }],
        crowdValue: [
          { required: true, message: "请填写人群名称", trigger: "blur" },
        ],
        position: [
          { required: true, message: "请选择位置", trigger: "change" }
        ],
        validityTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        circulateTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        class: [
          { required: true, message: "请输入类别文字", trigger: "blur" }
        ],
        classImg: [
          { required: true, message: "请选择类别图片", trigger: "change" }
        ],
        hrefType: [
          { required: true, message: "请选择转跳类型", trigger: "change" }
        ],
        hrefUrl: [
          { required: true, message: "请输入转跳链接", trigger: "blur" }
        ],
        hrefTemplateId: [
          { required: true, message: "请输入转跳模板id", trigger: "blur" }
        ],
      }, 
      // 时间不能大于当前时间
      disabledDate: time => {
        return time.getTime() > Date.now()
      },
      isEdit: false,
      goods: [{ required: true, validator: this.goodsValid, trigger: "blur" }],
      isShowHrefDialog: false,
      keys: 'id',
      dataList: [], // 查询完的列表
      addForm: {
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        className: '', //分类名称
        classBackgroundType: 1,
        classBackground: '#eee',
        classBtnBackgroundType: 1,
        classBtnBackground: '#eee',
        circulateTime: '',
        status: 1,
        timeType: 1,
        classBackgroundTransparency: 100,
        classBtnBackgroundTransparency: 100,
      },
      backCrowdName: '',
      tabCrowdName: '',
      addFormSelectLink: '',
      queryParams: {
        validityTime: '', //有效期
        className: '', //词组名称
        status: '', //状态
      },
      queryTabParams: {
        id: '',
        tabName: '',
        status: '',
        validityTime: '', //有效期
        crowdValue: '',
        class: '',
        position: '',
        backgroundName: '',
      },
      
      queryStatusOption: [
        {id: '', name: '全部'},
        {id: 1, name: '未开始'},
        {id: 2, name: '上线'},
        {id: 3, name: '已结束'},
        {id: 4, name: '下线'},
      ],
      hrefOption: [
        {value: 1, label: '不挂链接'},
        {value: 2, label: '专题页链接'},
        {value: 3, label: '店铺页链接'},
        {value: 4, label: '动态商品页链接'},
      ],
      carouselList: {
        bannerLocation: '',
        crowdValue: '',
        status: ''
      },
      currentData: {},
      currentDataIndex: 0,
      currentIndex: 0,
      activeTab: 'notInvalid',
      selectLoading: false,
      options: [],
      pickerOptions0: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      pickerOptions1: {
        shortcuts: [{
          text: '未来一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来六个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来一年',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      loading: false,
      addDialog: false,
      tabList: [],
      tabConfigList: [],
      dataForm: {
        bannerLocation: '',
        bannerType: "inLink",
        image: '',
        link: {
          meta: {
            id: 0,
            page_url: ''
          }
        },
        timevalue: '',
        // bgRes: "#00B377",
        bgRes: "#fff",
        rest_bgRes: "#ffffff",
        crowdType: 1,
        crowdValue: '',
        crowdId: '',
        timeType: 1,
        circulateTime:{}
      },
    }
  },
  filters: {
    link(data) {
      return data.meta.page_url;
    },
    dateFilter(date) {
      function formatDate(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return year + '-' + (String(month).length > 1 ? month : '0' + month) + '-' +
          (String(day).length > 1 ? day : '0' + day) + ' ' + (String(hour).length > 1 ? hour : '0' + hour) + ':' + (String(minute).length > 1 ? minute : '0' + minute)
          + ':' + (String(second).length > 1 ? second : '0' + second)
      }

      if (date) {
        let date1 = formatDate(new Date(date[0]));
        let date2 = formatDate(new Date(date[1]));
        // const nS=new Date(date).getTime()
        return date1 + "至" + date2
      } else {
        return " "
      }
    },
    jumpText(val) {
      if (!val) {
        return "app内部跳转"
      } else {
        if (val === "inLink") {
          return "app内部跳转"
        }
        return "跳转至外部"
      }
    }
  },
  mounted() {
    this.getTemplateData();
    this.initData();
    this.initDataStatus();
    this.initTabList();
    this.initTabConfigList();
    // this.rowDrop()
    this.changeTab('notInvalid')
    // this.searchList()
  },
  computed: {
    /**
     *   获取列的状态名称
     */
    getStatusName() {
      return function (timevalue, type) {
        let item = {}
        if (!timevalue) {
          item = {
            id: 4,
            name: '未设置时间'
          }
        } else {
          const _date = new Date().getTime();
          const start = new Date(timevalue[0]).getTime();
          const end = new Date(timevalue[1]).getTime();
          if (_date <= end && _date >= start) {
            item = {
              id: 1,
              name: '生效中'
            }
          } else if (_date > end) {
            item = {
              id: 3,
              name: '已失效'
            }
          } else if (_date < start) {
            item = {
              id: 2,
              name: '待生效'
            }
          }
        }
        if (type == 'id') {
          return item.id
        } else {
          return item.name
        }
      }
    },
    // list() {
    //   let list = _.get(this, 'content.list')
    //   if (list) {
    //     if (list.length > 0 && list[0].link.meta) {
    //       this.$nextTick(function () {
    //         this.setSort()
    //       })
    //     }
    //     return list
    //   } else {
    //     return [];
    //   }
    // }
  },
  methods: {
    formatTooltip(val) {
      return val / 100;
    },
    async getTemplateData() {
      let params = {
        category: "app",
        page_type: "new_home",
        pageSize:99999
        // department: 0,
      }
      const result = await api.topic.list(params);
      if (result.code == 200) {
        this.templateData = result.data.rows;
      }
    },
    searchTemplateId() {
      let obj = this.templateData.find(item => item.page_id == this.addTabForm.hrefTemplateId) || {};
      console.log(this.addTabForm.hrefTemplateId)
      console.log(obj, 'obj')
      if (obj) {
        this.templateName = obj.page_name;
      } else {
        this.templateName = "";
      }
    },
    hrefTypeChange() {
      this.$refs['addTabRuleForm'].clearValidate('hrefUrl');
      this.$refs['addTabRuleForm'].clearValidate('hrefTemplateId');
    },
    resetQueryBackParams() {
      this.queryBackParams = {
        id: '',
        status: '',
        validityTime: '',
        crowdValue: '',
        backgroundName: '',
      }
      this.backCrowdName = "";
    },
    resetQueryTabParams() {
      this.queryTabParams = {
        id: '',
        status: '',
        validityTime: '',
        crowdValue: '',
        backgroundName: '',
      }
      this.tabCrowdName = "";
    },
    changeSort(scope) {
      let ind = scope.$index;
      if (this.tabConfigList[ind].sort <= 0) {
        this.$message.warning("请输入大于0的数字！")
        return;
      }
      if (this.tabConfigList[ind].sort >= this.tabConfigList.length) {
        this.tabConfigList.splice(this.tabConfigList.length, 0, this.tabConfigList[ind]);
        this.tabConfigList.splice(ind, 1)
      } else {
        if (this.tabConfigList[ind].sort > ind) {
          this.tabConfigList.splice(this.tabConfigList[ind].sort, 0, this.tabConfigList[ind]);
          this.tabConfigList.splice(ind, 1);
        } else {
          this.tabConfigList.splice(this.tabConfigList[ind].sort - 1, 0, this.tabConfigList[ind]);
          this.tabConfigList.splice(ind + 1, 1);
        }
      }
      this.tabConfigList.forEach((item, index) => {
        item.sort = index + 1;
      })
    },
    online(scope) {
      this.$confirm(
        "确定要执行上线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.list[scope.$index].status = 2;
        this.initData();
        this.$message.success("操作成功！")
      });
    },
    offline(scope) {
      this.$confirm(
        "确定要执行下线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.list[scope.$index].status = 4;
        this.initData();
        this.$message.success("操作成功！")
      });
    },
    onBackline(scope) {
      this.$confirm(
        "确定要执行上线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.tabList[scope.$index].status = 2;
        this.initData();
        this.$message.success("操作成功！")
      });
    },
    offBackline(scope) {
      this.$confirm(
        "确定要执行下线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.tabList[scope.$index].status = 4;
        this.initData();
        this.$message.success("操作成功！")
      });
    },
    
    onTabline(scope) {
      this.$confirm(
        "确定要执行上线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        console.log(this.content, "configcontent");
        this.content.tabConfigList[scope.$index].status = 2;
        this.initData();
        this.$message.success("操作成功！")
      });
    },
    offTabline(scope) {
      this.$confirm(
        "确定要执行下线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.tabConfigList[scope.$index].status = 4;
        this.initData();
        this.$message.success("操作成功！")
      });
    },
    addBackDialogCancel() {
      this.resetBackAddForm();
      this.addBackDialog = false;
    },
    addTabDialogCancel() {
      this.resetTabAddForm();
      this.addTabDialog = false;
    },
    
    addBackDialogConfirm() {
      this.$refs.addBackRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        let id = 0;
        id = Math.floor(Math.random() * 90000) + 10000;  
        if (this.content.list.findIndex(item => item.id == id) > -1) {
          this.$message("id错误，请重新添加！");
          return;
        }
        this.$set(this.addBackForm, 'id', id);
        if (!this.content.tabList) {
          this.$set(this.content, 'tabList', []);
        }
        let arr = [...this.content.tabList];
        if(this.isBackEdit) {
          this.$set(this.content.tabList, this.currentBackDataIndex, this.addBackForm);
        } else {
          arr.splice(0, 0, this.addBackForm);
          this.$set(this.content, 'tabList', arr);
        }
        this.$message.success(`${this.isBackEdit ? '编辑':'添加'}成功！`);
        this.content.tabList.forEach((item,index) => {
          item.sort = index + 1;
        })
        this.resetBackAddForm();
        this.addBackDialog = false;
        this.initData();
      });
    },
    addTabDialogConfirm() {
      this.$refs.addTabRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        let id = 0;
        id = Math.floor(Math.random() * 90000) + 10000;  
        if (this.content.list.findIndex(item => item.id == id) > -1) {
          this.$message("id错误，请重新添加！");
          return;
        }
        if (this.addTabForm.hrefType == 2 && !this.templateName) {
          this.$message("模板id不存在或输入错误，请检查模板id！");
          return;
        }
        this.$set(this.addTabForm, 'id', id);
        if (!this.content.tabConfigList) {
          this.$set(this.content, 'tabConfigList', []);
        }
        let arr = [...this.content.tabConfigList];
        if(this.isTabEdit) {
          this.$set(this.content.tabConfigList, this.currentTabDataIndex, this.addTabForm);
        } else {
          arr.splice(0, 0, this.addTabForm);
          this.$set(this.content, 'tabConfigList', arr);
        }
        this.$message.success(`${this.isTabEdit ? '编辑':'添加'}成功！`);
        this.content.tabConfigList.forEach((item,index) => {
          item.sort = index + 1;
        })
        this.resetTabAddForm();
        this.addTabDialog = false;
        this.initData();
      });
    },
    addDialogCancel() {
      this.resetAddForm();
      this.addDialog = false;
    },
    hrefCancel() {
      this.addFormSelectLink = '';
      this.isShowHrefDialog = false;
    },
    hrefConfirm() {
      this.addTabForm.hrefUrl = this.addFormSelectLink;
      this.$refs['addTabRuleForm'].clearValidate('hrefUrl');
      this.isShowHrefDialog = false;
    },
    resetAddForm() {
      this.addForm = {
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        className: '', //分类名称
        classBackgroundType: 1,
        classBackground: '#eee',
        classBtnBackgroundType: 1,
        classBtnBackground: '#eee',
        circulateTime: '',
        status: 1,
        timeType: 1,
        classBackgroundTransparency: 100,
        classBtnBackgroundTransparency: 100,
      }
    },
    resetBackAddForm() {
      this.addBackForm = {
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        className: '', //分类名称
        circulateTime: '',
        status: 1,
        timeType: 1,
        tabBackImg: '#eee',
        tabBackType: 1,
        tabBackImgTransparency: 100
      }
    },
    resetTabAddForm() {
      this.addTabForm = {
        tabName: '',
        class: '',
        classImg: '',
        position: '',
        classColor: '#eee',
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        className: '', //分类名称
        circulateTime: '',//周期循环时间
        status: 1,
        timeType: 1,//展示时间type,
        classColorTransparency: 100,
        tabType: 1,
        hrefTemplateId: '',
        hrefType: 1,
        hrefUrl: '',
        isEyeType: 1,
        isEyeImg: "",
      }
    },
    addList() {
      this.isEdit = false;
      this.addDialog = true;
      this.dialogType = 'addForm'
      this.dialogRule = 'addRuleForm'
    },
    addBackList() {
      this.isBackEdit = false;
      this.addBackDialog = true;
      this.dialogType = 'addBackForm'
      this.dialogRule = 'addBackRuleForm'
    },
    addTabList() {
      this.isTabEdit = false;
      this.addTabDialog = true;
      this.dialogType = 'addTabForm'
      this.dialogRule = 'addTabRuleForm'
    },
    resetQueryParams() {
      this.queryParams = {
        validityTime: '', //有效期
        className: '', //分类名称
        status: '', //状态
      }
    },
    //打开时间循环
    toloopcirculateTime(){
      this.$refs.loopcirculateTime.showVisible=true
    },
    //循环时间回调
    loopcirculateTimeBack(data){
      this.$set(this[this.dialogType], 'circulateTime', data);
      this.$refs[this.dialogRule].clearValidate('circulateTime')
    },
    //链接去掉空格
    urlChange(){
      this.dataForm.link.meta.page_url=this.dataForm.link.meta.page_url.trim()
    },
    initData() {
      this.dataList = this.content.list;
      this.tabList = this.content.tabList;
      this.tabConfigList = this.content.tabConfigList;
    },
    initDataStatus() {
      this.tabConfigList.forEach((item, index) => {
        // item.sort = index + 1;
        this.$set(item, 'sort', index + 1);
        if (new Date() * 1 < new Date(item.validityTime[0]) * 1) {
          item.status = 1; // 未开始
        } else if (new Date() * 1 > new Date(item.validityTime[0]) * 1 && new Date() * 1 < new Date(item.validityTime[1]) * 1) {
          item.status = 2; //上线
        } else if (new Date() > new Date(item.validityTime[1]) * 1) {
          item.status = 3;
        } else {
          item.status = 4;
        }
      })
    },
    initTabList() {
      this.tabList.forEach((item, index) => {
        // item.sort = index + 1;
        this.$set(item, 'sort', index + 1);
        if (new Date() * 1 < new Date(item.validityTime[0]) * 1) {
          item.status = 1; // 未开始
        } else if (new Date() * 1 > new Date(item.validityTime[0]) * 1 && new Date() * 1 < new Date(item.validityTime[1]) * 1) {
          item.status = 2; //上线
        } else if (new Date() > new Date(item.validityTime[1]) * 1) {
          item.status = 3;
        } else {
          item.status = 4;
        }
      })
    },
    initTabConfigList() {
      this.tabConfigList.forEach((item, index) => {
        // item.sort = index + 1;
        this.$set(item, 'sort', index + 1);
        if (new Date() * 1 < new Date(item.validityTime[0]) * 1) {
          item.status = 1; // 未开始
        } else if (new Date() * 1 > new Date(item.validityTime[0]) * 1 && new Date() * 1 < new Date(item.validityTime[1]) * 1) {
          item.status = 2; //上线
        } else if (new Date() > new Date(item.validityTime[1]) * 1) {
          item.status = 3;
        } else {
          item.status = 4;
        }
      })
    },
    rowDrop() {
      const _this = this;
      const tbody = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0];
      sortableObject = Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        ghostClass: "sortable-ghost",
        onEnd: evt => {
          const currRow = (_this.dataList || []).splice(evt.oldIndex, 1)[0];
          (_this.dataList || []).splice(evt.newIndex, 0, currRow);
          const currRowData = (_this.content.list || []).splice(evt.oldIndex, 1)[0];
          (_this.content.list || []).splice(evt.newIndex, 0, currRowData);
        }
      });
    },
    changeCrowdValue(e) {
      if (!e) {
        this.dataForm.crowdId = '';
      }
      this.$forceUpdate();
    },
    clear_bgs(type) {
      console.log(this.content, "Cotnet")
      this.content[type + '_url'] = '';
      this.content[type + '_color'] = '';
      this.content[type + '_transparency'] = 100;
      
      // this.content.top_bgRes = "#fff";
      // this.content.meddle_bgRes = "#fff";
      // this.content.bottom_bgRes = "#fff";
      // this.content.hotWord_bgRes = "#fff"
    },

    // 设置轮播链接
    onSetLink(link) {
      this.addFormSelectLink = link.meta.page_url;
    },

    // 上传轮播图片
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.dataForm.image = res.data.url
    },

    // 上传banner对应的头部区域背景图片
    async UploadTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.addForm, type, res.data.url);
      this.$refs['addRuleForm'].clearValidate()
    },
    async UploadBackTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.addBackForm, type, res.data.url);
      this.$refs['addBackRuleForm'].clearValidate()
    },
    async UploadTabTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.addTabForm, type, res.data.url);
      this.$refs['addTabRuleForm'].clearValidate()
    },
    UploadhotWord_bgRes(res) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.content.hotWord_bgRes = res.data.url
    },
    // 上传banner对应的中间区域背景图片
    async UploadMeddleSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.content.meddle_bgRes = res.data.url
    },
    // 上传banner对应的底部区域背景图片
    async UploadBottomSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.content.bottom_bgRes = res.data.url
    },
    changeCrowdType() {
      this.addForm.crowdId = '';
      this.addForm.crowdValue = '';
    },
    async addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        // if (this.content.list.findIndex(item => item.wordName === this.addForm.wordName) > -1) {
        //   this.$message("热词组名称已存在！");
        //   return;
        // }
        let arr = [...this.content.list];
        if(this.isEdit) {
          this.$set(this.content.list, this.currentDataIndex, this.addForm);
        } else {
          arr.splice(0, 0, this.addForm);
          this.$set(this.content, 'list', arr);
        }
        
        this.$message.success(`${this.isEdit ? '编辑':'添加'}成功！`);
        this.content.list.forEach((item,index) => {
          item.sort = index + 1;
        })
        this.initData();
        this.resetQueryParams();
        this.addDialog = false;
      });
    },

    // 按照规则排序--排序规则优先级：人群 > 帧位 > 生效时间（生效中>待生效>未设置）
    sortByRule(data) {
      const samePeople = this.content.list.filter((item,index) => {
        return Number(item.crowdId) === Number(this.dataForm.crowdId)
      });
      // 相同人群的逻辑
      if (samePeople.length) {
        const sameLocation = samePeople.filter((item, index) => {
          return Number(item.bannerLocation) === Number(this.dataForm.bannerLocation)
        });
        // 相同人群下，相同帧位的逻辑
        if (sameLocation.length) {
          const sameStatus = sameLocation.filter((item, index) => {
            return this.getStatusName(this.dataForm.timevalue) === this.getStatusName(item.timevalue);
          });
          let tempIndex = undefined;
          if (sameStatus.length) {
            this.content.list.forEach((item, index) => {
              if(item.id === sameStatus[sameStatus.length-1].id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，相同状态，插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === '生效中') {
            this.content.list.forEach((item, index) => {
              if (sameLocation[0].id === item.id) {
                tempIndex = index;
              }
            })
            // 相同人群，相同帧位，生效中插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === '待生效') {
            this.content.list.map((item, index) => {
              if(this.getStatusName(item.timevalue) === '生效中') {
                tempIndex = index + 1
              }
            })
            if (!tempIndex) {
              // 说明没有生效中，找未设置的
              this.content.list.map((item, index) => {
                if(this.getStatusName(item.timevalue) === '未设置时间') {
                  tempIndex = index
                }
              })
            }
            if (!tempIndex) {
              // 说明没有生效中未设置的，插到帧位最后面
              this.content.list.map((item, index) => {
                if (sameLocation[sameLocation.length - 1].id === item.id) {
                  tempIndex = index + 1;
                }
              })
            }
            // 相同人群，相同帧位，待生效插到生效中后面或未设置时间的前面或同帧位最后面
            this.content.list.splice(tempIndex, 0, data);
          } else {
            this.content.list.map((item, index) => {
              if (sameLocation[sameLocation.length - 1].id === item.id) {
                tempIndex = index;
              }
            })
            // 相同人群，相同帧位，未设置插到后面
            this.content.list.splice(tempIndex + 1, 0, data);
          }
        } else {
          // 相同人群下，不同帧位，比较帧位大小
          let tempIndex = undefined;
          // 找到第一个大于新增帧位的项，有则插入到前面，没有则插入到同人群下最后一位
          let maxItem = samePeople.find((item, index) => {
            return item.bannerLocation > this.dataForm.bannerLocation
          });
          if (maxItem) {
            this.content.list.map((item, index) => {
              if (item.id === maxItem.id) {
                tempIndex = index
              }
            })
          } else {
            this.content.list.map((item, index) => {
              if (item.id === samePeople[samePeople.length-1].id) {
                tempIndex = index + 1
              }
            })
          }
          // 新增帧位小插到前面；新增帧位大插到后面
          this.content.list.splice(tempIndex, 0, data);
        }
      } else { // 新人群，直接添加
        let tempIndex = undefined;
        tempIndex = this.content.list.filter((item) => {
          return this.getStatusName(item.timevalue) !== '已失效'
        }).length;
        this.content.list.splice(tempIndex, 0, data)
      }
      this.$nextTick(() => {
        this.searchList();
      })
      // this.content.list.push(Object.assign({}, data));
      // this.$set(this.dataList, this.dataList.length, data)
    },
    changeTab(type) {
      this.activeTab = type;
      this.carouselList.status = '';
      this.searchList();
    },
    //生成唯一id
    genID(length) {
      return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36);
    },
    //查询
    searchList() {
      //只有查询全部的时候允许拖拽
      // if (this.carouselList.status || this.carouselList.crowdValue || this.carouselList.bannerLocation) {
      //   sortableObject.option('disabled', true)
      // } else {
      //   sortableObject.option('disabled', false)
      // }
      this.dataList = this.content.list;
      if (this.queryParams.validityTime.length) {
        this.dataList = this.dataList.filter((item, index) => {
          return new Date(this.queryParams.validityTime[0]) * 1 >= new Date(item.validityTime[0]) * 1 && new Date(this.queryParams.validityTime[1]) * 1 <= new Date(item.validityTime[1]) * 1;
        })
      }
      if (this.queryParams.className) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.className == item.className;
        })
      }
      if (this.queryParams.status) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.status == item.status;
        })
      }
    },
    searchBackList() {
      this.tabList = this.content.tabList;
      if (this.queryBackParams.validityTime.length) {
        this.tabList = this.tabList.filter((item, index) => {
          return new Date(this.queryBackParams.validityTime[0]) * 1 >= new Date(item.validityTime[0]) * 1 && new Date(this.queryBackParams.validityTime[1]) * 1 <= new Date(item.validityTime[1]) * 1;
        })
      }
      if (this.queryBackParams.id) {
        this.tabList = this.tabList.filter((item, index) => {
          return this.queryBackParams.id == item.id;
        })
      }
      if (this.queryBackParams.backgroundName) {
        this.tabList = this.tabList.filter((item, index) => {
          return this.queryBackParams.backgroundName == item.backgroundName;
        })
      }
      if (this.queryBackParams.status) {
        this.tabList = this.tabList.filter((item, index) => {
          return this.queryBackParams.status == item.status;
        })
      }
      this.initTabList();
    },
    searchTabList() {
      this.tabConfigList = this.content.tabConfigList;
      if (this.queryTabParams.validityTime.length) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return new Date(this.queryTabParams.validityTime[0]) * 1 >= new Date(item.validityTime[0]) * 1 && new Date(this.queryTabParams.validityTime[1]) * 1 <= new Date(item.validityTime[1]) * 1;
        })
      }
      if (this.queryTabParams.id) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return this.queryTabParams.id == item.id;
        })
      }
      if (this.queryTabParams.backgroundName) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return this.queryTabParams.backgroundName == item.backgroundName;
        })
      }
      if (this.queryTabParams.status) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return this.queryTabParams.status == item.status;
        })
      }
      this.initTabConfigList();
    },
    toEdit(data, index) {
      this.addForm = {...data};
      this.dialogType = 'addForm';
      this.dialogRule = 'addRuleForm'
      this.currentDataIndex = index;
      this.currentIndex = this.content.list.findIndex((item) => item.id == data.id);
      if(!data.timeType){
        data.timeType = 1
      }
      
      if(this.addForm.timeType==2&&this.addForm.circulateTime){
        this.$refs.loopcirculateTime.circulateTime=this.addForm.circulateTime
        this.$refs.loopcirculateTime.editInit()

      }
      this.isEdit = true;
      this.addDialog = true;
    },
    toTabEdit(data, index) {
      this.addTabForm = {...data};
      this.dialogType = 'addTabForm';
      this.dialogRule = 'addTabRuleForm'
      this.currentTabDataIndex = index;
      this.currentTabIndex = this.content.tabList.findIndex((item) => item.id == data.id);
      if(!data.timeType){
        data.timeType = 1
      }
      
      if(this.addTabForm.timeType==2&&this.addTabForm.circulateTime){
        this.$refs.loopcirculateTime.circulateTime=this.addTabForm.circulateTime
        this.$refs.loopcirculateTime.editInit()

      }
      console.log( this.addTabForm)
      this.isTabEdit = true;
      this.addTabDialog = true;
    },
    toBackEdit(data, index) {
      this.addBackForm = {...data};
      this.dialogType = 'addBackForm';
      this.dialogRule = 'addBackRuleForm'
      this.currentBackDataIndex = index;
      this.currentBackIndex = this.content.tabList.findIndex((item) => item.id == data.id);
      if(!data.timeType){
        data.timeType = 1
      }
      this.addBackForm = JSON.parse(JSON.stringify(data));
      if(this.addBackForm.timeType==2&&this.addBackForm.circulateTime){
        this.$refs.loopcirculateTime.circulateTime=this.addBackForm.circulateTime
        this.$refs.loopcirculateTime.editInit()

      }
      console.log( this.addBackForm)
      this.isBackEdit = true;
      this.addBackDialog = true;
    },
    toRemove(data) {
      let _self = this;
      return function () {
        _self.content.list.splice(_self.content.list.findIndex((item) => item.id == data.id), 1)
        _self.dataList.splice(_self.dataList.findIndex((item) => item.id == data.id), 1)
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    toTabRemove() {
      let _self = this;
      return function () {
        _self.content.tabConfigList.splice(_self.content.tabConfigList.findIndex((item) => item.id == data.id), 1)
        _self.tabConfigList.splice(_self.tabConfigList.findIndex((item) => item.id == data.id), 1)
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    toBackRemove(data) {
      let _self = this;
      return function () {
        _self.content.tabList.splice(_self.content.tabList.findIndex((item) => item.id == data.id), 1)
        _self.tabList.splice(_self.tabList.findIndex((item) => item.id == data.id), 1)
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [{
          label: data.name,
          value: val,
        }]
      } else {
        this.selectLoading = false;
        this.options = []
      }
    },
    selectCrowd(e) {
      if (e) {
        this.addBackForm.crowdId = Number(this.options[0].value.trim());
        this.addBackForm.crowdValue = this.options[0].label;
      } else {
        this.addBackForm.crowdId = '';
        this.addBackForm.crowdValue = '';
      }
      this.$forceUpdate();
    },
    selectTabCrowd(e) {
      if (e) {
        this.addTabForm.crowdId = Number(this.options[0].value.trim());
        this.addTabForm.crowdValue = this.options[0].label;
      } else {
        this.addTabForm.crowdId = '';
        this.addTabForm.crowdValue = '';
      }
      this.$forceUpdate();
    },
    async querySearchCrowd() {
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${this.queryBackParams.crowdValue}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.code == 1000) {
        this.backCrowdName = res.data.name;
      }
    },
    async queryTabSearchCrowd() {
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${this.queryTabParams.crowdValue}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.code == 1000) {
        this.tabCrowdName = res.data.name;
      }
    },
    
    // handleSelectCrowd(item) {
    //   this.dataForm.crowdId = item.id;
    // },
  },
  watch:{
    // "dataForm.timeType"(newdata,ordData){
     
    //   if(newdata==2){
    //     this.dataForm.timevalue=""
    //   }
    //   if(newdata==1){
    //     this.dataForm.circulateTime={}
    //   }
    // },
  }
 
}
  ;
</script>
<style lang="scss" scoped>

.topic-search {
  .add-color-back {
    display: flex;
    align-items: center;
    .el-button {
      height: 30px;
    }
    .upload-demo {
      margin-right: 15px;
      // width: 180px;
      .el-upload {
        display: flex;
        align-items: center;
        
        img {
          width: 60px;
          margin-left: 15px;
        }
      }
    }
  }
  .el-col {
    display: flex;
    >div {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .topic-item {
      display: flex;
      margin-right: 10px;
      margin-bottom: 10px;
      flex: 1;
      .demonstration {
        min-width: 80px;
        margin-right: 15px;
        white-space: nowrap;
      }
    }
    .topic-item-title {
      flex: auto;
      width: 275px;
      display: flex;
      justify-content: flex-start;
    }
  }
}
.tableBox {
  width: 100%;
}
.carouselFlexBox {
  padding-right: 10px;
  .carouselFlex {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
    padding-right: 20px;
    span {
      min-width: 100px;
      text-align: right;
    }
  }
  .el-date-editor {
    width: 400px !important;
    height: 30px !important;
    .el-range__icon, .el-range-separator {
      line-height: 21px;
    }
  }
  .carouselButton {
    text-align: right;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }
}

.container-table {
  margin: 10px auto;
  padding-bottom: 10px;
  display: flex;
  justify-content: space-around;

  .img {
    width: 50%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .button-list {
    width: 45%;
  }
}

.topic-image-upload {
  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 200px;
    height: 200px;
    line-height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    font-size: 50px;
  }
}

.topic-image-upload .el-upload {
  width: 100%;
}

.el-row {
  text-align: center;

  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
  .tabBox {
    display: flex;
    margin: 20px;
    border-bottom: 1px solid #F1F1F4;
    cursor: pointer;
    div {
      border: 1px solid #F1F1F4;
      border-bottom: none;
      padding: 5px 10px;
    }
    .activeTab {
      color: #13c2c2;
    }
  }
}
</style>
<style  lang="scss">
.topic-search {
  .upload-demo {
    // width: 180px;
    .el-upload {
      display: flex;
      align-items: center;
      .el-button {
        height: 30px;
      }
      img {
        width: 60px;
        margin-left: 15px;
      }
    }
  }
  .banner-dialog {
    .el-input {
      width: 400px;
    }
  }
  .el-date-editor {
    .el-input__icon, .el-range-separator {
      line-height: 21px;
    }
  }
  .crowdInput {
    .el-input {
      width: 200px !important;
      margin-right: 20px;
    }
  }
  .tableBox {
    .el-button {
      margin-left: 0;
      margin-right: 10px;
    }
    .preview-img {
      width: 60px;
    }
  }
}
</style>