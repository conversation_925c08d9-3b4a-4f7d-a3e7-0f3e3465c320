<template>
  <div>
    <!-- 新增 -->
    <el-dialog
      class="feed-scroll-dialog"
      :title="`${isEdit ? '编辑' : '新建'}`"
      :before-close="addDialogCancel"
      :visible.sync="addDialog"
    >
      <el-form
        label-position="right"
        ref="addRuleForm"
        :rules="addRuleForm"
        :model="addForm"
        :disabled="isInfo"
        size="small"
        label-width="100px"
        label-suffix="："
      >
        <el-form-item label="活动名称" prop="activityName">
          <el-input
            v-model="addForm.activityName"
            maxlength="20"
            size="mini"
            placeholder="请输入活动名称，20个字符以内"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="位置" class="is-required" prop="bannerLocation">
          <div class="dialog-activity-sort">
            <el-select
              v-model="addForm.bannerLocation"
              placeholder="请选择"
              
            >
              <!-- <el-option value="" label="全部"></el-option> -->
              <el-option
                v-for="item in bannerLocationList"
                :value="item.id"
                :label="item.name"
                :key="item.id"
              ></el-option>
            </el-select>
          </div>
        </el-form-item>

        <el-form-item label="指定人群" prop="crowdType">
          <el-radio-group v-model="addForm.crowdType" @change="changeCrowdType">
            <el-radio :label="1">该页面已选中人群</el-radio>
            <el-radio :label="2">
              指定人群
              <el-select
                v-if="addForm.crowdType === 2"
                style="margin-left: 10px"
                v-model.trim="addForm.crowdValue"
                :loading="selectLoading"
                filterable
                :filter-method="optionFilter"
                placeholder="请输入人群id"
                clearable
                @clear="options = []"
                @change="selectCrowd"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="展示时间">
          <el-radio v-model="addForm.timeType" :label="1">固定时段</el-radio>
          <el-date-picker
            v-model="addForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :picker-options="{
              disabledDate: (time) => {
                const times =
                  new Date(new Date().toLocaleDateString()).getTime() +
                  1095 * 8.64e7 -
                  1;
                return (
                  time.getTime() < Date.now() - 8.64e7 || time.getTime() > times
                ); // 如果没有后面的-8.64e7就是不可以选择今天的
              },
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker
          ><br />
          <el-radio v-model="addForm.timeType" :label="2">周期循环</el-radio>
          <el-button
            style="margintop: 10px"
            @click="toloopcirculateTime"
            type="primary"
            size="mini"
            >配置</el-button
          >
          <br>
          <div v-for="(item,index) in addForm.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[addForm.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{addForm.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
        </el-form-item>

        <el-form-item label="图片" prop="imgs">
          <el-upload
           
            :auto-upload="true"
            :file-list="imageList"
            :before-upload="(f) => (sending = true)"
            :on-change="onChange"
            :on-remove="onRemove"
            :on-success="uploadSuccess"
            ref="uploadImg"
            class="upload-demo"
            list-type="picture-card"
            accept="image/jpeg,image/jpg,image/png,image/bmp,image/gif"
          >
            <i v-if="!imageList || !imageList.length" class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">
              <i>只能上传jpeg/jpg/png/bmp/gif文件，且不超过1M</i>
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="转跳链接" prop="hrefUrl">
          <el-input v-model="addForm.hrefUrl" size="mini" clearable></el-input>
          <el-button
            type="primary"
            size="small"
            @click="isShowHrefDialog = true"
            >more</el-button
          >
        </el-form-item>
      </el-form>
      <el-dialog
        title="跳转链接配置"
        :visible.sync="isShowHrefDialog"
        width="50%"
        append-to-body
      >
        <div class="banner-url-type">
          <el-radio
            v-model="addFormSelectRadio"
            label="inLink"
            @change="inLinkChange"
            >内部跳转</el-radio
          >
          <el-radio
            v-model="addFormSelectRadio"
            label="outLink"
            @change="inLinkChange"
            >外部跳转</el-radio
          >
        </div>
        <el-row :gutter="20" style="margin-top: 10px">
          <el-col :span="24">
            <!-- 跳转链接<span>({{dataForm.link | link}})</span>-->
            <el-input
              v-show="addFormSelectRadio !== 'inLink'"
              placeholder="链接地址"
              v-model="addFormSelectLink"
              @input="urlChange"
            >
              <template slot="prepend">跳转链接</template>
            </el-input>
            <div v-if="!addFormSelectRadio || addFormSelectRadio === 'inLink'">
              <page-link
                @select="onSetLink"
                :params="{ branchCode: topic.branchCode }"
              ></page-link>
            </div>
          </el-col>
        </el-row>

        <span slot="footer" class="dialog-footer">
          <el-button @click="hrefCancel">取 消</el-button>
          <el-button type="primary" @click="hrefConfirm">确 定</el-button>
        </span>
      </el-dialog>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addDialogConfirm" v-if="!isInfo"
          >确定</el-button
        >
      </div>
    </el-dialog>


    <loopcirculateTime
      ref="loopcirculateTime"
      @loopcirculateTimeBack="loopcirculateTimeBack"
    ></loopcirculateTime>
  </div>
</template>


<script>
import loopcirculateTime from "../../../../components/loopcirculateTime.vue";
import swiperPoint from "views/apps/components/public/swiper-point";
import api from "api";
import { AppWebsite, getUrlParam } from "config";
import twoVue from "../../../AppIndex/ads-list/two.vue";
export default {
  components: { swiperPoint, loopcirculateTime },
  props: {
    value: Object,
    topic: Object,
    categoryList: Array,
    isInfo:Boolean,
  },
  data() {
    return {
      addRuleForm:{
        activityName: [
          { required: true, message: "请填写活动名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" },
        ],
        crowdType: [
          { required: true, message: "请选择指定人群", trigger: "change" },
        ],
        bannerLocation:[
        { required: true, message: "请选择位置", trigger: "change" },
        ],
        // crowdValue: [
        //   { required: true, message: "请填写人群名称", trigger: "blur" },
        // ],
        // bannerLocation:[
        // { required: true, message: "请选择轮播位置", trigger: "change" },
        // ],
        hrefUrl:[
          { required: true, message: "请输入跳转链接", trigger: "blur" },
        ]
      },
      imgShowType: "",
      imageList: [],
      isEdit: false,
      addDialog: false,
      options: [],
      selectLoading: false,
      isShowHrefDialog: false,
      currentRow: undefined,
      
      addForm: {
        position: "",
        activityName: "",
        bannerLocation: "",
        crowdType: 1,
        crowdId: "",
        crowdValue: "",
        timeType: 1,
        validityTime: [],
        circulateTime: {},
        bannerImg: "",
        hrefUrlType: "inLink",
        hrefUrl: "",
      },
      addFormSelectRadio: "inLink",
      addFormSelectLink: "",
      bannerLocationList: [
        {
          id: 1,
          name: "第一帧",
        },
        {
          id: 2,
          name: "第二帧",
        },
        {
          id: 3,
          name: "第三帧",
        },
        {
          id: 4,
          name: "第四帧",
        },
        {
          id: 5,
          name: "第五帧",
        },
        {
          id: 6,
          name: "第六帧",
        },
        {
          id: 7,
          name: "第七帧",
        },
        {
          id: 8,
          name: "第八帧",
        },
        {
          id: 9,
          name: "第九帧",
        },
      ],
    };
  },
  methods: {
    open(row, isEdit) {
      this.isEdit = isEdit;
      if (this.isEdit) {
        let keys = Object.keys(row);
        for (let index = 0; index < keys.length; index++) {
          const key = keys[index];
          this.addForm[key] = row[key];
          if(key === 'bannerImg'){
            this.imageList = [{url:row[key]}]
          }
          if(key=='bannerLocation'){
            
            this.addForm[key]=Number(row[key])
          }
          
        }
      }
      this.addDialog = true;
      this.$nextTick(()=>{
        this.$refs['addRuleForm'].clearValidate()
      })
    },
    delImageListRow(val) {
      console.log(val);
    },
    openLink(row) {
      this.currentRow = row;
      this.isShowHrefDialog = true;
    },
    handlePreview(val) {
      console.log(val);
    },

    uploadSuccess(res) {
      if (res.code != 200) {
        this.$message.warning(`[${res.code}]${res.msg}`);
        return;
      }
      this.addForm.bannerImg = res.data.url;
    },

    selectCrowd(e) {
      if (e) {
        this.addForm.crowdId = Number(this.options[0].value.trim());
        this.addForm.crowdValue = this.options[0].label;
      } else {
        this.addForm.crowdId = "";
        this.addForm.crowdValue = "";
      }
      this.$forceUpdate();
    },

    onSetLink(link) {
      this.addForm.hrefUrl = link.meta.page_url;
      this.addFormSelectLink = link.meta.page_url;
    },
    urlChange(link) {
      this.addForm.hrefUrl = link;
      this.addFormSelectLink = link;
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    //打开时间循环
    toloopcirculateTime() {
      this.$refs.loopcirculateTime.circulateTime=this.addForm.circulateTime
          this.$refs.loopcirculateTime.editInit()
      this.$refs.loopcirculateTime.showVisible = true;
    },
    onChange(file, fileList){
      console.log(fileList)
      if (fileList.length > 0) {
        this.imageList = [fileList[fileList.length - 1]]//这一步，是 展示最后一次选择文件
      }
    },
    onRemove(file, fileList){
      this.imageList=[]
      this.addForm.bannerImg=''
    },
    changeCrowdType() {
      this.addForm.crowdId = "";
      this.addForm.crowdValue = "";
    },
    //循环时间回调
    loopcirculateTimeBack(data) {
      this.addForm.circulateTime = data;
    },
    addDialogCancel() {
      this.resetAddForm();
      this.$refs['addRuleForm'].clearValidate()
      this.addDialog = false;
    },

    hrefCancel() {
      this.addFormSelectLink = "";
      this.isShowHrefDialog = false;
    },
    hrefConfirm() {
      this.isShowHrefDialog = false;
    },
    inLinkChange(val) {
      this.addForm.hrefUrlType = val;
    },
    resetAddForm() {
      this.addForm = {
        position: "",
        activityName: "",
        bannerLocation: "",
        crowdType: 1,
        crowdId: "",
        crowdValue: "",
        timeType: 1,
        validityTime: [],
        circulateTime: {},
        bannerImg: "",
        hrefUrlType: "",
        hrefUrl: "",
      };
      this.$refs.uploadImg.clearFiles()
      
    },
    async addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        if(this.addForm.crowdType==2&&!this.addForm.crowdId){
          this.$message.warning("请添人群ID");
          return false;
        }
        if (this.addForm.timeType==2&&(!this.addForm.circulateTime||Object.keys(this.addForm.circulateTime).length==0||!this.addForm.circulateTime.circulateList||this.addForm.circulateTime.circulateList.length==0)) {
          this.$message.warning("请添加[周期循环] 时间段。");
          return false;
        }
        if(this.addForm.timeType==1&&(!this.addForm.validityTime||this.addForm.validityTime.length==0)){
          this.$message.warning("请添加时段");
          return false;
        }
        if(!this.addForm.bannerImg){
          this.$message.warning("请上传图片");
          return false;
        }
         if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.addForm.hrefUrl)) {
          this.$message.error("跳转链接格式不正确，请检查");
          return false;
        }
        let linkErrMsg = '';
       
            let linkPageUrl = getUrlParam(this.addForm.hrefUrl, 'url')
             const loading = this.$loading({
                lock: true,
                text: '校验中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
            const result = await api.topic.checkPageUrl({ url: linkPageUrl });
             loading.close()
            if (((result || {}).data || {}).status != 200) {
              linkErrMsg = '跳转链接不存在，请检查';
            }
        
        if (linkErrMsg) {
          this.$message.error(linkErrMsg);
          return false;
        }

        this.$emit("done", this.addForm);
        //this.$message.success("添加成功！");
        // this.addDialogCancel();
      });
    },
  },
};
</script>

<style scoped>
.cowdtype-radio {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.tableBox {
  width: 100%;
}
.banner-url-type {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
</style>