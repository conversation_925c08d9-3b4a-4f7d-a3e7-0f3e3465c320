<template>
	<div>
		<!--背景图-->
		<div class="bg-img">
			<p height="50px" style="background-color: rgb(19, 194, 194);text-align: center;padding-top: 15px">
				<a @click="imgOnclick" style="cursor: pointer">清除背景图</a>
			</p>
		</div>
		<p class="blank_20"></p>
		<el-upload
			  class="topic-image-upload"
			  ref="upload"
			  accept="image/jpeg,image/jpg,image/png,image/gif"
			  :max-size="1"
			  :show-file-list="false"
			  :before-upload="() => {loading = true; return true;}"
			  :on-success="onUploadImg">
			<el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
			<div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
		</el-upload>
		<p class="blank_20"></p>
		添加购物车按钮：
		<el-checkbox v-model="content.isBtn">{{content.isBtn?'添加':'取消'}}</el-checkbox>
		<p class="blank_20"></p>
		<!--专区跳转连接-->
		<div class="topic-image-info">
			<div class="name">{{linkName}}</div>
			<el-input placeholder="链接地址" v-model="content.link.page_url">
			</el-input>
			<div class="data"></div>
			<div class="del el-icon-delete" @click="onResetLink"></div>
		</div>
		<!--活动标语-->
		<!--选择商品-->
		<all-link @select="onSetLink" :tabs="tabs" :params="{
				page: {
	                branchCode: branchCode
	            },
				goodsGroup: {
					minSel: 1,
					maxSel: 1,
					returnGoods: 0,
                    search: {
                      state: 1,
                        branchCode: branchCode
                    }
                }
             }"></all-link>
	</div>
</template>

<script>
	export default {
		props: {
			pageData: [Array, Object],
			branchCode: String,
			label: String,
			clearNum: {
				type: Number,
				default: 1
			}
		},
		name: "specialArea",
		data() {
			return {
				loading: false,
				tabs: [
					{label: '活动页', value: 'page'},
					{label: '商品组', value: 'goodsGroup'}
				],
				selectItem: [],
				content: {}
			}
		},
		created() {
			this.content = _.cloneDeep(this.pageData)
		},
		computed: {
			linkName() {
				return this.content.link.page_name || ''
			},
			linkData() {
				return this.content.link.page_url || ''
			}
		},
		watch: {
			'content': {
				deep: true,
				handler(val) {
					this.$emit('listenData', {key: this.label, data: val})
				}
			},
			'clearNum': function () {
				this.content = _.cloneDeep(this.pageData)
			}
		},
		methods: {
			imgOnclick() {
				this.content.bgImage = '';
			},
			async onUploadImg(res, file) {
				this.loading = false;
				if (res.code !== 200) {
					this.$message({
						message: `[${res.code}]${res.msg}`,
						type: 'warning'
					})
					return;
				}
				this.content.bgImage = res.data.url;
			},
			onResetLink() {
				this.content.link = {
					page_url: '',
					page_name: ''
				}
			},
			onSetLink(obj) {
				if (obj.tag == 'goodsGroup') {
					let {id, name, goods, code} = obj.data
					this.content.list.length = 0;
					this.content.list.push({
						id: id,
						code:code,
						goodsName: name,
						goodsIds: goods,
						goodsNum: goods.length
					})
				} else {
					let {page_url, page_name} = obj.meta;
					this.content.link = {
						page_url,
						page_name
					};
				}
			}
		}
	}
</script>
<style>
	.topic-image-upload .el-upload {
		width: 100%;
	}
</style>
<style lang="scss" scoped>

	.topic-image-info {
		position: relative;
		overflow: hidden;
		height: 62px;
		padding-bottom: 10px;
		margin: 5px 0;
		border: $border-base;
		font-size: 12px;

		.name {
			position: relative;
			overflow: hidden;
			height: 26px;
			padding: 0 5px;
			margin-bottom: 3px;
			font-size: 14px;
			line-height: 26px;
			border-bottom: $border-base;
		}

		.data {
			position: relative;
			overflow: hidden;
			height: 16px;
			padding: 0 5px;
			line-height: 16px;
			white-space: nowrap;
			text-overflow: ellipsis;
			color: #999;
		}

		.del {
			position: absolute;
			top: 0;
			right: 0;
			padding: 7px;
			border-left: $border-base;
			background: #fff;
			cursor: pointer;

			&:hover {
				background: $color-base-silver;
				color: #fff;
			}
		}
	}
</style>
