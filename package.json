{"name": "Work-GO", "version": "1.0.0", "description": "A Vue.js project", "private": true, "scripts": {"dev": "node build/dev-server.js", "build": "node build/build.js"}, "dependencies": {"axios": "^0.17.0", "bce-sdk-js": "^0.2.8", "element-ui": "2.7.2", "es6-promise": "^4.1.0", "eventbus": "^0.0.3", "fastclick": "^1.0.6", "highlight.js": "^9.12.0", "js-cookie": "^2.1.3", "lodash": "^4.17.4", "node-sass": "^4.14.1", "particles.js": "^2.0.0", "qs": "^6.5.1", "quill": "^1.3.5", "quill-delta": "^3.6.2", "sortablejs": "^1.7.0", "vue": "2.5.4", "vue-amap": "^0.4.1", "vue-awesome-swiper": "^2.6.2", "vue-axios": "^2.0.2", "vue-clipboard2": "^0.3.1", "vue-echarts": "^2.6.0", "vue-quill-editor": "^3.0.5", "vue-router": "^3.0.1", "vue-swipe": "^2.0.3", "vuex": "^3.0.1", "xlsx": "^0.14.1"}, "devDependencies": {"@tinymce/tinymce-vue": "^3.2.8", "autoprefixer": "^6.7.2", "babel-core": "^6.22.1", "babel-loader": "^6.2.10", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-component": "^0.9.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "cross-env": "^4.0.0", "css-loader": "^0.28.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "glob": "^7.1.1", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "loader-utils": "^1.1.0", "lodash": "^4.17.4", "md5": "^2.2.1", "mime": "^1.3.4", "object.assign": "^4.0.4", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "postcss-loader": "^1.3.3", "progress": "^2.0.0", "qrcode": "^0.8.1", "request": "^2.81.0", "rimraf": "^2.6.0", "sass-loader": "^6.0.3", "sass-resources-loader": "2.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "style-loader": "^0.17.0", "tinymce": "^5.7.0", "url-loader": "^0.5.8", "vue-loader": "^13.5.0", "vue-style-loader": "^3.0.3", "vue-template-compiler": "2.5.4", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 3 versions", "not ie <= 8"]}