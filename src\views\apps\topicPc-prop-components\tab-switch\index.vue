<template>
	<div>
		<!--选项卡升级-->
		<p class="blank_20"></p>
		<el-row :gutter="20">
			<el-col :span="4" v-show="isShowTextColor">
				<label>文字颜色:</label>
				<el-color-picker
					  v-model="content.color"
					  size="mini"
				></el-color-picker>
			</el-col>
			<el-col :span="6" v-show="isShowTextColor">
				<label>当前文字颜色:</label>
				<el-color-picker
					  v-model="content.hoverColor"
					  size="mini"
				></el-color-picker>
			</el-col>
			<el-col :span="8">
				<label>当前下划线颜色:</label>
				<el-color-picker
					  v-model="content.lineColor"
					  size="mini"
				></el-color-picker>
			</el-col>
			<el-col :span="6">
				<label>当前背景:</label>
				<el-color-picker
					  v-model="content.bgColor"
					  size="mini"
				></el-color-picker>
			</el-col>
		</el-row>
		<p class="blank_20"></p>
		<!--可以添加纯菜单，也可以添加洗项卡下的菜单-->
		<el-row :gutter="10">
			<el-col :span="6">
				<el-input placeholder="请输入选项卡名称" v-model="tabName" size="mini"></el-input>
			</el-col>
			<el-col :span="18">
				<el-button size="mini" type="primary" @click="addTab">添加选项卡</el-button>
			</el-col>

		</el-row>
		<el-form :model="form" :rules="rules" ref="form" :inline="true" label-width="0">
			<el-form-item prop="name">
				<el-input type="text" size="small" v-model="form.name"
				          autocomplete="off" placeholder="请输入子菜单"></el-input>
			</el-form-item>

			<el-form-item>
				<el-button type="primary" size="mini" @click="handleAdd('form')">添加子菜单</el-button>
				<el-button type="primary" size="mini" @click="handleDelete">删除</el-button>
			</el-form-item>
		</el-form>
		添加导航按钮：
		<el-checkbox v-model="content.isBtn">{{content.isBtn?'添加':'取消'}}</el-checkbox>
		<!--选项卡切换-->
		<el-tabs v-model="activeName" @tab-click="tabClick" closable @tab-remove="removeTab">
			<el-tab-pane v-for="item  in  list" :label="item.name" :name="item.name" :key="item.name">
				<el-table :data="item.content"  border fit highlight-current-row
				          style="width:  100%"
				          v-if="item.content.length>0"
				          @selection-change="handleSelection">
					<el-table-column
						  type="selection"
						  width="55"/>
					<el-table-column prop="title" label="标签名" width="120">
						<template slot-scope="scope">
							<template>
								<el-input v-model.lazy="scope.row.title" class="edit-input" size="mini"/>
							</template>
						</template>
					</el-table-column>
					<el-table-column label="跳转页面" prop="data.page_name">
					</el-table-column>
					<el-table-column label="活动连接">
						<template slot-scope="scope">
							<p class="hide-text">{{scope.row.data.page_url}}</p>
						</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
		</el-tabs>
		<p class="blank_20"></p>
		<all-link @select="onSetLink" :tabs="tabs" :params="{
			page: {
				branchCode: topic.branchCode
			}
		}"></all-link>
	</div>
</template>

<script>
	import base from "../base";
	import {common} from 'api';

	export default {
		extends: base,
		contentDefault: {
			list: [
				{
					"name": "高毛专区",
					"content": [
						{
							"title": "高毛首页",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "心脑血管",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "抗菌消炎",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "消化系统",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "五官用药",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "皮肤外用",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "感冒用药",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "滋补保健",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "妇科用药",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "风湿骨痛",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "辅助器材",
							"componentsName": [],
							"data": {}
						},
						{
							"title": "其他用药",
							"componentsName": [],
							"data": {}
						}
					]
				}
			],
			image: "",
			color: '#2A2A2A',
			hoverColor: '#333',
			lineColor: '#00DC82',
			bgColor:'#EBEBEB',
			isBtn:false,
		},
		data() {
			return {
				form: {name: ""},
				rules: {
					name: [
						{required: true, message: '请输入标签名称', trigger: 'blur'},
						{min: 2, max: 10, message: ' 长度在 3 到 10 个字符', trigger: 'blur'}
					]
				},
				selectItem: [],
				loading: false,
				tabs: [
					{label: '活动页', value: 'page'}
				],
				tabName: '',
				activeName: '',
				currentContent: [],
				currentTabIndex: 0,
				isShowTextColor:false
			}
		},
		computed: {
			list() {
				var list = _.get(this, 'content.list')
				if (list) {
					this.isShowTextColor=this.showColor(list)
					return list
				} else {
					return []

				}
			},

		},
		watch:{
			'content.list':{
				deep:true,
				handler(val){
					if(val.content){
						this.isShowTextColor=this.showColor(val.list)
						this.currentContent=val[this.currentTabIndex].content
					}

				}
			}
		},
		methods: {
			showColor(list){
				return list.some(item=>{
					return item.content.length>1
				})
			},
			addTab() {
				if (!this.tabName) {
					this.$message('请输入选项卡名称')
					return
				}
				if (this.content.list.length > 0) {
					const nameIndex = common.getRepeatResult('name', this.tabName, this.content.list);
					if (nameIndex >= 0) {
						this.$message.warning('您所添加的选项卡名称已经存在啦,请重新添加')
						return
					}
				}
				this.content.list.push({name: this.tabName, content: []})
			},

			removeTab(targetName) {
				const index = common.getRepeatResult('name', targetName, this.content.list);
				this.content.list.splice(index, 1)
			},
			tabClick() {
				this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.list);
				this.currentContent = this.content.list[this.currentTabIndex].content
			},
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},
			handleAdd(formName) {
				// 添加子菜单
				if (this.activeName === '0') {
					this.$message.error('请选中选项卡，再添加')
					return
				}
				this.$refs[formName].validate((valid) => {
					if (valid) {
						// 所添加的标签名不能重复
						// let arr = []
						// this.content.list.forEach(item => {
						// 	arr.push(...item.content)
						// })
						const labelArray = this.currentContent.map(item => {
							return item.title
						})

						const nameIndex = labelArray.indexOf(this.form.name)
						if (nameIndex >= 0) {
							this.$message.warning('您所创建的标签已经存在啦,请重新添加')
							return
						}
						this.currentContent.push({title: this.form.name, data: {page_name: '', page_url: ''}, id: ''})
					} else {
						console.log('error submit!!');
						return false;
					}
				});
			},
			onSetLink(link) {
				if (this.selectItem.length === 0 || this.selectItem.length !== 1)
					return this.$message.warning('请先选中1个标签');
				// 获取所有已选择活动的名称，所选活动不能重复
				// 计算所有的活动名称
				let arr = [];
				this.content.list.forEach(item => arr.push(...item.content));
				let nameArray = arr.map(item => !item.data.page_name ? '' : item.data.page_name);
				let nameIndex = nameArray.indexOf(link.meta.page_name);
				if (nameIndex >= 0)
					return this.$message.warning('您所选的活动已经存在啦,请重新选择');
				let labelArray = this.currentContent.map(item => item.title);
				let index = labelArray.indexOf(this.selectItem[0]['title']);
				let {page_name, page_url, id} = link.meta;
				this.currentContent[index].data = {page_name, page_url};
				this.currentContent[index].id = id;
			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = common.getRepeatResult('title', item.title, this.currentContent)
					this.currentContent.splice(index, 1)
				})
			}

		}

	}
</script>

<style>
	.blank_20 {
		height: 20px
	}
</style>
