<template>
    <div class="member-list">
        <el-row class="mb-10 border-b pb-10">
            <el-col :span="4">
                <span>成员列表：</span>
                <span>{{data.name}}</span>
            </el-col>
            <!--<el-col :span="4">
                <el-button type="primary" size="mini" icon="el-icon-plus" @click="add">添加成员</el-button>
            </el-col>-->
            <el-col :span="16">
                <el-row class="search-wrap" type="flex" :gutter="10">
                    <el-input v-model="searchParam.searchValue" class="input-with-select" size="mini" placeholder="输入查询" clearable>
                        <el-select v-model="searchParam.searchType" slot="prepend" placeholder="请选择">
                            <el-option label="账号" value="userName"></el-option>
                            <el-option label="邮箱" value="mail"></el-option>
                            <el-option label="姓名" value="realName"></el-option>
                            <el-option label="手机" value="mobile"></el-option>
                        </el-select>
                        <el-button slot="append" @click="loadData" icon="el-icon-search"></el-button>
                    </el-input>
                </el-row>
            </el-col>
        </el-row>
        <div v-if="!dataList || !dataList.length" class="empty-wrap"><i class="iconfont icon-tishi"></i><span>尚未添加成员</span></div>
        <el-table v-if="dataList && dataList.length"
                  :data="dataList"
                  v-loading="loading"
                  :row-class-name="tabRowCla"
                  class="custom-table"
                  size="mini"
                  border>
            <el-table-column
                    fixed="left"
                    prop="userName"
                    :show-overflow-tooltip="true"
                    label="登录账号"
                    width="160">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="toInfo(scope.row)">{{ scope.row.userName }}</el-button>
                </template>
            </el-table-column>
            <el-table-column
                    fixed="left"
                    prop="realName"
                    :show-overflow-tooltip="true"
                    label="姓名"
                    width="100">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="toInfo(scope.row)">{{ scope.row.realName }}</el-button>
                </template>
            </el-table-column>
            <el-table-column
                    prop="orgNames"
                    :show-overflow-tooltip="true"
                    width="200"
                    label="所属组织">
            </el-table-column>
            <el-table-column
                    prop="roleNames"
                    :show-overflow-tooltip="true"
                    width="200"
                    label="角色">
            </el-table-column>
            <el-table-column
                    label="状态"
                    width="100">
                <template slot-scope="scope">{{ scope.row.status == 1 ? '已启用' : '已停用' }}</template>
            </el-table-column>
            <el-table-column
                    prop="mobile"
                    :show-overflow-tooltip="true"
                    width="150"
                    label="手机"
                    align="right">
            </el-table-column>
            <el-table-column
                    prop="mail"
                    :show-overflow-tooltip="true"
                    width="200"
                    label="邮箱"
                    align="right">
            </el-table-column>
            <el-table-column
                    width="150"
                    prop="lastLogin"
                    label="最近登录">
                <template slot-scope="scope">{{scope.row.lastLogin | dateFmt}}</template>
            </el-table-column>
            <el-table-column
                    width="150"
                    prop="create_time"
                    label="注册时间">
                <template slot-scope="scope">{{scope.row.create_time | dateFmt}}</template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
    import api from 'api';
    // import { mapGetters } from 'vuex';
    import { fetch, getDate } from '../../../utils/time-format';

    export default {
        name: 'HomeMembers',
        data() {
            return {
                dataList: [],
                loading: false,
                searchParam: {
                    searchType: 'userName'
                }
            }
        },
        props: [ 'data' ],
        watch: {
            data() {
                this.loadData();
            }
        },
	    filters: {
		    dateFmt(date) {
			    return date ? getDate(date) : '';
		    }
	    },
        mounted() {
            this.loadData()
        },
        methods: {
            async loadData() {
                this.loading = true;
                const params = {};
                if(this.data.id){
                    params.orgIds = this.data.id;
                }
                if (this.searchParam.searchType && this.searchParam.searchValue) {
                    // params.searchType = this.searchParam.searchType;
                    params[this.searchParam.searchType] = this.searchParam.searchValue
                }
                const res = await api.user.query(params);
                this.loading = false;
                if (res.code === 200) {
                    this.$nextTick(() => {
                        this.dataList = res.data;
                    })
                } else {
                    this.$message.error(res.msg);
                }
            },
            toInfo(user) {
                this.$router.push({ name: 'sysUserInfo', params: { id: user.id } })
            },
            add() {
                this.$router.push({ name: 'addUser' })
            },
	        tabRowCla({row, i}) {
		        if (row.status != 1)
			        return 'bgc-warn';
		        return '';
	        }
        }
    }
</script>
<style lang="scss" rel="stylesheet/scss">


    .member-list {
        padding-left: 20px;
    }
    .el-table .bgc-warn {
        background: oldlace;
    }
</style>
