<template>
  <div>
    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块基本设置</div>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">背景色</span>
          <div>
            <el-color-picker
              v-model="content.tabs.default_bg"
              size="mini"
              @change="change_default_bg"
            />
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <el-upload
            style="display: inline-block; margin-right: 10px"
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="
              () => {
                loading = true;
                return true;
              }
            "
            :on-success="onUploadBgImage"
          >
            <el-button
              size="mini"
              class="btn-block"
              type="primary"
              :loading="loading"
              >上传背景图</el-button
            >
            <div slot="tip" class="el-upload__tip">
              支持类型：png/jpg/jpeg/gif
            </div>
          </el-upload>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <el-button
            size="mini"
            @click="
              () => {
                content.bgImg = '';
              }
            "
            >清除背景图</el-button
          >
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">默认字颜色</span>
          <div>
            <el-color-picker
              v-model="content.tabs.default_font"
              size="mini"
              @change="change_default_font"
            />
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">激活字颜色</span>
          <div>
            <el-color-picker
              v-model="content.tabs.active_font"
              size="mini"
              @change="change_active_font"
            />
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">激活横线颜色</span>
          <div>
            <el-color-picker
              v-model="content.tabs.active_line"
              size="mini"
              @change="change_active_line"
            />
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">激活背景颜色</span>
          <div>
            <el-color-picker
              v-model="content.tabs.active_bg"
              size="mini"
              @change="change_active_bg"
            />
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">tab分割线颜色</span>
          <div>
            <el-color-picker
              v-model="content.tabs.split_color"
              size="mini"
              @change="change_split_color"
            />
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">tab区域背景颜色</span>
          <div>
            <el-color-picker
              v-model="content.tabs.tab_bg"
              size="mini"
              @change="change_tab_bg"
            />
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 模块有效时间设置 -->
    <el-row :gutter="20" class="brand-time">
      <div class="title">模块有效时间设置</div>
      <el-col :span="24">
        <el-date-picker
          v-model="content.timevalue"
          @change="change_time"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        >
        </el-date-picker>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">人群设置</div>
      <el-col :span="24">
        <el-radio
          v-model="content.crowdType"
          :label="1"
          @change="changeCrowdType"
          >全部人群</el-radio
        >
        <el-radio
          v-model="content.crowdType"
          :label="2"
          @change="changeCrowdType"
          >指定人群</el-radio
        >
        <el-select
          v-if="content.crowdType === 2"
          v-model="content.crowdValue"
          :loading="selectLoading"
          filterable
          :filter-method="optionFilter"
          placeholder="请输入人群id"
          clearable
          size="small"
          @clear="options = []"
          @change="selectCrowd"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">展示个数设置</div>
      <el-col :span="24">
        <el-radio v-model="content.goods_num" :label="3">一页3个</el-radio>
        <el-radio v-model="content.goods_num" :label="6">一页6个</el-radio>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">选项卡名称设置</div>
      <el-col :span="18">
        <el-input placeholder="选项卡名称" v-model="tabName" size="mini" />
      </el-col>
      <el-col :span="6">
        <el-button size="mini" type="primary" @click="add_tab"
          >添加选项卡</el-button
        >
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { common } from "api";
import api from "api";
import { AppWebsite } from "config";
export default {
  props: ["content"],
  data() {
    return {
      options: [],
      selectLoading: false,
      loading: false,
      tabName: "",
      is_select: "spread",
      select_options: [
        {
          value: "spread",
          label: "有展示区",
        },
        {
          value: "no_spread",
          label: "无展示区",
        },
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  methods: {
    // 模块有效时间设置
    change_time() {
      for (let item of this.content.list) {
        this.$set(item, "time", this.content.timevalue);
      }
      this.content.list = this.content.list.map((item) => {
        return item;
      });
    },
    changeCrowdType() {
      this.content.crowdId = "";
      this.content.crowdValue = "";
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    selectCrowd(e) {
      if (e) {
        this.content.crowdId = Number(this.options[0].value.trim());
        this.content.crowdValue = this.options[0].label;
      } else {
        this.content.crowdId = "";
        this.content.crowdValue = "";
      }
      this.$forceUpdate();
    },
    async onUploadBgImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.bgImg = res.data.url;
    },
    change_default_bg(color) {
      this.$emit("change_color", "default_bg", color);
    },
    change_default_font(color) {
      this.$emit("change_color", "default_font", color);
    },
    change_active_font(color) {
      this.$emit("change_color", "active_font", color);
    },
    change_active_line(color) {
      this.$emit("change_color", "active_line", color);
    },
    change_tab_bg(color) {
      this.$emit("change_color", "tab_bg", color);
    },
    change_spread_color(color) {
      this.$emit("change_color", "spread_color", color);
    },
    change_active_bg(color) {
      this.$emit("change_color", "active_bg", color);
    },
    change_split_color(color) {
      this.$emit("change_color", "split_color", color);
    },
    change_list_color(color) {
      this.$emit("change_color", "list_color", color);
    },
    add_tab() {
      if (!this.tabName) {
        this.$message("请输入选项卡名称");
        return;
      }
      if (this.content.goods_list.length >= 10) {
        this.$message("选项卡最多10个");
        return;
      }
      if (this.content.goods_list.length > 0) {
        const nameIndex = common.getRepeatResult(
          "name",
          this.tabName,
          this.content.goods_list
        );
        if (nameIndex >= 0) {
          this.$message.warning("您所添加的选项卡名称已经存在啦,请重新添加");
          return;
        }
      }
      let obj = {
        name: this.tabName,
        goods_group: [],
        list: [],
        type: 0,
        img_url_list: [],
        pageCount: "3",
      };
      this.$emit("add_list", obj);
      this.tabName = "";
    },
  },
  watch: {
    is_select(new_val) {
      this.$emit("change_spread", new_val);
    },
  },
};
</script>

<style scoped lang="scss">
.el-row {
  text-align: center;
  margin-bottom: 10px;
  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
</style>
