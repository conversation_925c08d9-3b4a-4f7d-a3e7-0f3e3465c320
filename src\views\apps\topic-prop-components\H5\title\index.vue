<template>
    <div>
        <el-form size="small" label-width="100px">
            <el-form-item label="文本">
                <el-input v-model="content.text"></el-input>
            </el-form-item>
            <el-form-item label="字号">
                <el-input v-model="content.size"></el-input>
            </el-form-item>
            <el-form-item label="文字颜色">
                <el-color-picker v-model="content.color"></el-color-picker>
            </el-form-item>
            <el-form-item label="背景色">
                <el-color-picker v-model="content.bg_color"></el-color-picker>
            </el-form-item>
            <el-form-item label="对齐方式">
                <el-radio-group v-model="content.align">
                    <el-radio label="left">左</el-radio>
                    <el-radio label="center">中</el-radio>
                    <el-radio label="right">右</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="链接">
                {{content.link | moreLink}}
                <el-button @click="toOpenTopicDialog" type="primary">添加链接</el-button>
            </el-form-item>
            <el-form-item label="间距">
                <el-input style="width: 80px;" v-model="content.top" placeholder="上"></el-input>
                <el-input style="width: 80px;" v-model="content.right" placeholder="右"></el-input>
                <el-input style="width: 80px;" v-model="content.bottom" placeholder="下"></el-input>
                <el-input style="width: 80px;" v-model="content.left" placeholder="左"></el-input>
            </el-form-item>
            <el-form-item label="开始时间">
                <el-date-picker v-model="content.start_time" type="date" placeholder="选择日期"></el-date-picker>
            </el-form-item>

        </el-form>
        <el-dialog class="banner-dialog" title="选择链接" :visible.sync="addTopicDialog">
            <gen-link @select="onSetTopicLink" :tabs="tabs"></gen-link>
        </el-dialog>
    </div>
</template>

<script>
    import base from '../../base'

    export default {
        extends: base,
        contentDefault: {
            text: '',
            size: 16,
            color: '#000000',
            bg_color: '#ffffff',
            align: 'left',
            start_time: ''
        },
        data() {
            return {
                loading: false,
                addDialog: false,
                addTopicDialog: false,
                tabs: [
                    { label: '页面', value: 'topic', params: {branchCode : this.topic.branchCode}},
                ],
                dataForm: {
                    image: '',
                    link: {},
                },
            }
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            },
            moreLink(data) {
                if (!data || !data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            }
        },
        methods: {
            toOpenTopicDialog() {
                this.addTopicDialog = true;
            },
            onSetTopicLink(link) {
                this.addTopicDialog = false;
                //this.link = link;
                this.$set(this.content, 'link', link)
            },
        }
    }
</script>
