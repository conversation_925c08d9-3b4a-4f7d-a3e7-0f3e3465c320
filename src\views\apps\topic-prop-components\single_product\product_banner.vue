<template>
    <div class="topic-menu-list">
        <div class="container">
            <div class="container">
                <div style="margin: 10px 0">
                    设置banner的背景色:
                    <el-color-picker v-model="content.bg_color" size="mini"
                                     @change="clear_self_bg_img"></el-color-picker>
                    <el-button @click="banner_bg_dialog=true" type="primary">上传banner的背景图片</el-button>
                    <img :src="content.bg_img" alt="" width="40" height="40">
                </div>
                <div>
                    <h3>banner的商品</h3>
                    <el-table :data="list" style="width: 100%;margin-top: 5px" height="250"
                              ref="multipleTable">
                        <el-table-column fixed label="图片" width="80">
                            <template slot-scope="scope">
                                <img :src="scope.row.init_img_url" :alt="scope.row.productName"
                                     style="width:100%;max-height:50px;" v-if="scope.row.init_img_url">
                                <img :src="scope.row.imageUrl" :alt="scope.row.productName"
                                     style="width:100%;max-height:50px;"
                                     v-else>
                            </template>
                        </el-table-column>
                        <el-table-column prop="productName" label="药名" width="120">
                            <template slot-scope="scope">
                                <span v-if="scope.row.productName">{{scope.row.productName}}</span>
                                <span v-else>{{scope.row.showName}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="规格" width="80">
                            <template slot-scope="scope">
                                {{scope.row.mediumPackageTitle}}
                            </template>
                        </el-table-column>
                        <el-table-column prop="fob" label="价格" width="80">
                            <template slot-scope="scope">
                                {{scope.row.fob}}
                            </template>
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" width="100">
                            <template slot-scope="scope">
                                <div class="edit-button">
                                    <el-button @click="handleDelete(scope.row)" type="warning" size="mini">删除
                                    </el-button>
                                </div>
                                <div class="edit-button" style="margin-top: 5px">
                                    <el-button @click="add_img(scope.row)" type="primary" size="mini">上传图片
                                    </el-button>
                                </div>

                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>

            <!--选择商品-->
            <all-link @select="onSetLink" :tabs="tabs" :params="{
                productlink: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                importGoods: {
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                goodsGroup: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
            }"></all-link>

            <el-dialog class="banner-dialog" title="添加图片" :visible.sync="banner_bg_dialog">
                <el-upload
                        class="topic-image-upload"
                        ref="upload"
                        accept="image/jpeg,image/jpg,image/png,image/gif"
                        :show-file-list="false"
                        :before-upload="() => {loading = true; return true;}"
                        :on-success="upload_bg_img">
                    <img v-if="content.bg_img"
                         :src="content.bg_img" class="image">
                    <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                    <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                </el-upload>
                <div slot="footer" class="dialog-footer">
                    <el-button size="small" @click="banner_bg_dialog=false">取 消</el-button>
                    <el-button size="small" type="primary" @click="banner_bg_dialog=false">确定</el-button>
                </div>
            </el-dialog>


        </div>

        <!--选择商品图片-->
        <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addDialog">
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="onUploadImage">
                <img v-if="content.banner_list.length
                &&content.banner_list[cur_index]
                &&content.banner_list[cur_index].init_img_url"
                     :src="content.banner_list[cur_index].init_img_url" class="image">
                <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="confirm">取 消</el-button>
                <el-button size="small" type="primary" @click="confirm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import base from "../base";
    import {common} from 'api'

    export default {
        extends: base,
        contentDefault: {
            banner_list: [],
            bg_color: "#FC4340",
            bg_img: "",
        },
        data() {
            return {
                cur_index: 0,
                loading: false,
                addDialog: false,
                banner_bg_dialog: false,
                tabs: [
                    {label: '商品', value: 'productlink'},
                    {label: '导入商品', value: 'importGoods'},
                    {label: '商品组', value: 'goodsGroup'}
                ]
            };
        },
        computed: {
            list() {
                let list = _.get(this, 'content.banner_list');
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
        },
        methods: {
            clear_self_bg_img() {
                this.content.bg_img = ""
            },
            async upload_bg_img(res) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    });
                    return;
                }
                this.$set(this.content, "bg_img", res.data.url)
                this.$set(this.content, "bg_color", "")
            },
            confirm() {
                this.addDialog = false;
            },
            add_img(row) {
                const index = this.content.banner_list.indexOf(row);
                this.cur_index = index;
                this.addDialog = true;
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: "warning"
                    });
                    return;
                }
                this.$set(this.content.banner_list[this.cur_index], "init_img_url", res.data.url)
            },
            onSetLink(link) {
                function handle_arr(arr = []) {
                    return arr.map((item) => {
                        let obj = {};
                        obj.init_img_url = item.init_img_url;
                        obj.imageUrl = item.imageUrl;
                        obj.productName = item.productName;
                        obj.showName = item.showName;
                        obj.mediumPackageTitle = item.mediumPackageTitle;
                        obj.fob = item.fob;
                        obj.id = item.id;
                        return obj
                    });
                }

                if (link.tag === "goods" || link.tag === "importGoods") {
                    let _self_arr = handle_arr(link.data);
                    if (this.content.banner_list.length > 0) {
                        this.content.banner_list = [...common.removeRepeat(this.content.banner_list, _self_arr)]
                    } else {
                        this.content.banner_list = [..._self_arr]
                    }
                } else if (link.tag === "goodsGroup") {
                    let _self_arr = handle_arr(link.data._goods);
                        this.content.banner_list = [..._self_arr]
                }
            },
            handleDelete(row) {
                const index = this.content.banner_list.indexOf(row)
                this.content.banner_list.splice(index, 1)
            },
        }
    }
</script>
<style scoped lang="scss">
    .init_topic-banner {
        .banner-dialog {
            .el-dialog__body {
                padding-top: 10px;
            }
        }

        .init_container {
            display: flex;
            align-items: center;

            .img {
                width: 65%;

                img {
                    display: block;
                    width: 100%;
                }
            }

            .button-list {
                margin-left: 10px;
            }
        }

        .link-desc {
        }

        .topic-image-upload {
            .image {
                display: block;
                width: 100%;
            }

            .uploader-icon {
                width: 200px;
                height: 200px;
                line-height: 200px;
                border: 1px solid #8b8b8b;
                font-size: 50px;
            }
        }

        .topic-image-picker {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }

    .container {
        margin: 10px auto;
        padding-bottom: 10px;
    }

    .topic-image-upload {
        .image {
            display: block;
            width: 100%;
        }

        .uploader-icon {
            width: 200px;
            height: 200px;
            line-height: 200px;
            border: 1px solid #8b8b8b;
            font-size: 50px;
        }
    }


</style>
