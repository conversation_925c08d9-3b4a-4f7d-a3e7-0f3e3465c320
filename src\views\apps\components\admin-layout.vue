<template>
  <el-container class="admin-page">
    <el-header class="top-wrap" height="50px">
      <Topbarold v-if="this.menu_type_state == 0" :branchs="branchs" :departmentList="departmentList" :type="topbarType" :key="topbarType"></Topbarold>
      <Topbarapp v-if="this.menu_type_state == 1" :branchs="branchs" :departmentList="departmentList"></Topbarapp>
      <Topbarpc v-if="this.menu_type_state == 2" :branchs="branchs" :departmentList="departmentList"></Topbarpc>
    </el-header>
    <el-container :style="{height: contentHeight}">
      <el-aside width="200px" v-show="isShow">
        <Sidebar></Sidebar>
      </el-aside>
      <el-container>
        <el-header class="breadcrumb-layout" height="auto">
          <Breadcrumb></Breadcrumb>
        </el-header>
        <el-main class="main-layout">
          <slot></slot>
        </el-main>
      </el-container>
    </el-container>
  </el-container>
</template>

<script>
import dict from "api/dict";
import user from "api/user";
import Topbarold from "./topbarold";
import Topbarapp from "./topbarapp";
import Topbarpc from "./topbarpc";
import Sidebar from "./sidebar";
import Breadcrumb from "./breadcrumb";
import bus from "utils/eventbus";

export default {
  name: "AdminLayout",
  components: {
    Topbarapp,
    Topbarpc,
    Topbarold,
    Sidebar,
    Breadcrumb
  },

  data() {
    return {
      contentHeight: window.innerHeight - 50 + "px",
      menu_type_state: 0, //app1,pc2，other0，默认app
      branchs: [],
      topbarType: 'app',
      departmentList: [
        {
          id: 1,
          name: "药帮忙"
        },
        {
          id: 2,
          name: "控销"
        },
        {
          id: 8,
          name: "宜块钱"
        },
        {
          id: 9,
          name: "KA"
        }
      ]
    };
  },

  computed: {
    isShow() {
      return this.$store.getters["sideBar/isShow"];
    }
  },

  mounted() {
    const self = this;
    window.addEventListener("resize", () => {
      self.contentHeight = window.innerHeight - 50 + "px";
    });
    const branchs = dict.branchHasOpen().then((res) => {
      if (res.code === 200) {
        // res.data.unshift({
        //   branchName: "全国",
        //   branchCode: ""
        // })
        this.branchs = res.data
      }
    })
    // this.getInfo();
    bus.$on("menu_type", (type = "app") => {
      switch (type) {
        case "app":
          this.menu_type_state = 1;
          break;
        case "pc":
          this.menu_type_state = 2;
          break;
        default:
          this.menu_type_state = 0;
          this.topbarType = type;
          bus.$emit("topbarold_name", type);
          break;
      }
    });
  },

  methods: {
    getInfo() {},
  }
};
</script>

<style lang="scss" rel="stylesheet/scss">
.admin-page {
  .top-wrap {
    padding: 0;
  }
  .breadcrumb-layout,
  .main-layout {
    padding: 0;
  }
}
</style>
