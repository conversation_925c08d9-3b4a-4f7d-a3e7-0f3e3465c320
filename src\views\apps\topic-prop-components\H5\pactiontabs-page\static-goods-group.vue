<template>
	<div class="static-goods-group">
		<!--滚动商品组-->
        <div class="title">挑好货商品组</div>
		背景颜色:
		<el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>
		<p class="blank_20"></p>
		<el-button type="primary" @click="handleDelete" size="mini">删除</el-button>
		<p class="blank_20"></p>
		<el-table :data="goodslist" :row-key="getRowKeys" border fit highlight-current-row style="width: 100%"
		          v-if="goodslist && goodslist.length>0"
		          @selection-change="handleSelection">
			<el-table-column
				  type="selection"
				  width="55"/>
			<el-table-column label="商品组名" prop="goodsName">
			</el-table-column>
			<el-table-column label="跳转页面" prop="page_name">
			</el-table-column>
			<el-table-column label="活动图片">
				<template slot-scope="scope">
					<upload-image :index="scope.$index" :image="scope.row.image"
					              v-on:listenImage="getImage"></upload-image>
				</template>
			</el-table-column>
		</el-table>
		<p class="blank_20"></p>
		<all-link @select="onSetLink" :tabs="tabs" :params="{
				page: {
	                branchCode: topic.branchCode
	            },
				goodsGroup:{
					radio:0,
					returnGoods: 0,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    },
                    data: {
                        ids: goodsIds
                    }
                }
             }"></all-link>

	</div>
</template>

<script>
	import uploadImage from 'views/apps/components/upload-image'
	import {common} from 'api'

	export default {
        props: ['content', 'topic','currentTabIndex'],
		data() {
			return {
				tabs: [
					{label: '活动页', value: 'page'},
					{label: '商品组', value: 'goodsGroup'}
				],
				goodsIds: [],
				selectItem: [],
				loading: false
			}
		},
		computed: {
			goodslist() {
                var list = this.content.goods_list[this.currentTabIndex].static_goods_list;
				if (list) {
					if (list.length > 0) {
						// this.$nextTick(function () {
						// 	this.setSort()
						// })
					}
					return list
				} else {
					return []
				}
			}
		},
		components: {
			uploadImage
		},
		methods: {
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},

			getRowKeys(row) {
				if (!row.id) {
					return
				}
				return row.id
			},
			onSetLink(obj) {
				if (obj.tag == 'goodsGroup') {
					obj.data.forEach((item, index) => this.goodslist.push({
						id: item.id,
						goodsName: item.name,
						code:item.code,
						goodsIds: item.goods,
						image: '',
						page_url: '',
						page_name: ''
					}));
				} else {
					if (this.selectItem.length === 0 || this.selectItem.length !== 1)
						return this.$message.warning('请先选中1个标签');
					let index = this.goodslist.indexOf(this.selectItem[0]);
					this.goodslist[index].page_url = obj.meta.page_url;
					this.goodslist[index].page_name = obj.meta.page_name;
				}
			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = this.goodslist.indexOf(item)
					this.goodslist.splice(index, 1)
				})
			},
			getImage(data) {
				if (data.image) {
					this.goodslist[data.index].image = data.image
				}
			}
		}
	}
</script>

<style scoped lang="scss">
.static-goods-group {
    .title {
        text-align: left;
        line-height: 30px;
        background-color: #f2f2f2;
        margin: 10px 0;
        padding-left: 10px;
    }
}
</style>
