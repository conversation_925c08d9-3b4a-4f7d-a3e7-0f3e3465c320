<template>
    <div>
        <div class="auth-login" id="login">
            <div class="auth-login-title">
                CMS内容管理系统
            </div>
            <div class="auth-login-form">
                <el-form :model="auth" :rules="rules" ref="authForm" @keyup.enter.native="onSubmit">
                    <el-form-item prop="account">
                        <el-input v-model="auth.userName" name="account" ref="userName" placeholder="用户名"></el-input>
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input v-model="auth.password" name="password" type="password" ref="password" placeholder="密码"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-checkbox id="remember-account" v-model="isRememberAccount">记住帐号</el-checkbox>
                    </el-form-item>
                    <el-form-item class="submit-btn-wrap">
                        <el-button
                                id="submit-button"
                                type="primary"
                                class="submit"
                                size="large"
                                :disabled="sending"
                                :loading="sending"
                                @click.native.prevent="onSubmit">
                            {{ sending ? '处理中...' : '确定' }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script>
    import api from 'api';
    import md5 from 'md5';

    export default {
        name: 'LoginForm',
        data() {
            return {
                auth: {
                    userName: '',
                    password: ''
                },
                sending: false,
                rules: {
                    userName: [
                        { required: true, message: '请输入登录帐号', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请输入密码', trigger: 'blur' }
                    ]
                },
                isRememberAccount: false
            }
        },
        mounted() {
            let userId = window.localStorage.getItem('userId');
            if (!userId) {
            	var ref = 'userName';
            } else {
            	var ref = 'password';
                this.auth.userName = userId;
                this.isRememberAccount = true;
            }
	        this.$refs[ref].$el.querySelector('input').focus(); //输入框获取焦点
        },
        methods: {
            onSubmit() {
                this.auth.userName = this.auth.userName.trim();
                this.$refs.authForm.validate((valid) => {
                    if (valid) {
                        this.sending = true;
                        this.login();
                    }
                });
            },
            async login() {
                this.$store.dispatch('reset');
                const params = Object.assign({}, this.auth);
                params.password = md5(params.password);
                let result = await api.auth.login(params);
                this.sending = false;
                if (result.code == 200) {
                    if (this.isRememberAccount) {
                        window.localStorage.setItem('userId', this.auth.userName);
                    } else {
                        window.localStorage.removeItem('userId');
                    }
                    localStorage.setItem('token', result.data);

                    const menuList = await api.menu.list();
                    localStorage.setItem('menuList', JSON.stringify(menuList.data));
                    //this.$store.dispatch('sys/updateCurrentMenuList', menuList.data);
                    //this.$router.addRoutes([{ path: '/', redirect:menuList.data[0].actionUrl}])
                    let sts = menuList.data[0].actionUrl.split("/apps/");
                    this.$router.replace({ name: sts[1] });
                } else {
                    this.$message.error(result.msg);
                }
            }
        }
    }
</script>

<style lang="scss" rel="stylesheet/scss">
    .auth-login {
        position: relative;
        background: #fff;
        width: 500px;
        margin: 100px auto;
        border-radius: 10px;
        box-shadow: 0 3px 6px rgba(0, 0, 0, .15);
        padding-bottom: 30px;
    }

    .auth-login-title {
        font-size: 20px;
        padding: 30px 0 25px;
        text-align: center;
    }

    .auth-login-form {
        padding: 0 80px;
    }

    .submit-btn-wrap {
        text-align: center;
    }

    #submit-button {
        width: 240px;
        border-radius: 50px;
    }
</style>
