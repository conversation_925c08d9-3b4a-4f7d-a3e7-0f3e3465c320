<template>
	<div>
		<div class="mb-10">
			<el-row class="mb-5">
				<el-date-picker
						v-model="content.timevalue"
						:picker-options="dateOptions"
						:default-time="['08:00:00', '23:59:59']"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						type="datetimerange"
						align="center"
						size="small"
				></el-date-picker>
			</el-row>
			<el-row>
				<el-col :span="5">
					<el-tooltip placement="top" :open-delay="500">
						<div slot="content">
							<h4>小提示：</h4>
							<ul class="tips ml-20">
								<li>支持选项搜索；</li>
								<li>没有可选项时，可自行创建“时机编码（数字、英文）”；</li>
								<li>
									<i>建议：将自行创建的“时机编码”告知开发者，以完善可选项。</i>
								</li>
							</ul>
						</div>
						<span class="el-icon-info ml-10">
							<span class="ml-5">提醒时机：</span>
						</span>
					</el-tooltip>
				</el-col>
				<el-col :span="15">
					<el-select v-model.trim="content.occasion" @change="ocsVali" placeholder="提醒时机（何时提醒）" size="small" allow-create default-first-option filterable clearable>
						<el-option
								v-for="(v, k) in tipOccasion"
								:value="k"
								:label="v"
						></el-option>
					</el-select>
				</el-col>
			</el-row>
		</div>
		<div>
			<el-table :data="content.tips" size="mini" border>
				<el-table-column prop="img" align="center" width="80" label="图片">
					<template slot-scope="scope">
						<el-popover
								v-if="scope.row.img"
								placement="left"
								trigger="focus">
							<img :src="scope.row.img" class="avatar" alt="图片" />
							<img :src="scope.row.img" class="avatar tab-img" alt="图片" slot="reference" />
						</el-popover>
						<i v-else class="el-icon-plus tab-no-img" @click="edit(scope.row, scope.$index)"></i>
					</template>
				</el-table-column>
				<el-table-column prop="link" align="center" label="跳转链接">
					<template slot-scope="scope">
						<el-popover :disabled="!scope.row.link" placement="top" trigger="hover" :open-delay="800">
							<a target="_blank" :href="scope.row.link">
								<el-button type="text" size="mini">{{ scope.row.link }}</el-button>
							</a>
							<el-input type="text" size="mini" v-model.trim="scope.row.link" slot="reference" placeholder="编辑链接" clearable></el-input>
						</el-popover>
					</template>
				</el-table-column>
				<el-table-column align="center" width="110">
					<template slot="header" slot-scope="scope">
						<el-button size="mini" type="success" icon="el-icon-plus" @click="add" plain round></el-button>
					</template>
					<template slot-scope="scope">
						<el-button-group>
							<el-button size="mini" type="primary" icon="el-icon-edit" @click="edit(scope.row, scope.$index)"></el-button>
							<el-button size="mini" type="danger" icon="el-icon-delete" v-popover="`delPov_${scope.$index}`"></el-button>
						</el-button-group>
						<el-popover
								trigger="click"
								placement="top"
								:ref="`delPov_${scope.$index}`"
								width="80">
							<p>确定删除吗？</p>
							<div class="mr-10 dialog-footer">
								<el-button type="text" @click="del(scope)">确定</el-button>
							</div>
						</el-popover>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<edit :params="editPm" @commit="commit" @close="close" ref="activityTipsEdit"></edit>
	</div>
</template>
<script>
	import base from '../base';
	import edit from './edit';

	export default {
		name: 'activityTips',
		extends: base,
		data() {
			return {
				editPm: {
					data: {
						imgs: []
					}
				},
				dateOptionDict: {
					7: '未来一周',
					30: '未来一个月',
					91: '未来三个月',
					183: '未来半年',
					365: '未来一年'
				},
				tipOccasion: {  //多处使用时，应写在服务端，并提供接口
					/*init: '首次加载',
					manual: '手动触发',
					timer: '定时',
					disabled: '禁用',
					40: '每次加载（强制）'*/
					0 : "仅一次",
					1 : "每日一次"
				}
			};
		},
		computed: {
			dateOptions() {
				let shortcuts = [];
				let dict = this.dateOptionDict;
				for (let k in dict)
					shortcuts.push({
						text: dict[k],
						onClick(pick) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
							pick.$emit('pick', [start, end]);
						}
					});
				return { shortcuts };
			}
		},
		methods: {
			add() {
				let tips = this.content.tips;
				for (var i = 0, len = tips.length; i < len; i++)
					if (!Object.keys(tips[i]).length) { //已含有空Object，不再添加
						this.edit(tips[i], i);
						return;
					}
				let tip = {};
				// tips.push(tip);
				this.edit(tip);
			},
			edit(data, idx) {
				this.editPm.index = idx;
				this.editPm.branchCode = this.topic.branchCode;
				data = data || {};
				if (!data.imgs)
					data.imgs = [];
				if (data.img)   //添加图片上传数组（el-upload组件有bug）
					data.imgs[0] = {
						url: data.img
					};
				this.editPm.data = _.cloneDeep(data);
				this.$refs['activityTipsEdit'].show(true);
			},
			/**
			 * “提醒时机”验证
			 * @param v {@link String}
			 */
			ocsVali(v) {
				if (!v || !(v = v.trim()))
					return;
				let reg = /^[a-zA-Z\d]+$/;
				if (!reg.test(v)) {
					this.content.occasion = null;
					this.$notify.warning('提醒时机编码，只能为数字、英文');
				}
			},
			del(scope) {
				this.content.tips.splice(scope.$index, 1);
			},
			close() {
				this.editPm = {
					data: {
						imgs: []
					}
				};
			},
			commit(rs) {
				if (rs.data.imgs && rs.data.imgs.length) {  //切换图片数组后删除
					rs.data.img = rs.data.imgs[rs.data.imgs.length - 1];
					delete rs.data.imgs;
				}
				if (rs.index >= 0)
					this.$set(this.content.tips, rs.index, rs.data);
				else
					this.content.tips.push(rs.data);
				this.close();
			}
		},
		components: {
			edit
		},
		created() {
			if (!this.content.tips) {
				// let website = await api.dict.cdnWebsite();
				let website = {data: 'https://upload.ybm100.com/'};
				this.content.occasion = "0";
				this.content.tips = [ {
					img: `${website.data}ybm/app/layout/faee645e-5e63-4092-8fa5-ccdbab7dedde.png`
				}, {
					img: `${website.data}ybm/app/layout/118ef275-a77d-48a6-9ffe-b8489deef887.png`,
					link: 'ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/temp/chunjiewltzah.html?ybm_title=春节物流通知'
				} ];
			}
		}
	}
</script>
<style scoped>
	.tab-img {
		max-width: 100%;
		max-height: 30px;
		margin: auto;
	}
	.tab-no-img {
		font-size: 20px;
		color: #8c939d;
		width: 100%;
		text-align: center;
	}
	.tips li {
		list-style: square;
	}
</style>
