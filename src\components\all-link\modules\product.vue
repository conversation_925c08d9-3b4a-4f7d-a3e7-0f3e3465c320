<template>
  <div class="product-link">
    <el-input
      size="mini"
      class="mb-10"
      v-model="key"
      @keyup.enter.native="getList()"
      placeholder="请输入商品"
      clearable
    >
      <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
    </el-input>

    <el-table
      size="mini"
      :data="list"
      highlight-current-row
      @current-change="onSelect"
      style="margin-bottom:5px"
      v-loading="loading"
    >
      <el-table-column label="商品名称" width="230">
        <template slot-scope="scope">
          <p>{{scope.row.showName}}</p>
        </template>
      </el-table-column>
      <el-table-column label="库存数">
        <template slot-scope="scope">{{scope.row.availableQty}}</template>
      </el-table-column>
      <el-table-column label="图片" width="100">
        <template slot-scope="scope">
          <img style="display:block;width:100%;" :src="scope.row.imageUrl" />
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      small
      layout="pager"
      :current-page="pagination.current"
      :page-size="pagination.size"
      :total="pagination.total"
      @current-change="getList"
    ></el-pagination>
  </div>
</template>

<script>
import api from "api";
import { AppWebsite } from "config";

export default {
  data() {
    return {
      id: "",
      key: "",
      sku_key: "",
      list: [],
      pagination: {
        size: 5,
        current: 1,
        total: 0
      },
      loading: false,
      manualId: ""
    };
  },
  methods: {
    async getList(page = 1) {
      this.pagination.current = page;
      this.pagination.size = 5;
      this.loading = true;
      const params = {
        offset: this.pagination.current,
        limit: this.pagination.size
      };

      const searchParam = {
        showName: this.key || ""
      };
      let pms = Object.assign(params, searchParam);
      const result = await api.goods.selectGoods(pms);
      this.loading = false;
      if (result.code == 200) {
        let page = result.data;
        this.$nextTick(() => {
          this.list = page.list;
          this.pagination.total = page.total;
        });
      } else {
        this.$message.error(result.msg);
      }
    },
    onSelect(row) {
      if (!row) return;
      this.$emit("select", {
        type: "item",
        label: "商品",
        id: row.showName,
        desc: row.code,
        action: "GO_DETAILS",
        meta: row
      });
    }
  },
  mounted() {
    this.getList();
  },
  filters: {
    tradeType(value) {
      return (
        {
          1: "国内贸易",
          2: "跨境贸易"
        }[value] || ""
      );
    }
  }
};
</script>
<style lang="scss" scoped rel="stylesheet/scss">
.product-link {
  border: 1px solid #0cdcdc;
  padding: 3px;
}
</style>