<template>
    <div>
        <!--模块背景设置-->
        <el-row :gutter="20">
            <div class="title">模块基本设置</div>
            <el-col :span="6">
                <div class="block">
                    <span class="demonstration">默认字体颜色</span>
                    <div>
                        <el-color-picker v-model="content.tabs.default_font"
                                         size="mini"
                                         @change="change_default_font">
                        </el-color-picker>
                    </div>
                </div>
            </el-col>
            <el-col :span="6">
                <div class="block">
                    <span class="demonstration">激活字体颜色</span>
                    <div>
                        <el-color-picker v-model="content.tabs.active_font"
                                         size="mini"
                                         @change="change_active_font">
                        </el-color-picker>
                    </div>
                </div>
            </el-col>
            <el-col :span="6">
                <div class="block">
                    <span class="demonstration">激活横线颜色</span>
                    <div>
                        <el-color-picker v-model="content.tabs.active_line"
                                         size="mini"
                                         @change="change_active_line">
                        </el-color-picker>
                    </div>
                </div>
            </el-col>
            <el-col :span="6">
                <div class="block">
                    <span class="demonstration">tab区域背景颜色</span>
                    <div>
                        <el-color-picker v-model="content.tabs.tab_bg"
                                         size="mini"
                                         @change="change_tab_bg">
                        </el-color-picker>
                    </div>
                </div>
            </el-col>
            <el-col :span="6">
                <div class="block">
                    <span class="demonstration">是否有展开区域</span>
                    <div>
                        <el-select v-model="is_select"
                                   size="mini"
                                   placeholder="请选择">
                            <el-option
                                    v-for="item in select_options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </el-col>
            <el-col :span="6">
                <div class="block" v-if="is_select==='spread'">
                    <span class="demonstration">展开区域背景颜色</span>
                    <div>
                        <el-color-picker v-model="content.tabs.spread_color"
                                         size="mini"
                                         @change="change_spread_color">
                        </el-color-picker>
                    </div>
                </div>
            </el-col>

            <el-col :span="6">
                <div class="block">
                    <span class="demonstration">列表区域背景色</span>
                    <div>
                        <el-color-picker v-model="content.list_color"
                                         size="mini"
                                         @change="change_list_color">
                        </el-color-picker>
                    </div>
                </div>
            </el-col>

            <!--
            <el-col :span="6">
              <div class="block">
                <span class="demonstration">默认背景颜色</span>
                <div>
                  <el-color-picker v-model="content.tabs.bg_color"
                                   size="mini"
                                   @change="change_bgColor">
                  </el-color-picker>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="block">
                <span class="demonstration">激活背景颜色</span>
                <div>
                  <el-color-picker v-model="content.tabs.bg_color"
                                   size="mini"
                                   @change="change_bgColor">
                  </el-color-picker>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="block">
                <span class="demonstration">列表的背景颜色</span>
                <div>
                  <el-color-picker v-model="content.tabs.body_color"
                                   size="mini"
                                   @change="change_body_color"></el-color-picker>
                </div>
              </div>
            </el-col>
            -->
        </el-row>

        <el-row :gutter="20">
            <div class="title">选项卡名称设置</div>
            <el-col :span="18">
                <el-input placeholder="选项卡名称"
                          v-model="tabName"
                          size="mini"></el-input>
            </el-col>
            <el-col :span="6">
                <el-button size="mini" type="primary" @click="add_tab">添加选项卡</el-button>
            </el-col>
        </el-row>
    </div>

</template>

<script>

    import {common} from 'api'

    export default {
        props: ['content'],
        data() {
            return {
                tabName: "",
                is_select:"spread",
                select_options: [
                    {
                        value: 'spread',
                        label: '有展示区'
                    },
                    {
                        value: 'no_spread',
                        label: '无展示区'
                    },
                ]
            }
        },
        methods: {
            change_default_font(color) {
                this.$emit("change_color", "default_font", color)
            },
            change_active_font(color) {
                this.$emit("change_color", "active_font", color)
            },
            change_active_line(color) {
                this.$emit("change_color", "active_line", color)
            },
            change_tab_bg(color) {
                this.$emit("change_color", "tab_bg", color)
            },
            change_spread_color(color) {
                this.$emit("change_color", "spread_color", color)
            },
            change_list_color(color) {
                this.$emit("change_color", "list_color", color)
            },
            add_tab() {
                if (!this.tabName) {
                    this.$message('请输入选项卡名称')
                    return
                }

                if (this.content.goods_list.length > 0) {
                    const nameIndex = common.getRepeatResult('name', this.tabName, this.content.goods_list);
                    if (nameIndex >= 0) {
                        this.$message.warning('您所添加的选项卡名称已经存在啦,请重新添加')
                        return
                    }
                }
                let obj = {
                    name: this.tabName,
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                    static_goods_list: []
                };
                this.$emit("add_list", obj)
                this.tabName = ""
            },
        },
        watch:{
            is_select(new_val){
                this.$emit("change_spread", new_val)
            }
        }

    }
</script>

<style scoped lang="scss">
    .el-row {
        text-align: center;

        .title {
            text-align: left;
            line-height: 30px;
            background-color: #f2f2f2;
            margin: 10px 0;
            padding-left: 10px;
        }
    }

</style>
