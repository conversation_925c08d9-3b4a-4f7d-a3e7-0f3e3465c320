<template>
  <el-dialog class="banner-dialog" :iconUrl="isEdit ? '编辑词条' : '添加词条'" :visible.sync="visible" :before-close="closeEditContent">
    <el-form ref="form" :model="editData" label-width="120px">
      <el-form-item label="词条名称：">
        <el-input v-model="editData.entryName" class="entry-name"></el-input>
      </el-form-item>
      <el-form-item label="有效时间：">
        <el-date-picker
          v-model="editData.timevalue"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="词条icon：">
        <el-upload
          class="topic-image-upload"
          ref="upload"
          accept="image/jpeg,image/jpg, image/png, image/gif"
          :show-file-list="false"
          :before-upload="() => {editImgLoading = true; return true;}"
          :on-success="uploadEditContImage"
        >
          <div v-if="editData.iconUrl" class="imgBox">
            <img :src="editData.iconUrl" class="image" />
          </div>
          
          <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
          <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
        <!-- <span>建议尺寸：1242*350px</span> -->
      </el-form-item>
      <hr class="hr" />
      <el-form-item class="infoItem" label="链接类型：">
        <el-select v-model="editData.linkType" placeholder="请选择">
          <el-option
            v-for="item in linkOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <div class="topic-image-picker" v-if="editData.linkType !== 'dynamic'">
      <el-input placeholder="链接地址" v-model="editData.page_url">
        <template slot="prepend">跳转链接</template>
      </el-input>
    </div>

    <div v-if="editData.linkType === 'dynamic'">
      <div class="topic-image-picker">
        <el-input style="width:200px" placeholder="输入跳转id" v-model="editData.dynamicId">
          <template slot="prepend">跳转id</template>
        </el-input>
          <el-button type="primary" @click="putDynamicLink">生成链接</el-button>
      </div>
      <el-input placeholder="链接地址" v-model="editData.page_url">
        <template slot="prepend">跳转链接</template>
      </el-input>
    </div>

    <div v-if="editData.linkType==='topic'">
      <page-link @select="onSetLink($event)" :params="{branchCode: branchCode}"></page-link>
    </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="closeEditContent">取 消</el-button>
      <el-button size="small" type="primary" @click="add_confirmEdit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
  export default {
    name: 'wordsDialog',
    props:[ "visible", "branchCode", "adItemData", "editIndex" ],
    data() {
      return {
        editImgLoading: false,
        maxNumber: 100, 
        editData: {
          linkType: "topic",
          iconUrl: "",
          entryName: "",
          operation: "",
          timevalue: "",
          page_url: ""
        },
        linkOptions: [
          {
            value: "topic",
            label: "专题页链接"
          }, {
            value: "stores",
            label: "店铺页链接"
          }, {
            value: "dynamic",
            label: "动态商品链接"
          },
          {
            value: "redEnvelope",
            label: "绑定微信链接"
          }
        ],
        pickerOptions: {
          shortcuts: [
            {
              text: "未来一周",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来三个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来六个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一年",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                picker.$emit("pick", [start, end]);
              }
            }
          ]
        },
      };
    },
    mounted () { 
      if(this.isEdit) {
        this.editData = this.adItemData;
        console.log('000', this.editData);
      }
    },
    computed: {
      isEdit() {
        return Object.keys(this.adItemData).length > 0 ? true : false;
      }
    },
    methods: {
      putDynamicLink() {
        if (!this.editData.dynamicId) {
          this.$message({
            message: '请输入跳转id再点击生成链接',
            type: 'warning'
          });
          return false;
        }
        // let tempUrl = `ybmpage://homeSteadyChannel?strategyId=${this.editData.dynamicId}&iconUrl=${this.editData.entryName}`;
        // this.$set(this.editData, 'page_url', tempUrl)
        this.editData.page_url = `ybmpage://homeSteadyChannel?strategyId=${this.editData.dynamicId}&iconUrl=${this.editData.entryName}`
      },
      
      onSetLink(link) {
        // this.$set(this.editData, 'page_url', link.meta.page_url)
        this.editData.page_url = link.meta.page_url;
      },
      
      async uploadEditContImage(res, file) {
        this.editImgLoading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: "warning"
          });
          return;
        }
        this.editData.iconUrl = res.data.url;
      },
      
      closeEditContent() {
        this.$parent.closeEditContent();
      },
      
      //确定添加词条
      add_confirmEdit() {
        if (!this.editData.entryName) {
          this.$message.warning("请填写词条名称");
          return false;
        }
        if (!this.editData.timevalue) {
          this.$message.warning("请选择有效时间");
          return false;
        }
        if (!this.editData.iconUrl) {
          this.$message.warning("请上传词条icon");
          return false;
        }
        
        this.closeEditContent();
        this.psData = {
          iconUrl: this.editData.iconUrl,
          entryName: `${this.editData.entryName}`,
          timevalue: this.editData.timevalue,
          linkType: this.editData.linkType,
          page_url: this.editData.page_url,
        };
        if (this.isEdit) {
          this.$emit('saveDialog','edit', this.psData, this.editIndex)
        } else {
          this.$emit("saveDialog", 'add', this.psData);
        }
      },
    }
  };
 
</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .topic-image-upload {
    .imgBox {
      position: relative;
    }
    .image {
      display: block;
      width: 100%;
    }
    .modalImg {
      display: flex;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      .childModal {
        border: 1px solid #13c2c2;
        height: 100%;
      }
    }
    .uploader-icon {
      width: 100px;
      height: 100px;
      line-height: 100px;
      border: 1px solid $border-base;
      font-size: 30px;
    }
  }
  .hr {
    height: 1px;
    background: #E8E6E6;
  }
  .infoItem {
    margin-top: 10px;
  }
  .topic-image-picker {
    padding-bottom: 10px;
  }
</style>
