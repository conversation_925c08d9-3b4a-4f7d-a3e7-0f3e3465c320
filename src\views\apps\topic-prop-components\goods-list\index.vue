<template>
    <div class="topic-menu-list">
        <div style="height: 30px">
            <el-radio-group v-model="content.type">
                <el-radio :label="index" v-for="(item,index) in typeList" :key="index">{{item}}</el-radio>
            </el-radio-group>
        </div>
        <div v-if="list.length>0" style="border: 1px solid #3c763d;margin-bottom: 10px">
            <el-table :data="list" style="width: 100%;margin-top: 5px" height="300"
                      ref="multipleTable">
                <el-table-column fixed label="图片" width="80">
                    <template slot-scope="scope">
                        <img :src="scope.row.imageUrl" :alt="scope.row.productName" style="width:100%;max-height:50px;">
                    </template>
                </el-table-column>
                <el-table-column prop="productName" label="药名" width="120">
                </el-table-column>
                <el-table-column label="规格" width="80">
                    <template slot-scope="scope">
                        {{scope.row.mediumPackageTitle}}
                    </template>
                </el-table-column>
                <el-table-column prop="fob" label="价格" width="80">
                    <template slot-scope="scope">
                        {{scope.row.fob}}
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="80">
                    <template slot-scope="scope">
                        <div class="edit-button">
                            <el-button @click="handleDelete(scope.row)" type="warning" size="mini">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div>
            <el-table :data="goods_group" style="width: 100%;margin-top: 5px" height="150"
                      ref="multipleTable">
                <el-table-column fixed label="商品组名称">
                    <template slot-scope="scope">
                        {{scope.row.name}}
                    </template>
                </el-table-column>
                <el-table-column fixed label="商品组里商品数量">
                    <template slot-scope="scope">
                        {{scope.row.ids.length}}
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="80">
                    <template slot-scope="scope">
                        <div class="edit-button">
                            <el-button @click="group_delete(scope.row)" type="warning" size="mini">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!--选择商品-->
        <all-link @select="onSetLink" :tabs="tabs" :params="{
                  goodsGroup: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
            }"></all-link>
    </div>
</template>

<script>
    import base from "../base";
    import api from 'api'

    export default {
        name: "hotRecommend",
        extends: base,
        contentDefault: {
            list: [],
            goods_group: [],
            type:0
        },
        watch:{
            radio(new_val){
                this.content.radio=new_val
            }
        },
        data() {
            return {
                loading: false,
                tabs: [
                    {label: '商品组', value: 'goodsGroup'}
                ],
                typeList:['列表模式','大图模式']
            }
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            },
            moreLink(data) {
                if (!data || !data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            }
        },
        computed: {
            list() {
                let list = _.get(this, 'content.list');
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
            goods_group() {
                let list = _.get(this, 'content.goods_group');
                if (list) {
                    return list
                } else {
                    return [];
                }
            }
        },
        methods: {
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: "warning"
                    });
                    return;
                }
                this.content.image = res.data.url;
            },
            onSetLink(link) {
                if (link.tag === "goods" || link.tag === "importGoods") {
	                this.content.list = !this.content.list.length ?
			                [...link.data] :
                            [...api.common.removeRepeat(this.content.list, link.data)];
                } else if (link.tag === "goodsGroup") {
                    let obj = {};
                    obj.name = link.data.name;
                    obj.ids = link.data.goods;
                    obj.code = link.data.code;
                    let is_have = false;
                    for (let item of this.content.goods_group) {
                        if (item.code === obj.code) {
                            is_have = true;
                            break;
                        }
                    }
                    if (!is_have) {
                        this.content.goods_group.push(obj);
                    }
                }
            },
            group_delete(row) {
                const index = this.goods_group.indexOf(row);
                this.goods_group.splice(index, 1)
            },
            handleDelete(row) {
                const index = this.list.indexOf(row);
                this.list.splice(index, 1)

            }

        }
    }
</script>
<style scoped lang="scss">
    /*.topic-pic-upload{*/
    /*display:block;*/
    /*width:100% ;*/
    /*}*/
    /*.el-upload--text{width:100%}*/

</style>
