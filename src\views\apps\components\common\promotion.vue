<template>
	<div>
		<!--品牌促销-->
		<p class="blank_20"></p>
		<el-button type="primary" @click="handleAdd" size="mini">添加</el-button>
		<el-button type="primary" @click="handleDelete" size="mini">删除</el-button>
		<p class="blank_20"></p>
		每页显示商品数量：
		<el-radio-group v-model="content.count">
			<el-radio :label="item" v-for="(item,index) in countList" :key="index">{{`${item}个`}}</el-radio>
		</el-radio-group>
		<p class="blank_20"></p>
		<el-table :data="list" :row-key="getRowKeys" border fit highlight-current-row style="width: 100%"
		          v-if="list.length>0"
		          @selection-change="handleSelection">
			<el-table-column
				  type="selection"
				  width="55"/>
			<el-table-column prop="title" label="优惠活动" width="120">
				<template slot-scope="scope">
					<template>
						<el-input v-model.lazy="scope.row.title" class="edit-input" size="mini"/>
					</template>
				</template>
			</el-table-column>
			<el-table-column label="商品组名" prop="goodsName" width="120">
				<template slot-scope="scope">
					<template>
						<el-input v-model.lazy="scope.row.goodsName" class="edit-input" size="mini"/>
					</template>
				</template>
			</el-table-column>
			<el-table-column label="跳转页面" prop="page_url" width="180">
				<template slot-scope="scope">
					<template>
						<el-input v-model.lazy="scope.row.page_url" class="edit-input" size="mini" @input="changePageUrl(scope.$index)" />
					</template>
				</template>
			</el-table-column>
			<el-table-column label="品牌图标" width="100">
				<template slot-scope="scope">
					<upload-image :index="scope.$index" :image="scope.row.icon"
					              v-on:listenImage="getIcon"></upload-image>
				</template>
			</el-table-column>
			<el-table-column label="活动图片" width="100">
				<template slot-scope="scope">
					<upload-image :index="scope.$index" :image="scope.row.image"
					              v-on:listenImage="getImage"></upload-image>
				</template>
			</el-table-column>
			<!-- <el-table-column label="起始位置" width="100">
				<template slot-scope="scope">
					<el-input v-model.lazy="scope.row.start" class="edit-input" size="mini" clearable/>
				</template>
			</el-table-column>
			<el-table-column label="结束位置" width="100">
				<template slot-scope="scope">
					<el-input v-model.lazy="scope.row.end" class="edit-input" size="mini" clearable/>
				</template>
			</el-table-column> -->
			<el-table-column label="开始/结束" fixed="right" width="200">
				<template slot-scope="scope">
					<template>
						<el-date-picker size="mini" style="width:200px"
						                v-model="scope.row.time"
						                type="datetimerange"
						                range-separator="至"
						                start-placeholder="开始日期"
						                end-placeholder="结束日期">
						</el-date-picker>
					</template>
				</template>
			</el-table-column>
		</el-table>
		<p class="blank_20"></p>
		<all-link @select="onSetLink" :tabs="tabs" ref="allLink" :params="{
				page:{
					branchCode: branchCode
				},
				goodsGroup:{
					radio: 0,
					returnGoods: 0,
                    search: {
                        state: 1,
                        branchCode: branchCode
                    },
                    data: {
                        ids: goodsIds
                    }
                }
             }"></all-link>

	</div>
</template>

<script>
	import uploadImage from './../upload-image'
	import api from 'api';
	import { getUrlParam } from "config";

	export default {
		props: ['pageData', 'branchCode','label'],
		name: 'promotion',
		data() {
			return {
				tabs: [
					{label: '活动页', value: 'page'},
					{label: '商品组', value: 'goodsGroup'}
				],
				goodsIds: [],
				selectItem: [],
				loading: false,
				countList: [3, 6, 9, 12],
				content:{},
				activeIndex: 0,
			}
		},
		computed: {
			list() {
				var list = _.get(this, 'content.list')
				if (list) {
					if (list.length > 0) {
						this.$nextTick(function () {
							this.setSort()
						})
					}
					return list
				} else {
					return []
				}
			}
		},
		mounted(){
			this.content=_.cloneDeep(this.pageData)
		},
		created() {
      this.debounce = _.debounce(this.changeLink, 1000);
    },
		components: {
			uploadImage
		},
		watch: {
			'content': {
				deep: true,
				handler(val) {
					this.$emit('listenData',{key:this.label,data:val})
				}
			},
			'content.list': {
				deep: true,
				handler(val) {
					if (val.length && val[this.activeIndex].page_url) {
						this.debounce();
					}
				}
			}
		},
		methods: {
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},
			getRowKeys(row) {
				if (!row.id) {
					return
				}
				return row.id
			},
			onSetLink(obj) {
				if (obj.tag == 'goodsGroup') {
					obj.data.map((item, index) => this.list.push({
						id: item.id,
						goodsName: item.name,
						goodsIds: item.goods,
						storgeGoodsIds: item.goods,
						code:item.code,
						image: '',
						time: '',
						title: '',
						icon: '',
						page_url: '',
						page_name: '',
						// start: '',
						// end: ''
					}));
				} else {
					if (this.selectItem.length === 0 || this.selectItem.length !== 1)
						return this.$message.warning('请先选中1个标签');
					let index = this.list.indexOf(this.selectItem[0]);
					this.list[index].page_url = obj.meta.page_url;
					this.list[index].page_name = obj.meta.page_name;
				}
			},
			handleAdd(){
				this.$refs.allLink.changeTab('goodsGroup')
			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = this.list.indexOf(item)
					this.list.splice(index, 1)
				})
			},
			getImage(data) {
				if (data.image) {
					this.list[data.index].image = data.image
				}
			},
			getIcon(data){
				if (data.image) {
					this.list[data.index].icon = data.image
				}
			},
			changePageUrl(index) {
        this.activeIndex = index;
      },
			async changeLink() {
        if (this.content.list[this.activeIndex].page_url) {
          if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.content.list[this.activeIndex].page_url)) {
            this.$message.error('跳转链接格式不正确');
            this.content.list[this.activeIndex].page_url = '';
          } else {
            let linkPageUrl = getUrlParam(this.content.list[this.activeIndex].page_url, 'url');
            const result = await api.topic.checkPageUrl({ url: linkPageUrl });
            if (((result || {}).data || {}).status != 200) {
              this.$message.error('跳转链接不存在');
              this.content.list[this.activeIndex].page_url = '';
            }
          }
        }
      }
		}
	}
</script>
