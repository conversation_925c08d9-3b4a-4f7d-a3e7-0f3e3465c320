<template>
<div class="topic-menu-list">
    {{content.list.length}}
    <div class="block" style="margin-bottom: 4px">
        <el-date-picker v-model="content.timevalue" type="datetimerange" :picker-options="pickerOptions0"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
        </el-date-picker>
    </div>
    <div class="bg-img">
        <el-container style="height: auto; border: 1px solid #eee">
            <el-header height="50px" style="background-color: rgb(19, 194, 194);text-align: center;padding-top: 15px">
                <label class="demonstration">背景色:</label>
                <el-color-picker v-model="content.color" size="mini" @active-change="onSelect"></el-color-picker>
                <span style="margin-left: 50px"></span>
                <a @click="imgOnclick" style="cursor: pointer">清除背景图</a>
            </el-header>
            <el-main>
                <el-upload class="topic-image-upload" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false" :before-upload="() => {loading = true; return true;}" :on-success="onUploadImg">
                    <img v-if="content.image" :src="content.image" class="image">
                    <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                    <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                </el-upload>
            </el-main>
        </el-container>
    </div>
    {{dataList}}
    <el-table :data="dataList" style="width: 100%" height="250" v-if="dataList.length>0" ref="multipleTable"
        @selection-change="handleSelectionChange" >
        <el-table-column type="selection" width="55">
        </el-table-column>
        <el-table-column fixed label="图片" width="80">
            <template slot-scope="scope">
                <img :src="scope.row.meta.imageUrl" :alt="scope.row.meta.productName" style="width:100%;max-height:50px;">
            </template>
        </el-table-column>
        <el-table-column prop="meta.productName" label="药名" width="120">
        </el-table-column>
        <el-table-column label="规格" width="80">
            <template slot-scope="scope">
                {{scope.row.meta.mediumPackageTitle}}
            </template>
        </el-table-column>
        <el-table-column prop="meta.fob" label="价格" width="80">
            <template slot-scope="scope">
                {{scope.row.meta.fob}}
            </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" width="150">
            <template slot-scope="scope">
                <div class="edit-button">
                    <el-button @click="handleEdit(scope.row)" type="primary" size="mini">{{scope.row.meta.isMain?'取消主推':'设为主推'}}</el-button>
                    <el-button @click="handleCancle(scope.$index,scope.row)" type="warning" size="mini">取消推荐</el-button>
                </div>
            </template>
        </el-table-column>
    </el-table>
    <!-- 商品选择列表 -->
    <button @click="handleClick">点击测试</button>
    <section>
        <all-link @select="onSetLink" ></all-link>
    </section>
</div>
</template>

<script>
    import base from '../base'

    export default {
        extends: base,
        contentDefault: {
            list: [],
            bgRes: '', //背景图
            color: '#ffffff',
            page_url: '',
            image: ''
        },
        data() {
            return {
                pickerOptions0: {
                    disabledDate(time) {
                        return time.getTime() < Date.now();
                    },
                    shortcuts: [{
                            text: "未来一周",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来一个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来三个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来六个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来一年",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                                picker.$emit("pick", [start, end]);
                            }
                        }
                    ]
                },
                loading: false,
                maxLenght: 4,
                selectionTable: [], //请选中的药品列表添加连接
            }
        },
        computed:{
            dataList:{
                get:function(){
                    var dataList = _.get(this, 'content.list')
                    if (dataList) {
                        return dataList
                    } else {
                        return [];
                    }
                },
                set:function(val){
                    return val
                }
            },
            dataList() {
                var dataList = _.get(this, 'content.list')
                if (dataList) {
                    return dataList
                } else {
                    return [];
                }
            }
        },
        methods: {
            onSelect(val) {
                this.content.bgRes = val;
            },
            imgOnclick() {
                this.content.bgRes = '';
                this.content.color = '#ffffff',
                    this.content.image = '';
            },
            async onUploadImg(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.image = res.data.url;
                this.content.bgRes = res.data.url;
            },
            isArray(arr){
                return Object.prototype.toString.call(arr)==='[object Array]'
            },
            handleClick(){
                var link=[
                    {name:'lily'},
                    {name:'lucy'},
                ]
                this.dataList=[...link]
            },
            onSetLink(link) {
                if(this.isArray(link)){
                    //选中了商品
                    // this.selectProduct(link)
                    //组件功能更改,这里也会有相应的变动
                        if(link.length>this.maxLenght){
                             this.$message.error(`最多可以选择${this.maxLenght}个`)
                             return 
                        }
                        // for(var i=0;i<link.length;i++){
                        //     this.$set(link[i], 'isMain', false)
                        //     this.$set(link[i],'page_url','')
                        //     this.dataList.push(link[i])
                        // }
                }else{
                    //选中了连接,给选中的商品添加连接
                    if(this.selectionTable.length===0){
                        this.$message.error('请先选择商品')
                        return 
                    }
                    this.selectUrl(link)
                    this.$refs.multipleTable.clearSelection(); //清空选项
                }
            },
            selectUrl(link) {
                this.selectionTable.map((item, array, index) => {
                    let dataListIndex = this.dataList.indexOf(item)
                    if (dataListIndex >= 0) {
                        this.dataList[dataListIndex].meta.page_url = link.meta.page_url
                    }
                })
            },
            selectProduct(link) {
                let arr = this.dataList.filter(item => {
                    return item.meta.id === link.meta.id
                })
                if (arr && arr.length !== 0) {
                    this.$message.error('您所选的商品已存在')
                    return
                }
                if (this.dataList.indexOf(link) >= 0 || this.dataList.length >= this.maxLenght) {
                    this.$message.error(`最多可以选择${this.maxLenght}个`)
                    return
                }
                this.$set(link.meta, 'isMain', false)
                this.$set(link.meta,'page_url','')
                this.dataList.push(link)
                
            },
            handleEdit(row) {
                this.dataList.forEach(item => {
                    item.meta.isMain = false
                })
                row.meta.isMain=true
            },
            handleCancle(index, row) {
                this.dataList.splice(index, 1)
            },
            handleSelectionChange(val) {
                this.selectionTable = val
            }
        }
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">
    @import "../../../../styles/config";
    .topic-image-upload {
            .image {
                display: block;
                width: 100%;
            }
            .uploader-icon {
                width: 200px;
                height: 200px;
                line-height: 200px;
                border: 1px solid $border-base;
                font-size: 50px;
            }
        }
        .topic-image-picker {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    .edit-button{
        display:flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: center;
    }
</style>
