<template>
	<section>
		<el-form :model="form" :rules="rules" ref="form" label-width="100px" class="form">
			<el-form-item label="人群范围" prop="crowdType">
				<el-radio-group v-model="form.crowdType" @change="changeCrowdType">
					<el-radio :label="1">全部人群</el-radio>
					<el-radio :label="2">指定人群</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item
				v-if="form.crowdType===2"
				label="指定人群"
				prop="crowdValue"
			>
				<el-select
          v-model="form.crowdValue"
          :loading="selectLoading"
          filterable
          :filter-method="optionFilter"
          placeholder="请输入人群id"
          clearable
          @clear="options = []"
          @change="selectCrowd"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
				<!-- <el-autocomplete
					style="width: 100%"
					class="inline-input"
					v-model.trim="form.crowdValue"
					:fetch-suggestions="querySearchCrowd"
					placeholder="请输入人群id"
					:trigger-on-focus="false"
					@select="handleSelectCrowd"
				></el-autocomplete> -->
			</el-form-item>
			<el-form-item label="头条标题" prop="content">
				<el-input v-model="form.content"></el-input>
			</el-form-item>
			<el-form-item label="有效时间" prop="date">
				<el-date-picker
					v-model="form.date"
					type="datetimerange"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间" value-format="timestamp" :picker-options="pickerOptions0"
				>
				</el-date-picker>
			</el-form-item>
			<el-form-item label="跳转链接" prop="url">
				<el-input placeholder="请输入链接地址" v-model="form.url">
					<template slot="prepend">Http://</template>
				</el-input>
			</el-form-item>

			<el-form-item label="文字颜色" prop="color">
				<el-color-picker v-model="form.color" @active-change="onSelect"></el-color-picker>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" size="mini" @click="submitForm('form')">立即创建</el-button>
				<el-button @click="resetForm('form')" size="mini">重置</el-button>
			</el-form-item>
		</el-form>
		<!-- 添加连接 -->
		<div class="topic-image-picker">跳转链接</div>
		<page-link @select="onSetLink" :params="params"></page-link>
	</section>
</template>
<script>
	import { AppWebsite, getUrlParam } from "config";
  import api from "api";
	export default {
		props: ['editData', 'flag', 'params'],
		data() {
			let _this = this
			return {
				form: {
					content: "",
					startTime: '',
					endTime: '',
					date: [],
					url: "",
					color: "#676773",
					crowdType: 1,
					crowdId: '',
					crowdValue: '',
				},
				selectLoading: false,
				options: [],
				rules: {
					crowdType: [{
						required: true,
						message: "请选择人群",
						trigger: "change"
					}],
					crowdValue: [{
						required: true,
						message: "请指定人群",
						trigger: "blur"
					}],
					content: [{
						required: true,
						message: "请输入头条内容",
						trigger: "blur"
					},
						{
							min: 3,
							message: "长度在 3 个字符以上",
							trigger: "blur"
						}
					],
					date: [{
						required: true,
						message: "请选择时间",
						trigger: "change"
					}],
					color: [{
						required: true,
						message: "请选择颜色",
						trigger: "change"
					}]
				},
				pickerOptions0: {
					shortcuts: [{
						text: "未来一周",
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit("pick", [start, end]);
						}
					},
						{
							text: "未来一个月",
							onClick(picker) {
								const end = new Date();
								const start = new Date();
								end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
								picker.$emit("pick", [start, end]);
							}
						},
						{
							text: "未来三个月",
							onClick(picker) {
								const end = new Date();
								const start = new Date();
								end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
								picker.$emit("pick", [start, end]);
							}
						},
						{
							text: "未来六个月",
							onClick(picker) {
								const end = new Date();
								const start = new Date();
								end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
								picker.$emit("pick", [start, end]);
							}
						},
						{
							text: "未来一年",
							onClick(picker) {
								const end = new Date();
								const start = new Date();
								end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
								picker.$emit("pick", [start, end]);
							}
						}
					]
				}
			};
		},

		mounted() {
			this.setForm()
		},
		watch: {
			editData: {
				deep: true,
				handler(val) {
					this.setForm()
				}
			}
		},
		methods: {
			setForm() {
				if (this.flag === 'edit') {
					// this.form = Object.assign({}, this.editData);
					for (let key in this.editData) {
						this.form[key] = this.editData[key]
					}
					let {startTime, endTime} = this.editData;
					this.form.date = [startTime, endTime]
				} else {
					//添加条件时,重置
					this.$refs['form'].resetFields();
				}
			},
			changeCrowdType() {
        this.form.crowdId = '';
        this.form.crowdValue = '';
      },
      async optionFilter(val) {
        this.selectLoading = true;
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          this.selectLoading = false;
          this.options = [{
            label: data.name,
            value: val,
          }]
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCrowd(e) {
        if (e) {
          this.form.crowdId = Number(this.options[0].value.trim());
          this.form.crowdValue = this.options[0].label;
        } else {
          this.form.crowdId = '';
          this.form.crowdValue = '';
        }
				this.$forceUpdate();
      },
			onSetLink(link) {
				this.form.url = link.meta.page_url
			},
			async submitForm(formName) {
				if (this.currentCrowd) {
					this.form.crowdId = (this.currentCrowd || {}).id;
				}
				if (!this.form.crowdType || (this.form.crowdType === 2 && !this.form.crowdId)) {
        this.$message.warning("请选择正确的人群");
        return false;
      }
				let linkErrMsg = '';
				if (this.form.url) {
					if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.form.url)) {
						linkErrMsg = '跳转链接格式不正确，请检查';
					} else {
						let linkPageUrl = getUrlParam(this.form.url, 'url');
						const result = await api.topic.checkPageUrl({ url: linkPageUrl });
						if (((result || {}).data || {}).status != 200) {
							linkErrMsg = '跳转链接不存在，请检查';
						}
					}
				}
				if (linkErrMsg) {
					this.$message.error(linkErrMsg);
					return false;
				}
				this.$refs[formName].validate(valid => {
					if (valid) {
						let json = {
							data: _.cloneDeep(this.form)
						}
						this.$refs['form'].resetFields();
						this.$emit("give-form", json);
					} else {
						// console.log("error submit!!");
						return false;
					}
				});
			},
			resetForm(formName) {
				this.$refs[formName].resetFields();
			},
			onSelect(val) {
				this.form.color = this.toColor16(val);

			},
			toColor16(str) {
				if (/^(rgb|RGB)/.test(str)) {
					var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
					var strHex = "#";
					for (var i = 0; i < aColor.length; i++) {
						var hex = Number(aColor[i]).toString(16);
						if (hex === "0") {
							hex += hex;
						}
						strHex += hex;
					}

					if (strHex.length !== 7) {
						strHex = str;
					}
					return strHex.toUpperCase();
				} else {
					return str;
				}
			},
			async querySearchCrowd(queryString, cb) {
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          cb([{
						id: queryString,
						value: data.name || ""
					}]);
          return false;
        }
      },
      handleSelectCrowd(item) {
        this.currentCrowd = item;
      },
		}
	};
</script>

