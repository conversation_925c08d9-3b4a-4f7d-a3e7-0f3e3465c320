<template>
  <div class="fast-entry">
    <!--模块背景设置-->

    <el-row :gutter="20">
      <div class="title">布局模式</div>
      <el-col :span="24">
        <el-select v-model="content.modeType" placeholder="请选择">
          <el-option
            v-for="item in modeTypeList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">词片位置选择</div>
      <el-col :span="24">
        <el-select
          v-model="selectLocation"
          placeholder="请选择"
        >
          <el-option
            v-for="item in location_options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <!-- <el-select v-model="cur_index" placeholder="请选择" @change="handleChangePosition">
          <el-option
            v-for="item in positionList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select> -->
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">添加词片入口</div>
      <el-col :span="12">
        <el-button type="primary" @click="onAddEntry('add')" :disabled="selectLocation == '兜底'">添加词片入口</el-button>
      </el-col>
      <el-col :span="12">
        <el-button type="primary" @click="onAddEntry('isBottom')" :disabled="list.filter((item) => item.entryLocation == '兜底').length > 0 || selectLocation != '兜底'">添加兜底词片入口</el-button>
      </el-col>
    </el-row>
    <el-table :data="list" size="mini" :row-key="(row) => row.tempId || row.module_title" style="width: 100%; margin: 10px 0">
      <el-table-column type="index" width="50"></el-table-column>
      <el-table-column label="位置">
        <template slot-scope="scope">
          <span>{{scope.row.entryLocation}}</span>
        </template>
      </el-table-column>
      <el-table-column label="词片名称">
        <template slot-scope="scope">
          <span>{{scope.row.module_title}}</span>
        </template>
      </el-table-column>
      <el-table-column label="人群">
        <template slot-scope="scope">
          <span>{{scope.row.crowdValue || '全部人群'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="链接" :show-overflow-tooltip="true" width="120px">
        <template slot-scope="scope">
          <span>{{ scope.row.jump_url }}</span>
          <!-- <el-input
            type="text"
            size="mini"
            v-model="scope.row.jump_url"
          >{{scope.row.jump_url}}</el-input> -->
        </template>
      </el-table-column>
      <el-table-column label="链接类型">
        <template slot-scope="scope">
          <span>{{{'topic': '专题页链接', 'stores': '店铺页链接', 'dynamic': '动态商品链接'}[scope.row.linkType]}}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="primary" @click="onAddEntry('edit', scope.row, scope.$index)" size="mini">编辑</el-button>
          <el-button
            type="primary"
            icon="el-icon-caret-top"
            :disabled="scope.$index === 0 ? true : false"
            @click="handle_sort(scope.row, 'up')"
            circle
            sizi="mini"
          />
          <el-button
            type="primary"
            icon="el-icon-caret-bottom"
            :disabled="scope.$index === (list.length -1 ) ? true : false"
            @click="handle_sort(scope.row, 'down')"
            circle
            sizi="mini"
          />
        </template>
      </el-table-column>
    </el-table>

    <el-dialog class="banner-dialog" :title="isAddType ? '添加词片入口' : '编辑词片入口'" :visible.sync="addEntry">
      <!-- <div v-if="!isBottom">
        <span style="fontSize: 15px">入口位置：</span>
        <el-select
          v-model="editData.entryLocation"
          placeholder="请选择"
        >
          <el-option
            v-for="item in location_options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div> -->
      <div style="margin: 20px 0" v-if="!isBottom">
        <span style="fontSize: 15px">人群范围：</span>
        <el-radio-group v-model="editData.crowdType" @change="changeCrowdType">
          <el-radio :label="1">全部人群</el-radio>
          <el-radio :label="2">指定人群</el-radio>
        </el-radio-group>
      </div>
      <div v-if="editData.crowdType===2">
        <span style="fontSize: 15px">指定人群：</span>
        <el-select
          v-model="editData.crowdValue"
          :loading="selectLoading"
          filterable
          :filter-method="optionFilter"
          placeholder="请输入人群id"
          clearable
          @clear="options = []"
          @change="selectCrowd"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <!-- <el-autocomplete
          style="width: 300px"
          class="inline-input"
          v-model.trim="editData.crowdValue"
          :fetch-suggestions="querySearchCrowd"
          placeholder="请输入人群id"
          :trigger-on-focus="false"
          @select="handleSelectCrowd"
          @input="changeCrowdValue"
        ></el-autocomplete> -->
      </div>
      <el-row :gutter="20">
        <div class="title">有效时间：</div>
        <el-col :span="24">
          <el-date-picker
            v-model="editData.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <div class="title">词片属性</div>
        <el-col :span="24">
          <el-select v-model="editData.cur_module" placeholder="请选择" @change="initConfig">
            <el-option
              v-for="item in attributeList"
              :key="item.module_type"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <div class="title">模块配置</div>
        <el-col :span="24">
          <div class="block">
            <el-input placeholder="请输入内容" v-model="editData.module_title">
              <template slot="prepend">模块title</template>
            </el-input>
          </div>
        </el-col>
        <el-col :span="24" v-if="editData.cur_module !== 'good-activity-seckill'">
          <div class="block">
            <el-input placeholder="请输入内容" v-model="(editData.describe || {}).text">
              <template slot="prepend">模块标语</template>
            </el-input>
          </div>
        </el-col>
        <div class="describeImg" v-if="editData.cur_module !== 'good-activity-seckill'">
          <span style="fontSize: 15px">模块标语：</span>
          <el-upload
            class="imageUpload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :on-success="onUploadDescribeImage"
          >
            <img v-if="(editData.describe || {}).image" :src="(editData.describe || {}).image" class="describeImage" />
            <i v-else class="el-icon-plus uploader-icon2"></i>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>

          <el-button type="primary" size="mini" style="marginLeft: 20px" @click="clearImg" class="describeImage">清除标语图片</el-button>
        </div>
        <div
          class="linkType"
          v-if="editData.cur_module === 'good-common'  || editData.cur_module === 'good-activity-seckill'  || editData.cur_module === 'good-image'"
        >
          <span style="fontSize: 15px">链接类型：</span>
          <el-select
            v-model="editData.linkType"
            placeholder="请选择"
            @change="handleChangeLinkType"
          >
            <el-option
              v-for="item in linkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div v-if="editData.linkType === 'dynamic'">
          <div class="topic-image-picker">
            <el-input style="width:200px" placeholder="输入跳转id" v-model="editData.dynamicId">
              <template slot="prepend">跳转id</template>
            </el-input>
            <el-button type="primary" @click="putDynamicLink(editData)">生成链接</el-button>
          </div>
        </div>
        <el-col
          :span="24"
          v-if="editData.cur_module === 'good-common'  || editData.cur_module === 'good-activity-seckill'  || editData.cur_module === 'good-image'"
        >
          <div class="block" style="marginBottom: 10px;">
            <el-input placeholder="请输入内容" v-model="editData.jump_url">
              <template slot="prepend">跳转链接</template>
            </el-input>
          </div>
        </el-col>
        <div v-if="editData.linkType === 'topic'" style="padding: 11px; paddingTop: 60px;">
          <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
        </div>
        <div
          class="title"
          style="clear: both;text-align: left;font-size: 15px;"
          v-if="editData.cur_module === 'good-common'"
        >展示商品设置</div>
        <div v-if="editData.cur_module === 'good-common'">
          <el-radio v-model="editData.showGoodType" :label="1">自动</el-radio>
          <el-radio
            :disabled="editData.linkType === 'dynamic'"
            v-model="editData.showGoodType"
            :label="2"
          >指定商品</el-radio>
        </div>
        <el-col :span="24" v-if="editData.cur_module === 'good-common' && editData.showGoodType === 2">
          <div class="block">
            <el-table :data="editData.specifyGoodlist" size="mini" style="width: 100%">
              <el-table-column type="index" width="50"></el-table-column>

              <el-table-column label="商品ID">
                <template slot-scope="scope">
                  <el-input placeholder="请输入内容" v-model="scope.row.id" clearable></el-input>
                </template>
              </el-table-column>

              <el-table-column fixed="right" label="操作">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    @click="handleSelectGood(scope.row, scope.$index)"
                    type="primary"
                  >确定</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
        <div
          v-if="editData.cur_module === 'good-common' && editData.showGoodType === 1 && editData.linkType !== 'dynamic'"
        >
          <div class="title" style="font-size: 15px;">预览已选商品组</div>
          <el-col :span="24">
            <el-table :data="editData.goods_group" height="120">
              <el-table-column prop="name" label="组名"></el-table-column>
              <el-table-column prop="code" label="编号"></el-table-column>
              <el-table-column prop="branchCode" label="区域号"></el-table-column>
            </el-table>
          </el-col>
        </div>
        <el-col
          :span="24"
          v-if="editData.cur_module === 'good-common' && editData.showGoodType === 1  && editData.linkType !== 'dynamic'"
        >
          <div class="block">
            <all-link
              ref="all_link"
              @select="onSetGroup"
              :tabs="tabs"
              :params="{
                  goodsGroup: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                      state: 1,
                      branchCode: topic.branchCode
                    }
                  }
              }"
            ></all-link>
          </div>
        </el-col>
        <el-col :span="24" v-if="editData.cur_module==='good-image'">
          <div class="block">
            <el-table :data="editData.brand_list" size="mini" style="width: 100%">
              <el-table-column type="index" width="50"></el-table-column>
              <el-table-column label="icon">
                <template slot-scope="scope">
                  <img
                    v-if="scope.row.icon_url"
                    :src="scope.row.icon_url"
                    alt="图"
                    class="title-image"
                  />
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作">
                <template slot-scope="scope">
                  <el-button size="mini" @click="editIcon(scope.row, scope.$index)" type="primary">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addEntry=false">取 消</el-button>
        <el-button size="small" type="primary" @click="handleOk">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog class="banner-dialog" title="banner设置" :visible.sync="addDialog">
      <el-upload
        class="topic-image-upload"
        ref="upload"
        accept="image/jpeg,image/jpg, image/png, image/gif"
        :show-file-list="false"
        :on-success="onUploadImage"
      >
        <img v-if="iconObj.icon_url" :src="iconObj.icon_url" class="image" />
        <i v-else class="el-icon-plus uploader-icon"></i>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialog=false">取 消</el-button>
        <el-button size="small" type="primary" @click="closeDialog">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import base from "../base";
import api from 'api';
import { AppWebsite, getUrlParam } from "config";
export default {
  extends: base,
  computed: {
    list() {
      let list = _.get(this, "content.module_list");
      if (list) {
        // this.$nextTick(function() {
        //   this.setSort(true);
        // });
        // list.map((item, index) => {
        //   if(!item.entryLocation) {
        //     item.entryLocation = index < 2 ? `1-${index+1}` : index < 4 ? `2-${index-1}` : `3-${index-3}`;
        //     item.crowdType = 1;
        //     return item;
        //   }
        // })
        
        return list.filter((item, index) => {
          if(!item.entryLocation) {
            item.entryLocation = index < 2 ? `1-${index+1}` : index < 4 ? `2-${index-1}` : `3-${index-3}`;
            item.crowdType = 1;
            // return item;
          }
          if(item.entryLocation == this.selectLocation) {
            return item;
          } 
        });
      } else {
        return [];
      }
    }
  },
  data() {
    return {
      tabs: [{ label: "商品组", value: "goodsGroup" }],
      linkOptions: [
        {
          value: "topic",
          label: "专题页链接"
        },
        {
          value: "stores",
          label: "店铺页链接"
        },
        {
          value: "dynamic",
          label: "动态商品链接"
        },
        {
          value: "redEnvelope",
          label: "绑定微信链接"
        }
      ],
      modeTypeList: [
        {
          name: "2图模式",
          value: 2
        },
        {
          name: "4图模式",
          value: 4
        },
        {
          name: "6图模式",
          value: 6
        }
      ],
      attributeList: [
        {
          name: "展示商品-常规",
          value: "good-common"
        },
        {
          name: "展示商品-活动-秒杀",
          value: "good-activity-seckill"
        },
        {
          name: "展示图片",
          value: "good-image"
        },
        {
          name: "高毛专区",
          value: "GaoMao"
        },
        {
          name: "新品首推",
          value: "NewSku"
        }
      ],
      // cur_index: 0,
      addDialog: false,
      addEntry: false,
      iconObj: {
        icon_url: ""
      },
      location_options:[{
        value: '兜底',
        label: '兜底',
      }, {
        value: '1-1',
        label: '位置1-1',
      }, {
        value: '1-2',
        label: '位置1-2',
      }, {
        value: '2-1',
        label: '位置2-1',
      }, {
        value: '2-2',
        label: '位置2-2',
      }, {
        value: '3-1',
        label: '位置3-1',
      }, {
        value: '3-2',
        label: '位置3-2',
      }],
      isAddType: true,
      editData: {
        entryLocation: '',
        crowdType: 1,
        crowdValue: '',
        crowdId: '',
        jump_url: '',
        describe: {
          text: '',
          image: '',
        },
        module_title: '',
        linkType: '',
        dynamicId: null,
        showGoodType: '',
        specifyGoodlist: null,
        cur_module: '',
        timevalue: [],
      },
      isBottom: false,
      selectLocation: '',
      selectLoading: false,
      options: [],
      pickerOptions: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      }
      // editData: {},
    };
  },
  mounted() {
    // this.editData.cur_module = (this.list[0] || {})["module_type"];
    // this.setPositionList();
  },
  
  methods: {
    // changeCrowdValue(e) {
    //   if (!e) {
    //     this.editData.crowdId = '';
    //   }
    //   this.$forceUpdate();
    // },
    clearImg() {
      this.editData.describe.image = '';
    },
    handle_sort(item,type){
      const index = this.content.module_list.indexOf(item);
      if(type === 'up') {
        this.content.module_list.splice(index,1);
        this.content.module_list.splice(index-1,0,item)
      } else {
        this.content.module_list.splice(index,1);
        this.content.module_list.splice(index+1,0,item)
      }
    },
    
    handleChangeLinkType(value) {
      if (value === "dynamic") {
        this.editData.showGoodType = 1;
      }
    },
    onSetGroup(link) {
      if (link.tag === "goodsGroup") {
        let obj = {};
        obj.name = link.data.name;
        obj.branchCode = link.data.branchCode;
        obj.code = link.data.code;
        this.editData.groupId = obj.code;
        this.$set(this.editData, "goods_group", [obj]);
      }
    },
    async handleSelectGood(row, index) {
      if (!row.id) return false;
      let pms = {
        url: AppWebsite + "app/sku/select",
        dataType: "json",
        data: {
          offset: 0,
          limit: 10,
          branchCode: this.topic.branchCode,
          skuIdList: [+row.id]
        },
        head: {
          terminalType: 1,
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      const { result } = res;
      const { list } = result;
      let good;
      if (list && list.length) {
        good = list[0];
      }
      this.editData.specifyGoodIdList[index] = good.id;
      row.id = good.showName;
      this.$set(this.editData, "specifyGoodIdList", this.editData.specifyGoodIdList);
    },
    putDynamicLink(item) {
      if (!item.dynamicId) {
        this.$message({
          message: "请输入跳转id再点击生成链接",
          type: "warning"
        });
        return false;
      }
      item.jump_url = `ybmpage://homeSteadyChannel?strategyId=${item.dynamicId}&title=${item.module_title}`;
    },
    // handleChangePosition() {
    //   this.editData.cur_module = this.list[this.cur_index]["module_type"] || "";
    // },
    initConfig() {
      this.editData.module_type = "";
      this.editData.module_title =
        this.editData.cur_module === "GaoMao"
          ? "高毛专区"
          : this.editData.cur_module === "NewSku"
          ? "新品首推"
          : "";
      this.editData.jump_url = "";
      this.editData.linkType = "";
      this.editData.dynamicId = "";
      this.editData.showGoodType = 1;
      this.editData.specifyGoodIdList = [];
      this.editData.groupId = "";
      this.editData.describe = {
        text:
          this.editData.cur_module === "GaoMao"
            ? "毛利高 赚得多"
            : this.editData.cur_module === "NewSku"
            ? "天天焕新 发现好药"
            : "",
        image: '',
      };
      if (this.editData.cur_module === "good-common") {
        if (!this.editData.specifyGoodlist) {
          this.editData.specifyGoodlist = [
            {
              id: ""
            },
            {
              id: ""
            }
          ];
        }
      } else {
        this.editData.goods_group = [];
        this.editData.specifyGoodlist = null;
      }
      if (this.editData.cur_module === "good-image") {
        if (!this.editData.brand_list) {
          this.editData.brand_list = [
            {
              icon_url: ""
            },
            {
              icon_url: ""
            }
          ];
        }
      } else {
        this.editData.brand_list = null;
      }
      this.editData["module_type"] = this.editData.cur_module;
    },
    // setPositionList() {
    //   for (let i = 0; i < this.content.modeType; i++) {
    //     this.positionList.push({
    //       name: `词片${i + 1}`,
    //       value: i
    //     });
    //   }
    // },
    onSetLink(link) {
      this.editData.jump_url = link.meta.page_url;
    },
    editIcon(data, index) {
      this.iconObj.icon_url = data.icon_url;
      this.iconObj.icon_index = index;
      this.addDialog = true;
    },
    closeDialog() {
      this.editData.brand_list[
        this.iconObj.icon_index
      ].icon_url = this.iconObj.icon_url;
      this.addDialog = false;
    },
    changeCrowdType() {
      this.editData.crowdId = '';
      this.editData.crowdValue = '';
    },
    onAddEntry(type, record, index) {
      if(type == 'add' && !this.selectLocation) {
        this.$message.warning('请选择词片位置');
        return;
      }
      if (type == 'isBottom') {
        this.isBottom = true;
      } else {
        this.isBottom = false;
      }
      this.isAddType = type === 'add' || type === 'isBottom';
      this.addEntry = true;
      if (type === 'edit') {
        // this.nowIndex = index;
        this.nowIndex = this.content.module_list.indexOf(record);
        this.currentData = Object.assign({}, record);
        this.editData = Object.assign({}, record);
      } else {
        // this.editData.entryLocation = '';
        // this.editData.crowdType = 1;
        // this.editData.crowdValue = '';
        // this.editData.crowdId = '';
        // this.initConfig();
        this.editData = {
          entryLocation: '',
          crowdType: 1,
          crowdValue: '',
          crowdId: '',
          jump_url: '',
          describe: {
            text: '',
            image: '',
          },
          module_title: '',
          linkType: '',
          dynamicId: null,
          showGoodType: '',
          specifyGoodlist: null,
          cur_module: '',
          timevalue: [],
        }
      }
    },
    async handleOk() {
      if (this.isBottom) {
        this.editData.entryLocation = '兜底';
      }
      // if (!this.editData.entryLocation) {
      //   this.$message.warning("请选择入口位置");
      //   return false;
      // }
      if (!this.isBottom && (!this.editData.crowdType || (this.editData.crowdType === 2 && !this.editData.crowdId))) {
        this.$message.warning("请选择正确的人群");
        return false;
      }

      let linkErrMsg = '';
      if (this.editData.linkType === 'topic' && this.editData.jump_url) {
        if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.editData.jump_url)) {
          linkErrMsg = '跳转链接格式不正确，请检查';
        } else {
          let linkPageUrl = getUrlParam(this.editData.jump_url, 'url');
          const result = await api.topic.checkPageUrl({ url: linkPageUrl });
          if (((result || {}).data || {}).status != 200) {
            linkErrMsg = '跳转链接不存在，请检查';
          }
        }
      }
      if (linkErrMsg) {
        this.$message.error(linkErrMsg);
        return false;
      }

      this.$set(this.editData, "entryLocation", this.selectLocation)
      this.addEntry = false;
      if (this.isAddType) {
        // this.content.module_list.push(this.editData);
        // 此逻辑为了新增时相同词片在一起，排序不出错
        let lastIndex = '';
        this.content.module_list.map((val, index) => {
          if(val.entryLocation == this.selectLocation) {
            lastIndex = index;
          }
        });
        this.content.module_list.splice(lastIndex+1, 0, this.editData)
      } else {
        this.currentData = Object.assign(this.currentData, this.editData);
        this.content.module_list.splice(this.nowIndex, 1, this.currentData)
      }
    },
    async onUploadImage(res, file) {
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.iconObj.icon_url = res.data.url;
    },
    async onUploadDescribeImage (res) {
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.$set(this.editData.describe, 'image', res.data.url);
      // this.editData.describe.image = res.data.url;
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [{
          label: data.name,
          value: val,
        }]
      } else {
        this.selectLoading = false;
        this.options = []
      }
    },
    selectCrowd(e) {
      if (e) {
        this.editData.crowdId = Number(this.options[0].value.trim());
        this.editData.crowdValue = this.options[0].label;
      } else {
        this.editData.crowdId = '';
        this.editData.crowdValue = '';
      }
      this.$forceUpdate();
    },
    // async querySearchCrowd(queryString, cb) {
    //   const pms = {
    //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
    //     dataType: "json",
    //     data: {},
    //     head: {
    //       "Content-Type": "application/json;charset=UTF-8"
    //     }
    //   };
    //   const res = await api.proxy.post(pms);
    //   if (res.success) {
    //     const { data } = res;
    //     cb([{
    //       id: queryString,
    //       value: data.name || ""
    //     }]);
    //     return false;
    //   }
    // },
    // handleSelectCrowd(item) {
    //   // this.currentCrowd = item;
    //   this.editData.crowdId = item.id;
    // },
  },
  watch: {
    // editData.cur_module(new_val) {
    //   if (new_val) {
    //     this.list.forEach((item, index) => {
    //       if (item.module_type === new_val) {
    //         this.cur_index = index;
    //       }
    //     });
    //   }
    // }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss">
.fast-entry {
  padding-bottom: 10px;
  .container {
    display: flex;
    align-items: center;
    .img {
      width: 65%;

      img {
        display: block;
        width: 100%;
      }
    }

    .button-list {
      margin-left: 10px;
    }
  }

  .linkType {
    clear: both;
    text-align: left;
    margin: 0 0 0 12px;
    padding-top: 10px;
  }

  .content-setting {
    color: #fff;
    background-color: #13c2c2;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .title-image {
    width: 64px;
    height: 64px;
  }

  .describeImg {
    display: flex;
    clear: both;
    text-align: left;
    margin: 0 0 0 12px;
    padding-top: 10px;
    .describeImage {
      width: 96px;
      height: 33px;
    }
  }
  .imageUpload {
    .uploader-icon2 {
      width: 100px;
      height: 100px;
      line-height: 100px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }

  .topic-image-upload {
    .image {
      display: block;
      width: 100%;
    }

    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }

  .entry-name {
    width: 70%;
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  // single-upload
  .uploader-btn-state {
    text-align: center;
  }

  .topic-image-picker {
    padding: 10px 0;
    padding-bottom: 0;
    padding-left: 11px;
    text-align: left;
  }

  .el-table {
    .cell {
      text-align: center;
      padding: 0;
    }

    th .cell {
      color: #606266;
    }
  }

  .banner-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }

    .image {
      width: 64px;
      height: 64px;
    }
  }

  .no-img {
    font-size: 35px;
    display: block;
    color: #caccd0;
  }
}

.el-loading-spinner {
  top: auto !important;
  margin-top: auto !important;
}

.el-row {
  text-align: center;

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }

  .block {
    margin-top: 10px;
  }
}
</style>
