import Vue from 'vue'
import router from 'router'
import eventBus from 'utils/eventbus'
const ERRORS = {
  '1001': '接口认证失败',
  'no_permission': '无权访问',
  '1003': '缺失参数',
}
export default function handleError(err) {
  if (err.response) {
    handleError(err)
  } else {
    console.error('Error', err.message)
  }
}

function handleError(err) {
  let errData = err.response.data
  let errCode = errData.code
  switch (errCode) {
  case 1002:
    accessTokenExpire()
  }
  if (errData.message) {
    Vue.prototype.$message.error(errData.message)
  }
}

function accessTokenExpire() {
  eventBus.$emit('accessTokenExpire')
  router.replace('/auth/login')
}
