<template>
    <div class="main-content user-content" v-loading="loading">
        <breadcrumb></breadcrumb>
        <div class="content-title">
            基本信息
        </div>
        <el-form class="data-form" :model="dataForm" :rules="dataRules" ref="dataForm" label-width="90px"
                 label-position="left" size="small">
            <el-form-item label="姓名" prop="realName">
                <el-input v-model="dataForm.realName" placeholder="姓名"></el-input>
            </el-form-item>
            <el-form-item label="手机号" prop="mobile">
                <el-input v-model="dataForm.mobile" placeholder="手机号"></el-input>
            </el-form-item>
            <el-form-item label="邮箱" prop="mail">
                <el-input v-model="dataForm.mail" placeholder="邮箱"></el-input>
            </el-form-item>
        </el-form>
        <div class="content-title" v-if="!isEdit">
            登录信息
        </div>
        <el-form class="data-form" :model="dataForm" :rules="dataRules" ref="dataForm2" label-width="90px"
                 label-position="left" size="small" v-if="!isEdit">
            <el-form-item label="账号" prop="userName">
                <el-input v-model="dataForm.userName" placeholder="请输入账号"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
                <el-input type="password" v-model="dataForm.password" placeholder="英文、数字及英文符号，不能含空格等"></el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="checkPass">
                <el-input type="password" v-model="dataForm.checkPass" placeholder="请再次输入新密码"></el-input>
            </el-form-item>
        </el-form>
        <div class="content-title">
            权限管理
        </div>
        <el-form class="data-form" :model="dataForm" :rules="dataRules" ref="dataForm3" label-width="90px"
                 label-position="left" size="small">
            <el-form-item label="所属组织" prop="orgIds">
                <ul class="org-list" @click="showOrgDialog">
                    <li class="org" :key="org.id" v-for="(org,index) in orgNameList">
                        {{org.distName}}
                        <i @click.stop="removeOrg(org, index)" class="el-icon-close"></i></li>
                </ul>
            </el-form-item>
            <el-form-item label="角色" prop="roleIds">
                <el-select multiple v-model="dataForm.roleIds" placeholder="请选择角色" default-first-option filterable>
                    <el-option :key="role.id" v-for="role in roleList" :label="role.roleName" :value="role.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="区域" prop="branch">
                <el-select v-model="dataForm.branch" placeholder="请选择区域" @change="selectGet" default-first-option filterable>
                    <el-option :key="item.branchCode" v-for="(item, i) in branchs" :value="item.branchCode" :label="item.branchName"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <div class="content-title">
        </div>
        <el-form class="data-form" label-width="90px"
                 label-position="left" size="small">
            <el-form-item label="">
                <el-button type="primary" class="btn-save" @click="save" :loading="sending" :disabled="sending" size="small">{{ sending ? '处理中...' : '确定' }}</el-button>
            </el-form-item>
        </el-form>
        <el-dialog
                title="选择所属组织"
                :visible.sync="showDialog"
                width="460px">
            <ul class="org-list" v-if="orgNameList && orgNameList.length">
                <li class="org" :key="org.id" v-for="(org,index) in orgNameList">
                    {{org.distName}}
                    <i @click="removeOrg(org, index)" class="el-icon-close"></i>
                </li>
            </ul>
            <el-tree v-if="orgList && orgList.length" ref="orgTree" node-key="id" :data="orgList" :props="orgProp" @node-click="handleOrgClick"></el-tree>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="showDialog = false">取 消</el-button>
                <el-button size="small" type="primary" @click="changeOrg">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
    import api from 'api'
    import breadcrumb from '../components/breadcrumb.vue'
    import md5 from 'md5'
    export default {
        name: 'Home',
        data() {
            return {
                loading: false,
                sending: false,
                showDialog: false,
                branchs: null,
                isEdit: this.$route.name === 'editUser',
                dataForm: {
                    sysUserId: this.$route.params.id,
                    branch: null,
                },
                allOrgList: [],
                roleList: [],
                orgList: [],
                orgNameList: [],
                selectOrgList: [],
                orgProp: {
                    children: 'children',
                    label: 'name'
                },
                dataRules: {
                    realName: [
                        { required: true, message: '请填写姓名', trigger: 'blur' },
                        { min: 2, max: 20, message: '字数在2 - 20之间', trigger: 'blur' },
                        {
	                        validator: (rule, val, callback) => {
                                if (!/^[A-Za-z\u4e00-\u9fff]+$/.test(val))
                                    var e = new Error('请填写汉字或英文');
		                        return callback(e);
                            }, trigger: 'blur'
                        }
                    ],
                    mobile: [
                        { required: true, message: '请填写正确的手机号', trigger: 'blur' },
                        {
                            validator: (rule, val, callback) => {
                                if (!/^(\+?86)?1(3|[5-9])\d{9}$/.test(val))
                                    var e = new Error('请填写正确的手机号');
	                            return callback(e);
                            }, trigger: 'blur'
                        }
                    ],
	                mail: [
		                {
			                validator: (rule, val, callback) => {
				                if (val && !/^[a-zA-Z\d($\+\-\._#&)\u4e00-\u9fff]{1,20}@[a-zA-Z\d\-$_]{1,20}(\.[a-zA-Z\d]{2,3}){1,3}$/.test(val))
					                var e = new Error('请填写正确的邮箱');
				                return callback(e);
			                }, trigger: 'blur'
		                }
                    ],
                    userName: [
                        { required: true, message: '请填写登录账号', trigger: 'blur', },
	                    { min: 2, max: 36, message: '长度在2 - 36之间', trigger: 'blur' },
	                    {
		                    validator: (rule, val, callback) => {
			                    if (!/^[A-Za-z\d\.\$_@]+$/.test(val))
				                    var e = new Error('只能是英文、数字或符号（"_", ".", "@", "$"）');
			                    return callback(e);
		                    }, trigger: 'blur'
	                    },
	                    { validator: this.userNameValid, trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请输入密码', trigger: 'blur' },
                        { min: 5, max: 20, message: '长度应为5-20个字符', trigger: 'blur' },
	                    {
		                    validator: (rule, val, callback) => {
			                    if (/\s/.test(val)
                                    || !/^[A-Za-z\d\.\$_@,;!&]+$/.test(val))
				                    var e = new Error('密码只能包含英文、数字及部分英文符号（"_", ",", ".", "$", "@", ";", "!", "&"），且不能含有空格等');
			                    return callback(e);
		                    }, trigger: 'blur'
	                    }
                    ],
                    checkPass: [
                        { required: true, message: '请再次确认密码', trigger: 'blur' },
                        {
                            validator: (rule, val, callback) => {
                                if (val !== this.dataForm.password)
                                    var e = new Error('两次输入密码不一致');
	                            return callback(e);
                            }, trigger: 'blur'
                        }
                    ],
                    orgIds: [
                        { required: true, message: '请选择所属组织', trigger: 'change', }
                    ],
                    roleIds: [
                        { type: 'array', required: true, message: '请选择角色', trigger: 'change', }
                    ],
                    branch: [
                        {required: true, message: '请选择区域', trigger: 'change'}
                    ]
                }
            }
        },
        components: {
            breadcrumb
        },
        async mounted() {
            this.dict();
            this.$store.dispatch('breadcrumb/addPath', {
                title: this.isEdit ? '编辑成员' : '添加成员',
                action: this.isEdit ? 'editUser' : 'addUser'
            });
            const result = await api.role.list();
            if (result.code === 200) {
                this.roleList = result.data;
            } else {
                this.$message.error(result.msg);
            }
            await this.loadOrg();
            if (this.$route.name === 'editUser') {
                this.loading = true;
                const result = await api.user.get(this.$route.params.id);
                this.loading = false;
                if (result.code === 200) {
                    const orgList = [];
                    result.data.orgIds.forEach(id => {
                        let org = this.getOrg(id);
                        orgList.push(org);
                    })
                    const topOrgList = [], secondOrgList = [];
                    orgList.forEach(org => {
                        if (org.parentId == 0) {
                            org.level = 1;
                            topOrgList.push(org)
                        }
                    })
                    orgList.forEach(org => {
                        topOrgList.forEach(topOrg => {
                            if (org.parentId === topOrg.id) {
                                org.level = 2;
                                secondOrgList.push(org)
                            }
                        })
                    })
                    orgList.forEach(org => {
                        secondOrgList.forEach(secondOrg => {
                            if (org.parentId === secondOrg.id) {
                                org.level = 3;
                            }
                        })
                    })
                    orgList.forEach(org => {
                        this.handleOrgClick(org);
                    })
                    this.dataForm = {
                        sysUserId: this.$route.params.id,
                        realName: result.data.realName,
                        mobile: result.data.mobile,
                        roleIds: result.data.roleIds,
                        branch: result.data.branch.branchCode,
                    }
                } else {
                    this.$message.error(result.msg);
                }
            }
        },
        methods: {
            changeOrg() {
                this.showDialog = false;
            },
            async dict() {
                let bho = await api.dict.branchHasOpen();
                if (bho.code == 200)
                    this.$nextTick(() => this.branchs = bho.data);
                else
                    this.$message.error(bho.msg);
            },
            //下拉框选中事件
            selectGet(vId){//这个vId也就是value值
                this.dataForm.branch = vId;
            },
            getOrg(id) {
                let distOrg = {};
                this.allOrgList.some(org => {
                    if (id === org.id) {
                        distOrg = org;
                        return true;
                    }
                })
                return {
                    id: distOrg.id,
                    parentId: distOrg.parentId,
                    name: distOrg.name,
                };
            },
            removeOrg(org, index) {
                this.orgNameList.splice(index, 1);
                switch (org.level) {
                    case 1:
                        this.selectOrgList.some((topOrg, index) => {
                            if (topOrg.id === org.id) {
                                this.selectOrgList.splice(index, 1)
                                return true;
                            }
                        })
                        break;
                    case 2:
                        this.selectOrgList.some((topOrg, index) => {
                            if (topOrg.id === org.parentId) {
                                this.selectOrgList.splice(index, 1)
                                return true;
                            }
                        })
                        break;
                    case 3:
                        const parent = this.getOrg(org.parentId);
                        this.selectOrgList.some((topOrg, index) => {
                            if (topOrg.id === parent.parentId) {
                                this.selectOrgList.splice(index, 1)
                                return true;
                            }
                        })
                        break;
                }
            },
            handleOrgClick(currentOrg) {
                const data = {
                    id: currentOrg.id,
                    parentId: currentOrg.parentId,
                    name: currentOrg.name,
                    level: currentOrg.level,
                }
                switch (data.level) {
                    case 1:
                        const flag = this.selectOrgList.some(topOrg => {
                            return topOrg.id === data.id;
                        })
                        !flag && this.selectOrgList.push(data);
                        break;
                    case 2:
                        let parent;
                        this.selectOrgList.some(topOrg => {
                            if (topOrg.id === data.parentId) {
                                parent = topOrg;
                                return true;
                            }
                        })
                        if (!parent) {
                            parent = this.getOrg(data.parentId);
                            parent.children = [ data ];
                            this.selectOrgList.push(parent);
                        }
                        if (parent && parent.children) {
                            const flag = parent.children.some(childOrg => {
                                return childOrg.id === data.id;
                            })
                            !flag && parent.children.push(data);
                        }
                        if (parent && !parent.children) {
                            parent.children = [ data ];
                        }
                        break;
                    case 3:
                        let upParent;
                        this.selectOrgList.some(topOrg => {
                            return topOrg.children && topOrg.children.some(child => {
                                if (child.id === data.parentId) {
                                    upParent = child;
                                    return true;
                                }
                            })
                        })
                        if (!upParent) {
                            upParent = this.getOrg(data.parentId);
                            upParent.children = [ data ];
                            const topParent = this.getOrg(upParent.parentId);
                            topParent.children = [ upParent ];
                            this.selectOrgList.push(topParent);
                        }
                        if (upParent && upParent.children) {
                            const flag = upParent.children.some(childOrg => {
                                return childOrg.id === data.id;
                            })
                            !flag && upParent.children.push(data);
                        }
                        if (upParent && !upParent.children) {
                            upParent.children = [ data ];
                        }
                        break;
                }
                this.resetSelectOrgList();
            },
            resetSelectOrgList() {
                const orgList = [];
                this.selectOrgList.forEach(topOrg => {
                    if (topOrg.children && topOrg.children.length) {
                        topOrg.children.forEach(secondOrg => {
                            if (secondOrg.children && secondOrg.children.length) {
                                secondOrg.children.forEach(thirdOrg => {
                                    thirdOrg.distName = topOrg.name + '/' + secondOrg.name + '/' + thirdOrg.name;
                                    orgList.push(thirdOrg)
                                })
                            } else {
                                secondOrg.distName = topOrg.name + '/' + secondOrg.name;
                                orgList.push(secondOrg)
                            }
                        })
                    } else {
                        topOrg.distName = topOrg.name;
                        orgList.push(topOrg)
                    }
                    this.orgNameList = orgList;
                })
            },
            async loadOrg() {
                this.loading = true;
                const result = await api.org.list();
                this.loading = false;
                if (result.code === 200) {
                    const orgList = [];
                    result.data.forEach(data => {
                        data.children = [];
                        this.allOrgList.push({
                            id: data.id,
                            parentId: data.parentId,
                            name: data.name,
                        })
                        if (data.parentId == 0) {
                            data.level = 1;
                            orgList.push(data)
                        }
                    })
                    result.data.forEach(data => {
                        orgList.forEach(org => {
                            if (data.parentId == org.id) {
                                result.data.forEach(tempData => {
                                    if (tempData.parentId == data.id) {
                                        tempData.level = 3;
                                        data.children.push(tempData)
                                    }
                                })
                                data.level = 2;
                                org.children.push(data);
                            }
                        })
                    })
                    this.orgList = orgList;
                } else {
                    this.$message.error(result.msg);
                }
            },
            showOrgDialog() {
                this.showDialog = true;
            },
            async save() {
                let valid = await this.$refs.dataForm.validate();
                if (!valid)
	                return false;
                if (this.$route.name === 'addUser' && !(await this.$refs.dataForm2.validate()))
	                return false;
                const orgIds = [];
                this.selectOrgList.forEach(org => {
                    orgIds.push(org.id);
                    if (org.children) {
                        org.children.forEach(child => {
                            orgIds.push(child.id);
                            if (child.children) {
                                child.children.forEach(third => {
                                    orgIds.push(third.id);
                                })
                            }
                        })
                    }
                });
                this.dataForm.orgIds = orgIds;
	            if (!(await this.$refs.dataForm3.validate()))
		            return false;
                let branch;     //区域对象
	            this.sending = true;
	            this.branchs.forEach(item => {
                    if (item.branchCode == this.dataForm.branch) {
                        branch = item;
                        return;
                    }
                });
                if (this.$route.name === 'editUser') {
                    const res = await api.user.update(this.$route.params.id,{
                        userId: this.$route.params.id,
                        realName: this.dataForm.realName,
                        mobile: this.dataForm.mobile,
	                    mail: this.dataForm.mail,
                        orgIds: orgIds,
                        roleIds: this.dataForm.roleIds,
                        branch: branch
                    });
                    this.sending = false;
                    if (res.code == 200) {
                        this.$message.success('修改成功');
                        this.$router.go(-1);
                    } else {
                        this.$message.error(res.message);
                    }
                } else {
                    const res = await api.user.add({
                        realName: this.dataForm.realName,
                        mobile: this.dataForm.mobile,
                        mail: this.dataForm.mail,
                        userName: this.dataForm.userName,
                        password: md5(this.dataForm.password),
                        orgIds: orgIds,
                        roleIds: this.dataForm.roleIds,
                        branch: branch
                    });
                    this.sending = false;
                    if (res.code == 200) {
                        this.$message.success('新增成功');
                        this.$router.go(-1);
                    } else {
                        this.$message.error(res.message);
                    }
                }
            },
	        userNameValid(rule, val, callback) {
		        let data = this.dataForm;
		        if (!data.userName)
			        return callback();

		        if (this.timId)
			        clearTimeout(this.timId);
		        this.timId = setTimeout(async () => {   //规定时间内，不频繁验证
			        let pms = {
				        userName: data.userName
			        };
			        let res = await api.user.validate(pms);
			        if (res.code == 200 && res.data && res.data.length > 0)
				        var e = new Error('此帐号已存在');
			        clearTimeout(this.timId);
			        return callback(e);
		        }, 1000);
            }
        }
    }
</script>
<style lang="scss" rel="stylesheet/scss">

    .user-content {
        padding-top: 0;
    }

    .main-content {
        .preview-img {
            width: 180px;
        }
        .avatar-uploader-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 180px;
            height: 58px;
            border: 1px solid $extra-black;
        }
        .org-list {
            display: flex;
            padding: 10px 5px;
            flex-wrap: wrap;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            min-height: 32px;
            .org {
                position: relative;
                margin-right: 5px;
                margin-bottom: 5px;
                font-size: 12px;
                height: 24px;
                line-height: 24px;
                background-color: #F1F1F1;
                padding-left: 5px;
                padding-right: 30px;
                border: 1px solid #ddd;
                .el-icon-close {
                    @include middle-center-y();
                    right: 5px;
                    cursor: pointer;
                }
            }
        }
        .data-form {
            .org-list {
                box-sizing: border-box;
                width: 350px;
                border-radius: 4px;
            }
        }
    }
</style>
