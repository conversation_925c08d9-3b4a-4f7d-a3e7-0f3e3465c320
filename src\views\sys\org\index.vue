<template>
  <div>
    <div class="org-form" v-loading="loading">
      <breadcrumb></breadcrumb>
      <el-container>
        <el-aside width="240px" class="organization-wrap">
          <el-card>
            <div slot="header">
              <span>组织架构</span>
              <!--<el-button class="btn-add" size="mini" icon="el-icon-plus" @click="addOrg">添加组织</el-button>-->
            </div>
            <el-tree
              v-if="orgList && orgList.length"
              ref="orgTree"
              node-key="id"
              :render-content="renderContent"
              :data="orgList"
              :props="orgProp"
              @node-click="handleOrgClick"
            ></el-tree>
            <div v-else class="empty-wrap">
              <i class="iconfont icon-tishi"></i>
              <span>尚未添加组织</span>
            </div>
          </el-card>
        </el-aside>
        <el-main class="member-wrap">
          <members :data="orgData"></members>
        </el-main>
      </el-container>
    </div>
    <el-dialog
      :title="orgInfo.id ? '编辑':'添加'"
      :visible.sync="showOrgDialog"
      @close="resetOrgDialog"
      width="460px"
    >
      <el-form
        class="dialog-form"
        :model="orgInfo"
        :rules="orgRules"
        ref="orgForm"
        label-width="90px"
        label-position="left"
        size="small"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="orgInfo.name"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="orgInfo.description"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-checkbox v-if="orgInfo.id" v-model="isDelOrg" :true-label="1" :false-label="0">删除组织</el-checkbox>
        <el-button size="small" @click="showOrgDialog = false">取 消</el-button>
        <el-button
          size="small"
          type="primary"
          :loading="sending"
          @click="saveOrg"
        >{{sending ? '正在提交...' : '确定'}}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="修改密码"
      :visible.sync="showPwdDialog"
      :close-on-click-modal="false"
      @close="pwdForm = {}"
      width="460px"
    >
      <el-form
        class="dialog-form"
        :model="pwdForm"
        :rules="pwdRules"
        ref="pwdForm"
        label-width="90px"
        label-position="left"
        size="small"
      >
        <el-form-item label="原密码" prop="oldPwd">
          <el-input type="password" v-model="pwdForm.oldPwd" placeholder="请输入原密码"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input type="password" v-model="pwdForm.password" placeholder="英文、数字及英文符号，不能含空格等"></el-input>
        </el-form-item>
        <el-form-item label="确认新密码" prop="checkPass">
          <el-input type="password" v-model="pwdForm.checkPass" placeholder="请再次输入新密码"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showPwdDialog = false">取 消</el-button>
        <el-button
          size="small"
          type="primary"
          :loading="sending"
          @click="updatePwd"
        >{{sending ? '正在提交...' : '确定'}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import api from "api";
import members from "./members.vue";
import breadcrumb from "../components/breadcrumb.vue";

export default {
  name: "Home",
  data() {
    return {
      sending: false,
      loading: false,
      orgList: [],
      orgData: {},
      orgProp: {
        children: "children",
        label: "name"
      },
      isDelOrg: false,
      showOrgDialog: false,
      orgInfo: {},
      orgRules: {
        name: [
          { required: true, message: "请填写名称", trigger: "blur" },
          { max: 20, message: "组织名称不得超过20个字符", trigger: "blur" }
        ],
        description: [
          { max: 30, message: "内容长度不可超过30个字符", trigger: "blur" }
        ]
      },
      showPwdDialog: false,
      pwdForm: {},
      pwdRules: {
        oldPwd: [
          { required: true, message: "请输入原密码", trigger: "blur" },
          { min: 5, max: 20, message: "长度应为5-20个字符", trigger: "blur" }
        ],
        password: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          { min: 5, max: 20, message: "长度应为5-20个字符", trigger: "blur" },
          {
            validator: (rule, val, callback) => {
              if (val == this.pwdForm.oldPwd)
                var e = new Error("新密码不允许和原密码相同");
              return callback(e);
            },
            trigger: "blur"
          },
          {
            validator: (rule, val, callback) => {
              if (/\s/.test(val) || !/^[A-Za-z\d\.\$_@,;!&]+$/.test(val))
                var e = new Error(
                  '密码只能包含英文、数字及部分英文符号（"_", ",", ".", "$", "@", ";", "!", "&"），且不能含有空格等'
                );
              return callback(e);
            },
            trigger: "blur"
          }
        ],
        checkPass: [
          { required: true, message: "请再次确认密码", trigger: "blur" },
          {
            validator: (rule, val, callback) => {
              if (val !== this.pwdForm.password)
                var e = new Error("两次输入密码不一致");
              return callback(e);
            },
            trigger: "blur"
          }
        ]
      }
    };
  },
  components: {
    members,
    breadcrumb
  },
  mounted() {
    this.$store.dispatch("breadcrumb/clearPath");
    this.$store.dispatch("breadcrumb/addPath", {
      title: "组织/成员管理",
      subTitle: "成员列表",
      action: "org"
    });
    this.loadOrg();
  },
  methods: {
    addOrg() {
      this.showOrgDialog = true;
      this.orgInfo = {};
      this.$nextTick(() => {
        this.$refs.orgForm.resetFields();
        this.orgInfo = {};
      });
    },
    resetOrgDialog() {
      this.$refs.orgForm.resetFields();
      this.orgInfo = {};
    },
    async loadOrg() {
      this.loading = true;
      const result = await api.org.list();
      this.loading = false;
      if (result.code === 200) {
        const orgList = [];
        result.data.forEach(data => {
          data.children = [];
          if (data.parentId == 0) {
            orgList.push(data);
          }
        });
        result.data.forEach(data => {
          orgList.forEach(org => {
            if (data.parentId == org.id) {
              result.data.forEach(tempData => {
                if (tempData.parentId == data.id) {
                  data.children.push(tempData);
                }
              });
              org.children.push(data);
            }
          });
        });
        orgList.unshift({
          id: 0,
          name: "全部",
          children: []
        });
        this.orgData = orgList[0];
        this.orgList = orgList;
        this.$nextTick(() => {
          this.$refs.orgTree.setCurrentKey("0");
        });
      } else {
        this.$message.error(result.msg);
      }
    },
    handleOrgClick(data) {
      this.isDelOrg = false;
      this.orgData = data;
    },
    renderContent(h, { node, data, store }) {
      if (data.id == 0) {
        return h("div", data.name);
      }
      return h("div", [
        data.name,
        h("i", {
          class: { "el-icon-edit-outline": true },
          on: {
            click: event => {
              event.stopPropagation();
              this.orgInfo = {
                id: data.id,
                name: data.name,
                description: data.description
              };
              this.showOrgDialog = true;
            }
          }
        })
      ]);
    },
    async saveOrg() {
      if (this.isDelOrg) {
        const result = await api.org.remove(this.orgInfo.id);
        if (result.code == 200) {
          this.showOrgDialog = false;
          this.$message.success("删除成功");
          this.orgInfo = {};
          this.loadOrg();
        } else {
          this.$message.error(result.msg);
        }
        return;
      }
      this.$refs.orgForm.validate(async valid => {
        if (valid) {
          if (!this.orgInfo.id) {
            this.orgInfo.parentId = this.orgData.id || 0;
          }
          let result;
          if (this.orgInfo.id) {
            result = await api.org.update(this.orgInfo.id, {
              name: this.orgInfo.name
            });
          } else {
            result = await api.org.add(this.orgInfo);
          }
          if (result.code == 200) {
            this.showOrgDialog = false;
            if (this.orgInfo.id) {
              this.orgData.name = this.orgInfo.name;
              this.orgData.description = this.orgInfo.description;
              this.$message.success("修改成功");
            } else {
              this.$message.success("添加成功");
              this.loadOrg();
            }
            this.$refs.orgForm.resetFields();
            this.orgInfo = {};
            this.showDialog = false;
          } else {
            this.$message.error(result.msg);
          }
        } else {
          return false;
        }
      });
    },
    async updatePwd() {
      this.$refs.pwdForm.validate(async valid => {
        if (!valid) return false;
        const result = await api.sys.updateAccount({
          oldPwd: this.pwdForm.oldPwd,
          password: this.pwdForm.password
        });
        if (result.code == 200) {
          this.$message.success("密码修改成功");
          this.showPwdDialog = false;
        } else {
          this.$message.error(result.msg);
        }
      });
    }
  }
};
</script>
<style lang="scss" rel="stylesheet/scss">
.org-form {
  padding: 0 20px;
}

.organization-wrap {
  .el-card__header {
    position: relative;
    padding: 10px;
    background-color: #efefef;
    .btn-add {
      @include middle-center-y();
      right: 10px;
      padding: 5px;
    }
  }
  .el-tree-node {
    &.is-current {
      > .el-tree-node__content {
        background-color: #efefef;
        .el-icon-edit-outline {
          display: block;
        }
      }
      > .el-tree-node__children {
        background-color: #fff;
      }
    }
  }
  .el-tree-node__content {
    position: relative;
    .el-icon-edit-outline {
      display: none;
      @include middle-center-y();
      right: 10px;
      font-size: 15px;
      color: #999;
    }
  }
}

.member-wrap {
  padding: 0;
  overflow-x: hidden;
}
</style>
