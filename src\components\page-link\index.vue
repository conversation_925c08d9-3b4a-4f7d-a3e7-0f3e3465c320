<template>
  <div class="page-link">
    <el-input size="mini" class="mb-10" v-model="key" @keyup.enter.native="getList()" placeholder="关键字" clearable>
      <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
    </el-input>

    <el-table size="mini" :data="list" highlight-current-row @current-change="onSelect" style="margin-bottom:5px"
              v-loading="loading">
      <el-table-column label="模板名称" width="100">
        <template slot-scope="scope">
          <p>{{scope.row.page_name}}</p>
        </template>
      </el-table-column>
      <el-table-column label="用户可见名称" width="100">
        <template slot-scope="scope">
          <p>{{scope.row.page_title?scope.row.page_title:scope.row.page_name}}</p>
        </template>
      </el-table-column>
      <el-table-column label="路径" prop="item_name">
        <template slot-scope="scope">
          <span>{{scope.row.page_url?scope.row.page_url:`ybmpage://commonh5activity?cache=0&url=${scope.row.url}`}}</span>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      small
      layout="pager"
      :current-page="pagination.current"
      :page-size="pagination.size"
      :total="pagination.total"
      @current-change="getList">
    </el-pagination>
  </div>
</template>

<script>
  import api from 'api';

  export default {
    props: {
      params: Object
    },
    data() {
      return {
        id: '',
        key: '',
        sku_key: '',
        list: [],
        pagination: {
          size: 5,
          current: 1,
          total: 0
        },
        loading: false,
        manualId: ''
      }
    },
    methods: {
      async getList(page = 1) {
        this.pagination.current = page;
        this.pagination.size = 5;
        this.loading = true;
        let page_type = "";
        if (this.params.page_type==="YKQ") {
          page_type="YKQ"
        }
        const params = {
          pageFrom: this.pagination.current,
          pageSize: this.pagination.size,
          state: 1,
          branchCode: this.params.branchCode,
          page_type: page_type,
          category: 'app',
        };
        const searchParam = {
          page_name: this.key || '',
        };
        let pms = Object.assign(params, searchParam);
        let result;
        if(page_type){
          result=await api.topic.list(pms);
        }else {
          result = await api.activity.list(pms);
        }
        this.loading = false;
        if (result.code === 200) {
          this.$nextTick(() => {
            this.list = result.data.rows;
            this.pagination.total = result.data.total;
          })
        } else {
          this.$message.error(result.msg);
        }
      },
      onSelect(row) {
        console.log("惦记了！！！")
        if (!row)
          return;
        this.$emit('select', {
          type: 'item',
          label: '活动页',
          id: row.productSid,
          desc: row.productName,
          meta: {
            'id': row.id,
            'page_url': row.page_url?row.page_url:`ybmpage://commonh5activity?cache=0&url=${row.url}`,
            'page_name': row.page_name
          }
        })
      }
    },
    mounted() {
      this.getList();
    },
  }
</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .page-link {
    border: 1px solid #0cdcdc;
    padding: 3px;
  }
</style>
