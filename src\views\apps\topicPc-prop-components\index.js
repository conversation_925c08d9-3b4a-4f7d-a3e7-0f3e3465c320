import image from './H5/image'
import floorSpacing from './floor-spacing'
import fastEntry from './fast-entry'
import recommendList from './recommend-list'
import tabbar from './tabbar'
import fontTitle from "./fontTitle"
import imgTitle from "./imgTitle"

import mainOne from "./H5/the-main-venue/one"
import mainTwo from "./H5/the-main-venue/two"
import mainThree from "./H5/the-main-venue/three"
import mainFour from "./H5/the-main-venue/four"
import mainOnePlusTwo from "./H5/the-main-venue/one_plus_two"
import mainTwoPlusOne from "./H5/the-main-venue/two_plus_one"
import mainOnePlusThree from "./H5/the-main-venue/one_plus_three"
import mainOnePlusFour from "./H5/the-main-venue/one_plus_four"
import mainTwoPlusTwo from "./H5/the-main-venue/two_plus_two"
// 弹窗优惠券
import popUpCoupon from "./H5/popUpCoupon";
import controlOne from "./controlMall-H5/common/controlMallAds/one";

//商品流
import commodity_flow from "./H5/commodity-flow"

// 品类商品流
import categiory_flow from './H5/categiory-flow';

//选项卡
import tabs_page from "./H5/tabs-page"

//倒计时商品
import limitTimeGoods from "./H5/limit-group"

import swiper from "./swiper"

export default {
  pc_h5: [{
      title: 'PC活动页',
      name: 'PcActivityPage',
      children: [
        {
          title: '头图',
          name: 'image',
          component: image
        },
        {
          title: '一图',
          name: 'mainOne',
          component: mainOne
        },
        {
          title: '二图',
          name: 'mainTwo',
          component: mainTwo
        },
        {
          title: '三图',
          name: 'mainThree',
          component: mainThree
        },
        {
          title: '四图',
          name: 'mainFour',
          component: mainFour
        },
        {
          title: '1+2图',
          name: 'mainOnePlusTwo',
          component: mainOnePlusTwo
        },
        {
          title: '1+3图',
          name: 'mainOnePlusThree',
          component: mainOnePlusThree
        },
        {
          title: '1+4图',
          name: 'mainOnePlusFour',
          component: mainOnePlusFour
        },
        {
          title: '2+1图',
          name: 'mainTwoPlusOne',
          component: mainTwoPlusOne
        },
        {
          title: '2+2图',
          name: 'mainTwoPlusTwo',
          component: mainTwoPlusTwo
        },
        {
          title: '倒计时',
          name: 'limitTimeGoods',
          component: limitTimeGoods
        },
        {
          title: '商品流',
          name: 'commodity_flow',
          component: commodity_flow
        },
        {
          title: '选项卡',
          name: 'tabs_page',
          component: tabs_page
        },
        {
          title: '轮播图',
          name: 'swiper',
          component: swiper
        },
        {
          title: '快捷入口',
          name: 'fastEntry',
          component: fastEntry
        },
        {
          title: '文字标题',
          name: 'fontTitle',
          component: fontTitle
        },
        {
          title: '图片标题',
          name: 'imgTitle',
          component: imgTitle
        },
        {
          title: '横向商品展示',
          name: 'recommendList',
          component: recommendList
        },
        {
          title: '楼层间隔',
          name: 'floorSpacing',
          component: floorSpacing
        },
        {
          title: '悬浮导航',
          name: 'tabbar',
          component: tabbar
        },
        {
          title: "品类商品流",
          name: "categiory_flow",
          component: categiory_flow,
        }
      ]
    }
  ],
  pc_diaNewLog: [
    {
      title: "PC弹窗",
      children: [
        {
          title: "一图",
          name: "controlOne",
          component: controlOne
        },
        {
          title: "弹窗优惠券",
          name: "popUpCoupon",
          component: popUpCoupon
        }
      ]
    }
  ],
  // pc_dialog: [
  //     {
  //       title: 'PC弹窗',
  //       children: [
  //         {
  //           title: '活动提醒',
  //           name: 'activityTips',
  //           component: activityTips
  //         }
  //       ]
  //     }
  //   ],
}
