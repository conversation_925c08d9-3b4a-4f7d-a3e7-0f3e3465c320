<template>
    <div>
        <el-input size="small" style="margin-bottom:5px" v-model="manualId" placeholder="手动输入优惠券ID">
            <el-button slot="append" @click="confirm">确定</el-button>
        </el-input>
        <el-input size="small" style="margin-bottom:5px" v-model="key" placeholder="请输入关键字">
            <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
        </el-input>
        <el-table size="mini" :data="list"  highlight-current-row  @current-change="onSelect" :row-style="getRowStyle" style="margin:5px 0" v-loading="loading">
            <el-table-column label="名称/code/有效期">
                <template slot-scope="scope">
                    <p>
                        <small style="color:#999">{{scope.row.code}}</small>
                    </p>
                    <p>
                        {{scope.row.start_time}}
                        <span style="color:#ccc;margin:0 .5em">至</span>
                        {{scope.row.end_time}}

                        <span style="float:right;color:#ccc;">{{scope.row.status | couponStatus}}</span>
                    </p>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination
            small
            layout="pager"
            :current-page="pagination.current"
            :page-size="pagination.size"
            :total="pagination.total"
            @current-change="getList">
        </el-pagination>
    </div>
</template>

<script>
export default {
    data() {
        return {
            text         : '',
            pagination: {
                size   : 5,
                current: 1,
                total  : 0
            },
            list: [],
            loading: false
        }
    },
    methods: {
        confirm(){
            if(!this.manualId){
                this.$message.warning('请输入优惠券ID');
                return false;
            }
            this.$emit('select', {
                type : 'coupon',
                label: '优惠券' + this.manualId,
                codes: this.manualId,
                desc : this.manualId,
                meta : this.manualId
            })
        },
        async getList(page=1){
            this.pagination.current = page;
            this.loading = true;
            this.loading = false;
        },
        onSelect(row){
            this.$emit('select', {
                type : 'coupon',
                label: '优惠券',
                codes: row.code,
                desc : row.name,
                meta : row
            })
        },
        getRowStyle(row){
            return _.merge({
                height: '80px',
                cursor: 'pointer',
            }, {
                1: {},
                2: {},
                3: {
                    color: '#999',
                    background: '#eee'
                },
                4: {
                    color: '#999',
                    background: '#eee'
                }
            }[row.status])
        },
    },
    mounted(){
        this.getList();
    },
    filters: {
        couponStatus(value){
            return {
                1: '未开始',
                2: '进行中',
                3: '已结束',
                4: '已失效'
            }[value]
        }
    }
}
</script>
