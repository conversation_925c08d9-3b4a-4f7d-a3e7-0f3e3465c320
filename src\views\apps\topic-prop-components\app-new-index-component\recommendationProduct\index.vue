
<template>
  <div class="topic-search">
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="12">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
          <img v-if="content.bgImg" :src="content.bgImg" alt="">
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <div>
            <el-button @click="imgOnclick">清除背景图</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">背景色</span>
          <el-color-picker v-model="content.bgRes" size="mini" @change="onSelect"></el-color-picker>
        </div>
      </el-col>
    </el-row>
    <!--table-->
    <el-row :gutter="20">
      <div class="title">信息配置</div>
      <el-row class="carouselFlexBox">
        <el-col :span="11" class="carouselFlex">
          <span>活动id：</span>
          <el-input v-model="queryParams.activityId" size="mini" clearable></el-input>
        </el-col>
        <el-col :span="11" class="carouselFlex">
          <span>活动名称：</span>
          <el-input v-model="queryParams.activityName" size="mini" clearable></el-input>
        </el-col>
        <el-col  class="carouselFlex crowdInput">
          <span>人群id：</span>
          <el-input v-model="queryParams.crowdValue" @blur="querySearchCrowd" @change="crowdName = ''" size="mini" clearable></el-input>
          <div class="crowdNameDiv">{{ crowdName }}</div>
        </el-col>
        <el-col class="carouselFlex">
          <span>展示时间：</span>
          <el-date-picker
            v-model="queryParams.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-col>
        <el-col :span="10" class="carouselFlex">
          <span>状态：</span>
          <el-select v-model="queryParams.status" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in queryStatusOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col class="carouselButton">
          <el-button type="primary" @click="addList" size="mini">新增</el-button>
          <el-button type="primary" @click="searchList" size="mini">查询</el-button>
          <el-button type="primary" @click="resetQueryParams" size="mini">重置</el-button>
        </el-col>
      </el-row>
      <el-table :data="dataList" size="mini" class="tableBox" style="margin: 0 0 20px" ref="tableBox" :row-key="row => row.id">
        <el-table-column label="id" width="150" prop="activityId"></el-table-column>
        <el-table-column label="活动名称" width="150" prop="activityName"></el-table-column>
        <el-table-column label="顺序" >
          <template slot-scope="scope">
            <el-input v-model="scope.row.sort" onkeyup="value=value.replace(/[^\d]/g,'')" @blur="changeSort(scope)" @keyup.enter.native="changeSort(scope)"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="人群" show-overflow-tooltip width="150">
          <template slot-scope="scope">
            <p v-if="scope.row.crowdType == 2">
              {{scope.row.crowdId + '/' + scope.row.crowdValue || '全部人群'}}
            </p>
            <p v-else>该页面已选人群</p>
          </template>
        </el-table-column>
        <el-table-column label="展示时间" width="300">
          <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
            </div>
            <div v-else> 
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div >
              {{ ['未开始', '上线', '已结束', '下线'][scope.row.status - 1] || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button size="mini" v-if="scope.row.status == 1 || scope.row.status == 3 || scope.row.status == 4" @click="toEdit(scope.row, scope.$index)" type="text">编辑
            </el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="toRemove(scope.row)" type="text">删除</el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="online(scope)" type="text">上线</el-button>
            <el-button size="mini" @click="offline(scope)" v-if="scope.row.status == 2" type="text">下线</el-button>
            <el-button size="mini" v-if="scope.row.status == 2" @click="toEdit(scope.row, scope.$index,true)" type="text">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <!-- 新增 -->
    <el-dialog class="banner-dialog" :title="`推荐商品${isEdit ? '编辑': '新建'}`" :before-close="addDialogCancel" :visible.sync="addDialog">
      <el-form :disabled="isInfo" label-position="right" ref="addRuleForm"  :model="addForm" :rules="addRules" size="small" label-width="100px" label-suffix="：">
        <el-form-item label="活动名称" prop="activityName">
          <el-input v-model="addForm.activityName" maxlength="20" size="mini" placeholder="请输入热词组名称，20个字符以内" clearable></el-input>
        </el-form-item>
        <el-form-item label="推荐位置" prop="">
          <el-radio-group v-model="addForm.commendationPosition">
            <el-radio :label="1">左侧推荐区</el-radio>
            <el-radio :label="2">右侧推荐区</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="人群圈选" prop="crowdType">
          <el-radio-group v-model="addForm.crowdType" @change="changeCrowdType">
            <el-radio :label="1">该页面已选中人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="addForm.crowdType===2" prop="crowdValue" label="指定人群">
          <el-select
            v-model.trim="addForm.crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-autocomplete style="width: 100%" class="inline-input" v-model.trim="dataForm.crowdValue" :fetch-suggestions="querySearchCrowd" placeholder="请输入人群id" :trigger-on-focus="false"
            @select="handleSelectCrowd" @input="changeCrowdValue"></el-autocomplete> -->
        </el-form-item>
        <el-form-item label="展示时间" :prop="addForm.timeType == 1 ? 'validityTime' : 'circulateTime'">
          <el-radio  v-model="addForm.timeType" :label="1">固定时段</el-radio> 
          <el-date-picker
            v-if="addForm.timeType == 1"
            v-model="addForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            type="datetimerange"
            :picker-options="{
              disabledDate: (time) => {
                const times = new Date(new Date().toLocaleDateString()).getTime() + 1095 * 8.64e7 - 1
                return time.getTime() < Date.now() - 8.64e7 || time.getTime() > times// 如果没有后面的-8.64e7就是不可以选择今天的
              }
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker><br>
          <el-radio  v-model="addForm.timeType" :label="2">周期循环</el-radio>
          <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
          <br>
          <div v-for="(item,index) in addForm.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[addForm.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{addForm.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
        </el-form-item>
        <el-form-item label="背景图" prop="bgImg">
          <div class="add-color-back">
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'bgImg')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addForm.bgImg" :src="addForm.bgImg" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addForm.bgImg = ''">清空重置</el-button>
          </div>
        </el-form-item>
        <el-form-item label="楼层图标" prop="floorIcon">
          <div class="add-color-back">
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'floorIcon')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addForm.floorIcon" :src="addForm.floorIcon" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addForm.floorIcon = ''">清空重置</el-button>
          </div>
        </el-form-item>
        <el-form-item label="主标题" prop="mainTitle">
          <el-radio-group v-model="addForm.mainTitleType">
            <el-radio :label="1">文字</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group>
          <div class="add-color-item" v-if="addForm.mainTitleType == 1">
            <el-input v-model="addForm.mainTitleText" maxlength="999" size="mini" placeholder="限制6个字，默认居中显示" clearable></el-input>
            <span class="demonstration">点击设置纯色</span>
            <div>
              <el-color-picker v-model="addForm.mainTitle" size="mini"></el-color-picker>
            </div>
            <div class="titleBlock" style="width: 200px;">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider v-model="addForm.mainTitleColorTransparency" :format-tooltip="formatTooltip"></el-slider>
              </div>
            </div>
          </div>
          <div class="add-color-back" v-else>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'mainTitle')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addForm.mainTitleType == 2 && addForm.mainTitle" :src="addForm.mainTitle" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addForm.mainTitle = ''">清空重置</el-button>
          </div>
        </el-form-item>
        <el-form-item label="副标题" prop="subTitle">
          <el-radio-group v-model="addForm.subTitleType">
            <el-radio :label="1">文字</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group>
          <div class="add-color-item" v-if="addForm.subTitleType == 1">
            <el-input v-model="addForm.subTitleText" maxlength="999" size="mini" placeholder="限制6个字，默认居中显示" clearable></el-input>
            <span class="demonstration">点击设置纯色</span>
            <div>
              <el-color-picker v-model="addForm.subTitle" size="mini"></el-color-picker>
            </div>
            <div class="titleBlock" style="width: 200px;">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider v-model="addForm.subTitleColorTransparency" :format-tooltip="formatTooltip"></el-slider>
              </div>
            </div>
          </div>
          <div class="add-color-back" v-else>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'subTitle')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addForm.subTitleType == 2 && addForm.subTitle" :src="addForm.subTitle" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addForm.subTitle = ''">清空重置</el-button>
          </div>
        </el-form-item>
        <el-form-item label="选品方式" prop="selectProductType">
        <el-select size="small" @change="changeProductsType" v-model="addForm.selectProductType" placeholder="请选择">
          <!-- <el-option label="指定商品" value="appointProduct" /> -->
          <el-option label="指定商品组" value="appointProductGroup" />
          <el-option label="系统自动" value="systemAuto" />
        </el-select>
      </el-form-item>
      <el-form-item label="商品组ID" v-if="addForm.selectProductType === 'appointProductGroup'" prop="selectProductGroupId">
        <el-input style="width: 200px" size="small" placeholder="请输入内容" v-model="addForm.selectProductGroupId" />
      </el-form-item>
      <el-form-item label="转跳类型" prop="hrefType">
        <el-radio-group v-model="addForm.hrefType">
        <el-radio :label="1">内部跳转</el-radio>
        <el-radio :label="2">外部跳转</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="转跳链接" prop="hrefUrl">
          <el-input v-model="addForm.hrefUrl" size="mini" clearable></el-input>
          <el-button v-if="addForm.hrefType == 1" type="primary" size="small" @click="isShowHrefDialog = true">more</el-button>
      </el-form-item>
      <!-- <el-table
        v-if="addForm.selectProductType === 'appointProduct'"
        :data="addForm.selectProducts"
        size="mini"
      >
        <el-table-column label="显示序号" type="index" width="100" />
        <el-table-column label="指定商品ID">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row"
              size="mini"
              placeholder="请输入指定商品ID"
              clearable
              style="width: 200px"
              @input="changeInput($event, scope.$index)"
            />
          </template>
        </el-table-column>
      </el-table>-->
      
      </el-form>
      <el-dialog
        title="跳转链接配置"
        :visible.sync="isShowHrefDialog"
        width="30%"
        append-to-body
      >
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
        <span slot="footer" class="dialog-footer">
          <el-button @click="hrefCancel">取 消</el-button>
          <el-button type="primary" @click="hrefConfirm">确 定</el-button>
        </span>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" v-if="!isInfo" @click="addDialogConfirm">确定</el-button>
      </div>
    </el-dialog>
    <loopcirculateTime ref="loopcirculateTime" @loopcirculateTimeBack="loopcirculateTimeBack"></loopcirculateTime>
  </div>
</template>
<script>
import loopcirculateTime from '../../../components/loopcirculateTime.vue';
import base from "../../base";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import Sortable from 'sortablejs';
let sortableObject = {}
export default {
  name: 'searchBox',
  props: {
    core: Object,
  },
  extends: base,
  components: { swiperPoint,loopcirculateTime },
  contentDefault: {
    tab_bg_color1: "",
    tab_bg_color2: "",
    pro_obj: {
      pro_type: "longBar",
      pro_auto: 0,
      pro_align_type: "center",
      default_color: "#ffffff",
      default_opacity: 30,
      active_color: "#555555",
      active_opacity: 100,
      component_name: "searchBox", //区分模块的标识
    },
    list: [],
    search_text: "您常搜",
    active_icon_color: "#ffffff",
    default_icon_color: "#ffffff",
    // top_bgRes: "#00B377",
    // hotWord_bgRes: "#00B377",
    // meddle_bgRes: "#00B377",
    // bottom_bgRes: "#00B377",
    // refresh_bgRes: "#00B377"
    top_bgRes: "#fff",
    hotWord_bgRes: "#fff",
    meddle_bgRes: "#fff",
    bottom_bgRes: "#fff",
    refresh_bgRes: "#fff"
  },
  data() {
    return {
      isInfo:false,
      currentId: '',
      addRules: {
        activityName: [
          { required: true, message: "请填写活动名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" }
        ],
        crowdType: [{ required: true, message: "请选择指定人群", trigger: "change" }],
        crowdValue: [
          { required: true, message: "请填写人群名称", trigger: "blur" },
        ],
        bgImg: [
          { required: true, message: "请选择背景图", trigger: "change" }
        ],
        floorIcon: [
          { required: true, message: "请选择楼层图标", trigger: "change" }
        ],
        mainTitle: [
          { required: true, message: "请选择主标题", trigger: "change" }
        ],
        subTitle: [
          { required: true, message: "请选择副标题", trigger: "change" }
        ],
        hrefUrl: [
          { required: true, message: "请填写转跳链接", trigger: "blur" },
        ],
        validityTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        circulateTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        selectProductType: [
          { required: true, message: "请选择选品方式", trigger: "change" }
        ],
        selectProductGroupId: [
          { required: true, message: "请输入商品组id", trigger: "blur" },
        ],
        hrefType: [
          { required: true, message: "请选择转跳类型", trigger: "change" }
        ],
      }, 
      // 时间不能大于当前时间
      disabledDate: time => {
        return time.getTime() > Date.now()
      },
      isEdit: false,
      goods: [{ required: true, validator: this.goodsValid, trigger: "blur" }],
      isShowHrefDialog: false,
      keys: 'id',
      dataList: [], // 查询完的列表
      addForm: {
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        activityId: '', //活动id
        activityName: '', //活动名称
        crowdValue: '', // 人群
        commendationPosition: 1,
        bgImg: '',
        floorIcon: '',
        mainTitleType: 1,
        mainTitleText: '',
        mainTitle: '#eee',
        mainTitleColorTransparency: 100,
        subTitleType: 1,
        subTitleText: '',
        subTitle: '#eee',
        subTitleColorTransparency: 100,
        hrefUrl: '',
        hrefType: 1,
        timeType: 1,
        circulateTime: '',
        status: 1,
        selectProductType: 'appointProductGroup',
        selectProductGroupId: '',
      },
      crowdName: '',
      addFormSelectLink: '',
      queryParams: {
        activityId: '',
        activityName: '',
        validityTime: '', //有效期
        crowdValue: '', // 人群
        status: '', //状态
      },
      queryStatusOption: [
        {id: '', name: '全部'},
        {id: 1, name: '未开始'},
        {id: 2, name: '上线'},
        {id: 3, name: '已结束'},
        {id: 4, name: '下线'},
      ],
      hrefOption: [
        {value: 1, label: '不挂链接'},
        {value: 2, label: '专题页链接'},
        {value: 3, label: '店铺页链接'},
        {value: 4, label: '动态商品页链接'},
      ],
      carouselList: {
        bannerLocation: '',
        crowdValue: '',
        status: ''
      },
      currentData: {},
      currentDataIndex: 0,
      currentIndex: 0,
      activeTab: 'notInvalid',
      selectLoading: false,
      options: [],
      pickerOptions0: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      pickerOptions1: {
        shortcuts: [{
          text: '未来一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来六个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来一年',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      loading: false,
      addDialog: false,
      dataForm: {
        bannerLocation: '',
        bannerType: "inLink",
        image: '',
        link: {
          meta: {
            id: 0,
            page_url: ''
          }
        },
        timevalue: '',
        // bgRes: "#00B377",
        bgRes: "#eee",
        rest_bgRes: "#ffffff",
        crowdType: 1,
        crowdValue: '',
        crowdId: '',
        timeType:'1',
        circulateTime:{}
      },
    }
  },
  filters: {
    link(data) {
      return data.meta.page_url;
    },
    dateFilter(date) {
      function formatDate(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return year + '-' + (String(month).length > 1 ? month : '0' + month) + '-' +
          (String(day).length > 1 ? day : '0' + day) + ' ' + (String(hour).length > 1 ? hour : '0' + hour) + ':' + (String(minute).length > 1 ? minute : '0' + minute)
          + ':' + (String(second).length > 1 ? second : '0' + second)
      }

      if (date) {
        let date1 = formatDate(new Date(date[0]));
        let date2 = formatDate(new Date(date[1]));
        // const nS=new Date(date).getTime()
        return date1 + "至" + date2
      } else {
        return " "
      }
    },
    jumpText(val) {
      if (!val) {
        return "app内部跳转"
      } else {
        if (val === "inLink") {
          return "app内部跳转"
        }
        return "跳转至外部"
      }
    }
  },
  async mounted() {
    // const params = {
    //   type: this.addForm.selectProductType === 'appointProduct' ? 1: 2,
    //   exhibitionId: this.addForm.selectProductGroupId,
    //   // csuIds: this.addForm.selectProducts.filter(i => i).map(Number),
    // }
    // const result = await api.topic.checkBindCsuOrProductGroup(params);
    this.initData();
    this.initDataStatus();
    // this.rowDrop()
    this.changeTab('notInvalid')
    // this.searchList()
  },
  computed: {
    /**
     *   获取列的状态名称
     */
    getStatusName() {
      return function (timevalue, type) {
        let item = {}
        if (!timevalue) {
          item = {
            id: 4,
            name: '未设置时间'
          }
        } else {
          const _date = new Date().getTime();
          const start = new Date(timevalue[0]).getTime();
          const end = new Date(timevalue[1]).getTime();
          if (_date <= end && _date >= start) {
            item = {
              id: 1,
              name: '生效中'
            }
          } else if (_date > end) {
            item = {
              id: 3,
              name: '已失效'
            }
          } else if (_date < start) {
            item = {
              id: 2,
              name: '待生效'
            }
          }
        }
        if (type == 'id') {
          return item.id
        } else {
          return item.name
        }
      }
    },
    // list() {
    //   let list = _.get(this, 'content.list')
    //   if (list) {
    //     if (list.length > 0 && list[0].link.meta) {
    //       this.$nextTick(function () {
    //         this.setSort()
    //       })
    //     }
    //     return list
    //   } else {
    //     return [];
    //   }
    // }
  },
  methods: {
    // 校验绑定商品
      async checkBindCsuOrProductGroup() {
        let canSave = true;
        (this.addForm.selectProducts || []).forEach((item) => {
          if (isNaN(item) || item < 0) {
            canSave = false;
          }
        });
        if (!canSave) {
          this.$message.error('指定商品ID只能输入数字');
          return;
        }
        const params = {
          type: this.addForm.selectProductType === 'appointProduct' ? 1: 2,
          exhibitionId: this.addForm.selectProductGroupId,
          // csuIds: this.addForm.selectProducts.filter(i => i).map(Number),
        }
        const result = await api.topic.checkBindCsuOrProductGroup(params);
        if ((result.data.data || {}).checkResult) {
          // if (this.addForm.selectProductType === 'appointProduct' ) {
          //   this.$set(this.addForm, 'selectProducts', this.addForm.selectProducts)
          // } else {
          //   this.addForm.selectProductGroupId = this.productGroupId;
          // }
          this.$message.success('绑定成功')
          this.confirm();
        } else {
          // this.addForm.selectProductGroupId = '';
          if (this.addForm.selectProductType === 'appointProduct' ) {
            this.$message.error(`以下商品id绑定失败：${((result.data.data || {}).failureCsuIds || []).join()}`)
          } else {
            this.$message.error(result.data.msg)
          }
        }
      },
    changeProductsType() {
      this.addForm.selectProductGroupId = '';
      // this.productGroupId = '';
    },
    onSelect(val, isEditEntry) {
      if (isEditEntry == "editing") {
        if (val) {
          this.addForm.frontColor = val;
        } else {
          this.addForm.frontColor = "#000000";
        }
      } else {
        if (val) {
          this.content.bgRes = val;
        } else {
          this.content.bgRes = "#eee";
        }
      }
    },
    imgOnclick() {
      this.content.bgImg = null;
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      // this.content.bgImg = res.data.url;
      this.$set(this.content, 'bgImg', res.data.url);
    },
    formatTooltip(val) {
      return val / 100;
    },
    changeSort(scope) {
      let ind = scope.$index;
      if (this.dataList[ind].sort <= 0) {
        this.$message.warning("请输入大于0的数字！")
        return;
      }
      if (this.dataList[ind].sort >= this.dataList.length) {
        this.dataList.splice(this.dataList.length, 0, this.dataList[ind]);
        this.dataList.splice(ind, 1)
      } else {
        if (this.dataList[ind].sort > ind) {
          this.dataList.splice(this.dataList[ind].sort, 0, this.dataList[ind]);
          this.dataList.splice(ind, 1);
        } else {
          this.dataList.splice(this.dataList[ind].sort - 1, 0, this.dataList[ind]);
          this.dataList.splice(ind + 1, 1);
        }
      }
      this.dataList.forEach((item, index) => {
        item.sort = index + 1;
      })
    },
    online(scope) {
      this.$confirm(
        "确定要执行上线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.list[this.content.list.findIndex(i => i.activityId == scope.row.activityId)].status = 2;
        this.$message.success("操作成功！")
        this.initData();
        this.searchList();
      });
    },
    offline(scope) {
      this.$confirm(
        "确定要执行下线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        // this.content.list[scope.$index].status = 4;
        this.content.list[this.content.list.findIndex(i => i.activityId == scope.row.activityId)].status = 4;
        this.$message.success("操作成功！")
        this.initData();
        this.searchList();
      });
    },
    addDialogCancel() {
      this.resetAddForm();
      this.addDialog = false;
    },
    hrefCancel() {
      this.addFormSelectLink = '';
      this.isShowHrefDialog = false;
    },
    hrefConfirm() {
      this.addForm.hrefUrl = this.addFormSelectLink;
      this.$refs['addRuleForm'].clearValidate('hrefUrl');
      this.isShowHrefDialog = false;
    },
    resetAddForm() {
      this.addForm = {
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        activityId: '', //活动id
        activityName: '', //活动名称
        crowdValue: '', // 人群
        commendationPosition: 1,
        bgImg: '',
        floorIcon: '',
        mainTitleType: 1,
        mainTitleText: '',
        mainTitle: '#eee',
        mainTitleColorTransparency: 100,
        subTitleType: 1,
        subTitleText: '',
        subTitle: '#eee',
        subTitleColorTransparency: 100,
        hrefUrl: '',
        hrefType: 1,
        timeType: 1,
        circulateTime: '',
        status: 1,
        selectProductType: 'appointProductGroup',
        selectProductGroupId: '',
      }
    },
    addList() {
      this.isEdit = false;
      this.isInfo=false
      this.addDialog = true;
    },
    resetQueryParams() {
      this.queryParams = {
        activityId: '',
        activityName: '',
        validityTime: '', //有效期
        wordName: '', //词组名称
        crowdValue: '', // 人群
        status: '', //状态
      }
      this.crowdName = "";
      this.searchList()
    },
    //打开时间循环
    toloopcirculateTime(){
      this.$refs.loopcirculateTime.showVisible=true
    },
    //循环时间回调
    loopcirculateTimeBack(data){
      this.$set(this.addForm, 'circulateTime', data);
    },
    //链接去掉空格
    urlChange(){
      this.dataForm.link.meta.page_url=this.dataForm.link.meta.page_url.trim()
    },
    initData() {
      this.dataList = this.content.list;
    },
    initDataStatus() {
      this.dataList = this.setStatusInitList(this.dataList);
    },
    setStatusInitList(data) {
      if (!data) {
        return;
      }
      data.forEach((item, index) => {
        // item.sort = index + 1;
        this.$set(item, 'sort', index + 1);
        // 1:"月 ",2:"周 ",3:"日 "
        // if (item.status != 4) {
          if(item.timeType==2){
            if(item.status!=4){
              item.status=2
            }
          }else{
            if (new Date() * 1 < new Date(item.validityTime[0]) * 1) {
              item.status = 1; // 未开始
            } else if (new Date() * 1 > new Date(item.validityTime[0]) * 1 && new Date() * 1 < new Date(item.validityTime[1]) * 1 && item.status != 4) {
              item.status = 2; //上线
            } else if (new Date() * 1 > new Date(item.validityTime[1]) * 1) {
              item.status = 3;
            } else {
              item.status = 4;
            }
          }
        // }
      })
      return data;
    },
    rowDrop() {
      const _this = this;
      const tbody = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0];
      sortableObject = Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        ghostClass: "sortable-ghost",
        onEnd: evt => {
          const currRow = (_this.dataList || []).splice(evt.oldIndex, 1)[0];
          (_this.dataList || []).splice(evt.newIndex, 0, currRow);
          const currRowData = (_this.content.list || []).splice(evt.oldIndex, 1)[0];
          (_this.content.list || []).splice(evt.newIndex, 0, currRowData);
        }
      });
    },
    changeCrowdValue(e) {
      if (!e) {
        this.dataForm.crowdId = '';
      }
      this.$forceUpdate();
    },
    clear_bgs(type) {
      this.content[type + '_url'] = '';
      this.content[type + '_color'] = '';
      this.content[type + '_transparency'] = 100;
      
      // this.content.top_bgRes = "#fff";
      // this.content.meddle_bgRes = "#fff";
      // this.content.bottom_bgRes = "#fff";
      // this.content.hotWord_bgRes = "#fff"
    },

    // 设置轮播链接
    onSetLink(link) {
      this.addFormSelectLink = link.meta.page_url;
    },

    // 上传banner对应的头部区域背景图片
    async UploadTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.addForm, type, res.data.url);
      this.$refs['addRuleForm'].clearValidate();
    },
    changeCrowdType() {
      this.addForm.crowdId = '';
      this.addForm.crowdValue = '';
    },
    async addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        // if (!this.isEdit && this.content.list.findIndex(item => item.wordName === this.addForm.wordName) > -1) {
        //   this.$message("热词组名称已存在！");
        //   return;
        // }
        if (this.addForm.mainTitleType == 1 && !this.addForm.mainTitleText) {
          this.$message.warning("主标题不能为空！");
          return;
        } else if (this.addForm.subTitleType == 1 && !this.addForm.subTitleText) {
          this.$message.warning("副标题不能为空！");
          return;
        }
        
        if (this.addForm.hrefType==1&&!new RegExp("^ybmpage://commonh5activity.*$").test(this.addForm.hrefUrl)) {
          this.$message.error("跳转链接格式不正确，请检查");
          return false;
        }
        let linkErrMsg = '';
        if(this.addForm.hrefType==1){
            let linkPageUrl = getUrlParam(this.addForm.hrefUrl, 'url')
             const loading = this.$loading({
                lock: true,
                text: '校验中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
            const result = await api.topic.checkPageUrl({ url: linkPageUrl });
             loading.close()
            if (((result || {}).data || {}).status != 200) {
              linkErrMsg = '跳转链接不存在，请检查';
            }
        }
        if (linkErrMsg) {
          this.$message.error(linkErrMsg);
          return false;
        }

        if (this.addForm.selectProductType === 'systemAuto') {
          this.confirm();
        } else {
          this.checkBindCsuOrProductGroup();
        }
      });
    },
    confirm() {
      let classFlag = false;
      let filterArr = JSON.parse(JSON.stringify(this.content.list));
      if (this.isEdit) {
        filterArr = filterArr.filter(item => item.activityId != this.addForm.activityId);
      }
      if (this.addForm.commendationPosition == 1) {
        filterArr = filterArr.filter(item => item.commendationPosition == 1);
      } else if (this.addForm.commendationPosition == 2) {
        filterArr = filterArr.filter(item => item.commendationPosition == 2);
      }
      // if (
      //   (this.addForm.crowdType == 1 && filterArr.findIndex(item => item.crowdType == 1) > -1)
      //   || this.addForm.crowdType != 1 && filterArr.findIndex(item => item.crowdType != 1 && item.crowdId == this.addForm.crowdId && item.timeType == this.addForm.timeType) > -1
      // ) {
      //   if (this.addForm.timeType == 1) {
      //     const start_form = new Date(this.addForm.validityTime[0]).getTime();          
      //     const end_form = new Date(this.addForm.validityTime[1]).getTime();          
      //     filterArr.forEach(item => {
      //       if(item.timeType == 1) {
      //         const start = new Date(item.validityTime[0]).getTime();
      //         const end = new Date(item.validityTime[1]).getTime();
      //         if (start_form <= start && end_form >= end) {
      //           classFlag = true;
      //         } else if ((start_form >= start && start_form <= end) || (end_form >= start && end_form <= end)) {
      //           classFlag = true;
      //         }
      //       }
      //     })
      //   } else if (this.addForm.timeType == 2) {
      //     // 1:周 2:月 3:日
      //     filterArr.forEach(item => {
      //       if(Array.isArray(this.addForm.circulateTime.circulateList)){
      //         // let _date =  new Date().toLocaleTimeString('en-US', {hour12: false});  
      //         if (item.timeType == 2) {
      //           let _date = this.addForm.circulateTime.circulateList[0].selectTimeData;     
      //           if(this.addForm.circulateTime.circulateType==3){
      //             item.circulateTime.circulateList.forEach(element => {
      //               if ((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1])) {
      //                 classFlag = true;
      //               }
      //             });
      //           }
      //           if(item.circulateTime.circulateType==1 || item.circulateTime.circulateType==2){
      //             item.circulateTime.circulateList.forEach(element => {
      //               if (this.addForm.circulateTime.circulateList[0].weekOrday==element.weekOrday&&(_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1])) {
      //                 classFlag = true;
      //               }
      //             });
      //           }
      //         }
      //       }
      //     })
      //   }
      // }
      
      // if (classFlag) {
      //   this.$message.warning("展示时间不能包含列表已存在数据时间！");
      //   return;
      // }
      if (this.addForm.crowdType == 1) {
        this.addForm.crowdId = this.core.crowdId;
        this.addForm.crowdValue = this.core.crowdValue;
      }
      if (!this.isEdit) {
        let id = 0;
        id = Math.floor(Math.random() * 90000) + 10000;  
        if (this.content.list.findIndex(item => item.activityId == id) > -1) {
          this.$message("id错误，请重新添加！");
          return;
        }
        this.$set(this.addForm, 'activityId', id);
      }
      
      let arr = [...this.content.list];
      if (this.isEdit) {
        this.$set(this.content.list, this.content.list.findIndex(i => i.activityId == this.currentId), this.addForm);
      } else {
        arr.splice(0, 0, this.addForm);
        this.$set(this.content, 'list', arr);
      }
      this.$message.success(`${this.isEdit ? '编辑':'添加'}成功！`);
      this.content.list.forEach((item,index) => {
        item.sort = index + 1;
      })
      this.initData();
      this.resetAddForm();
      this.initDataStatus();
      this.resetQueryParams();
      this.searchList();
      this.addDialog = false;
    },
    // 按照规则排序--排序规则优先级：人群 > 帧位 > 生效时间（生效中>待生效>未设置）
    sortByRule(data) {
      const samePeople = this.content.list.filter((item,index) => {
        return Number(item.crowdId) === Number(this.dataForm.crowdId)
      });
      // 相同人群的逻辑
      if (samePeople.length) {
        const sameLocation = samePeople.filter((item, index) => {
          return Number(item.bannerLocation) === Number(this.dataForm.bannerLocation)
        });
        // 相同人群下，相同帧位的逻辑
        if (sameLocation.length) {
          const sameStatus = sameLocation.filter((item, index) => {
            return this.getStatusName(this.dataForm.timevalue) === this.getStatusName(item.timevalue);
          });
          let tempIndex = undefined;
          if (sameStatus.length) {
            this.content.list.forEach((item, index) => {
              if(item.id === sameStatus[sameStatus.length-1].id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，相同状态，插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === '生效中') {
            this.content.list.forEach((item, index) => {
              if (sameLocation[0].id === item.id) {
                tempIndex = index;
              }
            })
            // 相同人群，相同帧位，生效中插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === '待生效') {
            this.content.list.map((item, index) => {
              if(this.getStatusName(item.timevalue) === '生效中') {
                tempIndex = index + 1
              }
            })
            if (!tempIndex) {
              // 说明没有生效中，找未设置的
              this.content.list.map((item, index) => {
                if(this.getStatusName(item.timevalue) === '未设置时间') {
                  tempIndex = index
                }
              })
            }
            if (!tempIndex) {
              // 说明没有生效中未设置的，插到帧位最后面
              this.content.list.map((item, index) => {
                if (sameLocation[sameLocation.length - 1].id === item.id) {
                  tempIndex = index + 1;
                }
              })
            }
            // 相同人群，相同帧位，待生效插到生效中后面或未设置时间的前面或同帧位最后面
            this.content.list.splice(tempIndex, 0, data);
          } else {
            this.content.list.map((item, index) => {
              if (sameLocation[sameLocation.length - 1].id === item.id) {
                tempIndex = index;
              }
            })
            // 相同人群，相同帧位，未设置插到后面
            this.content.list.splice(tempIndex + 1, 0, data);
          }
        } else {
          // 相同人群下，不同帧位，比较帧位大小
          let tempIndex = undefined;
          // 找到第一个大于新增帧位的项，有则插入到前面，没有则插入到同人群下最后一位
          let maxItem = samePeople.find((item, index) => {
            return item.bannerLocation > this.dataForm.bannerLocation
          });
          if (maxItem) {
            this.content.list.map((item, index) => {
              if (item.id === maxItem.id) {
                tempIndex = index
              }
            })
          } else {
            this.content.list.map((item, index) => {
              if (item.id === samePeople[samePeople.length-1].id) {
                tempIndex = index + 1
              }
            })
          }
          // 新增帧位小插到前面；新增帧位大插到后面
          this.content.list.splice(tempIndex, 0, data);
        }
      } else { // 新人群，直接添加
        let tempIndex = undefined;
        tempIndex = this.content.list.filter((item) => {
          return this.getStatusName(item.timevalue) !== '已失效'
        }).length;
        this.content.list.splice(tempIndex, 0, data)
      }
      this.$nextTick(() => {
        this.searchList();
      })
      // this.content.list.push(Object.assign({}, data));
      // this.$set(this.dataList, this.dataList.length, data)
    },
    changeTab(type) {
      this.activeTab = type;
      this.carouselList.status = '';
      this.searchList();
    },
    //生成唯一id
    genID(length) {
      return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36);
    },
    //查询
    searchList() {
      //只有查询全部的时候允许拖拽
      // if (this.carouselList.status || this.carouselList.crowdValue || this.carouselList.bannerLocation) {
      //   sortableObject.option('disabled', true)
      // } else {
      //   sortableObject.option('disabled', false)
      // }
      this.dataList = this.content.list;
      if (this.queryParams.validityTime.length) {
        this.dataList = this.dataList.filter((item, index) => {
          return new Date(this.queryParams.validityTime[0]) * 1 >= new Date(item.validityTime[0]) * 1 && new Date(this.queryParams.validityTime[1]) * 1 <= new Date(item.validityTime[1]) * 1;
        })
      }
      if (this.queryParams.activityId) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.activityId == item.activityId;
        })
      }
      
      if (this.queryParams.activityName) {
        this.dataList = this.dataList.filter((item, index) => {
          return item.activityName.indexOf(this.queryParams.activityName) > -1;
        })
      }
      if (this.queryParams.crowdValue) {
        this.dataList = this.dataList.filter((item, index) => {
          return (this.queryParams.crowdValue == item.crowdId) || (item.crowdType == 1 && this.queryParams.crowdValue == this.core.crowdId);
        })
      }
      if (this.queryParams.status) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.status == item.status;
        })
      }
      this.initDataStatus();
    },
    toEdit(data, index,isInfo) {
      if(isInfo){
        this.isInfo=true
      }else{
         this.isInfo=false
      }
      this.addForm = {...data};
      this.currentDataIndex = index;
      this.currentIndex = this.content.list.findIndex((item) => item.activityId == data.activityId);
      this.currentId = data.activityId;
      if(!data.timeType){
        data.timeType=1
      }
      if(this.addForm.timeType==2&&this.addForm.circulateTime){
        this.$refs.loopcirculateTime.circulateTime=this.addForm.circulateTime
        this.$refs.loopcirculateTime.editInit()
      }
      this.isEdit = true;
      this.addDialog = true;
    },
    toRemove(data) {
      let _self = this;
      return function () {
        _self.content.list.splice(_self.content.list.findIndex((item) => item.activityId == data.activityId), 1)
        _self.dataList.splice(_self.dataList.findIndex((item) => item.activityId == data.activityId), 1)
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [{
          label: data.name,
          value: val,
        }]
      } else {
        this.selectLoading = false;
        this.options = []
      }
    },
    selectCrowd(e) {
      if (e) {
        this.addForm.crowdId = Number(this.options[0].value.trim());
        this.addForm.crowdValue = this.options[0].label;
      } else {
        this.addForm.crowdId = '';
        this.addForm.crowdValue = '';
      }
      this.$forceUpdate();
    },
    async querySearchCrowd() {
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${this.queryParams.crowdValue}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.code == 1000 && this.queryParams.crowdValue) {
        this.crowdName = res.data.name;
      }
    },
    // handleSelectCrowd(item) {
    //   this.dataForm.crowdId = item.id;
    // },
  },
  watch:{
    // "dataForm.timeType"(newdata,ordData){
     
    //   if(newdata==2){
    //     this.dataForm.timevalue=""
    //   }
    //   if(newdata==1){
    //     this.dataForm.circulateTime={}
    //   }
    // },
  }
 
}
  ;
</script>
<style lang="scss" scoped>

.topic-search {
  .add-color-back {
    display: flex;
    align-items: center;
    .el-button {
      height: 30px;
    }
    .upload-demo {
      margin-right: 15px;
      // width: 180px;
      .el-upload {
        display: flex;
        align-items: center;
        
        img {
          width: 60px;
          margin-left: 15px;
        }
      }
    }
  }
  .el-col {
    display: flex;
    >div {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .topic-item {
      display: flex;
      margin-right: 10px;
      margin-bottom: 10px;
      flex: 1;
      .demonstration {
        min-width: 80px;
        margin-right: 15px;
        white-space: nowrap;
      }
    }
    .topic-item-title {
      flex: auto;
      width: 275px;
      display: flex;
      justify-content: flex-start;
    }
  }
}
.tableBox {
  width: 100%;
}
.carouselFlexBox {
  padding-right: 10px;
  .carouselFlex {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
    padding-right: 20px;
    span {
      min-width: 100px;
      text-align: right;
    }
  }
  .el-date-editor {
    width: 400px !important;
    height: 30px !important;
    .el-range__icon, .el-range-separator {
      line-height: 21px;
    }
  }
  .carouselButton {
    text-align: right;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }
}

.container-table {
  margin: 10px auto;
  padding-bottom: 10px;
  display: flex;
  justify-content: space-around;

  .img {
    width: 50%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .button-list {
    width: 45%;
  }
}

.topic-image-upload {
  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 200px;
    height: 200px;
    line-height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    font-size: 50px;
  }
}

.topic-image-upload .el-upload {
  width: 100%;
}

.el-row {
  text-align: center;

  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
  .tabBox {
    display: flex;
    margin: 20px;
    border-bottom: 1px solid #F1F1F4;
    cursor: pointer;
    div {
      border: 1px solid #F1F1F4;
      border-bottom: none;
      padding: 5px 10px;
    }
    .activeTab {
      color: #13c2c2;
    }
  }
}
.block{
  width: 100%;
  img {
    width: 60px;
  }
}
.titleBlock {
  width: 100%;
  display: flex;
  flex-direction: column;
  img {
    width: 60px;
  }
  >div {
    width: 100%;
  }
}
</style>
<style  lang="scss">
.topic-search {
  .upload-demo {
    // width: 180px;
    .el-upload {
      display: flex;
      align-items: center;
      .el-button {
        height: 30px;
      }
      img {
        width: 60px;
        margin-left: 15px;
      }
    }
  }
  .banner-dialog {
    .el-input {
      width: 400px;
    }
    .add-color-item {
      display: flex;
      flex-direction: column;
    }
  }
  .el-date-editor {
    .el-input__icon, .el-range-separator {
      line-height: 21px;
    }
  }
  
  .crowdInput {
    .el-input {
      width: 200px !important;
      margin-right: 20px;
    }
  }
  .tableBox {
    .el-button {
      margin-left: 0;
      margin-right: 10px;
    }
    .preview-img {
      width: 60px;
    }
  }
}
</style>