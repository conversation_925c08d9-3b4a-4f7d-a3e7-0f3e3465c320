
<template>
  <div class="topic-search">
    <el-row :gutter="20">
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="活动id:">
            <el-input placeholder="请输入内容" v-model="queryParams.activityId">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="活动名称:">
            <el-input
              placeholder="请输入内容"
              v-model="queryParams.activityName"
            >
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="展示时间:">
            <el-date-picker
              v-model="queryParams.validityTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="人群id:"
            >
            <el-select
              style="margin-left: 10px"
              v-model.trim="queryParams.crowdValue"
              :loading="selectLoading"
              filterable
              :filter-method="optionFilter"
              placeholder="请输入人群id"
              clearable
              @clear="options = []"
              @change="selectCrowd"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
         <el-form-item label="状态:">
            <el-select
              v-model="queryParams.status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="item in status"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <!-- <el-col :span="12">
          <el-form-item label="展示类型:">
            <el-select
              v-model="queryParams.classType"
              placeholder="选择展示类型"
              default-first-option
              filterable
            >
              <el-option label="一行一个" :value="1"></el-option>
              <el-option label="一行两个" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-form>
    </el-row>
    <div class="three-button">
      <el-button type="primary" @click="searchList" size="mini">查询</el-button>
      <el-button type="primary" @click="resetList" size="mini">重置</el-button>
      <el-button type="primary" @click="openTabTopImgAlert" size="mini"
        >新建</el-button
      >
    </div>
    <el-table
      :data="dataList"
      size="mini"
      class="tableBox"
      style="margin-top: 10px"
      ref="tableBox"
      :row-key="(row) => row.activityId"
    >
      <el-table-column
        label="id"
        prop="activityId"
        width="100"
      ></el-table-column>
      <!-- <el-table-column label="展示类别" prop="classType">
        <template slot-scope="scope">
          {{ {1:'一行一个',2:"一行两个"}[scope.row.classType] }}


        </template>
      </el-table-column> -->
      <el-table-column label="活动名称" prop="activityName"></el-table-column>
      <el-table-column label="缩略图">
        <template slot-scope="scope">
          <div style="width: 40px; height: 40px">
            <img
              :src="scope.row.imgUrl"
              alt=""
              style="width: 40px; height: 40px"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="人群" show-overflow-tooltip>
        <template slot-scope="scope">
          <p>
            {{ scope.row.crowdValue || "该页面已选人群" }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="展示时间" width="200">
        <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
            </div>
            <div v-else> 
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <div>
            {{  ["未开始", "上线", "已结束", "下线"][scope.row.status - 1] || "-" }}
           <!--  {{ scope.row.status || 9 }} -->
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="150px">
        <template slot-scope="scope">
          <el-row style="display: flex;">
            <el-button size="mini" @click="toEdit(scope.row, scope.$index)"  v-if="scope.row.status == 1 ||scope.row.status == 3 || scope.row.status == 4"
            >编辑
          </el-button>
          <el-button size="mini" @click="toRemove(scope.row)" type="danger"  v-if="scope.row.status == 4"
            >删除</el-button
          >
          </el-row>
        <el-row style="margin-top: 10px;display: flex;" >
          <el-button size="mini" @click="online(scope)" type="danger" v-if="scope.row.status == 4"
            >上线</el-button
          >
          <el-button size="mini" @click="outline(scope)" type="danger"  v-if="scope.row.status == 2"
            >下线</el-button
          >
        </el-row>
        </template>
      </el-table-column>
    </el-table>
    <tabTopImgAlert
      style="margin-top: 5px"
      ref="tabTopImgAlert"
      :topic="topic"
      @done="tabTopImgAlertCallBack"
    ></tabTopImgAlert>
  </div>
</template>
<script>
import tabTopImgAlert from "./components/tabTopImgAlert.vue";
import base from "../../base";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import Sortable from "sortablejs";
let sortableObject = {};
export default {
  name: "tabTopImage",
  extends: base,
  components: { swiperPoint, tabTopImgAlert },
  contentDefault: {
    list: [],
  },
  props: {
    core: Object,
  },
  data() {
    return {
      tableKey:1,
      isEdit: false,
      isShowHrefDialog: false,
      status: [
        { id: "", name: "全部" },
        { id: 1, name: "未开始" },
        { id: 2, name: "上线" },
        { id: 3, name: "已结束" },
        { id: 4, name: "下线" },
      ],
      addFormSelectLink: "",
      queryParams: {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        crowdValue: "", // 人群
        crowdId: "", // 人群
        status: "", //状态
        classType:"",
      },

      options: [],
      selectLoading: false,

      dataList: [],
      goodInfoDataList: [],
      carouselList: {
        bannerLocation: "",
        crowdValue: "",
        status: "",
      },

      // 时间不能大于当前时间
      disabledDate: (time) => {
        return time.getTime() > Date.now();
      },
    };
  },
  filters: {
    link(data) {
      return data.meta.page_url;
    },
    dateFilter(date) {
      function formatDate(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return (
          year +
          "-" +
          (String(month).length > 1 ? month : "0" + month) +
          "-" +
          (String(day).length > 1 ? day : "0" + day) +
          " " +
          (String(hour).length > 1 ? hour : "0" + hour) +
          ":" +
          (String(minute).length > 1 ? minute : "0" + minute) +
          ":" +
          (String(second).length > 1 ? second : "0" + second)
        );
      }

      if (date) {
        let date1 = formatDate(new Date(date[0]));
        let date2 = formatDate(new Date(date[1]));
        // const nS=new Date(date).getTime()
        return date1 + "至" + date2;
      } else {
        return " ";
      }
    },
    jumpText(val) {
      if (!val) {
        return "app内部跳转";
      } else {
        if (val === "inLink") {
          return "app内部跳转";
        }
        return "跳转至外部";
      }
    },
  },
  mounted() {
    this.initData();
    this.initDataStatus()
    this.rowDrop();
    this.changeTab("notInvalid");
    // this.getDict();
  },
  computed: {
    /**
     *   获取列的状态名称
     */
    getStatusName() {
      return function (timevalue, type) {
        let item = {};
        if (!timevalue) {
          item = {
            id: 4,
            name: "未设置时间",
          };
        } else {
          const _date = new Date().getTime();
          const start = new Date(timevalue[0]).getTime();
          const end = new Date(timevalue[1]).getTime();
          if (_date <= end && _date >= start) {
            item = {
              id: 1,
              name: "生效中",
            };
          } else if (_date > end) {
            item = {
              id: 3,
              name: "已失效",
            };
          } else if (_date < start) {
            item = {
              id: 2,
              name: "待生效",
            };
          }
        }
        if (type == "id") {
          return item.id;
        } else {
          return item.name;
        }
      };
    },
  },
  methods: {
    async getDict() {
      let status = await api.goods.status();
      if (status.code == 200) this.$nextTick(() => (this.status = status.data));
      else this.$message.error(status.msg);
    },
    resetList() {
      this.dataList = JSON.parse(JSON.stringify(this.content.list));
      this.resetQueryParams();
      this.initDataStatus();
    },
    resetQueryParams() {
      this.queryParams = {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        crowdValue: "", // 人群
        crowdId: "", // 人群
        status: "", //状态
        classType:"",
      };
    },
    initDataStatus() {
      
      this.dataList = this.setStatusInitList(this.dataList);
    },
    setStatusInitList(data) {
      if (!data) {
        return;
      }
      data.forEach((item, index) => {
        // if(item.status==4){
        //   return
        // }
        // item.sort = index + 1;
        this.$set(item, "sort", index + 1);
        // 1:"月 ",2:"周 ",3:"日 "
        // if (item.status != 4) {
          if (item.timeType == 2) {
            // if (Array.isArray(item.circulateTime.circulateList)) {
            //   let _date = new Date().toLocaleTimeString("en-US", {
            //     hour12: false,
            //   });
            //   let dateTime = new Date().getTime();
            //   if (item.circulateTime.circulateType == 3) {
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       }
            //       else if (_date >= element.selectTimeData[1]) {
            //         item.status = 3; //已结束
            //       }
            //       else if(_date<element.selectTimeData[0]){
            //          item.status = 1; //未开始
            //       }
            //     });
            //   }
            //   if (item.circulateTime.circulateType == 1) {
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         new Date().getDate() == element.weekOrday &&
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       } else if (
            //         dateTime > new Date(element.selectTimeData[1]).getTime()
            //       ) {
            //         item.status = 3; //已结束
            //       } else if(_date<element.selectTimeData[0]){
            //          item.status = 1; //未开始
            //       }
            //     });
            //   }
            //   if (item.circulateTime.circulateType == 2) {
            //     const dayOfWeek = [
            //       "周日",
            //       "周一",
            //       "周二",
            //       "周三",
            //       "周四",
            //       "周五",
            //       "周六",
            //     ][new Date().getDay()];
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         dayOfWeek == element.weekOrday &&
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       } else if (
            //         dateTime > new Date(element.selectTimeData[1]).getTime()
            //       ) {
            //         item.status = 3; //已结束
            //       } else if(_date<element.selectTimeData[0]){
            //          item.status = 1; //未开始
            //       }
            //     });
            //   }
            // }
            if(item.status!=4){
              item.status=2
            }
          } else {
            if (new Date() * 1 < new Date(item.validityTime[0]) * 1) {
               item.status = 1; // 未开始
            } else if (
              new Date() * 1 > new Date(item.validityTime[0]) * 1 &&
              new Date() * 1 < new Date(item.validityTime[1]) * 1&&item.status!=4
            ) {
              item.status = 2; //上线
            } else if (new Date() * 1 > new Date(item.validityTime[1]) * 1) {
              item.status = 3;
            } else {
              item.status = 4;
            }
          }
        // }
      });
      return data;
    },
    //链接去掉空格
    urlChange() {
      this.dataForm.link.meta.page_url =
        this.dataForm.link.meta.page_url.trim();
    },
    initData() {
      this.dataList.length=0
       this.dataList.push(...this.content.list)
      
    },
    rowDrop() {
      const _this = this;
      const tbody = document.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      sortableObject = Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        ghostClass: "sortable-ghost",
        onEnd: (evt) => {
          const currRow = (_this.dataList || []).splice(evt.oldIndex, 1)[0];
          (_this.dataList || []).splice(evt.newIndex, 0, currRow);
          const currRowData = (_this.content.list || []).splice(
            evt.oldIndex,
            1
          )[0];
          (_this.content.list || []).splice(evt.newIndex, 0, currRowData);
          console.log(_this.content.list,22)
          _this.initData()
        },
      });
    },
    changeCrowdValue(e) {
      if (!e) {
        this.dataForm.crowdId = "";
      }
      this.$forceUpdate();
    },
    clear_bgs(type) {
      console.log(this.content, "Cotnet");
      this.content[type + "_url"] = "";
      this.content[type + "_color"] = "";
      this.content[type + "_transparency"] = 100;

      // this.content.top_bgRes = "#fff";
      // this.content.meddle_bgRes = "#fff";
      // this.content.bottom_bgRes = "#fff";
      // this.content.hotWord_bgRes = "#fff"
    },

    // 上传banner对应的头部区域背景图片
    async UploadTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.content, type, res.data.url);
      console.log("添加", this.content, "conetnt");
    },
    UploadhotWord_bgRes(res) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.hotWord_bgRes = res.data.url;
    },
    // 上传banner对应的中间区域背景图片
    async UploadMeddleSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.meddle_bgRes = res.data.url;
    },
    // 上传banner对应的底部区域背景图片
    async UploadBottomSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.bottom_bgRes = res.data.url;
    },
    changeCrowdType() {
      this.addForm.crowdId = "";
      this.addForm.crowdValue = "";
    },

   
    changeTab(type) {
      this.activeTab = type;
      this.carouselList.status = "";
      this.searchList();
    },
    //生成唯一id
    genID(length) {
      return Number(
        Math.random().toString().substr(3, length) + Date.now()
      ).toString(36);
    },
    //查询
    searchList() {
      //只有查询全部的时候允许拖拽
      // if (this.carouselList.status || this.carouselList.crowdValue || this.carouselList.bannerLocation) {
         sortableObject.option('disabled', false)
      // } else {
      //   sortableObject.option('disabled', false)
      // }
      this.dataList = JSON.parse(JSON.stringify(this.content.list))
      if (Array.isArray(this.queryParams.validityTime)&&this.queryParams.validityTime.length) {
        sortableObject.option('disabled', true)
        this.dataList = this.dataList.filter((item, index) => {
          return (
            new Date(this.queryParams.validityTime[0]) * 1 >=
              new Date(item.validityTime[0]) * 1 &&
            new Date(this.queryParams.validityTime[1]) * 1 <=
              new Date(item.validityTime[1]) * 1
          );
        });
      }
      if (this.queryParams.activityId) {
        sortableObject.option('disabled', true)
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.activityId === item.activityId.toString();
        });
      }
      if (this.queryParams.activityName) {
        sortableObject.option('disabled', true)
        this.dataList = this.dataList.filter((item, index) => {
          return new RegExp(this.queryParams.activityName).test(item.activityName)
        });
      }
      if (this.queryParams.crowdValue) {
        sortableObject.option('disabled', true)
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.crowdValue === item.crowdValue;
        });
      }
      if (this.queryParams.status) {
        sortableObject.option('disabled', true)
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.status === item.status;
        });
      }
      if (this.queryParams.classType) {
        sortableObject.option('disabled', true)
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.classType === item.classType;
        });
      }
    },
    toEdit(data, index) {
      this.currentDataIndex = index;
      this.isEdit = true;

      this.$refs.tabTopImgAlert.open(data, true);
    },
    toRemove(data) {
      let _self = this;
      return function () {
        _self.content.list.splice(
          _self.content.list.findIndex(
            (item) => item.activityId == data.activityId
          ),
          1
        );
        
        _self.initData();
        _self.initDataStatus();
        _self.$message({
          type: "success",
          message: "删除成功!",
        });
      }.confirm(_self)();
    },
    online(scope) {
      this.$confirm("确定要执行上线操作吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        //this.content.list[scope.$index].status = 2;
        this.content.list.find(item=>item.activityId==scope.row.activityId).status=2
        this.$message.success("操作成功！");
        this.initData();
      });
    },
    outline(scope) {
      this.$confirm("确定要执行下线操作吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.content.list.find(item=>item.activityId==scope.row.activityId).status=4
        //this.content.list[scope.$index].status = 4;
        this.$message.success("操作成功！");
        this.initData();
      });
    },

    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },

    selectCrowd(e) {
      if (e) {
        this.queryParams.crowdId = Number(this.options[0].value.trim());
        this.queryParams.crowdValue = this.options[0].label;
      } else {
        this.queryParams.crowdId = "";
        this.queryParams.crowdValue = "";
      }
      this.$forceUpdate();
    },

    openTabTopImgAlert() {
      this.isEdit = false;
      this.$refs.tabTopImgAlert.open();
    },
    tabTopImgAlertCallBack(val) {
      let arr = [...this.content.list];
        //校验重复
      //   if(this.isEdit){
      //   arr.splice(arr.findIndex(item => item.activityId == val.activityId),1)
      // }
      //   let classFlag = false;
      // if (arr.findIndex(item => item.crowdId == val.crowdId) > -1 
      // || (arr.findIndex(item => item.crowdType == 1) > -1 && !this.core.crowdId)
      // || (arr.length && val.crowdType == 1 && !this.core.crowdId)
      // ) {
      //   if (val.timeType == 1) {
      //     const start_form = new Date(val.validityTime[0]).getTime();          
      //     const end_form = new Date(val.validityTime[1]).getTime();          
      //     arr.forEach(item => {
      //       const start = new Date(item.validityTime[0]).getTime();
      //       const end = new Date(item.validityTime[1]).getTime();
      //       if (start_form <= start && end_form >= end) {
      //         classFlag = true;
      //       } else if ((start_form >= start && start_form <= end) || (end_form >= start && end_form <= end)) {
      //         classFlag = true;
      //       }
      //     })
      //   } else if (val.timeType == 2) {
      //     // 1:周 2:月 3:日
      //     arr.forEach(item => {
      //       if(Array.isArray(val.circulateTime.circulateList)){
      //         // let _date =  new Date().toLocaleTimeString('en-US', {hour12: false});  
      //         if (item.timeType == 2) {
      //           let _date = val.circulateTime.circulateList[0].selectTimeData;     
      //           if(val.circulateTime.circulateType==3){
      //             item.circulateTime.circulateList.forEach(element => {
      //               if ((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1])) {
      //                 classFlag = true;
      //               }
      //             });
      //           }
      //           if(item.circulateTime.circulateType==1 || item.circulateTime.circulateType==2){
      //             item.circulateTime.circulateList.forEach(element => {
      //               if (val.circulateTime.circulateList[0].weekOrday==element.weekOrday&&((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1]))) {
      //                 classFlag = true;
      //               }
      //             });
      //           }
      //         }
      //       }
      //     })
      //   }
      // }
      // if (classFlag) {
      //   this.$message.error("展示时间不能包含列表已存在数据时间！");
      //   return;
      // }
      if (this.isEdit) {
        //this.$set(this.content.list, this.currentDataIndex, val);
       // this.content.list[this.content.list.findIndex(item=>item.activityId==val.activityId)]=val
        this.$set(this.content.list,this.content.list.findIndex(item=>item.activityId==val.activityId),val)
      } else {
        let id = 0;
        id = Math.floor(Math.random() * 90000) + 10000;
        this.$set(val, "activityId", id);
        if (this.content.list.findIndex((item) => item.activityId == id) > -1||!val.activityId) {
          this.$message("id错误，请重新添加！");
          return;
        }
   
        arr.splice(0, 0, val);
        this.$set(this.content, "list", arr);
      }
      this.$message.success(`${this.isEdit ? "编辑" : "添加"}成功！`);
      this.initData();
      this.initDataStatus();
      this.resetList();
      this.$refs.tabTopImgAlert.addDialogCancel();
     
    },
  },
};
</script>
<style lang="scss" scoped>
.title {
  text-align: left;
  line-height: 30px;
  background-color: #f2f2f2;
  margin: 10px 0;
  padding-left: 10px;
}
.three-button {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
.tableBox {
  width: 100%;
}
.dialog-activity-sort {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.topic-tab-top-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>