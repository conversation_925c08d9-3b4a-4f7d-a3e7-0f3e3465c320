<template>
  <!--轮播进度设置-->
  <div style>
    <el-row :gutter="20">
      <div class="title">轮播点设置</div>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">轮播点类型：</span>
          <div>
            <el-select v-model="content.pro_obj.pro_type" placeholder="请选择">
              <el-option
                v-for="item in pro_options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
      </el-col>

      <el-col :span="8">
        <div class="block">
          <span class="demonstration">排列方式；</span>
          <div>
            <el-select v-model="content.pro_obj.pro_align_type" placeholder="请选择">
              <el-option
                v-for="item in pro_align_options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
      </el-col>

      <el-col :span="8">
        <div class="block">
          <span class="demonstration">轮播时间：</span>
          <div>
            <el-select v-model="content.pro_obj.pro_auto" placeholder="请选择">
              <el-option
                v-for="item in pro_auto_options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="6">
        <div class="block noflex">
          <span class="demonstration">默认透明度</span>
          <div>
            <el-slider v-model="content.pro_obj.default_opacity" :format-tooltip="formatTooltip"></el-slider>
          </div>
        </div>
        <div class="block noflex">
          <span class="demonstration">激活透明度</span>
          <div>
            <el-slider v-model="content.pro_obj.active_opacity" :format-tooltip="formatTooltip"></el-slider>
          </div>
        </div>
      </el-col>

      <el-col :span="6">
        <div class="block noflex">
          <span class="demonstration">默认背景颜色：</span>
          <div>
            <el-color-picker v-model="content.pro_obj.default_bg_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="block noflex">
          <span class="demonstration">激活背景颜色：</span>
          <div>
            <el-color-picker v-model="content.pro_obj.active_bg_color" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>

      <el-col :span="6">
        <div class="block noflex">
          <span class="demonstration">默认文字颜色：</span>
          <div>
            <el-color-picker v-model="content.pro_obj.default_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="block noflex">
          <span class="demonstration">激活文字颜色：</span>
          <div>
            <el-color-picker v-model="content.pro_obj.active_color" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  props: ["content"],
  data() {
    return {
      pro_options: [
        {
          value: "text",
          label: "文字"
        },
        {
          value: "longBar",
          label: "矩形"
        },
        {
          value: "bar",
          label: "正方形"
        },
        {
          value: "circle",
          label: "圆形"
        }
      ],

      pro_align_options: [
        {
          value: "center",
          label: "居中"
        },
        {
          value: "left",
          label: "左对齐"
        },
        {
          value: "right",
          label: "右对齐"
        }
      ],

      pro_auto_options: [
        {
          value: 0,
          label: "不轮播"
        },
        {
          value: 3000,
          label: "3秒间隔"
        },
        {
          value: 4000,
          label: "4秒间隔"
        },
        {
          value: 5000,
          label: "5秒间隔"
        }
      ]
    };
  },
  methods: {
    formatTooltip(val) {
      return val / 100;
    }
  }
};
</script>
