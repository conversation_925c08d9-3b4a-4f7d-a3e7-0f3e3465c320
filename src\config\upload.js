import Vue from 'vue';
import { Upload } from 'element-ui';
import {API_SERVER} from 'config'
let _handleStart = Upload.methods.handleStart;
let _uploader = Upload.components.Upload
let _ajax = _uploader.props.httpRequest.default
_uploader.props.httpRequest.default = function (options) {
    _ajax(options)
}
export default function () {
    Vue.component(Upload.name, {
        props: {
            withCredentials: {
                type: Boolean,
                default: true
            },
            action: {
                required: false,
                default() {
                    return `${API_SERVER}upload`
                }
            },
            // 图片上传大小限制(默认1M)
            maxSize: {
                type: Number,
                default: 1
            },
            maxWidth: {
                type: Number,
                default: 2000
            },
            // 跳过校验图片大小
            skipRule: {
                type: Boolean,
                default: false,
            }
        },
        methods: {
            async handleStart(rawFile) {
                console.log('rawFile', rawFile);
                if (this.maxSize && rawFile.size > this.maxSize * 1024 * 1024) {
                    if (this.$message) {
                        this.$message({
                            message: `图片超过 ${this.maxSize} M 无法上传`,
                            type: 'warning'
                        })
                    } else {
                        console.log(`图片超过${this.maxSize}M无法上传`);
                    }
                    return;
                }
                const res = await this.checkWAndH(rawFile);
                if (!this.skipRule && (res.imgWidth > this.maxWidth || res.imgHeight > this.maxWidth)) {
                    if (this.$message) {
                        this.$message({
                            message: `图片尺寸超过${this.maxWidth}px无法上传`,
                            type: 'warning'
                        })
                    } else {
                        console.log(`图片尺寸超过${this.maxWidth}px无法上传`);
                    }
                    return;
                }
                _handleStart.call(this, rawFile);
            },
            checkWAndH(rawFile) {
                const reader = new FileReader();
                const img = new Image();
                return new Promise((resolve, reject) => {
                    reader.onload = (e) => {
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(rawFile);
                    img.onload = (e) => {
                        const that = e.target;
                        // 图片原始尺寸
                        const originWidth = that.width;
                        const originHeight = that.height;
                        resolve({
                            imgWidth: originWidth,
                            imgHeight: originHeight
                        });
                    }
                })
            },
        },
        extends: Upload
    })
}