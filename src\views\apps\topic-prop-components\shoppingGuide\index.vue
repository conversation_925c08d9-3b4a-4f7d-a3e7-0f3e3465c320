<template>
  <div class="fast-entry">
    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块选择</div>
      <el-col :span="24">
        <el-select v-model="cur_module" placeholder="请选择">
          <el-option
            v-for="item in list"
            :key="item.module_type"
            :label="item.module_title"
            :value="item.module_type">
          </el-option>
        </el-select>
      </el-col>
    </el-row>



    <el-row :gutter="20">
      <div class="title">模块配置</div>
      <el-col :span="24">
        <div class="block">
        <el-input placeholder="请输入内容" v-model="list[cur_index].module_title">
          <template slot="prepend">模块title</template>
        </el-input>
        </div>
      </el-col>
      <br>
      <el-col :span="24" v-if="cur_module!=='Seckill'">
        <div class="block">
        <el-input placeholder="请输入内容" v-model="list[cur_index].describe.text">
          <template slot="prepend">模块标语</template>
        </el-input>
        </div>
      </el-col>

      <el-col :span="24" v-if="cur_module==='DaySell'||cur_module==='Seckill'||cur_module==='RankingList'||cur_module==='StarBrand'">
        <div class="block">
          <el-input placeholder="请输入内容" v-model="list[cur_index].jump_url">
            <template slot="prepend">跳转链接</template>
          </el-input>
        </div>
      </el-col>

      <el-col :span="24" v-if="cur_module==='StarBrand'">
        <div class="block">
          <el-table :data="list[cur_index].brand_list" size="mini" style="width: 100%">
            <el-table-column
              type="index"
              width="50">
            </el-table-column>

            <el-table-column label="icon">
              <template slot-scope="scope">
                <img v-if="scope.row.icon_url"
                     :src="scope.row.icon_url"
                     alt="图"
                     class="title-image"/>
              </template>
            </el-table-column>

            <el-table-column fixed="right" label="操作">
              <template slot-scope="scope">
                <el-button size="mini"
                           @click="editIcon(scope.row, scope.$index)" type="primary">编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>


      <el-col :span="24" v-if="cur_module==='DaySell'||cur_module==='Seckill'||cur_module==='RankingList'||cur_module==='StarBrand'">
        <div class="block">
          <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
        </div>
      </el-col>



    </el-row>

    <el-dialog class="banner-dialog"
               title="banner设置" :visible.sync="addDialog">
      <el-upload
        class="topic-image-upload"
        ref="upload"
        accept="image/jpeg,image/jpg,image/png,image/gif"
        :show-file-list="false"
        :on-success="onUploadImage">
        <img v-if="iconObj.icon_url" :src="iconObj.icon_url" class="image">
        <i v-else class="el-icon-plus uploader-icon"></i>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialog=false">取 消</el-button>
        <el-button size="small" type="primary" @click="closeDialog">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import base from '../base'
  export default {
    extends: base,
    computed: {
      list() {
        const list = _.get(this, 'content.module_list');
        if (list) {
          return list
        } else {
          return [];
        }
      }
    },
    data() {
      return {
        cur_module: 'Seckill',
        cur_index:0,
        addDialog:false,
        iconObj:{
          icon_url:"",
        }
      }
    },
    methods: {
      onSetLink(link) {
        this.list[this.cur_index].jump_url=link.meta.page_url
      },
      editIcon(data,index){
        this.iconObj.icon_url=data.icon_url;
        this.iconObj.icon_index=index;
        this.addDialog=true
      },
      closeDialog(){
        this.list[this.cur_index].brand_list[this.iconObj.icon_index].icon_url=this.iconObj.icon_url;
        this.addDialog=false
      },
      async onUploadImage(res, file) {
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          });
          return;
        }
        this.iconObj.icon_url = res.data.url
      },
    },
    watch:{
      cur_module(new_val){
        if(new_val){
          this.list.forEach((item,index)=>{
             if(item.module_type===new_val){
               this.cur_index=index
             }
          });
        }
      }
    }
  }
</script>

<style lang="scss" rel="stylesheet/scss">


  .fast-entry {
    .container {
      display: flex;
      align-items: center;

      .img {
        width: 65%;

        img {
          display: block;
          width: 100%;
        }
      }

      .button-list {
        margin-left: 10px;
      }
    }

    .content-setting {
      color: #fff;
      background-color: #13c2c2;
      padding: 10px;
      text-align: center;
      font-size: 16px;
      margin-bottom: 10px;
    }

    .title-image {
      width: 64px;
      height: 64px;
    }

    .topic-image-upload {
      .image {
        display: block;
        width: 100%;
      }

      .uploader-icon {
        width: 200px;
        height: 200px;
        line-height: 200px;
        border: 1px solid $border-base;
        font-size: 50px;
      }
    }

    .entry-name {
      width: 70%;
    }

    .el-form-item {
      margin-bottom: 12px;
    }

    // single-upload
    .uploader-btn-state {
      text-align: center;
    }

    .topic-image-picker {
      padding: 10px 0;
    }

    .el-table {
      .cell {
        text-align: center;
        padding: 0;
      }

      th .cell {
        color: #606266;
      }
    }

    .banner-dialog {
      .el-dialog__body {
        padding-top: 10px;
      }

      .image {
        width: 64px;
        height: 64px;
      }
    }

    .no-img {
      font-size: 35px;
      display: block;
      color: #caccd0;
    }
  }

  .el-loading-spinner {
    top: auto !important;
    margin-top: auto !important;
  }

  .el-row {
    text-align: center;

    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }

    .block{
      margin-top: 10px;
    }
  }
</style>
