<template>
    <div class="all-link">
        <el-tabs v-model="selectedConfigTab" type="border-card" style="box-shadow:none">
            <el-tab-pane v-for="item in tabsUsed" :key="item.value" :label="item.label" :name="item.value">
                <component v-if="selectedConfigTab === item.value" :is="item.value" @select="onSelect" :params="item.params || {}"></component>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
    import page from './modules/page'
    import productlink from './modules/productlink'
    export default {
        props: {
            tabs: Array,
            defaultSelected: String
        },
        data() {
            return {
                selectedConfigTab: null,
                tabsDefault: [
                    { label: '活动页', value: 'page' },
                    { label: '商品', value: 'productlink' },
                ]
            }
        },
        computed: {
            tabsUsed() {
                return this.tabs || this.tabsDefault
            }
        },
        methods: {
            onSelect(item) {
                this.$emit('select', item)
            }
        },
        mounted() {
            this.selectedConfigTab = _.get(this.tabsUsed, '0.value')
        },
        components: {
            page: page,
            productlink: productlink,
        }
    }
</script>
<style lang="scss" scoped rel="stylesheet/scss">

    .el-tabs {
        margin-top: 0;
        padding-top: 0;
    }
</style>
