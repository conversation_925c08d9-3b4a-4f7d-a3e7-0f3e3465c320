<template>
    <div class="topic-banner">
        <el-button @click="toAdd" class="btn-block mb-10" type="primary">上传图片</el-button>
        <el-form class="data-form" size="mini" label-width="90px" label-suffix=" : ">
            <el-row :gutter="10">
                <el-col :span="14">
                    <el-form-item label="间距高度">
                        <el-input v-model="content.height" placeholder="750设计稿的单位像素"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="10">
                    <el-form-item label="间距背景色">
                        <el-color-picker v-model="content.color"></el-color-picker>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
                :data="list"
                size="mini"
                style="width: 100%">
            <el-table-column
                    label="图片">
                <template slot-scope="scope">
                    <div class="container">
                        <div class="img"><img :src="scope.row.image"></div>
                        <div class="button-list">
                            <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑</el-button>
                            <el-button size="mini" @click="toRemove(scope.row)" type="danger">删除</el-button>
                        </div>
                    </div>
                    <div class="link-desc">{{scope.row.link | link}}</div>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addDialog">
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="onUploadImage">
                <img v-if="dataForm.image" :src="dataForm.image" class="image">
                <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
            <div class="topic-image-picker">
                链接填选项<span>({{dataForm.link | link}})</span>
            </div>
            <gen-link @select="onSetLink" :tabs="tabs"></gen-link>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeAddDialog">取 消</el-button>
                <el-button size="small" type="primary" @click="confirm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import base from '../base'

    export default {
        extends: base,
        contentDefault: {
            list: [],
            color: '#ffffff',
        },
        data() {
            return {
                loading: false,
                addDialog: false,
                tabs: [
                    { label: '商品', value: 'item' },
                    { label: '商品组', value: 'group' },
                    { label: '页面', value: 'topic' },
                    { label: '协议', value: 'scheme' },
                    { label: '品牌', value: 'brand' },
                    { label: '分类', value: 'category' }

                ],
                dataForm: {
                    image: '',
                    link: {},
                }
            }
        },
        computed: {
            list() {
                var list = _.get(this, 'content.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '') + (data.desc ? ',' : '') + data.desc;
            }
        },
        methods: {
            closeAddDialog() {
                this.addDialog = false;
            },
            toRemove(data) {
                this.list.splice(this.list.indexOf(data), 1)
            },
            toEdit(data, index) {
                this.currentData = data;
                this.currentIndex = index;
                this.dataForm = Object.assign({}, data);
                this.isEdit = true;
                this.addDialog = true;
            },
            toAdd() {
                this.isEdit = false;
                this.dataForm = {
                    image: '',
                    link: {},
                };
                this.addDialog = true;
            },
            onSetLink(link) {
                this.dataForm.link = link
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.dataForm.image = res.data.url
            },
            confirm() {
                if (!this.dataForm.image) {
                    this.$message.warning('请上传图片');
                    return false;
                }
                this.closeAddDialog();
                if (this.isEdit) {
                    this.currentData = Object.assign(this.currentData, this.dataForm);
                    this.list.splice(this.currentIndex, 1, this.currentData);
                } else {
                    this.list.push(Object.assign({}, this.dataForm));
                }
            },
        },
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .topic-banner {
        .container {
            display: flex;
            align-items: center;
            .img {
                width: 65%;
                img {
                    display: block;
                    width: 100%;
                }
            }
            .button-list {
                margin-left: 10px;
            }
        }
        .link-desc {
        }
        .topic-image-upload {
            .image {
                display: block;
                width: 100%;
            }
            .uploader-icon {
                width: 200px;
                height: 200px;
                line-height: 200px;
                border: 1px solid $border-base;
                font-size: 50px;
            }
        }
        .topic-image-picker {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }

</style>
<style lang="scss" rel="stylesheet/scss">
    .topic-banner {
        .banner-dialog {
            .el-dialog__body {
                padding-top: 10px;
            }
        }
    }
    .topic-image-upload {
        .el-loading-spinner {
            top: auto!important;
            margin-top: auto!important;
        }
    }
</style>
