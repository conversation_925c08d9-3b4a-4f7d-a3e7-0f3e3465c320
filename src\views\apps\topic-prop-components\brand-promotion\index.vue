<template>
	<div>
		<!--品牌促销-->
		<p class="blank_20"></p>
		<el-button type="primary" @click="handleDelete" size="mini">删除</el-button>
		<p class="blank_20"></p>
		每页显示商品数量：
		<el-radio-group v-model="content.count">
			<el-radio :label="item" v-for="(item,index) in countList" :key="index">{{`${item}个`}}</el-radio>
		</el-radio-group>
		<p class="blank_20"></p>
		<el-table :data="list" :row-key="getRowKeys" border fit highlight-current-row style="width: 100%"
		          v-if="list.length>0"
		          @selection-change="handleSelection">
			<el-table-column
				  type="selection"
				  width="55"/>
			<el-table-column label="商品组名" prop="goodsName">
			</el-table-column>
			<el-table-column label="跳转页面" prop="page_name">
			</el-table-column>
			<el-table-column label="活动图片">
				<template slot-scope="scope">
					<upload-image :index="scope.$index" :image="scope.row.image"
					              v-on:listenImage="getImage"></upload-image>
				</template>
			</el-table-column>
		</el-table>
		<p class="blank_20"></p>
		<all-link @select="onSetLink" :tabs="tabs" :params="{
				page: {
	                branchCode: topic.branchCode
	            },
				goodsGroup:{
					radio: 0,
					returnGoods: 0,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    },
                    data: {
                        ids: goodsIds
                    }
                }
             }"></all-link>

	</div>
</template>

<script>
	import base from '../base'
	import uploadImage from '../../components/upload-image'
	import {common} from 'api'

	export default {
		name: 'brandPromotion',
		extends: base,
		contentDefault: {
			list: [],
			count: 3
		},
		data() {
			return {
				tabs: [
					{label: '活动页', value: 'page'},
					{label: '商品组', value: 'goodsGroup'}
				],
				goodsIds: [],
				selectItem: [],
				loading: false,
				countList: [3, 6, 9, 12]
			}
		},
		computed: {
			list() {
				var list = _.get(this, 'content.list')
				if (list) {
					if (list.length > 0) {
						this.$nextTick(function () {
							this.setSort()
						})
					}
					return list
				} else {
					return []
				}
			}
		},
		components: {
			uploadImage
		},
		methods: {
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},
			getRowKeys(row) {
				if (!row.id) {
					return
				}
				return row.id
			},
			onSetLink(obj) {
				if (obj.tag == 'goodsGroup') {
					obj.data.forEach((item, index) => this.list.push({
						id: item.id,
						goodsName: item.name,
						code:item.code,
						goodsIds: item.goods,
						image: '',
						page_url: '',
						page_name: ''
					}));
				} else {
					if (this.selectItem.length === 0 || this.selectItem.length !== 1)
						return this.$message.warning('请先选中1个标签');
					const index = this.list.indexOf(this.selectItem[0]);
					this.list[index].page_url = obj.meta.page_url;
					this.list[index].page_name = obj.meta.page_name;
				}
			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = this.list.indexOf(item)
					this.list.splice(index, 1)
				})
			},
			getImage(data) {
				if (data.image) {
					this.list[data.index].image = data.image
				}
			}
		}
	}
</script>
