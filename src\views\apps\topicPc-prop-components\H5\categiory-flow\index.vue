<template>
  <div class="topic-menu-list">
    <el-row :gutter="20">
      <div class="panel-common-header">
        模块有效时间设置
      </div>
      <div class="timevalue">
        <el-date-picker
          v-model="content.timevalue"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        ></el-date-picker>
      </div>
      <div class="panel-common-header">
        模块背景设置
      </div>
      <div class="background-img">
        <el-upload
          class="topic-image-upload"
          ref="upload"
          accept="image/jpeg,image/jpg, image/png, image/gif"
          :show-file-list="false"
          :before-upload="() => {loading = true; return true;}"
          :on-success="onUploadImage"
        >
          <el-button type="primary" :loading="loading">上传背景图</el-button>
          <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
        <el-button class="btn-block" @click="content.backgroundImage = ''">清除背景图</el-button>
      </div>
    </el-row>

    <div class="panel-common-header" style="margin-bottom: 20px">商品关联</div>
    <el-row :gutter="20">
      <div v-if="goods_group.length">
        <el-col :span="24">
          <el-table :data="goods_group" height="140" ref="multipleTable">
            <el-table-column prop="name" label="组名"></el-table-column>
            <el-table-column prop="code" label="编号"></el-table-column>
            <el-table-column prop="branchCode" label="区域号"></el-table-column>
          </el-table>
        </el-col>
      </div>
    </el-row>

    <!--选择商品-->
    <all-link
      ref="all_link"
      @select="onSetLink"
      :tabs="tabs"
      :params="{
        goodsGroup: {
          seledShow: false,
          minSel: 1,
          search: {
            state: 1,
            branchCode: topic.branchCode
          }
        },
        from: 'pc'
      }"
    ></all-link>
  </div>
</template>

<script>
import base from "../../base";

export default {
  name: "categiory-flow",
  extends: base,
  contentDefault: {
    timevalue: "",
    list: [],
    goods_group: [],
    type: 1,
    list_body_color: "#f1f1f1",
    backgroundImage: '',
  },

  props: {
    pageTimeValue: Array
  },

  data() {
    return {
      loading: false,
      tabs: [{ label: "商品组", value: "goodsGroup" }],
    };
  },

  computed: {
    goods_group() {
      let list = _.get(this, "content.goods_group");
      if (list) {
        return list;
      } else {
        return [];
      }
    }
  },

  mounted () {
    if (this.pageTimeValue) {
      this.content.timevalue = this.content.timevalue || this.pageTimeValue;
    }
  },
  watch: {
    "pageTimeValue"(new_val) {
      if (new_val) {
        this.content.timevalue = new_val;
      }
    }
  },
  methods: {
    pickerOptions: {
      shortcuts: [
        {
          text: "未来一周",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来一个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来三个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来六个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来一年",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
            picker.$emit("pick", [start, end]);
          }
        }
      ]
    },
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.backgroundImage = res.data.url;
    },
    onSetLink(link) {
      function handle_arr(arr = []) {
        return arr.map(item => {
          let obj = {};
          obj.init_img_url = item.init_img_url;
          obj.imageUrl = item.imageUrl;
          obj.productName = item.productName;
          obj.showName = item.showName;
          obj.mediumPackageTitle = item.mediumPackageTitle;
          obj.fob = item.fob;
          obj.id = item.id;
          return obj;
        });
      }
      if (link.tag === "goods" || link.tag === "importGoods") {
        let _self_arr = handle_arr(link.data);
        this.content.list = [..._self_arr];
        this.content.goods_group = [];
      } else if (link.tag === "goodsGroup") {
        let obj = {};
        obj.name = link.data.name;
        obj.branchCode = link.data.branchCode;
        obj.code = link.data.code;
        this.content.goods_group.splice(0, 1, obj);
        this.content.list = [];
      }
    },
  }
};
</script>
<style scoped lang="scss">
.el-row {
  text-align: center;
  .title {
    text-align: left;
    line-height: 30px;
    color: #13c2c2;
    padding-left: 10px;
    margin: 10px;
  }
}
.topic-menu-list {
  .panel-common-header {
    height: 35px;
    line-height: 35px;
    background: #f2f2f2;
    padding: 0 0 0 15px;
    text-align: left;
}
  .timevalue, .background-img {
    padding: 20px 0;
  }
  .background-img {
    text-align: left;
    padding-left: 20px;
    .topic-image-upload {
      display: inline-block;

    }
    .btn-block {
      display: inline-block;
      width: auto;
    }
    .btn-block:last-child {
      margin-left: 20px;
    }
  }
}
</style>
