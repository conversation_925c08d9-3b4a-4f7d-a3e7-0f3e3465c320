<template>
	<div>
		<!--选项卡升级-->
		<p class="blank_20"></p>
		<el-row :gutter="5">
			<el-col :span="4" v-show="isShowTextColor">
				<label>文字:</label>
				<el-color-picker
					  v-model="content.color"
					  size="mini"
				></el-color-picker>
			</el-col>
			<el-col :span="6" v-show="isShowTextColor">
				<label>当前文字:</label>
				<el-color-picker
					  v-model="content.hoverColor"
					  size="mini"
				></el-color-picker>
			</el-col>
			<el-col :span="8">
				<label>当前下划线:</label>
				<el-color-picker
					  v-model="content.lineColor"
					  size="mini"
				></el-color-picker>
			</el-col>
			<el-col :span="6">
				<label>当前背景:</label>
				<el-color-picker
					  v-model="content.bgColor"
					  size="mini"
				></el-color-picker>
			</el-col>
		</el-row>
		<p class="blank_20"></p>
		<!--可以添加纯菜单，也可以添加洗项卡下的菜单-->
		<!-- <el-row :gutter="10">
			<el-col :span="6">
				<el-input placeholder="请输入选项卡名称" v-model="tabName" size="mini"></el-input>
			</el-col>
			<el-col :span="18">
				<el-button size="mini" type="primary" @click="addTab">添加选项卡</el-button>
			</el-col>

		</el-row> -->
		<!-- <el-form :model="form" ref="form" :inline="true" label-width="0">
			<el-form-item prop="name">
				<el-input type="text" size="small" v-model="form.name"
				          autocomplete="off" placeholder="请输入子菜单"></el-input>
			</el-form-item>

			<el-form-item>
				<el-button type="primary" size="mini" @click="handleAdd('form')">添加子菜单</el-button>
				<el-button type="primary" size="mini" @click="handleDelete">删除</el-button>
			</el-form-item>
		</el-form> -->
		<!-- 添加导航按钮：
		<el-checkbox v-model="content.isBtn">{{content.isBtn?'添加':'取消'}}</el-checkbox> -->
		<!--选项卡切换-->
		<el-tabs v-model="activeName" @tab-click="tabClick" closable @tab-remove="removeTab">
			<el-tab-pane v-for="(item,index)  in  list" :label="item.name" :name="item.name" :key="item.name">
				<el-table :data="item.content" border fit highlight-current-row
				          style="width:  100%"
				          v-if="false"
				          @selection-change="handleSelection">
					<el-table-column
						  type="selection"
						  width="55"/>
					<el-table-column prop="title" label="标签名">
						<template slot-scope="scope">
							<template>
								<el-input v-model.lazy="scope.row.title" class="edit-input" size="mini"/>
							</template>
						</template>
					</el-table-column>
					<el-table-column label="操作">
						<template slot-scope="scope">
							<el-button type="primary" size="mini" @click="handleEdit(index,scope.$index)">编辑</el-button>
						</template>
					</el-table-column>
				</el-table>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>

	import {common} from 'api'

	export default {
		props: ['pageData', 'componentsList' ,'label'],
		data() {
			return {
				form: {name: ""},
				selectItem: [],
				tabName: '',
				activeName: '',
				currentContent: [],
				currentTabIndex: 0,
				componentsName: [],
				isShowTextColor:false,
				content:{}
			}
		},
		computed: {
			list() {
				var list = _.get(this, 'content.list')
				if (list) {
					this.isShowTextColor=this.showColor(list)
					return list
				} else {
					return []

				}
			}
		},
		mounted(){
			this.content=_.cloneDeep(this.pageData)
		},
		watch: {
			'pageData': {
				deep: true,
				handler(val) {
					if (val.list.content) {
						this.isShowTextColor=this.showColor(val.list)
						this.currentContent = val.list[this.currentTabIndex].content
					}
				}
			},
			'content':{
				deep:true,
				handler(val){
					this.$emit('listenData', {key: this.label, data: val})
				}
			}
		},
		methods: {
			showColor(list){
				return list.some(item=>{
					return item.content.length>1
				})
			},
			textArr: function (value) {
				//转换成文本
				if (!value) return ''
				return value.map(item=>{
					let arr=this.componentsList.filter(sitem=>{
						if(item===sitem.name){
							return sitem.label
						}
					})
					return arr[0].label
				}).join(',')
			},
			addTab() {
				if (!this.tabName) {
					this.$message('请输入选项卡名称')
					return
				}
				this.content.list.push({name: this.tabName, content: []})
			},

			removeTab(targetName) {
				const index = common.getRepeatResult('name', targetName, this.content.list);
				this.content.list.splice(index, 1)
			},
			tabClick() {
				this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.list);
				this.currentContent = this.content.list[this.currentTabIndex].content
				this.$emit('listenCurrentRow', {key:'tab',data:[this.currentTabIndex, 0]})
			},
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},
			handleAdd(formName) {
				// 添加子菜单
				if (this.activeName === '0') {
					this.$message.error('请选中选项卡，再添加')
					return
				}

				this.currentContent.push({title: this.form.name, componentsName: [], data: {}})

			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = common.getRepeatResult('title', item.title, this.currentContent)
					this.currentContent.splice(index, 1)
				})
			},
			handleEdit(firstIndex, secIndex) {
				this.componentsName = this.content.list[firstIndex].content[secIndex].componentsName
				this.$emit('listenCurrentRow', {key:this.label,data:[firstIndex, secIndex]})
			}

		}

	}
</script>

