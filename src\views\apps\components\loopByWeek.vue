<template>
  <div v-if="weekDatas.length">
    <el-dialog
      title="按周循环 "
      :visible="true"
      :before-close="handleCancel"
      append-to-body
    >
      <div class="btnBox">
        <div
          class="btnItem"
          v-for="item in weekDatas"
          :key="item.key"
          :class="{activeBtn: activeKey === item.key}"
          @click="changeWeekDay(item.key)"
        >
          {{ item.weekDay }}
        </div>
      </div>
      <div class="timeBox">
        <div v-for="(timeItem, index) in (weekDatas[activeKey-1].timeData || [])" :key="index">
          <el-time-picker
            is-range
            v-model="timeItem.selectTimeData"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="HH:mm:ss"
            placeholder="选择时间范围"
          >
          </el-time-picker>
          <span
            style="color: #13c2c2"
            v-if="index === 0"
            @click="addOrDelete(index)"
          >
            +添加时段
          </span>
          <span
            style="color: #EF3035;"
            v-else
            @click="addOrDelete(index)"
          >
            -删除时段
          </span>
        </div>
      </div>
      <span class="dialog-footer" slot="footer">
        <el-button size="small" type="text" @click="cancelData">清空</el-button>
        <el-button size="small" @click="handleConfirm">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: '',
  props: {
    // dialogShow: Boolean,
    weekData: Array,
  },
  data() {
    return {
      activeKey: 1,
      weekDatas: []
    }
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      this.weekDatas = (this.weekData || []).length
      ? this.weekData
      : [...Array(7)].map((i, index) => {
        return {
          weekDay:  `周${["一", "二", "三", "四", "五", "六", "日"][index]}`,
          key: index + 1,
          timeData: [{}],
        }
      });
    },
    changeWeekDay(key) {
      this.activeKey = key;
    },
    addOrDelete(index) {
      if (index > 0) {
        this.weekDatas[this.activeKey-1].timeData.splice(index, 1);
      } else if (this.weekDatas[this.activeKey-1].timeData.length < 3) {
        this.weekDatas[this.activeKey-1].timeData.push({ selectTimeData: '' });
      } else if (this.weekDatas[this.activeKey-1].timeData.length == 3) {
        this.$message.error('最多可添加3个时段')
      }
    },
    handleConfirm() {
      let canSave = true;
      // 校验时间是否重叠
      this.weekDatas.forEach((item) => {
        let selectTimeArr = []
        item.timeData.forEach((item2) => {
          let obj = {};
          if (item2.selectTimeData) {
            obj.s = item2.selectTimeData[0];
            obj.e = item2.selectTimeData[1];
            selectTimeArr.push(obj);
          }
        });
        const passCheck = this.checkTime(selectTimeArr);
        if(!passCheck) {
          canSave = false;
          this.$message.error(`${item.weekDay}设置的时间段有重叠，请检查`);
          return;
        }
      });
      if(canSave) {
        this.weekDatas.forEach((item) => {
          let timeSlot = [];
          item.timeData.forEach((item2) => {
            if (item2.selectTimeData) {
              timeSlot.push(`${item2.selectTimeData[0]}-${item2.selectTimeData[1]}`);
            }
          })
          item.timeSlot = timeSlot;
        });
        this.$emit('confirmSetLoopTime', this.weekDatas);
      }
    },
    // 校验时间是否重叠
    checkTime(dateAr) {
      for (let k in dateAr) {
        if (!this.Fn(k, dateAr)) {
          return false
        }
      }
      return true
    },
    Fn(idx, dateAr) {
      for (let k in dateAr) {
        if (idx !== k) {
          if (dateAr[k].s <= dateAr[idx].s && dateAr[k].e > dateAr[idx].s) {
            return false
          }
          if (dateAr[k].s < dateAr[idx].e && dateAr[k].e >= dateAr[idx].e) {
            return false
          }
        }
      }
      return true
    },
    handleCancel() {
      this.$emit('cancelModal', false);
    },
    cancelData() {
      this.weekDatas.forEach((item) => {
        item.timeData = [{}];
      });
    }
  }
}
</script>
<style lang="scss" scoped>
  .btnBox {
    display: flex;
    align-items: center;
    justify-content: center;
    .btnItem {
      cursor: pointer;
      border: 1px solid #D2D2D2;
      padding: 2px 20px;
      margin: 0 10px;
      border-radius: 4px;
    }
    .activeBtn {
      background: #13c2c2;
      color: #fff;
    }
  }
  .timeBox {
    text-align: center;
    margin: 20px auto 10px;
    div {
      margin: 5px 0;
      span {
        cursor: pointer;
      }
    }
  }
</style>
