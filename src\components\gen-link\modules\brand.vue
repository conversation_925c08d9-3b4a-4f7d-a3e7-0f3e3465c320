<template>
    <div>
        <el-input size="small" icon="search" v-model="text" placeholder="请输入关键字">
            <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
        </el-input>
        <el-table size="mini" :data="list"  highlight-current-row  @current-change="onSelect" :row-style="{height:'80px',cursor:'pointer'}" style="margin:5px 0" v-loading="loading">
            <el-table-column label="ID" prop="brandId" width="80"></el-table-column>
            <el-table-column label="中文名" prop="brandCname"></el-table-column>
            <el-table-column label="英文名" prop="brandEname"></el-table-column>
            <el-table-column label="LOGO" width="100">
                <template slot-scope="scope"><img style="display:block;width:100%;" :src="scope.row.brandLogo"></template>
            </el-table-column>
        </el-table>

        <el-pagination
            small
            layout="pager"
            :current-page="pagination.current"
            :page-size="pagination.size"
            :total="pagination.total"
            @current-change="getList">
        </el-pagination>
    </div>
</template>

<script>
export default {
    data() {
        return {
            text: '',
            pagination: {
                size   : 5,
                current: 1,
                total  : 0
            },
            list: [],
            loading: false
        }
    },
    methods: {
        async getList(page=1){
            //http://**************:8080/router?v=1.0&appKey=100001&method=product.getBrandList
            this.loading = true;
            const result = await this.$http.get('http://api.playlounge.cn/router?appKey=100001&v=1.0&method=brand.sort', {
                params: {}
            })
            if(result.result.result == 1){
                const dataList = [];
                Object.keys(result.data).forEach(key => {
                    result.data[key].forEach(data => {
                        dataList.push(data)
                    })
                })
                this.list = dataList;
            }
            this.loading = false;
        },
        onSelect(row){
            this.$emit('select', {
                type : 'brand',
                label: '品牌',
                id   : row.brandId,
                desc : row.brandCname,
                meta : row
            })
        }
    },
    mounted(){
        this.getList();
    }
}
</script>
