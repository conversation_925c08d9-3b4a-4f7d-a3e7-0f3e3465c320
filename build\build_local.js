require('./check-versions')()
var config = require('../config');
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = config.local.env.NODE_ENV;
  process.env.BUILD_ENV = JSON.parse(config.local.env.BUILD_ENV);
}
var ora = require('ora')
var rm = require('rimraf')
var path = require('path')
var chalk = require('chalk')
var webpack = require('webpack')
var config = require('../config')
var utils = require('./utils')

var webpackConfig = require('./webpack.prod.conf')
var env = process.env.BUILD_ENV;
console.info('NODE_ENV:'+process.env.NODE_ENV);
var spinner = ora('building for '+env+'...')
spinner.start()

rm(path.join(config[ env ].assetsRoot, config[ env ].assetsSubDirectory), err => {
  if (err) throw err
  webpack(webpackConfig, function (err, stats) {
    spinner.stop()
    if (err) throw err
    process.stdout.write(stats.toString({
        colors: true,
        modules: false,
        children: false,
        chunks: false,
        chunkModules: false
      }) + '\n\n')
    console.log(chalk.cyan('  Build complete.\n'))
    console.log(chalk.yellow(
      '  Tip: built files are meant to be served over an HTTP server.\n' +
      '  Opening index.html over file:// won\'t work.\n'
    ))
    console.info('NODE_ENV:' + process.env.NODE_ENV);
    utils.resolveImg();
  })
})
