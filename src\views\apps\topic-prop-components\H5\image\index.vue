<template>
    <div class="topic-image">
        <div class="topic-image-tips">
            注：大图请使用具有切片功能的<b>单图</b>组件，此组件只适合于不想切片的<b>小图</b>（gif动图、二维码图、需长按保存的图），上传体积不允许超过<b>2M</b>。
        </div>
        <el-form>
            <el-form-item label="间距">
                <el-input style="width: 80px;" v-model="content.top" placeholder="上"></el-input>
                <el-input style="width: 80px;" v-model="content.right" placeholder="右"></el-input>
                <el-input style="width: 80px;" v-model="content.bottom" placeholder="下"></el-input>
                <el-input style="width: 80px;" v-model="content.left" placeholder="左"></el-input>
            </el-form-item>
        </el-form>
        <el-upload
                class="topic-image-upload"
                ref="upload"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                :show-file-list="false"
                :before-upload="() => {loading = true; return true;}"
                :on-success="onUploadImage">
            <el-button class="btn-block" type="primary" :loading="loading">上传单图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
        <div class="topic-image-info">
            <div class="name">{{linkName}}</div>
            <el-input placeholder="链接地址" v-model.trim="content.link.page_url">
            </el-input>
            <div class="desc">{{linkDesc}}</div>
            <div class="data"></div>
            <div class="del el-icon-delete" @click="onResetLink()"></div>
        </div>

        <div class="topic-image-picker">跳转链接</div>
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
    </div>
</template>

<script>
    import base from '../../base'
    import api from "api";
    import { getUrlParam } from "config"
    export default {
        extends: base,
        contentDefault: {
            image: '',
            bgRes : '',
            color: '#ffffff',
            link: {
                page_url: '',
                page_name:''
            }
        },
        data() {

            return {
                loading: false
            }
        },
        computed: {
            image() {
                var url = _.get(this, 'content.image')
                if (url) {
                    return `url(${url})`
                } else {
                    return '';
                }
            },
            linkName() {
                return this.content.link.page_name || '未填链接'
            },
            linkDesc() {
                    return ''
            }
        },
        created() {
            this.debounce = _.debounce(this.changeLink, 1000);
        },
        methods: {
            onSetLink(link) {
                this.content.link = link.meta
            },
            onResetLink() {
                this.content.link = {
                    page_url: '',
                    page_name:''
                }
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.image = res.data.url
            },
            toColor16(str) {
                if (/^(rgb|RGB)/.test(str)) {
                    var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
                    var strHex = "#";
                    for (var i = 0; i < aColor.length; i++) {
                        var hex = Number(aColor[i]).toString(16);
                        if (hex === "0") {
                            hex += hex;
                        }
                        strHex += hex;
                    }

                    if (strHex.length !== 7) {
                        strHex = str;
                    }
                    return strHex.toUpperCase();
                } else {
                    return str;
                }
            },
            async changeLink() {
                if (this.content.link.page_url) {
                if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.content.link.page_url)) {
                    this.$message.error('跳转链接格式不正确');
                    this.content.link.page_url = '';
                } else {
                    let linkPageUrl = getUrlParam(this.content.link.page_url, 'url');
                    const result = await api.topic.checkPageUrl({ url: linkPageUrl });
                    if (((result || {}).data || {}).status != 200) {
                    this.$message.error('跳转链接不存在');
                    this.content.link.page_url = '';
                    }
                }
                }
            }
        },
        //监听input输入值变化
        watch:{
        'content.link.page_url': {
            handler(val, oldVal) {
                if (val) {
                    this.debounce();
                }
            }
        }
        }
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .topic-image-tips {
        padding: 5px 0;
        font-size: 12px;
        color: #999;

        b {
            color: $color-danger;
        }
    }

    .topic-image-info {
        position: relative;
        overflow: hidden;
        height: 62px;
        padding-bottom: 10px;
        margin: 5px 0;
        border: $border-base;
        font-size: 12px;
        .name {
            position: relative;
            overflow: hidden;
            height: 26px;
            padding: 0 5px;
            margin-bottom: 3px;
            font-size: 14px;
            line-height: 26px;
            border-bottom: $border-base;
        }
        .desc {
            position: relative;
            overflow: hidden;
            height: 16px;
            padding: 0 5px;
            line-height: 16px;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .data {
            position: relative;
            overflow: hidden;
            min-height: 16px;
            padding: 0 5px;
            line-height: 16px;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #999;
        }
        .del {
            position: absolute;
            top: 0;
            right: 0;
            padding: 7px;
            border-left: $border-base;
            background: #fff;
            cursor: pointer;
            &:hover {
                background: $color-base-silver;
                color: #fff;
            }
        }

    }

    .topic-image-picker {
        line-height: 40px;
        text-align: center;
        background: $color-base-gray;
        color: $border-color-hover;
    }

</style>
<style>
    .topic-image-upload .el-upload {
        width: 100%;
    }
</style>
