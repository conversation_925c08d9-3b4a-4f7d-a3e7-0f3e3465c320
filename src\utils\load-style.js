/**
 * 异步加载脚本
 * @param  {String}   url      脚本地址
 */
export default function loadScript(url) {
  return new Promise((resolve, reject) => {
    const style = document.createElement('link');
    const head = document.querySelector('head');
    style.setAttribute('rel', 'stylesheet');
    style.setAttribute('href', url);
    if (style.readyState) {
      style.onreadystatechange = () => {
        if (style.readyState === 'loaded' || style.readyState === 'complete') {
          style.onreadystatechange = null
          resolve()
        }
      }
    } else { // 其他浏览器
      style.onload = () => {
        resolve()
      }
    }
    head.appendChild(style);
  })
}
