<template>
  <div>
    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">吸顶设置</div>
      <div class="block">
        <el-radio v-model="content.tabs.ceil" :label="false" @change="handleCeil">不吸顶</el-radio>
        <el-radio v-model="content.tabs.ceil" :label="true" @change="handleCeil">吸顶</el-radio>
      </div>

      <div class="title">模块有效时间设置</div>
      <div class="block justify">
        <el-date-picker
          v-model="content.tabs.timevalue"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
          size="small"
        ></el-date-picker>
      </div>

      <div class="title">模块背景设置</div>
      <div class="block">
        <span class="demonstration">主选项卡背景颜色：</span>
        <div>
          <el-color-picker
            v-model="content.tabs.mainBgImage"
            size="mini"
          ></el-color-picker>
        </div>
        <!-- <el-upload
          class="topic-pic-upload"
          ref="upload"
          accept="image/jpeg, image/png, image/gif"
          :max-size="1"
          :show-file-list="false"
          :before-upload="() => {loading = true; return true;}"
          :on-success="onUploadImage"
        >
          <el-button class="btn-block" type="primary" size="small" :loading="loading">设置主选项卡背景色</el-button>
        </el-upload> -->
        <!-- <el-button
          class="btn-block widthAuto ml-10"
          size="small"
          :loading="loading"
          @click="content.tabs.mainBgImage = ''"
        >清除主选项卡背景图</el-button> -->
        <el-upload
          class="topic-pic-upload ml-10"
          ref="upload"
          accept="image/jpeg,image/jpg, image/png, image/gif"
          :max-size="1"
          :show-file-list="false"
          :before-upload="() => {loading = true; return true;}"
          :on-success="onUploadSubImage"
        >
          <el-button class="btn-block" type="primary" size="small" :loading="loading">上传子选项卡背景图</el-button>
          <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
        <el-button
          class="btn-block widthAuto ml-10"
          size="small"
          :loading="loading"
          @click="content.tabs.subBgImage = ''"
        >清除子选项卡背景图</el-button>
      </div>

      <div class="title">主选项卡颜色设置</div>
      <div class="block" style="padding: 0">
        <div class="block">
          <span class="demonstration">默认文字颜色：</span>
          <div>
            <el-color-picker
              v-model="content.tabs.default_font"
              size="mini"
              @change="change_default_font"
            ></el-color-picker>
          </div>
        </div>
        <el-col :span="6">
          <div class="block">
            <span class="demonstration">激活文字颜色：</span>
            <div>
              <el-color-picker
                v-model="content.tabs.active_font"
                size="mini"
                @change="change_active_font"
              ></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="block">
            <span class="demonstration">默认背景颜色：</span>
            <div>
              <el-color-picker v-model="content.tabs.bg_color" size="mini" @change="change_bgColor"></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="block">
            <span class="demonstration">激活背景颜色：</span>
            <div>
              <el-color-picker
                v-model="content.tabs.active_bg_color"
                size="mini"
                @change="change_activeBgColor"
              ></el-color-picker>
            </div>
          </div>
        </el-col>
      </div>

      <div class="title">子选项卡颜色设置</div>
      <div class="block" style="padding: 0">
        <div class="block">
          <span class="demonstration">默认文字颜色：</span>
          <div>
            <el-color-picker
              v-model="content.tabs.sub_default_font"
              size="mini"
              @change="change_sub_default_font"
            ></el-color-picker>
          </div>
        </div>
        <el-col :span="6">
          <div class="block">
            <span class="demonstration">激活文字颜色：</span>
            <div>
              <el-color-picker
                v-model="content.tabs.sub_active_font"
                size="mini"
                @change="change_sub_active_font"
              ></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="block">
            <span class="demonstration">默认背景颜色：</span>
            <div>
              <el-color-picker
                v-model="content.tabs.sub_bg_color"
                size="mini"
                @change="change_sub_bgColor"
              ></el-color-picker>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="block">
            <span class="demonstration">激活背景颜色：</span>
            <div>
              <el-color-picker
                v-model="content.tabs.sub_active_bg_color"
                size="mini"
                @change="change_sub_activeBgColor"
              ></el-color-picker>
            </div>
          </div>
        </el-col>
      </div>
    </el-row>

    <el-row :gutter="20">
      <div class="title">选项卡名称设置</div>
      <div class="block">
        <span>选项卡名称：</span>
        <el-input class="btn-block m-10" placeholder="输入主选项卡名称" v-model="tabName" size="small"></el-input>
        <el-input class="btn-block m-10" placeholder="输入子选项卡名称" :disabled="!tabName" v-model="subTabName" size="small"></el-input>
        <el-button size="small" type="primary" @click="add_tab">添加选项卡</el-button>
      </div>
    </el-row>
  </div>
</template>

<script>
import { common } from "api";

export default {
  props: ["content"],
  data() {
    return {
      loading: false,
      tabName: "",
      subTabName: "",
      is_select: "spread",
      select_options: [
        {
          value: "spread",
          label: "有展示区"
        },
        {
          value: "no_spread",
          label: "无展示区"
        }
      ]
    };
  },
  methods: {
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.tabs.mainBgImage = res.data.url;
    },
    async onUploadSubImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.tabs.subBgImage = res.data.url;
    },
    pickerOptions: {
      shortcuts: [
        {
          text: "未来一周",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来一个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来三个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来六个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来一年",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
            picker.$emit("pick", [start, end]);
          }
        }
      ]
    },
    handleCeil(value) {
      this.ceil = value;
      this.content.tabs.ceil = value;
    },
    change_bgColor(color) {
      this.$emit("change_color", "bg_color", color);
    },
    change_activeBgColor(color) {
      this.$emit("change_color", "active_bg_color", color);
    },
    change_default_font(color) {
      this.$emit("change_color", "default_font", color);
    },
    change_active_font(color) {
      this.$emit("change_color", "active_font", color);
    },

    change_sub_bgColor(color) {
      this.$emit("change_color", "sub_bg_color", color);
    },
    change_sub_activeBgColor(color) {
      this.$emit("change_color", "sub_active_bg_color", color);
    },
    change_sub_default_font(color) {
      this.$emit("change_color", "sub_default_font", color);
    },
    change_sub_active_font(color) {
      this.$emit("change_color", "sub_active_font", color);
    },

    change_active_line(color) {
      this.$emit("change_color", "active_line", color);
    },
    change_tab_bg(color) {
      this.$emit("change_color", "tab_bg", color);
    },
    change_spread_color(color) {
      this.$emit("change_color", "spread_color", color);
    },
    change_list_color(color) {
      this.$emit("change_color", "list_color", color);
    },
    add_tab() {
      if (!this.tabName) {
        this.$message("请输入主选项卡名称");
        return;
      }

      if (this.content.goods_list.length > 0) {
        const nameIndex = common.getRepeatResult(
          "name",
          this.tabName,
          this.content.goods_list
        );
        if (nameIndex >= 0) {
          this.$message.warning("您所添加的选项卡名称已经存在啦,请重新添加");
          return;
        }
      }
      let obj = {
        name: this.tabName,
        goods_group: [],
        list: [],
        type: 0,
        img_url_list: [],
        static_goods_list: []
      };
      this.$emit("add_list", obj);
      this.tabName = "";
    }
  },
  watch: {
    is_select(new_val) {
      this.$emit("change_spread", new_val);
    }
  }
};
</script>

<style scoped lang="scss">
.topic-editor-prop-body {
  padding: 0;
}
.el-row {
  text-align: center;

  .title {
    text-align: left;
    line-height: 35px;
    background-color: #f2f2f2;
    padding-left: 15px;
  }
  .block {
    display: flex;
    padding: 20px 0 20px 15px;
    align-items: center;
    background: #fff;
    .widthAuto {
      width: auto;
    }
    .btn-block {
      width: auto;
    }
  }
  .justify {
    justify-content: center;
  }
}
</style>
