<template>
  <div class="images-box">
    <el-row :gutter="20" class="brand-time">
      <div class="title">模块有效时间设置</div>
      <div class="block">
        <el-col :span="24">
          <el-date-picker
            v-model="content.timevalue"
            @change="change_time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-col>
      </div>
    </el-row>

    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块图片上传</div>
      <el-col :span="24">
        <div class="block">
          <div class="hotView">
            <p>热区示意图</p>
            <img width="100%" :src="`/static/images/${content.main_type}.jpg`" alt />
          </div>
        </div>
        <div class="block">
          <div class="hotView" v-if="content.bgRes">
            <p>实际上传图</p>
            <img width="100%" :src="content.bgRes" alt />
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="block noPt">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg, image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="() => {loading = true; return true;}"
            :on-success="onUploadImg"
          >
            <el-button class="btn-block" type="primary" :loading="loading">上传图片</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </div>
      </el-col>
    </el-row>

    <!--模块图片热区设置-->
    <el-row :gutter="20">
      <div class="title">模块图片热区设置</div>
      <el-col :span="24">
        <el-table :data="list" size="mini" style="padding: 20px">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column label="热区信息（热区宽度累加起来应该等于100）,1+n系列不支持配置宽度">
            <template slot-scope="scope">
              <div>
                热区宽度：
                <span style="color: red">{{scope.row.link.meta.page_width}}</span>
              </div>
              <!-- <div>
                是否分享：
                {{scope.row.link.meta.is_share|is_no}}
              </div>-->
              <div>
                埋点名称（用户可见名称）：
                {{scope.row.link.meta.page_name}}
              </div>
              <div>
                链接：
                {{scope.row.link.meta.page_url}}
              </div>
              <div>
                <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <!--上传图片弹框-->
    <el-dialog class="banner-dialog" title="编辑图片" :visible.sync="addDialog">
      <div class="level" v-if="dataForm.link.meta.page_width">
        热区宽度（百分比）
        <el-input-number v-model="dataForm.link.meta.page_width" :max="100" label="描述文字"></el-input-number>
      </div>

      <!-- <div class="level">
        <el-radio v-model="is_share" :label="false">无分享功能</el-radio>
        <el-radio v-model="is_share" :label="true">此图具有分享功能</el-radio>
      </div>-->

      <div class="level" v-if="!is_share">
        <div class="topic-image-picker">
          <span>此字段用于：埋点统计的名称，默认赋予（用户可见名称） 可修改！</span>
          <el-input placeholder="页面名称" v-model="dataForm.link.meta.page_name">
            <template slot="prepend">用户可见名称</template>
          </el-input>
        </div>

        <div class="block pt">
          <el-radio-group v-model="dataForm.link.meta.linkType">
            <el-radio :label="'link'">链接</el-radio>
            <el-radio :label="'point'">锚点</el-radio>
            <!-- <el-radio :label="'topic'">店铺</el-radio> -->
            <el-radio :label="'stores'">商详</el-radio>
          </el-radio-group>
        </div>

        <div class="topic-image-picker">
          <el-input placeholder="链接地址" v-model="dataForm.link.meta.page_url">
            <template slot="prepend">跳转链接</template>
          </el-input>
        </div>

        <div v-if="dataForm.link.meta.linkType==='link'">
          <all-link
            @select="onSetLink"
            :tabs="[{label: '活动页', value: 'page'}]"
            :params="{branchCode: topic.branchCode, from: 'pc'}"
          ></all-link>
        </div>

        <div v-if="dataForm.link.meta.linkType==='point'" class="block" style="paddingLeft: 0">
          <el-radio-group v-model="dataForm.link.meta.page_url" @change="handleChangeFloor">
            <el-radio-button
              v-for="count in coreLength"
              :key="count"
              :label="`#floor${count - 1}`"
            >楼层{{count}}</el-radio-button>
          </el-radio-group>
        </div>

        <!-- <div v-if="dataForm.link.meta.linkType==='topic'">
          <control-page @select="onSetLink" :params="{branchCode: topic.branchCode}"></control-page>
        </div> -->

        <!-- <page-link
          v-if="dataForm.link.meta.linkType === 'topic'"
          @select="onSetLink"
          :params="{branchCode: topic.branchCode, page_type:topic.page_type}"
        ></page-link>-->
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeAddDialog">取 消</el-button>
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import base from "../../base";
import api from "api";

export default {
  extends: base,
  props: {
    pageTimeValue: Array,
    coreLength: Number
  },
  data() {
    return {
      currentIndex: 0,
      is_share: false,
      loading: false,
      addDialog: false,
      dataForm: {
        link: {
          meta: {
            page_url: "",
            page_name: "",
            is_share: false,
            page_width: "",
            linkType: "link"
          }
        }
      }
    };
  },
  computed: {
    list() {
      let list = _.get(this, "content.list");
      if (list.length) {
        return list;
      } else {
        return [];
      }
    }
  },
  filters: {
    is_no(val) {
      if (val) return "是";
      return "否";
    }
  },
  mounted () {
    if (this.pageTimeValue) {
      this.content.timevalue = this.content.timevalue || this.pageTimeValue;
    }
  },
  methods: {
    change_time() {
      for (let item of this.content.list) {
        this.$set(item, "time", this.content.timevalue);
      }
      this.content.list = this.content.list.map(item => {
        return item;
      });
    },
    closeAddDialog() {
      this.addDialog = false;
    },
    toEdit(data, index) {
      this.currentIndex = index;
      this.dataForm = JSON.parse(JSON.stringify(data));
      this.addDialog = true;
      // this.dataForm.link.meta.linkType = this.dataForm.link.meta.linkType || 'link';
    },
    handleChangeFloor(value) {
      this.dataForm.link.meta.page_name = "";
      this.dataForm.link.meta.page_url = value;
    },
    onSetLink(link) {
      this.dataForm.link.meta.page_url = link.meta.page_url;
      this.dataForm.link.meta.page_name = link.meta.page_name;
    },
    async confirm() {
      let linkErrMsg = '';
      if (this.dataForm.link.meta.page_url) {
        if (!this.dataForm.link.meta.page_name) {
          this.$message({
            message: "埋点名称不能为空",
            type: "warning",
            time: 3000
          });
          return;
        }
        if (this.dataForm.link.meta.linkType == 'link') {
          const result = await api.topic.checkPageUrl({ url: this.dataForm.link.meta.page_url });
          if (((result || {}).data || {}).status != 200) {
            linkErrMsg = '跳转链接不存在，请检查';
          }
        }
      }
      if (this.dataForm.link.meta.page_name) {
        if (!this.dataForm.link.meta.page_url) {
          this.$message({
            message: "链接不能为空",
            type: "warning",
            time: 3000
          });
          return;
        }
      }
      if (linkErrMsg) {
        this.$message.error(linkErrMsg);
        return false;
      }

      this.content.list.splice(this.currentIndex, 1, this.dataForm);
      this.closeAddDialog();
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.bgRes = res.data.url;
    }
  },
  watch: {
    // is_share(new_val) {
    //   this.dataForm.link.meta.is_share = new_val;
    // }
    "pageTimeValue"(new_val) {
      if (new_val) {
        this.content.timevalue = new_val;
      }
    }
  }
};
</script>

<style lang="scss" scoped rel="stylesheet/scss">
.images-box {
  .container {
    display: flex;
    align-items: center;

    .img {
      width: 78%;

      img {
        display: block;
        max-width: 300px;
        height: 100px;
      }
    }

    .button-list {
      margin-left: 10px;
    }
  }

  .content-setting {
    color: #fff;
    background-color: #13c2c2;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .el-icon-circle-plus-outline {
    font-size: 35px;
    color: #c7bdbd;
  }

  .topic-image-upload {
    .image {
      display: block;
      width: 100%;
    }

    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }

  .topic-image-picker {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

.el-row {
  text-align: center;

  .title {
    text-align: left;
    line-height: 35px;
    background-color: #f2f2f2;
    padding-left: 15px;
  }

  .block {
    display: flex;
    padding: 20px 0;
    padding-left: 15px;
    padding-right: 15px;
    .el-col-24 {
      float: none;
    }
    .pl {
      margin-left: 40px;
    }
    .btn-block:last-child {
      margin-left: 40px;
    }
    .hotView {
      display: flex;
      align-items: center;
      p {
        margin-right: 20px;
      }
    }
    .topic-image-upload {
      flex: 1;
      .el-upload {
        display: block !important;
      }
    }
  }

  .noflex {
    display: block;
    .el-col-24:last-child {
      margin-top: 15px;
    }
  }

  .noPt {
    padding-top: 0;
  }
}
</style>
<style lang="scss" rel="stylesheet/scss">
.el-dialog__body {
  padding-top: 10px;
}
</style>
