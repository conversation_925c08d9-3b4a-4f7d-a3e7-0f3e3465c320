import * as http from 'utils/http'
import {AppWebsite} from "../config";
import proxy from "./proxy";

export default {
    login(params) {
        return http.post(
            'login/in', params
        )
    },
    async initPublicKey(params){
        let pms = {
            url: AppWebsite + 'cms/login/initPublicKey',
            dataType: 'json',
            data: {
                qt : params
            },
            head: {
                terminalType: 1,
                'Content-Type': 'application/json;charset=UTF-8'
            }
        };
        return await proxy.post(pms);
    },
    getSysMenu() {
        return http.get(
            'bg/sys/menu'
        )
    },
    current() {
        return http.get(
            'user/current'
        )
    },
    /**
     * 重置密码
     * @param  {Object} params 重置密码参数信息
     * @return {Promise}
     */
    resetPassword(params) {
        return http.post(
            'corp/password/forgot', params, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        )
    },
}
