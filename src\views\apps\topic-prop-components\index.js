import pingou from "./pingou";
import image from "./H5/image";
import title from "./H5/title";
import space from "./space";
import richtext from "./H5/richtext";
import floorSpacing from "./floor-spacing";
import banner from "./banner";
import fastEntry from "./fast-entry";
import bottomFastEntry from "./bottom-fast-entry";
import itemList from "./item-list";
import itemHscroll from "./item-hscroll";
import menuList from "./menu-list";
import brand from "./brand";
import brandH from "./brand-h";
import itemHscrollPc from "./item-hscroll-pc";
import pcShopList from "./pc-shop-list";
import topicList from "./topic-list";
import seckill from "./seckill";
import streamer from "./streamer";
import newStreamer from "./newStreamer";
import hotZoneStreamer from "./hotZoneStreamer";
import headline from "./headline";
import productExhibition from "./productExhibition";
import startPage from "./startPage";
import twoImage from "./two-image";

import fourImage from "./four-image";
import recommendList from "./recommend-list";
import searchBox from "./searchBox";
import activityTips from "./activity-tips";
import tabbar from "./tabbar";
import specialArea from "./special-area";
import homeTab from "./home-tab";
import columnProduct from "./column-product";
import shoppingGuide from "./shoppingGuide";
import newShoppingGuide from "./newShoppingGuide";
import RecommendedForYou from "./recommendForYou";

import goods_new_show from "./goods-new-show";

//-----更改活动
import moreActive from "./more-active";
import wonderActive from "./wonder-active";

//app活动页
import gradualColor from "./gradual-color";
import hotRecommend from "./hot-recommend";
import topList from "./top-list";
import brandPromotion from "./brand-promotion";
import brandCollection from "./H5/brand-collection";
import swiperGoodsGroup from "./H5/swiper-goods-group";
import Article from "./article";
import highmarginTitle from "./highmargin-title";
import mainPromotion from "./main-promotion";

import goods_sales_banner from "./goods-sales";

import goods_list from "./goods-list";
import new_goods from "./new-goods-grounding";

import goods_recommend from "./goods-recommend";

//整个活动页

import activityBrand from "./H5/activity-brand";

import activityPickGoods from "./activity-pickGoods";
import activityPublic from "./activity-public";
import activityRanking from "./activity-ranking";

import single_product_banner from "./single_product/product_banner";
import single_product_init from "./single_product/product_init";
import single_product_list from "./single_product/goods_list";

//-----活动页2019/11/4 修改
// 主会场
import mainOne from "./H5/the-main-venue/one";
import mainTwo from "./H5/the-main-venue/two";
import mainThree from "./H5/the-main-venue/three";
import mainFour from "./H5/the-main-venue/four";
import mainOnePlusTwo from "./H5/the-main-venue/one_plus_two";
import mainTwoPlusOne from "./H5/the-main-venue/two_plus_one";
import mainOnePlusThree from "./H5/the-main-venue/one_plus_three";
import mainOnePlusFour from "./H5/the-main-venue/one_plus_four";
import mainTwoPlusTwo from "./H5/the-main-venue/two_plus_two";

// 广告位
import adOneH5 from "./H5/ads-list/one";
import adTwoH5 from "./H5/ads-list/two";
import adThreeH5 from "./H5/ads-list/three";
import adFourH5 from "./H5/ads-list/four";
import onePlusTwoH5 from "./H5/ads-list/one_plus_two";
import onePlusThreeH5 from "./H5/ads-list/one_plus_three";
import onePlusFourH5 from "./H5/ads-list/one_plus_four";
import twoPlusTwoH5 from "./H5/ads-list/two_plus_two";
//商品流
import commodity_flow from "./H5/commodity-flow";
//推荐商品流
import recommend_flow from "./H5/commodity-recommend-flow";
// 排行榜商品流
import chart_flow from "./H5/chart-flow";
// 品类商品流
import categiory_flow from "./H5/categiory-flow";
// 秒杀商品流
import seckill_flow from "./H5/seckill-flow";

//选项卡
//滑动选项卡
import tabs_page from "./H5/tabs-page";
//平铺选项卡
import noScrollTab from "./H5/noScrollTab";
//定位选项卡
// import handlePositionTab from "./H5/noScrollTab";
// 商品授权选项卡
import pactiontabsPage from "./H5/pactiontabs-page";
// 单品选项卡
import item_tabControl from "./H5/item_tabControl";

//优惠券
import coupon from "./H5/coupon";
// 弹窗优惠券
import popUpCoupon from "./H5/popUpCoupon";
//倒计时商品
import limitTimeGoods from "./H5/limit-group";
//滑动商品
import groupBanner from "./H5/group-banner";
//拼团
import pinTuan from "./H5/pintuan";
import pinTuanGoldGoods from "./H5/pinTuanGoldGoods";
// 大转盘
import truntable from "./H5/truntable";
// 主推单品
import mainGoods from "./H5/mainGoods";
// 订单优惠券
import orderCoupons from "./H5/orderCoupons";

//-----活动页2019/11/4 修改结束

//-----首页组件
// 广告位
import adOne from "./AppIndex/ads-list/one";
import adTwo from "./AppIndex/ads-list/two";
import adThree from "./AppIndex/ads-list/three";
import adFour from "./AppIndex/ads-list/four";
import onePlusTwo from "./AppIndex/ads-list/one_plus_two";
import onePlusThree from "./AppIndex/ads-list/one_plus_three";
import onePlusFour from "./AppIndex/ads-list/one_plus_four";
import twoPlusTwo from "./AppIndex/ads-list/two_plus_two";

// 首页精选店铺
import carefullySelectedShops from "./AppIndex/carefullySelectedShops";

// 首页人气好店
import popularQualityShops from "./AppIndex/popularQualityShops";

// 首页拼团定时活动
import groupTimeActivity from "./AppIndex/groupTimeActivity";
// 首页定时活动
import timingActivity from "./AppIndex/timingActivity";
// 首页推荐商品流
import recommendTab from "./AppIndex/recommendTab";

//-----首页组件结束

//老版控销商城
import control_storeEntry from "./controlMall-H5/controlMall-index/store-entry";
import control_goodsFlow from "./controlMall-H5/common/commodity-flow";
import control_groupBanner from "./controlMall-H5/common/slide-goods";

//新版控销商城

import control_IndexBanner from "./controlMall-H5/controlMall-index/banner";
import control_newIndexBanner from "./controlMall-H5/controlMall-index/new-banner";
import single_control_newIndexBanner from "./controlMall-H5/controlMall-index/single-new-banner";
import control_StoreBanner from "./controlMall-H5/controlMall-Store/banner";
import control_storeTitle from "./controlMall-H5/controlMall-Store/store-title";
import control_tabs_page from "./controlMall-H5/common/tabs-page";
import control_noScrollTab from "./controlMall-H5/common/noScrollTab";
import control_goldGoods from "./controlMall-H5/newControlMall/gold-goods";
import control_group_ps from "./controlMall-H5/common/group-ps";
import control_new_storeEntry from "./controlMall-H5/newControlMall/store-entry";
import control_new_newStoreEntry from "./controlMall-H5/newControlMall/new-store-entry";
import control_new_goodsFlow from "./controlMall-H5/newControlMall/commodity-flow";
import control_new_groupBanner from "./controlMall-H5/newControlMall/group-goods";
//不区分版本组件
import controlOne from "./controlMall-H5/common/controlMallAds/one";
import controlTwo from "./controlMall-H5/common/controlMallAds/two";
import controlThree from "./controlMall-H5/common/controlMallAds/three";
import controlFour from "./controlMall-H5/common/controlMallAds/four";
import controlOnePlusTwo from "./controlMall-H5/common/controlMallAds/one_plus_two";
import controlTwoPlusOne from "./controlMall-H5/common/controlMallAds/two_plus_one";
import controlOnePlusThree from "./controlMall-H5/common/controlMallAds/one_plus_three";
import controlOnePlusFour from "./controlMall-H5/common/controlMallAds/one_plus_four";
import controlTwoPlusTwo from "./controlMall-H5/common/controlMallAds/two_plus_two";

// 新控销商城首页tab
import categoryFloor from "./controlMall-H5/newControlMall/categoryFloor";
// 新控销快捷入口
import pactiontabsFastEntry from "./controlMall-H5/newControlMall/pactiontabsFastEntry";

//控销结束

import setMeal from "./H5/setMeal";
// import searchFound from './H5/searchFound';

import shopList from "./H5/shopList";

// 新app首页
import newIndexSearch from "./app-new-index-component/search";
// import newIndexFeedScroll from "./app-new-index-component/new_index_feed_scroll";
import newIndexFeedGoods from "./app-new-index-component/new_index_feed_goods";
import newIndexBottomView from "./app-new-index-component/new_index_bottom_view";

// 首页tab及分类
// import tabAndClass from './app-new-index-component/tabAndClass';
// 轮播及胶囊位
import bannerCapsule from "./app-new-index-component/bannerCapsule";
// icon 金刚位组件
import iconEntry from "./app-new-index-component/iconEntry";
// 推荐商品楼层
import recommendationProduct from "./app-new-index-component/recommendationProduct";
// 常购清单楼层
import frequentPurchaseList from "./app-new-index-component/FrequentPurchaseList";
// 超值推荐楼层
import valueRecommendation from "./app-new-index-component/valueRecommendation";
// tab子页
import tabSearch from "./app-new-index-component/tabSearch";
// 新首页底部导航
import newIndexTabbar from "./app-new-index-component/new_index_tabBar";

//吸底
import newTabBottomView from "./tab/new_index_bottom_view";
import newTabTopImage from "./tab/new_tab_top_image";
import newLRGoodsPosition from "./tab/new_tab_lr_goods_position";
import newTabDiscountRecomment from "./tab/new_tab_discount_recommend_floor";
import newTabFeedGoods from "./tab/new_tab_goods_position";
import iconEntryTab from "./app-new-index-component/iconEntryTab";

export default {
  app_h5: [
    {
      title: "APP楼层",
      children: [
        {
          title: "快捷入口",
          name: "fastEntry",
          component: fastEntry
        },
        {
          title: "药头条",
          name: "headline",
          component: headline
        },
        {
          title: "秒杀",
          name: "seckill",
          component: seckill
        },
        {
          title: "集合推荐",
          name: "productExhibition",
          component: productExhibition
        },
        {
          title: "横向商品展示",
          name: "recommendList",
          component: recommendList
        },
        {
          title: "竖向商品列表",
          name: "columnProduct",
          component: columnProduct
        },
        {
          title: "楼层间隔",
          name: "floorSpacing",
          component: floorSpacing
        },
        {
          title: "文字标题",
          name: "moreActive",
          component: moreActive
        },
        {
          title: "图片标题",
          name: "wonderActive",
          component: wonderActive
        }
      ]
    },
    {
      title: "APP活动页",
      name: "AppActivityPage",
      children: [
        {
          title: "活动品牌",
          name: "activityBrand",
          component: activityBrand
        },
        {
          title: "活动挑好货",
          name: "activityPickGoods",
          component: activityPickGoods
        },
        {
          title: "活动单品满减",
          name: "goods_sales_banner",
          component: goods_sales_banner
        },
        {
          title: "活动新." + "" + "" + "品上架",
          name: "new_goods_banner",
          component: new_goods
        },
        {
          title: "活动通用组件",
          name: "activityPublic",
          component: activityPublic
        },
        {
          title: "活动排行榜",
          name: "activityRanking",
          component: activityRanking
        },
        {
          title: "品类商品流",
          name: "categiory_flow",
          component: categiory_flow
        }
      ]
    },
    {
      title: "活动页模块",
      name: "ActivePageModule",
      children: [
        {
          title: "广告一图",
          name: "adOneH5",
          component: adOneH5
        },
        {
          title: "广告二图",
          name: "adTwoH5",
          component: adTwoH5
        },
        {
          title: "广告三图",
          name: "adThreeH5",
          component: adThreeH5
        },
        {
          title: "广告四图",
          name: "adFourH5",
          component: adFourH5
        },
        {
          title: "广告1+2布局",
          name: "onePlusTwoH5",
          component: onePlusTwoH5
        },
        {
          title: "广告1+3布局",
          name: "onePlusThreeH5",
          component: onePlusThreeH5
        },
        {
          title: "广告1+4布局",
          name: "onePlusFourH5",
          component: onePlusFourH5
        },
        {
          title: "广告2+2布局",
          name: "twoPlusTwoH5",
          component: twoPlusTwoH5
        },
        {
          title: "横幅广告",
          name: "streamer",
          component: streamer
        },
        {
          title: "商品列表",
          name: "topList",
          component: topList
        },
        {
          title: "渐变颜色",
          name: "gradualColor",
          component: gradualColor
        },
        {
          title: "热门推荐",
          name: "hotRecommend",
          component: hotRecommend
        },
        {
          title: "轮播图",
          name: "banner",
          component: banner
        },
        {
          title: "专区",
          name: "specialArea",
          component: specialArea
        },
        {
          title: "优惠券",
          name: "coupon",
          component: coupon
        },
        {
          title: "单品",
          name: "goods_new_show",
          component: goods_new_show
        },
        // {
        //     title: "氛围头图",
        //     name: "newTabTopImage",
        //     component: newTabTopImage
        // }
      ]
    },
    {
      title: "主会场",
      name: "theMainVenue",
      children: [
        {
          title: "一图",
          name: "mainOne",
          component: mainOne
        },
        {
          title: "二图",
          name: "mainTwo",
          component: mainTwo
        },
        {
          title: "三图",
          name: "mainThree",
          component: mainThree
        },
        {
          title: "四图",
          name: "mainFour",
          component: mainFour
        },
        {
          title: "1+2图",
          name: "mainOnePlusTwo",
          component: mainOnePlusTwo
        },
        {
          title: "2+1图",
          name: "mainTwoPlusOne",
          component: mainTwoPlusOne
        },
        {
          title: "1+3图",
          name: "mainOnePlusThree",
          component: mainOnePlusThree
        },
        {
          title: "1+4图",
          name: "mainOnePlusFour",
          component: mainOnePlusFour
        },
        {
          title: "2+2图",
          name: "mainTwoPlusTwo",
          component: mainTwoPlusTwo
        }
      ]
    },
    {
      title: "单品满减",
      name: "FullReductionSingle",
      children: [
        {
          title: "商品banner",
          name: "single_product_banner",
          component: single_product_banner
        },
        {
          title: "自定义banner",
          name: "single_product_init",
          component: single_product_init
        },
        {
          title: "商品列表",
          name: "single_product_list",
          component: single_product_list
        },
        {
          title: "商品流",
          name: "commodity_flow",
          component: commodity_flow
        }
      ]
    },
    {
      title: "品牌",
      name: "brand",
      children: [
        {
          title: "品牌促销",
          name: "brandPromotion",
          component: brandPromotion
        },
        {
          title: "品牌集合",
          name: "brandCollection",
          component: brandCollection
        },
        {
          title: "品牌集合四图",
          name: "brandFour",
          component: brandCollection
        }
      ]
    },
    {
      title: "高毛",
      name: "GaoMao",
      children: [
        {
          title: "高毛标题",
          name: "highmarginTitle",
          component: highmarginTitle
        }
      ]
    },
    {
      title: "挑好货",
      name: "PickGoods",
      children: [
        {
          title: "销售导航",
          name: "navSales",
          component: brandCollection
        },
        {
          title: "滚动商品组",
          name: "swiperGoodsGroup",
          component: swiperGoodsGroup
        },
        {
          title: "静态商品组",
          name: "staticGoodsGroup",
          component: swiperGoodsGroup
        },
        {
          title: "文章",
          name: "article",
          component: Article
        },
        {
          title: "单图",
          name: "image",
          component: image
        },
        {
          title: "标题",
          name: "title",
          component: title
        },
        {
          title: "富文本",
          name: "richtext",
          component: richtext
        }
      ]
    },
    {
      title: "排行榜",
      name: "rankingList",
      children: [
        // {
        //   title: '优+套餐',
        //   name: 'excellentGroup',
        //   component: excellentGroup
        // },
        {
          title: "销售导航",
          name: "navSales",
          component: brandCollection
        },
        {
          title: "促销主页",
          name: "mainPromotion",
          component: mainPromotion
        }
      ]
    },
    {
      title: "爆款推荐",
      name: "recommendationExplosives",
      children: [
        {
          title: "内容配置",
          name: "goods_recommend",
          component: goods_recommend
        }
      ]
    },
    {
      title: "Tab切换",
      children: [
        {
          title: "滚动选项卡",
          name: "tabs-page",
          component: tabs_page
        },
        {
          title: "平铺选项卡",
          name: "noScrollTab",
          component: noScrollTab
        },
        // {
        //   title: "定位选项卡",
        //   name: "handlePositionTab",
        //   component: handlePositionTab
        // },
        {
          title: "商品授权选项卡",
          name: "pactiontabs-page",
          component: pactiontabsPage
        },
        {
          title: "单品选项卡",
          name: "item_tabControl",
          component: item_tabControl
        }
      ]
    },
    {
      title: "倒计时商品",
      children: [
        {
          title: "倒计时商品",
          name: "limitTimeGoods",
          component: limitTimeGoods
        }
      ]
    },
    {
      title: "滑动商品",
      children: [
        {
          title: "滑动商品",
          name: "groupBanner",
          component: groupBanner
        }
      ]
    },
    {
      title: "拼团模块",
      children: [
        {
          title: "拼团模块",
          name: "pinTuan",
          component: pinTuan
        },
        {
          title: "拼团黄金单品",
          name: "pinTuanGoldGoods",
          component: pinTuanGoldGoods
        }
      ]
    },
    {
      title: "店铺列表",
      children: [
        {
          title: "店铺列表",
          name: "shopList",
          component: shopList
        }
      ]
    },
    {
      title: "大转盘",
      children: [
        {
          title: "大转盘",
          name: "truntable",
          component: truntable
        }
      ]
    },
    {
      title: "轮播+黄金单品+店铺楼层+商品流",
      children: [
        {
          title: "轮播",
          name: "control_newBanner",
          component: control_newIndexBanner
        },
        {
          title: "精选店铺",
          name: "control_new_newStoreEntry",
          component: control_new_newStoreEntry
        },
        {
          title: "商品流",
          name: "control_new_goodsFlow",
          component: control_new_goodsFlow
        },
        {
          title: "黄金单品",
          name: "control_goldGoods",
          component: control_goldGoods
        },
        {
          title: "店铺楼层",
          name: "control_new_groupBanner",
          component: control_new_groupBanner
        },

        {
          title: "关联一图",
          name: "control_group_ps",
          component: control_group_ps
        },
        {
          title: "一图",
          name: "controlOne",
          component: controlOne
        },
        {
          title: "二图",
          name: "controlTwo",
          component: controlTwo
        },
        {
          title: "三图",
          name: "controlThree",
          component: controlThree
        },
        {
          title: "四图",
          name: "controlFour",
          component: controlFour
        },
        {
          title: "1+2图",
          name: "controlOnePlusTwo",
          component: controlOnePlusTwo
        },
        {
          title: "2+1图",
          name: "controlTwoPlusOne",
          component: controlTwoPlusOne
        },
        {
          title: "1+3图",
          name: "controlOnePlusThree",
          component: controlOnePlusThree
        },
        {
          title: "1+4图",
          name: "controlOnePlusFour",
          component: controlOnePlusFour
        },
        {
          title: "2+2图",
          name: "controlTwoPlusTwo",
          component: controlTwoPlusTwo
        },
        {
          title: "优惠券",
          name: "couponNew",
          component: coupon
        },
        {
          title: "底部快捷入口",
          name: "bottomFastEntry",
          component: bottomFastEntry
        }
      ]
    },
    {
      title: "推荐商品流",
      children: [
        {
          title: "推荐商品流",
          name: "recommend_flow",
          component: recommend_flow
        }
      ]
    },
    {
      title: "套餐",
      children: [
        {
          title: "套餐",
          name: "setMeal",
          component: setMeal
        }
      ]
    },
    {
      title: "排行榜商品流",
      children: [
        {
          title: "排行榜商品流",
          name: "chart_flow",
          component: chart_flow
        }
      ]
    },
    {
      title: "秒杀商品流",
      children: [
        {
          title: "秒杀商品流",
          name: "seckill_flow",
          component: seckill_flow
        }
      ]
    }
  ],
  app_tab: [
    // {
    //   title: "首页搜索、tab及分类",
    //   name: "newIndexSearch",
    //   component: newIndexSearch
    // },
    // {
    //   title: "吸底广告配置",
    //   name: "newIndexBottomView",
    //   component: newIndexBottomView
    // },
    {
      title: "Tab",
      children: [
        {
          title: "吸底广告配置",
          name: "newTabBottomView",
          component: newTabBottomView
        },
        {
          title: "氛围头图",
          name: "newTabTopImage",
          component: newTabTopImage
        },
        {
          title: "左右侧推荐商品位",
          name: "newLRGoodsPosition",
          component: newLRGoodsPosition
        },
        {
          title: "超值推荐楼层",
          name: "newTabDiscountRecomment",
          component: newTabDiscountRecomment
        },
        {
          title: "feed流商品位",
          name: "newTabFeedGoods",
          component: newTabFeedGoods
        },
        {
          title: "tab子页搜索、tab及分类背景",
          name: "tabSearch",
          component: tabSearch
        },
        {
          title: "icon快捷入口",
          name: "iconEntryTab",
          component: iconEntryTab
        },
      ]
    }
  ],
  app_index: [
    {
      title: "APP楼层",
      children: [
        {
          title: "首页搜索+轮播",
          name: "searchBox",
          component: searchBox
        },
        {
          title: "快捷入口",
          name: "fastEntry",
          component: fastEntry
        },
        {
          title: "导购模块",
          name: "shoppingGuide",
          component: shoppingGuide
        },
        {
          title: "为你推荐",
          name: "RecommendedForYou",
          component: RecommendedForYou
        },
        {
          title: "药头条",
          name: "headline",
          component: headline
        },
        {
          title: "秒杀",
          name: "seckill",
          component: seckill
        },
        {
          title: "多图广告位",
          name: "brand-h",
          component: brandH
        },
        {
          title: "集合推荐",
          name: "productExhibition",
          component: productExhibition
        },
        {
          title: "横向商品展示",
          name: "recommendList",
          component: recommendList
        },
        {
          title: "楼层间隔",
          name: "floorSpacing",
          component: floorSpacing
        },
        {
          title: "横幅广告",
          name: "streamer",
          component: streamer
        },
        {
          title: "选项卡",
          name: "homeTab",
          component: homeTab
        },
        {
          title: "竖向商品列表",
          name: "columnProduct",
          component: columnProduct
        },
        {
          title: "首页一图",
          name: "adOne",
          component: adOne
        },
        {
          title: "首页二图",
          name: "adTwo",
          component: adTwo
        },
        {
          title: "首页三图",
          name: "adThree",
          component: adThree
        },
        {
          title: "首页四图",
          name: "adFour",
          component: adFour
        },
        {
          title: "首页1+2布局",
          name: "onePlusTwo",
          component: onePlusTwo
        },
        {
          title: "首页1+3布局",
          name: "onePlusThree",
          component: onePlusThree
        },
        {
          title: "首页1+4布局",
          name: "onePlusFour",
          component: onePlusFour
        },
        {
          title: "首页2+2布局",
          name: "twoPlusTwo",
          component: twoPlusTwo
        },
        {
          title: "文字标题",
          name: "moreActive",
          component: moreActive
        },
        {
          title: "图片标题",
          name: "wonderActive",
          component: wonderActive
        },
        {
          title: "底部导航",
          name: "tabbar",
          component: tabbar
        },
        {
          title: "推荐商品流",
          name: "recommend_flow",
          component: recommend_flow
        },
        {
          title: "精选店铺",
          name: "carefullySelectedShops",
          component: carefullySelectedShops
        },
        {
          title: "人气好店",
          name: "popularQualityShops",
          component: popularQualityShops
        },
        {
          title: "拼团定时活动",
          name: "groupTimeActivity",
          component: groupTimeActivity
        },
        {
          title: "定时活动",
          name: "timingActivity",
          component: timingActivity
        },
        {
          title: "推荐选项卡",
          name: "recommendTab",
          component: recommendTab
        }
      ]
    },
    {
      title: "请在新首页布局中使用",
      children: [
        {
          title: "新导购模块",
          name: "newShoppingGuide",
          component: newShoppingGuide
        },
        {
          title: "新横幅广告",
          name: "newStreamer",
          component: newStreamer
        },
        {
          title: "多热区横幅广告",
          name: "hotZoneStreamer",
          component: hotZoneStreamer
        }
      ]
    }
  ],
  app_newIndex: [
    {
      title: "APP楼层",
      children: [
        {
          title: "首页搜索、tab及分类",
          name: "newIndexSearch",
          component: newIndexSearch
        },
        {
          title: "首页头轮播及胶囊位",
          name: "bannerCapsule",
          component: bannerCapsule
        },
        {
          title: "icon快捷入口",
          name: "iconEntry",
          component: iconEntry
        },
        {
          title: "推荐商品楼层",
          name: "recommendationProduct",
          component: recommendationProduct
        },
        {
          title: "常购清单楼层",
          name: "frequentPurchaseList",
          component: frequentPurchaseList
        },
        {
          title: "超值推荐楼层",
          name: "valueRecommendation",
          component: valueRecommendation
        },
        {
          title: "首页吸底广告",
          name: "newIndexBottomView",
          component: newIndexBottomView
        },
        {
          title: "底部导航",
          name: "newIndexTabbar",
          component: newIndexTabbar
        },
        
        
        // {
        //   title: "首页tab及分类",
        //   name: "tabAndClass",
        //   component: tabAndClass
        // },
        // {
        //   title: "feed轮播",
        //   name: "newIndexFeedScroll",
        //   component: newIndexFeedScroll
        // },
        {
          title: "feed商品位",
          name: "newIndexFeedGoods",
          component: newIndexFeedGoods
        },

        {
          title: "快捷入口",
          name: "fastEntry",
          component: fastEntry
        },
        {
          title: "导购模块",
          name: "shoppingGuide",
          component: shoppingGuide
        },
        {
          title: "为你推荐",
          name: "RecommendedForYou",
          component: RecommendedForYou
        },
        {
          title: "药头条",
          name: "headline",
          component: headline
        },
        {
          title: "秒杀",
          name: "seckill",
          component: seckill
        },
        {
          title: "多图广告位",
          name: "brand-h",
          component: brandH
        },
        {
          title: "集合推荐",
          name: "productExhibition",
          component: productExhibition
        },
        {
          title: "横向商品展示",
          name: "recommendList",
          component: recommendList
        },
        {
          title: "楼层间隔",
          name: "floorSpacing",
          component: floorSpacing
        },
        {
          title: "横幅广告",
          name: "streamer",
          component: streamer
        },
        {
          title: "选项卡",
          name: "homeTab",
          component: homeTab
        },
        {
          title: "竖向商品列表",
          name: "columnProduct",
          component: columnProduct
        },
        {
          title: "首页一图",
          name: "adOne",
          component: adOne
        },
        {
          title: "首页二图",
          name: "adTwo",
          component: adTwo
        },
        {
          title: "首页三图",
          name: "adThree",
          component: adThree
        },
        {
          title: "首页四图",
          name: "adFour",
          component: adFour
        },
        {
          title: "首页1+2布局",
          name: "onePlusTwo",
          component: onePlusTwo
        },
        {
          title: "首页1+3布局",
          name: "onePlusThree",
          component: onePlusThree
        },
        {
          title: "首页1+4布局",
          name: "onePlusFour",
          component: onePlusFour
        },
        {
          title: "首页2+2布局",
          name: "twoPlusTwo",
          component: twoPlusTwo
        },
        {
          title: "文字标题",
          name: "moreActive",
          component: moreActive
        },
        {
          title: "图片标题",
          name: "wonderActive",
          component: wonderActive
        },
        {
          title: "底部导航",
          name: "tabbar",
          component: tabbar
        },
        {
          title: "推荐商品流",
          name: "recommend_flow",
          component: recommend_flow
        },
        {
          title: "精选店铺",
          name: "carefullySelectedShops",
          component: carefullySelectedShops
        }
      ]
    }
  ],
  app_static: [
    {
      title: "APP静态页",
      children: [
        {
          title: "单图",
          name: "image",
          component: image
        },
        {
          title: "轮播图",
          name: "banner",
          component: banner
        }
      ]
    },
    {
      title: "功能",
      children: [
        {
          title: "标题",
          name: "title",
          component: title
        },
        {
          title: "空白",
          name: "space",
          component: space
        },
        {
          title: "富文本",
          name: "richtext",
          component: richtext
        }
      ]
    }
  ],
  app_start: [
    {
      title: "APP启动页",
      children: [
        {
          title: "启动页",
          name: "startPage",
          component: startPage
        }
      ]
    }
  ],
  // app_dialog: [
  //   {
  //     title: "APP弹窗",
  //     children: [
  //       {
  //         title: "活动提醒",
  //         name: "activityTips",
  //         component: activityTips
  //       }
  //     ]
  //   }
  // ],
  app_diaNewLog: [
    {
      title: "新版APP弹窗",
      children: [
        {
          title: "一图",
          name: "controlOne",
          component: controlOne
        },
        {
          title: "弹窗优惠券",
          name: "popUpCoupon",
          component: popUpCoupon
        }
      ]
    }
  ],
  app_exhibitionPosition: [
    {
      title: "展位配置",
      children: [
        {
          title: "轮播",
          name: "control_newBanner",
          component: control_newIndexBanner
        },
        {
          title: "多热区横幅广告",
          name: "hotZoneStreamer",
          component: hotZoneStreamer
        },
        {
          title: "主推单品",
          name: "mainGoods",
          component: mainGoods
        },
        {
          title: "订单页优惠券",
          name: "orderCoupons",
          component: orderCoupons
        },
        {
          title: "订单页轮播",
          name: "single_control_newBanner",
          component: single_control_newIndexBanner
        }
        // {
        //   title: "搜索发现",
        //   name: "searchFound",
        //   component: searchFound
        // },
      ]
    }
  ],
  app_newControlMall: [
    {
      title: "新控销商城首页",
      name: "newControlMall",
      children: [
        {
          title: "轮播",
          name: "control_newBanner",
          component: control_newIndexBanner
        },
        {
          title: "多热区横幅广告",
          name: "hotZoneStreamer",
          component: hotZoneStreamer
        },
        {
          title: "快捷入口",
          name: "fastEntry",
          component: fastEntry
        },
        {
          title: "品类楼层",
          name: "categoryFloor",
          component: categoryFloor
        },
        {
          title: "商品授权选项卡",
          name: "pactiontabs-page",
          component: pactiontabsPage
        },
        {
          title: "新控销快捷入口",
          name: "pactiontabsFastEntry",
          component: pactiontabsFastEntry
        }
      ]
    }
  ],
  app_controlMallHome: [
    {
      title: "新版控销商城首页组件",
      name: "controlMallHome",
      children: [
        {
          title: "控销轮播",
          name: "control_banner",
          component: control_IndexBanner
        },
        {
          title: "精选店铺",
          name: "control_new_storeEntry",
          component: control_new_storeEntry
        },
        {
          title: "控销商品流",
          name: "control_new_goodsFlow",
          component: control_new_goodsFlow
        },
        {
          title: "黄金单品",
          name: "control_goldGoods",
          component: control_goldGoods
        },
        {
          title: "店铺楼层",
          name: "control_new_groupBanner",
          component: control_new_groupBanner
        },

        {
          title: "关联一图",
          name: "control_group_ps",
          component: control_group_ps
        },
        {
          title: "一图",
          name: "controlOne",
          component: controlOne
        },
        {
          title: "二图",
          name: "controlTwo",
          component: controlTwo
        },
        {
          title: "三图",
          name: "controlThree",
          component: controlThree
        },
        {
          title: "四图",
          name: "controlFour",
          component: controlFour
        },
        {
          title: "1+2图",
          name: "controlOnePlusTwo",
          component: controlOnePlusTwo
        },
        {
          title: "2+1图",
          name: "controlTwoPlusOne",
          component: controlTwoPlusOne
        },
        {
          title: "1+3图",
          name: "controlOnePlusThree",
          component: controlOnePlusThree
        },
        {
          title: "1+4图",
          name: "controlOnePlusFour",
          component: controlOnePlusFour
        },
        {
          title: "2+2图",
          name: "controlTwoPlusTwo",
          component: controlTwoPlusTwo
        }
      ]
    },
    {
      title: "老版控销商城首页组件",
      name: "controlMallHome",
      children: [
        {
          title: "控销轮播",
          name: "control_banner",
          component: control_IndexBanner
        },
        {
          title: "精选店铺",
          name: "control_storeEntry",
          component: control_storeEntry
        },
        {
          title: "控销商品流",
          name: "control_goodsFlow",
          component: control_goodsFlow
        },
        {
          title: "滑动商品",
          name: "control_groupBanner",
          component: control_groupBanner
        },
        {
          title: "关联一图",
          name: "control_group_ps",
          component: control_group_ps
        },
        {
          title: "一图",
          name: "controlOne",
          component: controlOne
        },
        {
          title: "二图",
          name: "controlTwo",
          component: controlTwo
        },
        {
          title: "三图",
          name: "controlThree",
          component: controlThree
        },
        {
          title: "四图",
          name: "controlFour",
          component: controlFour
        },
        {
          title: "1+2图",
          name: "controlOnePlusTwo",
          component: controlOnePlusTwo
        },
        {
          title: "2+1图",
          name: "controlTwoPlusOne",
          component: controlTwoPlusOne
        },
        {
          title: "1+3图",
          name: "controlOnePlusThree",
          component: controlOnePlusThree
        },
        {
          title: "1+4图",
          name: "controlOnePlusFour",
          component: controlOnePlusFour
        },
        {
          title: "2+2图",
          name: "controlTwoPlusTwo",
          component: controlTwoPlusTwo
        }
      ]
    }
  ],
  app_controlMallStore: [
    {
      title: "控销商城店铺页组件",
      name: "controlMallStore",
      children: [
        {
          title: "控销轮播",
          name: "control_StoreBanner",
          component: control_StoreBanner
        },
        {
          title: "店铺头部",
          name: "control_storeTitle",
          component: control_storeTitle
        },
        {
          title: "控销商品流",
          name: "control_goodsFlow",
          component: control_goodsFlow
        },
        {
          title: "滚动选项卡",
          name: "control_tabs-page",
          component: control_tabs_page
        },
        {
          title: "平铺选项卡",
          name: "control_noScrollTab",
          component: control_noScrollTab
        },
        {
          title: "一图",
          name: "controlOne",
          component: controlOne
        },
        {
          title: "二图",
          name: "controlTwo",
          component: controlTwo
        },
        {
          title: "三图",
          name: "controlThree",
          component: controlThree
        },
        {
          title: "四图",
          name: "controlFour",
          component: controlFour
        },
        {
          title: "1+2图",
          name: "controlOnePlusTwo",
          component: controlOnePlusTwo
        },
        {
          title: "2+1图",
          name: "controlTwoPlusOne",
          component: controlTwoPlusOne
        },
        {
          title: "1+3图",
          name: "controlOnePlusThree",
          component: controlOnePlusThree
        },
        {
          title: "1+4图",
          name: "controlOnePlusFour",
          component: controlOnePlusFour
        },
        {
          title: "2+2图",
          name: "controlTwoPlusTwo",
          component: controlTwoPlusTwo
        }
      ]
    }
  ],
  app_controlMallActivity: [
    {
      title: "控销商城活动页组件",
      name: "controlMall",
      children: [
        {
          title: "控销商品流",
          name: "control_goodsFlow",
          component: control_goodsFlow
        },
        {
          title: "滚动选项卡",
          name: "control_tabs-page",
          component: control_tabs_page
        },
        {
          title: "平铺选项卡",
          name: "control_noScrollTab",
          component: control_noScrollTab
        },
        {
          title: "一图",
          name: "controlOne",
          component: controlOne
        },
        {
          title: "二图",
          name: "controlTwo",
          component: controlTwo
        },
        {
          title: "三图",
          name: "controlThree",
          component: controlThree
        },
        {
          title: "四图",
          name: "controlFour",
          component: controlFour
        },
        {
          title: "1+2图",
          name: "controlOnePlusTwo",
          component: controlOnePlusTwo
        },
        {
          title: "2+1图",
          name: "controlTwoPlusOne",
          component: controlTwoPlusOne
        },
        {
          title: "1+3图",
          name: "controlOnePlusThree",
          component: controlOnePlusThree
        },
        {
          title: "1+4图",
          name: "controlOnePlusFour",
          component: controlOnePlusFour
        },
        {
          title: "2+2图",
          name: "controlTwoPlusTwo",
          component: controlTwoPlusTwo
        }
      ]
    }
  ],
  // app_YKQ: [
  //   {
  //     title: "宜块钱组件",
  //     name: "theMainVenue",
  //     children: [
  //       {
  //         title: "一图",
  //         name: "mainOne",
  //         component: mainOne
  //       },
  //       {
  //         title: "二图",
  //         name: "mainTwo",
  //         component: mainTwo
  //       },
  //       {
  //         title: "三图",
  //         name: "mainThree",
  //         component: mainThree
  //       },
  //       {
  //         title: "四图",
  //         name: "mainFour",
  //         component: mainFour
  //       },
  //       {
  //         title: "1+2图",
  //         name: "mainOnePlusTwo",
  //         component: mainOnePlusTwo
  //       },
  //       {
  //         title: "2+1图",
  //         name: "mainTwoPlusOne",
  //         component: mainTwoPlusOne
  //       },
  //       {
  //         title: "1+3图",
  //         name: "mainOnePlusThree",
  //         component: mainOnePlusThree
  //       },
  //       {
  //         title: "1+4图",
  //         name: "mainOnePlusFour",
  //         component: mainOnePlusFour
  //       },
  //       {
  //         title: "2+2图",
  //         name: "mainTwoPlusTwo",
  //         component: mainTwoPlusTwo
  //       },
  //       {
  //         title: "商品流",
  //         name: "commodity_flow",
  //         component: commodity_flow
  //       },
  //       {
  //         title: "滚动选项卡",
  //         name: "tabs-page",
  //         component: tabs_page
  //       },
  //       {
  //         title: "平铺选项卡",
  //         name: "noScrollTab",
  //         component: noScrollTab
  //       }
  //     ]
  //   }
  // ],
  app_KActivity: [
    {
      title: "KA活动页",
      name: "KAAppActivityPage",
      children: [
        {
          title: "活动品牌",
          name: "activityBrand",
          component: activityBrand
        },
        {
          title: "活动挑好货",
          name: "activityPickGoods",
          component: activityPickGoods
        },
        {
          title: "活动单品满减",
          name: "goods_sales_banner",
          component: goods_sales_banner
        },
        {
          title: "活动新." + "" + "" + "品上架",
          name: "new_goods_banner",
          component: new_goods
        },
        {
          title: "活动通用组件",
          name: "activityPublic",
          component: activityPublic
        },
        {
          title: "活动排行榜",
          name: "activityRanking",
          component: activityRanking
        }
      ]
    },
    {
      title: "活动页模块",
      name: "ActivePageModule",
      children: [
        {
          title: "广告一图",
          name: "adOneH5",
          component: adOneH5
        },
        {
          title: "广告二图",
          name: "adTwoH5",
          component: adTwoH5
        },
        {
          title: "广告三图",
          name: "adThreeH5",
          component: adThreeH5
        },
        {
          title: "广告四图",
          name: "adFourH5",
          component: adFourH5
        },
        {
          title: "广告1+2布局",
          name: "onePlusTwoH5",
          component: onePlusTwoH5
        },
        {
          title: "广告1+3布局",
          name: "onePlusThreeH5",
          component: onePlusThreeH5
        },
        {
          title: "广告1+4布局",
          name: "onePlusFourH5",
          component: onePlusFourH5
        },
        {
          title: "广告2+2布局",
          name: "twoPlusTwoH5",
          component: twoPlusTwoH5
        },
        {
          title: "横幅广告",
          name: "streamer",
          component: streamer
        },
        {
          title: "商品列表",
          name: "topList",
          component: topList
        },
        {
          title: "渐变颜色",
          name: "gradualColor",
          component: gradualColor
        },
        {
          title: "热门推荐",
          name: "hotRecommend",
          component: hotRecommend
        },
        {
          title: "轮播图",
          name: "banner",
          component: banner
        },
        {
          title: "专区",
          name: "specialArea",
          component: specialArea
        },
        {
          title: "优惠券",
          name: "coupon",
          component: coupon
        },
        {
          title: "单品",
          name: "goods_new_show",
          component: goods_new_show
        }
      ]
    },
    {
      title: "主会场",
      name: "theMainVenue",
      children: [
        {
          title: "一图",
          name: "mainOne",
          component: mainOne
        },
        {
          title: "二图",
          name: "mainTwo",
          component: mainTwo
        },
        {
          title: "三图",
          name: "mainThree",
          component: mainThree
        },
        {
          title: "四图",
          name: "mainFour",
          component: mainFour
        },
        {
          title: "1+2图",
          name: "mainOnePlusTwo",
          component: mainOnePlusTwo
        },
        {
          title: "2+1图",
          name: "mainTwoPlusOne",
          component: mainTwoPlusOne
        },
        {
          title: "1+3图",
          name: "mainOnePlusThree",
          component: mainOnePlusThree
        },
        {
          title: "1+4图",
          name: "mainOnePlusFour",
          component: mainOnePlusFour
        },
        {
          title: "2+2图",
          name: "mainTwoPlusTwo",
          component: mainTwoPlusTwo
        }
      ]
    },
    {
      title: "单品满减",
      name: "FullReductionSingle",
      children: [
        {
          title: "商品banner",
          name: "single_product_banner",
          component: single_product_banner
        },
        {
          title: "自定义banner",
          name: "single_product_init",
          component: single_product_init
        },
        {
          title: "商品列表",
          name: "single_product_list",
          component: single_product_list
        },
        {
          title: "商品流",
          name: "commodity_flow",
          component: commodity_flow
        }
      ]
    },
    {
      title: "品牌",
      name: "brand",
      children: [
        {
          title: "品牌促销",
          name: "brandPromotion",
          component: brandPromotion
        },
        {
          title: "品牌集合",
          name: "brandCollection",
          component: brandCollection
        },
        {
          title: "品牌集合四图",
          name: "brandFour",
          component: brandCollection
        }
      ]
    },
    {
      title: "高毛",
      name: "GaoMao",
      children: [
        {
          title: "高毛标题",
          name: "highmarginTitle",
          component: highmarginTitle
        }
      ]
    },
    {
      title: "挑好货",
      name: "PickGoods",
      children: [
        {
          title: "销售导航",
          name: "navSales",
          component: brandCollection
        },
        {
          title: "滚动商品组",
          name: "swiperGoodsGroup",
          component: swiperGoodsGroup
        },
        {
          title: "静态商品组",
          name: "staticGoodsGroup",
          component: swiperGoodsGroup
        },
        {
          title: "文章",
          name: "article",
          component: Article
        },
        {
          title: "单图",
          name: "image",
          component: image
        },
        {
          title: "标题",
          name: "title",
          component: title
        },
        {
          title: "富文本",
          name: "richtext",
          component: richtext
        }
      ]
    },
    {
      title: "排行榜",
      name: "rankingList",
      children: [
        // {
        //   title: '优+套餐',
        //   name: 'excellentGroup',
        //   component: excellentGroup
        // },
        {
          title: "销售导航",
          name: "navSales",
          component: brandCollection
        },
        {
          title: "促销主页",
          name: "mainPromotion",
          component: mainPromotion
        }
      ]
    },
    {
      title: "爆款推荐",
      name: "recommendationExplosives",
      children: [
        {
          title: "内容配置",
          name: "goods_recommend",
          component: goods_recommend
        }
      ]
    },
    {
      title: "Tab切换",
      children: [
        {
          title: "滚动选项卡",
          name: "tabs-page",
          component: tabs_page
        },
        {
          title: "平铺选项卡",
          name: "noScrollTab",
          component: noScrollTab
        }
        // {
        //   title: "定位选项卡",
        //   name: "handlePositionTab",
        //   component: handlePositionTab
        // }
      ]
    },
    {
      title: "倒计时商品",
      children: [
        {
          title: "倒计时商品",
          name: "limitTimeGoods",
          component: limitTimeGoods
        }
      ]
    },
    {
      title: "滑动商品",
      children: [
        {
          title: "滑动商品",
          name: "groupBanner",
          component: groupBanner
        }
      ]
    },
    {
      title: "拼团模块",
      children: [
        {
          title: "拼团模块",
          name: "pinTuan",
          component: pinTuan
        }
      ]
    }
  ],
  app_KAHome: [
    {
      title: "KA首页楼层",
      children: [
        {
          title: "首页搜索+轮播",
          name: "searchBox",
          component: searchBox
        },
        {
          title: "快捷入口",
          name: "fastEntry",
          component: fastEntry
        },
        {
          title: "导购模块",
          name: "shoppingGuide",
          component: shoppingGuide
        },
        {
          title: "为你推荐",
          name: "RecommendedForYou",
          component: RecommendedForYou
        },
        {
          title: "药头条",
          name: "headline",
          component: headline
        },
        {
          title: "秒杀",
          name: "seckill",
          component: seckill
        },
        {
          title: "多图广告位",
          name: "brand-h",
          component: brandH
        },
        {
          title: "集合推荐",
          name: "productExhibition",
          component: productExhibition
        },
        {
          title: "横向商品展示",
          name: "recommendList",
          component: recommendList
        },
        {
          title: "楼层间隔",
          name: "floorSpacing",
          component: floorSpacing
        },
        {
          title: "横幅广告",
          name: "streamer",
          component: streamer
        },
        {
          title: "选项卡",
          name: "homeTab",
          component: homeTab
        },
        {
          title: "竖向商品列表",
          name: "columnProduct",
          component: columnProduct
        },
        {
          title: "首页一图",
          name: "adOne",
          component: adOne
        },
        {
          title: "首页二图",
          name: "adTwo",
          component: adTwo
        },
        {
          title: "首页三图",
          name: "adThree",
          component: adThree
        },
        {
          title: "首页四图",
          name: "adFour",
          component: adFour
        },
        {
          title: "首页1+2布局",
          name: "onePlusTwo",
          component: onePlusTwo
        },
        {
          title: "首页1+3布局",
          name: "onePlusThree",
          component: onePlusThree
        },
        {
          title: "首页1+4布局",
          name: "onePlusFour",
          component: onePlusFour
        },
        {
          title: "首页2+2布局",
          name: "twoPlusTwo",
          component: twoPlusTwo
        },
        {
          title: "文字标题",
          name: "moreActive",
          component: moreActive
        },
        {
          title: "图片标题",
          name: "wonderActive",
          component: wonderActive
        },
        {
          title: "底部导航",
          name: "tabbar",
          component: tabbar
        }
      ]
    }
  ],
  pc_h5: [
    {
      title: "图片视频",
      children: [
        {
          title: "5图一组的轮播",
          name: "five_banners",
          component: goods_list
        },
        {
          title: "页面药品系列",
          name: "subject_goods",
          component: goods_list
        },
        {
          title: "轮播图",
          name: "banner",
          component: banner
        },
        {
          title: "快捷入口",
          name: "fastEntry",
          component: fastEntry
        },
        {
          title: "单图",
          name: "image",
          component: image
        },
        {
          title: "药头条",
          name: "headline",
          component: headline
        },
        {
          title: "多图广告位",
          name: "brand-h",
          component: brandH
        },
        {
          title: "秒杀",
          name: "seckill",
          component: seckill
        },
        {
          title: "二图",
          name: "twoImage",
          component: twoImage
        },
        {
          title: "四图",
          name: "fourImage",
          component: fourImage
        },
        {
          title: "集合推荐",
          name: "productExhibition",
          component: productExhibition
        },
        {
          title: "横向商品展示",
          name: "recommendList",
          component: recommendList
        },
        {
          title: "楼层间隔",
          name: "floorSpacing",
          component: floorSpacing
        },
        {
          title: "横幅广告",
          name: "streamer",
          component: streamer
        },
        {
          title: "为您推荐|常购清单",
          name: "recommend",
          component: pingou
        },
        {
          title: "底部导航",
          name: "tabbar",
          component: tabbar
        },
        {
          title: "品购",
          name: "pingou",
          component: pingou
        },
        {
          title: "广告位展示区",
          name: "advertisingExhibition",
          component: pingou
        },
        {
          title: "滑动商品组",
          name: "item-hscroll",
          component: itemHscroll
        },
        {
          title: "商品列表",
          name: "item-list",
          component: itemList
        },
        {
          title: "推荐品牌",
          name: "brand",
          component: brand
        },
        {
          title: "品牌列表",
          name: "brand-h",
          component: brandH
        },
        {
          title: "页面精选",
          name: "topic-list",
          component: topicList
        }
      ]
    },
    {
      title: "功能",
      children: [
        {
          title: "标题",
          name: "title",
          component: title
        },
        {
          title: "空白",
          name: "space",
          component: space
        },
        {
          title: "菜单列表",
          name: "menu",
          component: menuList
        },
        {
          title: "富文本",
          name: "richtext",
          component: richtext
        }
      ]
    }
  ],
  pc_index: [
    {
      title: "APP楼层",
      children: [
        {
          title: "起始页",
          name: "startPage",
          component: startPage
        },
        {
          title: "轮播图",
          name: "banner",
          component: banner
        },
        {
          title: "快捷入口",
          name: "fastEntry",
          component: fastEntry
        },
        {
          title: "单图",
          name: "image",
          component: image
        },
        {
          title: "药头条",
          name: "headline",
          component: headline
        },
        {
          title: "多图广告位",
          name: "brand-h",
          component: brandH
        },
        {
          title: "秒杀",
          name: "seckill",
          component: seckill
        },
        {
          title: "二图",
          name: "twoImage",
          component: twoImage
        },
        {
          title: "四图",
          name: "fourImage",
          component: fourImage
        },
        {
          title: "集合推荐",
          name: "productExhibition",
          component: productExhibition
        },
        {
          title: "横向商品展示",
          name: "recommendList",
          component: recommendList
        },
        {
          title: "楼层间隔",
          name: "floorSpacing",
          component: floorSpacing
        },
        {
          title: "横幅广告",
          name: "streamer",
          component: streamer
        },

        {
          title: "底部导航",
          name: "tabbar",
          component: tabbar
        }
      ]
    }
  ],
  pc_static: [
    {
      title: "图片视频",
      children: [
        {
          title: "品购",
          name: "pingou",
          component: pingou
        },
        {
          title: "单图",
          name: "image",
          component: image
        },
        {
          title: "轮播图",
          name: "banner",
          component: banner
        },
        {
          title: "商品组(横向)",
          name: "item-hscroll-pc",
          component: itemHscrollPc
        },
        {
          title: "门店列表",
          name: "pc-shop-list",
          component: pcShopList
        }
      ]
    },
    {
      title: "功能",
      children: [
        {
          title: "标题",
          name: "title",
          component: title
        },
        {
          title: "空白",
          name: "space",
          component: space
        },
        {
          title: "富文本",
          name: "richtext",
          component: richtext
        }
      ]
    }
  ],
  pc_dialog: [
    {
      title: "PC弹窗",
      children: [
        {
          title: "活动提醒",
          name: "activityTips",
          component: activityTips
        }
      ]
    }
  ]
};
