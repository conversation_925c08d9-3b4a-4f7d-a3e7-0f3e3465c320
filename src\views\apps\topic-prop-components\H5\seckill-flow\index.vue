<template>
  <div>
    <el-row :gutter="20" class="brand-time">
      <div class="title" style="margin: 0 10px;">标题设置</div>
      <el-col :span="24">
        <el-input
          size="small"
          v-model="content.title"
          placeholder="请输入标题"
          @input="()=>{
            if(content.title.length>10){
              this.$message.error('标题最大允许输入10个汉字')
              content.title=content.title.substring(0, 10)
            }
          }"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import base from "views/apps/topic-prop-components/base.vue";

  export default {
    extends: base,
    name: "seckill-flow",
    contentDefault:{
      title:""
    },
    data() {
      return {}
    },
    mounted () {},
    created() {},
    methods: {}
  }
</script>
<style scoped lang="scss">
  
</style>
