<template>
	<div class="topic-menu-list">
		<div class="bg-img">
			<el-header height="50px" style="background-color: rgb(19, 194, 194);text-align: center;padding-top: 15px">
				<label class="demonstration">背景色:</label>
				<el-color-picker v-model="content.color" size="mini" ></el-color-picker>
				<span style="margin-left: 50px"></span>
			</el-header>
			<el-container style="height: auto; border: 1px solid #eee">
				<el-main>
					<el-upload class="topic-pic-upload" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"
					           :max-size="1"
					           :show-file-list="false" :before-upload="() => {loading = true; return true;}"
					           :on-success="onUploadImage">
						<el-button type="warning" :loading="loading">上传单图</el-button>
						<div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
					</el-upload>
				</el-main>
			</el-container>
		</div>
		<el-table :data="list" style="width: 100%" height="250" v-if="list.length>0" ref="multipleTable"
		>
			<el-table-column fixed label="图片" width="80">
				<template slot-scope="scope">
					<img :src="scope.row.imageUrl" :alt="scope.row.productName" style="width:100%;max-height:50px;">
				</template>
			</el-table-column>
			<el-table-column prop="productName" label="药名" width="120">
			</el-table-column>
			<el-table-column label="规格" width="80">
				<template slot-scope="scope">
					{{scope.row.mediumPackageTitle}}
				</template>
			</el-table-column>
			<el-table-column prop="fob" label="价格" width="80">
				<template slot-scope="scope">
					{{scope.row.fob}}
				</template>
			</el-table-column>
			<el-table-column fixed="right" label="操作" width="80">
				<template slot-scope="scope">
					<div class="edit-button">
						<el-button @click="handleDelete(scope.row)" type="warning" size="mini">删除</el-button>
					</div>
				</template>
			</el-table-column>
		</el-table>
		<!--选择商品-->
		<all-link @select="onSetLink" :tabs="tabs" :params="{
                productlink: {
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                importGoods: {
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                }
            }"></all-link>
	</div>
</template>

<script>
	import base from "../base";
	import api from 'api'

	export default {
		name: "hotRecommend",
		extends: base,
		contentDefault: {
			list: [],
			image: '',
			color:'red'
		},
		data() {
			return {
				loading: false,
				tabs: [
					{label: '商品', value: 'productlink'},
					{label: '导入商品', value: 'importGoods'}
				],
				selectionTable: [],
			}
		},
		computed: {
			list() {
				var list = _.get(this, 'content.list')
				if (list) {
					return list
				} else {
					return [];
				}
			}
		},
		methods: {
			async onUploadImage(res, file) {
				this.loading = false;
				if (res.code !== 200) {
					this.$message({
						message: `[${res.code}]${res.msg}`,
						type: "warning"
					});
					return;
				}
				this.content.image = res.data.url;
			},
			onSetLink(link) {
				this.content.list = !this.content.list.length ?
						[...link.data] :
						[...api.common.removeRepeat(this.content.list, link.data)];
			},
			handleDelete(row) {
				const index = this.list.indexOf(row)
				this.list.splice(index, 1)
			}

		}
	}
</script>
<style scoped lang="scss">
	/*.topic-pic-upload{*/
	/*display:block;*/
	/*width:100% ;*/
	/*}*/
	/*.el-upload--text{width:100%}*/

</style>
