<template>
  <div class="topic-menu-list">
    <div>
      <el-row :gutter="20">
        <div class="title">列表基础配置</div>
        <el-col :span="24" style="margin: 20px">
          <el-radio v-model="content.styleType" :label="1">默认样式</el-radio>
          <el-radio v-model="content.styleType" :label="2">定制样式（高毛）</el-radio>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <div class="title">商品设置</div>
        <div class="tabBox">
          <div :class="activeTab == 0 ? 'activeTab' : ''" @click="changeTab(0)">进行中</div>
          <div :class="activeTab == 1 ? 'activeTab' : ''" @click="changeTab(1)">未开始</div>
          <div :class="activeTab == 2 ? 'activeTab' : ''" @click="changeTab(2)">自定义楼层</div>
          <!-- <div :class="activeTab == 3 ? 'activeTab' : ''" @click="changeTab(3)">关联活动</div> -->
        </div>
      </el-row>
      <div
        v-if="activeTab === 1"
        style="padding-bottom: 10px"
      >
        是否展示未开始选项卡 <el-switch v-model="content.isShowNotStarted" active-text="展示" inactive-text="隐藏"></el-switch>
      </div>
      <el-table v-if="activeTab !== 3" :data="content.list[activeTab].dataSource" size="mini" class="tableBox" style="margin: 0 0 20px" ref="tableBox" :row-key="row => row.id">
        <el-table-column label="类型">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.$index == 0 && activeTab == 0" style="color: #f56c6c">*</span>
              {{scope.row.type}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="人群范围" v-if="activeTab == 2" width="160">
          <template slot-scope="scope">
            <div class="crowdBox">
              <el-radio-group v-model="scope.row.crowdType" @change="changeCrowdType(scope.row)">
                <el-radio :label="1">全部人群</el-radio>
                <el-radio :label="2">指定人群</el-radio>
                <el-select
                  size="mini"
                  v-if="scope.row.crowdType == 2"
                  v-model="scope.row.crowdValue"
                  :loading="selectLoading"
                  filterable
                  :filter-method="optionFilter"
                  placeholder="请输入人群id"
                  clearable
                  @clear="options = []"
                  @change="selectCrowd(scope.row, $event)"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-radio-group>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品组编码" width="300">
          <template slot-scope="scope">
            <div style="display: flex">
              <el-input
                v-model="scope.row.code"
                size="mini"
                placeholder="请输入编码"
                style="width: 180px; marginRight: 10px"
                @change="changeCode(scope.row)"
              />
              <el-button size="mini" type="primary" @click="checkBindCsuOrProductGroup(scope.row, scope.$index)">确认</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="组名" show-overflow-tooltip>
          <template slot-scope="scope">
            {{scope.row.exhibitionName}}
          </template>
        </el-table-column>
        <el-table-column label="标题名称" show-overflow-tooltip v-if="activeTab != 2">
          <template slot-scope="scope">
            <div style="display: flex">
              <el-input
                v-model="scope.row.title"
                size="mini"
                placeholder="请输入"
                @input="()=>{
                if(scope.row.title.length>10){
                    $message.error('标题最大允许输入10个汉字')
                   scope.row.title=scope.row.title.substring(0, 10)
                }
              }"
                style="width: 100px; marginRight: 10px"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="展示名称" width="120" v-if="activeTab == 2">
          <template slot-scope="scope">
            <div style="display: flex">
              <el-input
                v-model="scope.row.showName"
                size="mini"
                placeholder="请输入"
                maxlength="5"
                style="width: 100px; marginRight: 10px"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="图标" width="120" v-if="activeTab == 2">
          <template slot-scope="scope">
            <div class="uploadBox">
              <el-upload
                class="topic-image-upload"
                ref="upload"
                accept="image/jpeg,image/jpg, image/png, image/gif"
                :show-file-list="false"
                :on-success="uploadImage"
              >
                <img v-if="scope.row.icon" :src="scope.row.icon" class="menuIconImage" @click="activeIndex=scope.$index" />
                <el-button v-else v-loading="uploadImgLoading" size="mini" type="primary" @click="activeIndex=scope.$index">上传图标</el-button>
                <div slot="tip" class="el-upload__tip">尺寸：40px*24px</div>
              </el-upload>
              <i v-if="scope.row.icon" class="el-icon-circle-close" @click="delMenuIcon(scope.$index)" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right" v-if="activeTab == 2">
          <template slot-scope="scope">
            <div>
              <el-button size="mini" type="primary" @click="addOrDelFloor('add')">添加</el-button>
              <el-button size="mini" type="warning" @click="addOrDelFloor('del', scope.$index)" v-if="scope.$index > 0">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- <el-table v-if="activeTab === 3" :data="content.associatedActivitiesList" size="mini" class="tableBox" style="margin: 0 0 20px" ref="tableBox" :row-key="row => row.id">
        <el-table-column label="活动类型">
          <template slot-scope="scope">
            <div>
              <el-select size="small" v-model="scope.row.activityType" placeholder="请选择类型" default-first-option filterable>
                <el-option v-for="(item, index) in activityTypeOptions" :key="index" :label="item.title" :value="item.code"></el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="活动ID" width="200">
          <template slot-scope="scope">
            <div>
              <el-select
                style="width: 70%; display:inline-block"
                class="inline-input"
                size="mini"
                v-model="scope.row.activityValue"
                placeholder="请输入活动id"
                filterable
                :filter-method="querySearchActivityId"
                clearable
                @clear="handleClearActivity(scope.row, $event)"
                @change="handleSelectAcitvity(scope.row, $event)"
                @focus="changeActivityValue(scope.row, $event)"
                :loading="selectLoading"
              >
                <el-option
                  v-for="item in activityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              <el-button @click="handelSureBind(scope.$index)" type="primary" size="mini">确定</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="活动名称">
          <template slot-scope="scope">
            <div>
              {{ scope.row.activityName }}
            </div>
          </template>
        </el-table-column>
         <el-table-column label="操作">
          <template slot-scope="scope">
            <div>
              <el-button @click="handelAddActivity(scope.$index)" type="primary" size="mini">添加</el-button>
              <el-button v-if="scope.$index > 0" @click="handleDeleteActivity(scope.$index)" type="danger" size="mini">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table> -->

      <!--选择商品-->
      <all-link ref="all_link"
        @select="onSetLink"
        :tabs="tabs"
        :params="{
          goodsGroup: {
            seledShow: false,
            minSel: 1,
            search: {
              state: 1,
              branchCode: topic.branchCode,
              indexType: 1
            }
          }
        }"
      />
    </div>
  </div>
</template>

<script>
  import base from "../../base";
  import api from 'api';
  import { AppWebsite } from "config";
  import Sortable from 'sortablejs';
  let sortableObject = {}
  export default {
    extends: base,
    contentDefault: {
      list: [],
      styleType: 1,
      isShowNotStarted: true,
      associatedActivitiesList: [{
        activityType: '',
        curActivityId: '',
        activityId: '',
        activityValue: '',
        activityName: '',
        isSure: false,
      }]
    },
    data() {
      return {
        activeTab: 0,
        tabs: [{label: '商品组', value: 'goodsGroup'}],
        activityBase: {
          activityType: '',
          curActivityId: '',
          activityId: '',
          activityValue: '',
          activityName: '',
          isSure: false,
        },
        activityTypeOptions: [],
        currentActivity: null,
        selectLoading: false,
        options: [],
        activityOptions: [],
        uploadImgLoading: false,
        activeIndex: 0,
      }
    },
    computed: {},
    mounted () {
      this.rowDrop();
      this.initRowDrop();
      this.getActivityTypeEnum();
    },
    created() {
         if(this.content&&this.content.list&&this.content.list[0]&&this.content.list[0].dataSource){
          this.content.list[0].dataSource=this.content.list[0].dataSource.filter(obj => obj.type != '策略推送数据');
         }
      this.initData();
      if (this.content.isShowNotStarted === undefined) {
        this.content.isShowNotStarted = true;
      }
      if (!this.content.associatedActivitiesList) {
        this.content.associatedActivitiesList = [];
        this.content.associatedActivitiesList.push({...this.activityBase});
      }
    },
    methods: {
      handleClearActivity(row) {
        row.activityId = '';
        row.activityName = '';
        row.activityValue = '';
        row.curActivityId = '';
        this.activityOptions = [];
      },
      handelSureBind(index) {
        this.content.associatedActivitiesList[index].isSure = true;
        this.content.associatedActivitiesList[index].activityId = this.content.associatedActivitiesList[index].curActivityId;
        this.$message.success('绑定成功');
      },
      handleSelectAcitvity(row, e) {
        if (e) {
          row.activityName = this.activityOptions[0].label;
          row.curActivityId = this.activityOptions[0].value;
          row.activityValue = this.activityOptions[0].value;
          this.activityOptions[0].label = this.activityOptions[0].value;
          setTimeout(() => {
            this.activityOptions = [];
          })
        }
      },
      changeActivityValue(row) {
        this.currentActivity = row;
      },
      async querySearchActivityId(queryString) {
        this.selectLoading = true;
        const pms = {
          type: (this.currentActivity || {}).activityType || '',
          id: queryString,
        };
        const res = await api.topic.getActivityInfos(pms);
        const { data } = res;
        this.selectLoading = false;
        if (data.success) {
          if (Object.keys(data.data.activity).length) {
            this.activityOptions = [({
              label: data.data.activity.name || "",
              value: queryString
            })]
          } else {
            this.activityOptions = [];
          }
        }
      },
      async getActivityTypeEnum() {
        const res = await api.topic.getActivityTypeEnum();
        if (res.data && res.data.success) {
          this.activityTypeOptions = res.data.data.types || [];
          const def = this.activityTypeOptions.find((item) => {
            return item.title === '支付成功返券'
          })
          if (def) {
            this.content.associatedActivitiesList.forEach((item) => {
              if (item.activityType === '') {
                item.activityType = def.code;
              }
            });
          }
        }
      },
      handleDeleteActivity(index) {
        this.$confirm('确认删除此条数据吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.content.associatedActivitiesList.splice(index, 1);
        }).catch(() => {});
      },
      handelAddActivity(index) {
        if (this.content.associatedActivitiesList.length === 10) {
           this.$message.warning('最多只能添加10个楼层');
           return false;
        }
        this.content.associatedActivitiesList.splice(index, 0, {...this.activityBase});
      },
      initRowDrop() {
        sortableObject.option('disabled', true);
      },
      rowDrop() {
        const tbody = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0];
        sortableObject = Sortable.create(tbody, {
          // 官网上的配置项,加到这里面来,可以实现各种效果和功能
          ghostClass: "sortable-ghost",
          onEnd: evt => {
            const tempList = this.content.list[this.activeTab].dataSource || [];
            const currRow = (tempList || []).splice(evt.oldIndex, 1)[0];
            (tempList || []).splice(evt.newIndex, 0, currRow);
          }
        });
      },
      genID(length) {
        return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36);
      },
      initData() {
        let initList = [];
        if ((this.content.list || []).length === 3) {
          initList = this.content.list;
        } else if ((this.content.list || []).length === 2) {
          this.content.list.push({
            listType: 3,
            dataSource: [{
              type: '自定义楼层',
              crowdType: 1,
              crowdId: '',
              crowdValue: '',
              id: this.genID(12)
            }],
          });
          initList = this.content.list;
        } else {
          initList = [{
            ptStatus: 0, // 进行中
            listType: 1,
            dataSource: [{
              type: '基础商品数据',
              title:""
            }],
          }, {
            ptStatus: 1, // 未开始
            listType: 2,
            dataSource: [{
              type: '基础商品数据',
              title:""
            }],
          }, {
            listType: 3,
            dataSource: [{
              type: '自定义楼层',
              crowdType: 1,
              crowdId: '',
              crowdValue: '',
              id: this.genID(12),
            }],
          }]
        }
        this.content.list = initList;
        this.$set(this.content, 'componentVersion', 2);
      },
      changeTab(type) {
        this.activeTab = type;
        if (type === 3) return false;
        //只有自定义楼层允许拖拽
        if (type === 2) {
          sortableObject.option('disabled', false)
        } else {
          sortableObject.option('disabled', true)
        }
      },
      changeCrowdType(row) {
        row.crowdId = '';
        row.crowdValue = '';
      },
      async optionFilter(val) {
        this.selectLoading = true;
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          this.selectLoading = false;
          this.options = [{
            label: data.name,
            value: val,
          }]
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCrowd(row, e) {
        if (e) {
          row.crowdId = Number(this.options[0].value.trim());
          row.crowdValue = this.options[0].label;
        } else {
          row.crowdId = '';
          row.crowdValue = '';
        }
        this.$forceUpdate();
      },
      // async querySearchCrowd(queryString, cb) {
      //   const pms = {
      //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
      //     dataType: "json",
      //     data: {},
      //     head: {
      //       "Content-Type": "application/json;charset=UTF-8"
      //     }
      //   };
      //   const res = await api.proxy.post(pms);
      //   if (res.success) {
      //     const { data } = res;
      //     cb([{
      //       id: queryString,
      //       value: data.name || ""
      //     }]);
      //     return false;
      //   }
      // },
      // handleSelectCrowd(row, item) {
      //   console.log(row, item);
      //   row.crowdId = item.id;
      // },
      // changeCrowdValue(row, e) {
      //   if (!e) {
      //     row.crowdId = '';
      //   }
      //   this.$forceUpdate();
      // },
      // change过程中手动清空，防止没有绑定的情况下id和name不对应
      changeCode(row) {
        row.exhibitionName = '';
      },
      async checkBindCsuOrProductGroup(row, index) {
        const params = {
          type: 2,
          exhibitionId: row.code,
          indexType: 1,
        }
        const result = await api.topic.checkBindCsuOrProductGroup(params);
        if ((result.data.data || {}).checkResult) {
          this.$set(row,'exhibitionName',((result.data.data || {}).productGroupInfo || {}).exhibitionName)
          // row.exhibitionName = ((result.data.data || {}).productGroupInfo || {}).exhibitionName;
          // this.$nextTick(() => {
          //   const dataSource = this.content.list[this.activeTab].dataSource || [];
          //   console.log('????', dataSource[index]);
            // this.$set(row, 'exhibitionName', ((result.data.data || {}).productGroupInfo || {}).exhibitionName);
          // })
          
          this.$message.success('绑定成功');
        } else {
          this.$message.error(result.data.msg);
        }
      },
      copyUrl(data) {
        const url = data;
        const oInput = document.createElement('input');
        oInput.value = url;
        document.body.appendChild(oInput);
        oInput.select(); // 选择对象;
        console.log(oInput.value);
        document.execCommand('Copy'); // 执行浏览器复制命令
        // this.$message.success('已成功复制到剪切板');
        oInput.remove();
      },
      onSetLink(link) {
        if (link.tag === "goodsGroup") {
          let obj = {};
          obj.name = link.data.name;
          obj.branchCode = link.data.branchCode;
          obj.code = link.data.code;
          this.copyUrl(link.data.code)
          // this.content.goods_group.splice(0, 1, obj);
          // this.content.list = []
        }
      },
      addOrDelFloor(type, index) {
        if (type === 'add') {
          if (this.content.list[2].dataSource.length === 10) {
            this.$message.warning('最多只能添加10个楼层');
            return;
          }
          this.content.list[2].dataSource.push({
            type: '自定义楼层',
            crowdType: 1,
            crowdId: '',
            crowdValue: '',
            id: this.genID(12),
          });
        } else {
          this.$confirm('确认删除此条数据吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.content.list[2].dataSource.splice(index, 1);
          }).catch(() => {});
        }
      },
      async uploadImage(res, row) {
        this.uploadImgLoading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: "warning"
          });
          return;
        }
        this.$set(this.content.list[this.activeTab].dataSource[this.activeIndex], 'icon', res.data.url);
        // this.content.list[this.activeTab].dataSource[this.activeIndex].icon = res.data.url;
        // this.$forceUpdate();
      },
      delMenuIcon(index) {
        this.content.list[this.activeTab].dataSource[index].icon = '';
      }
    }
  }
</script>
<style lang="scss">
  .crowdBox .el-radio__label {
    font-size: 12px !important;
  }
</style>
<style scoped lang="scss">
  .el-row {
    text-align: center;
    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
    .tabBox {
      display: flex;
      margin: 20px;
      border-bottom: 1px solid #F1F1F4;
      cursor: pointer;
      div {
        border: 1px solid #F1F1F4;
        border-bottom: none;
        padding: 5px 10px;
      }
      .activeTab {
        color: #13c2c2;
      }
    }
  }
  .el-button--mini {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
  }
  .menuIconImage {
    max-width: 50px;
    max-height: 20px;
  }
  .uploadBox {
    position: relative;
    .el-icon-circle-close {
      position: absolute;
      left: 50px;
      top: 0px;
    }
  }
  .el-upload {
    text-align: left;
  }
</style>
