<template>
  <el-container class="admin-page">
    <el-header class="top-wrap" height="50px">
      <Topbar></Topbar>
    </el-header>
    <div class="page-title">我的账号</div>
    <el-container :style="{height: contentHeight}">
      <el-aside width="213px">
        <Sidebar></Sidebar>
      </el-aside>
      <el-main class="main-layout">
        <slot></slot>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
  import Topbar from './topbar'
  import Sidebar from './sidebar'
  import Breadcrumb from './breadcrumb'
  export default {
    name: 'AdminLayout',
    components: {
      Topbar,
      Sidebar,
      Breadcrumb,
    },
    data() {
      return {
        contentHeight: window.innerHeight - 50 + 'px',
      }
    },
    computed: {
      isShow(){
        return this.$store.getters[ 'sideBar/isShow' ]
      },
    },
    mounted() {
      const self = this;
      window.addEventListener('resize', () => {
        self.contentHeight = window.innerHeight - 50 + 'px';
      })
      this.getInfo()
    },
    methods: {
      getInfo() {
      }
    }
  }
</script>

<style lang="scss" rel="stylesheet/scss">


  .admin-page {
    .top-wrap {
      padding: 0;
    }
    .breadcrumb-layout, .main-layout{
      padding: 0;
    }
    .main-layout {
      padding-bottom: 100px;
    }
    .page-title {
      padding-left: 20px;
      padding-top: 20px;
      padding-bottom: 15px;
      font-size: 20px;
      color: $black;
      border-bottom: 1px solid $extra-black;
    }
  }
</style>
