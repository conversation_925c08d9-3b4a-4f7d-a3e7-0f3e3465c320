<template>
  <div>
    <div>
      <el-row :gutter="20">
        <div class="title">选项卡名称设置</div>
        <el-col :span="18">
          <el-input placeholder="选项卡名称" v-model="tabName" size="mini" />
        </el-col>
        <el-col :span="6">
          <el-button size="mini" type="primary" @click="add_tab">添加选项卡</el-button>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <div class="title">配置选项卡内容</div>
        <el-col :span="6">
          <el-select v-model="activeName" size="mini" placeholder="请选择">
            <el-option
              v-for="item in (content.goods_list || [])"
              :key="item.name"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button
            size="mini"
            type="primary"
            :disabled="!can_config"
            @click="tabIsShow=true"
          >
            配置
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button size="mini"
            type="danger"
            :disabled="!can_config || activeName === '精选商品'"
            @click="remove_tab">删除
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-input v-model="cur_tab_name"
            :disabled="!can_config || activeName === '精选商品'"
            size="mini"
            placeholder="修改当前标签名称"></el-input>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <div class="title">选项卡顺序调整</div>
        <el-col :span="6">
          <span class="demonstration">选择顺序</span>
          <div>
            <el-select v-model="content.handle_sort_index"
              size="mini"
              :disabled="!can_config"
              placeholder="请选择"
            >
              <el-option
                v-for="index in (content.goods_list || []).length"
                :key="index"
                :label="index"
                :value="index">
              </el-option>
            </el-select>
          </div>
        </el-col>
        <el-col :span="6">
          <span class="demonstration">当前顺序</span>
          <div v-show="!can_config">
            <el-input disabled value="" size="mini" />
          </div>
          <div v-show="can_config">
            <el-input disabled :value="currentTabIndex+1" size="mini" />
          </div>
        </el-col>
      </el-row>
    </div>
    <!--选项卡内容-->
    <el-dialog
      v-if="tabIsShow"
      :title="activeName"
      :visible.sync="tabIsShow"
      :show-close="false"
      width="50%"
      :before-close="handleClose">
      <div>
        <span class="demonstration">选项卡副标题：</span>
            <el-input
              v-model="((content.goods_list || [])[currentTabIndex] || {}).subName"
              size="mini"
              style="width: 200px"
            />
      </div>
      <div style="margin: 10px 0">
        <span style="fontSize: 15px">人群范围：</span>
        <el-radio-group v-model="((content.goods_list || [])[currentTabIndex] || {}).crowdType" @change="changeCrowdType">
          <el-radio :label="1">全部人群</el-radio>
          <el-radio :label="2">指定人群</el-radio>
        </el-radio-group>
      </div>
      <div v-if="((content.goods_list || [])[currentTabIndex] || {}).crowdType===2">
        <span style="fontSize: 15px">指定人群：</span>
        <el-select
          v-model="((content.goods_list || [])[currentTabIndex] || {}).crowdValue"
          :loading="selectLoading"
          filterable
          :filter-method="optionFilter"
          placeholder="请输入人群id"
          clearable
          @clear="options = []"
          @change="selectCrowd"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <!-- <el-autocomplete
          style="width: 300px"
          class="inline-input"
          size="small"
          v-model.trim="((content.goods_list || [])[currentTabIndex] || {}).crowdValue"
          :fetch-suggestions="querySearchCrowd"
          placeholder="请输入人群id"
          :trigger-on-focus="false"
          @select="handleSelectCrowd"
          @input="changeCrowdValue"
        ></el-autocomplete> -->
      </div>
      <el-tabs v-model="cur_config_name" type="card" v-if="activeName !== '精选商品'">
        <el-tab-pane label="商品列表配置" name="goods_config">
          <div v-if="can_config">
            <el-row :gutter="20">
              <div class="title">选择列表模式</div>
              <el-col :span="24">
                <div style="text-align: left">
                  <el-radio-group v-model="((content.goods_list || [])[currentTabIndex] || {}).type"
                                  v-if="(content.goods_list || [])[currentTabIndex]">
                    <el-radio :label="index" v-for="(item,index) in typeList" :key="index">{{item}}
                    </el-radio>
                  </el-radio-group>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <div v-if="(((content.goods_list || [])[currentTabIndex] || {}).goods_group || []).length">
                <div class="title" style="margin-bottom: 0">预览已选商品组</div>
                <el-col :span="24">
                  <el-table
                    :data="((content.goods_list || [])[currentTabIndex] || {}).goods_group"
                    height="120"
                    ref="multipleTable"
                  >
                    <el-table-column prop="name" label="组名">
                    </el-table-column>
                    <el-table-column prop="code" label="编号">
                    </el-table-column>
                    <el-table-column prop="branchCode" label="区域号">
                    </el-table-column>
                  </el-table>
                </el-col>
              </div>
            </el-row>
            <el-row :gutter="20" v-if="activeName !== '精选商品'">
              <div>
                <el-col :span="24">
                  <!--选择商品-->
                  <all-link
                    ref="all_link"
                    @select="onSetLink"
                    :tabs="tabs_goods_option"
                    :params="{
                      goodsGroup: {
                        seledShow: false,
                        minSel: 1,
                        search: { state: 1, branchCode: topic.branchCode }
                      }
                    }"
                  />
                </el-col>
              </div>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="tabIsShow = false" size="mini">取 消</el-button>
        <el-button type="primary" @click="confirm" size="mini">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import base from "views/apps/topic-prop-components/base";
  import { common } from 'api'
  import api from "api";
  import { AppWebsite } from "config";

  export default {
    extends: base,
    data() {
      return {
        up_loading: false,
        cur_tab_name: "",
        cur_config_name: "goods_config",
        tabIsShow: false,
        activeName: "",
        currentTabIndex: 0,
        tabs_goods_option: [
          {label: '商品组', value: 'goodsGroup'}
        ],
        typeList: ['列表模式'],
        tabName: '',
        selectLoading: false,
        options: [],
      }
    },
    computed: {
      can_config() {
        if ((this.content.goods_list || []).length && this.activeName) {
          return true
        } else {
          return false
        }
      },
    },
    created() {
      this.content.handle_sort_index = null;
      this.cur_tab_name = this.activeName;
      this.content.active_name = ((this.content.goods_list || [])[0] || {}).name || ''
    },
    watch: {
      cur_tab_name(new_val) {
        if (new_val) {
          if (this.content.goods_list[this.currentTabIndex].name !== new_val) {
            this.content.goods_list[this.currentTabIndex].name = new_val
            this.activeName = new_val
          }
        }
      },
      tabIsShow(new_val) {
        if (!new_val && this.$refs.all_link) {
          this.$refs.all_link.close_select()
        }
      },
      activeName(new_val, old_val) {
        if (new_val) {
          this.content.active_name = new_val;
          this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.goods_list);
          this.cur_tab_name = new_val;
          this.content.handle_sort_index = null
        }
      },
      "content.handle_sort_index"(new_val, old_val) {
        if (new_val) {
          this.content.goods_list.splice(new_val - 1, 0, this.content.goods_list.splice(this.currentTabIndex, 1)[0])
          this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.goods_list);
        }
      }
    },
    methods: {
      change_spread(is_spread) {
        if (is_spread === "spread") {
          this.content.tabs.is_spread = true
        } else {
          this.content.tabs.is_spread = false
        }
      },
      handleClose(done) {
        this.$confirm('您已配置完次选项卡了么？')
          .then(_ => {
            done();
          })
          .catch(_ => {
          });
      },
      add_tab() {
        if (!this.tabName) {
          this.$message('请输入选项卡名称')
          return
        }
        if (this.content.goods_list.length == 20) {
          this.$message.warning('最多只能加20个');
          return;
        }
        if (this.content.goods_list.length > 0) {
          const nameIndex = common.getRepeatResult('name', this.tabName, this.content.goods_list);
          if (nameIndex >= 0) {
            this.$message.warning('您所添加的选项卡名称已经存在啦,请重新添加')
            return
          }
        }
        let obj = {
          name: this.tabName,
          goods_group: [],
          list: [],
          type: 0,
          img_url_list:[],
          static_goods_list: [],
          subName: '',
          tabType: 'productGroup',
          crowdType: 1,
          crowdId: '',
          crowdValue: '',
        };
        this.content.goods_list.push(obj)
        this.tabName = ""
      },
      tab_click() {
        this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.goods_list);
      },
      remove_tab() {
        const index = common.getRepeatResult('name', this.activeName, this.content.goods_list);
        this.content.goods_list.splice(index, 1)
        if (this.can_config) {
          this.activeName = this.content.goods_list[0].name
        } else {
          this.activeName = ""
        }
      },
      onSetLink(link) {
        function handle_arr(arr = []) {
          return arr.map((item) => {
            let obj = {};
            obj.init_img_url = item.init_img_url;
            obj.imageUrl = item.imageUrl;
            obj.productName = item.productName;
            obj.showName = item.showName;
            obj.mediumPackageTitle = item.mediumPackageTitle;
            obj.fob = item.fob;
            obj.id = item.id;
            obj.availableQty = item.availableQty;
            return obj
          });
        }
        if (link.tag === "goods" || link.tag === "importGoods") {
          let _self_arr = handle_arr(link.data);
          this.content.goods_list[this.currentTabIndex].list = [..._self_arr]
          this.content.goods_list[this.currentTabIndex].goods_group = []
        } else if (link.tag === "goodsGroup") {
          let obj = {};
          obj.name = link.data.name;
          obj.branchCode = link.data.branchCode;
          obj.code = link.data.code;
          this.content.goods_list[this.currentTabIndex].goods_group.splice(0, 1, obj);
          this.content.goods_list[this.currentTabIndex].list = []
        }
        this.re_load()
      },
      re_load() {
        this.content.is_reload = true
        setTimeout(() => {
          this.content.is_reload = false
        }, 2000)
      },
      changeCrowdType() {
        ((this.content.goods_list || [])[this.currentTabIndex] || {}).crowdId = '';
        ((this.content.goods_list || [])[this.currentTabIndex] || {}).crowdValue = '';
      },
      async optionFilter(val) {
        this.selectLoading = true;
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          this.selectLoading = false;
          this.options = [{
            label: data.name,
            value: val,
          }]
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCrowd(e) {
        if (e) {
          ((this.content.goods_list || [])[this.currentTabIndex] || {}).crowdId = Number(this.options[0].value.trim());
          ((this.content.goods_list || [])[this.currentTabIndex] || {}).crowdValue = this.options[0].label;
        } else {
          ((this.content.goods_list || [])[this.currentTabIndex] || {}).crowdId = '';
          ((this.content.goods_list || [])[this.currentTabIndex] || {}).crowdValue = '';
        }
        this.$forceUpdate();
      },
      // async querySearchCrowd(queryString, cb) {
      //   const pms = {
      //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
      //     dataType: "json",
      //     data: {},
      //     head: {
      //       "Content-Type": "application/json;charset=UTF-8"
      //     }
      //   };
      //   const res = await api.proxy.post(pms);
      //   if (res.success) {
      //     const { data } = res;
      //     cb([{
      //       id: queryString,
      //       value: data.name || ""
      //     }]);
      //     return false;
      //   }
      // },
      // handleSelectCrowd(item) {
      //   ((this.content.goods_list || [])[this.currentTabIndex] || {}).crowdId = item.id;
      // },
      // changeCrowdValue(e) {
      //   if (!e) {
      //     ((this.content.goods_list || [])[this.currentTabIndex] || {}).crowdId = '';
      //   }
      //   this.$forceUpdate();
      // },

      confirm() {
        const editItem = (this.content.goods_list || [])[this.currentTabIndex] || {};
        if((((this.content.goods_list || [])[this.currentTabIndex] || {}).goods_group || []).length == 0 && this.activeName !== '精选商品') {
          this.$message.warning('请添加商品组');
          return;
        }
        if (editItem.crowdType == 2 && !editItem.crowdId) {
          this.$message.warning('请选择正确的人群');
          return;
        }
        this.tabIsShow = false;
      }
    }
  }
</script>

<style scoped lang="scss">
  .el-row {
    .title {
      text-align: left;
      line-height: 30px;
      color: #13c2c2;
      padding-left: 10px;
      margin: 10px;
    }
  }

</style>
