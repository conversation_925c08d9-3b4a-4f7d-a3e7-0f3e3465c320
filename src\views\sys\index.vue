<template>
    <AdminLayout>
        <transition name="page">
            <router-view/>
        </transition>
    </AdminLayout>
</template>

<script>
    import AdminLayout from './components/admin-layout'
    import TokenVerify from 'utils/token-verify'
    import api from 'api'
    import eventbus from 'utils/eventbus'

    export default {
        name: 'app',
        components: {
            AdminLayout
        },
        data() {
            return {}
        },
        mounted() {
            this.initialize();
            this.tokenVerify = new TokenVerify()
            this.tokenVerify.start();
        },
        beforeDestroy() {
            this.tokenVerify.cancel()
        },
        methods: {
            async initialize() {
                eventbus.$on('accessTokenExpire', () => {
                    this.$router.replace({ name: 'login' })
                })
                const accountResult = await api.user.current();
                if (accountResult.code == 200) {
                    this.$store.dispatch('sys/updateCurrentMember', accountResult.data);
                } else {
                    this.$message.error(accountResult.message);
                }
            }
        }
    }
</script>

<style lang="scss" rel="stylesheet/scss">

</style>

