<template>
    <div>
        <el-row>
            <el-col :span="12">
                <el-select size="small" @change="getList" v-model="groupType" placeholder="请选择类型" default-first-option filterable>
                    <el-option label="页面" :value="1"></el-option>
                    <el-option label="品类" :value="2"></el-option>
                    <el-option label="品牌" :value="3"></el-option>
                    <el-option label="预售" :value="4"></el-option>
                    <el-option label="推荐" :value="5"></el-option>
                </el-select>
            </el-col>
            <el-col :span="12">
                <el-input size="small" style="margin-bottom:5px" v-model="groupName" placeholder="名称">
                    <el-button slot="append" icon="el-icon-search" @click="getList"></el-button>
                </el-input>
            </el-col>
        </el-row>
        <el-table size="mini" :data="list" highlight-current-row @current-change="onSelect" style="margin-bottom:5px"
                  v-loading="loading">
            <el-table-column label="ID" prop="id"></el-table-column>
            <el-table-column label="名称" prop="name"></el-table-column>
            <el-table-column label="类型" prop="type">
                <template slot-scope="scope">
                    {{scope.row.type | type}}
                </template>
            </el-table-column>
        </el-table>

        <el-pagination
                small
                layout="pager"
                :current-page="pagination.current"
                :page-size="pagination.size"
                :total="pagination.total"
                @current-change="getList">
        </el-pagination>
    </div>
</template>

<script>
    import { BUSINESS_API_SERVER } from 'config'

    export default {
        data() {
            return {
                groupType: '',
                groupName: '',
                list: [],
                pagination: {
                    size: 6,
                    current: 1,
                    total: 0
                },
                loading: false,
                manualId: ''
            }
        },
        filters: {
            type(type) {
                switch (type) {
                case 1:
                    return '页面';
                case 2:
                    return '品类';
                case 3:
                    return '品牌';
                case 4:
                    return '预售';
                case 5:
                    return '推荐';
                }
            }
        },
        methods: {
            async getList(page = 1) {
                this.pagination.current = page;
                this.loading = true;
                const params = {
                    pageNo: this.pagination.current,
                    pageSize: this.pagination.size,
                }
                if (this.groupName) {
                    params.groupName = this.groupName;
                }
                if (this.groupType) {
                    params.groupType = this.groupType;
                }
                const result = await this.$http.get(BUSINESS_API_SERVER + '&method=product.getGroupList', {
                    params: params
                })
                if (result.result.result == 1) {
                    this.list = result.data.result;
                    this.pagination.total = result.data.totalCount;
                }
                this.loading = false;
            },
            onSelect(row) {
                this.$emit('select', {
                    type: 'group',
                    label: '商品组',
                    id: row.id,
                    desc: row.name,
                    action: 'GO_GROUP',
                    meta: row
                })
            }
        },
        mounted() {
            this.getList();
        },
    }
</script>
