<template>
	<div>
		<!--品牌促销-->
		<p class="blank_20"></p>
		<el-button type="primary" @click="handleAdd" size="mini">添加</el-button>
		<el-button type="primary" @click="handleDelete" size="mini">删除</el-button>
		<p class="blank_20"></p>
		<el-row>
			<el-col :span="6">
				<em>标题名称</em>
			</el-col>
			<el-col :span="18">
				<el-input type="primary" size="mini" v-model="content.title" style="width:80%"></el-input>
			</el-col>
		</el-row>
		<p class="blank_20"></p>
		<el-table :data="list" :row-key="getRowKeys" border fit highlight-current-row style="width: 100%"
		          v-if="list.length>0"
		          @selection-change="handleSelection">
			<el-table-column
				  type="selection"
				  width="55"/>
			<el-table-column label="跳转页面" prop="data.page_name">
			</el-table-column>
			<el-table-column label="图片">
				<template slot-scope="scope">
					<upload-image :index="scope.$index" :image="scope.row.image"
					              v-on:listenImage="getImage"></upload-image>
				</template>
			</el-table-column>
		</el-table>
		<p class="blank_20"></p>
		<all-link @select="onSetLink" :tabs="tabs" :params="{
				page: {
	                branchCode: branchCode
	            }
            }"></all-link>
	</div>
</template>

<script>
	import uploadImage from './../upload-image'
	import {common} from 'api'
	export default {
		props:{
			pageData:[Array,Object],
			branchCode:String,
			label:String,
			clearNum:{
				type:Number,
				default:1
			}
		},
		name: "brandCollection",
		data() {
			return {
				tabs: [
					{label: '活动页', value: 'page'}
				],
				selectItem: [],
				loading: false,
				content:{}
			}
		},
		created(){
			this.content=_.cloneDeep(this.pageData)
		},
		watch: {
			'content':{
				deep: true,
				handler(val) {
					this.$emit('listenData',{key:this.label,data:val})
				}
			},
			'clearNum':function(){
				this.content=_.cloneDeep(this.pageData)
			}
		},
		computed: {
			list() {
				var list = _.get(this, 'content.list')
				if (list) {
					if (list.length > 0) {
						this.$nextTick(function () {
							this.setSort()
						})
					}
					return list
				} else {
					return []
				}
			}
		},
		components: {
			uploadImage
		},
		methods: {
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},
			handleAdd() {
				this.list.push({id: '', image: '', data: {page_name: '', page_url: ''}})
			},
			getRowKeys(row) {
				if (!row.id) {
					return
				}
				return row.id
			},
			onSetLink(obj) {
				if (this.selectItem.length === 0 || this.selectItem.length !== 1)
					return this.$message.warning('请先选中1个标签');
				if (this.selectItem[0].id.length > 0) {
					let nameArray = this.list.map(item => !item.data.page_name ? '' : item.data.page_name);
					const nameIndex = nameArray.indexOf(obj.meta.page_name);
					if (nameIndex >= 0)
						return this.$message.warning('您所选择活动页已经存在啦,请重新选择');
				}
				let index = this.list.indexOf(this.selectItem[0]);
				let {page_name, page_url, id} = obj.meta;
				this.list[index].data = {page_name, page_url};
				this.list[index].id = id;
			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = this.list.indexOf(item)
					this.list.splice(index, 1)
				})
			},
			getImage(data) {
				if (data.image) {
					this.list[data.index].image = data.image
				}
			}
		}
	}
</script>
