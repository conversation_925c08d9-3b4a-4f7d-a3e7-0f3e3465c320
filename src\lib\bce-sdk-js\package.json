{"name": "bce-sdk-js", "version": "0.2.8", "description": "Baidu Cloud Engine JavaScript SDK", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "./test/run-all.sh", "fecs": "fecs src", "pack": "node_modules/.bin/browserify -s baidubce.sdk index.js -o baidubce-sdk.bundle.js && uglifyjs baidubce-sdk.bundle.js --compress --mangle -o baidubce-sdk.bundle.min.js"}, "repository": {"type": "git", "url": "https://github.com/baidubce/bce-sdk-js.git"}, "author": "<EMAIL>", "license": "MIT", "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "q": "^1.1.2", "underscore": "^1.7.0"}, "devDependencies": {"browserify": "10.2.6", "coveralls": "^2.11.8", "expect.js": "^0.3.1", "istanbul": "^0.4.2", "mocha": "^2.4.5"}}