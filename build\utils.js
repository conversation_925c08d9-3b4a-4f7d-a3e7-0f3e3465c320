var path = require('path')
var config = require('../config')
var ExtractTextPlugin = require('extract-text-webpack-plugin')
var env = process.env.BUILD_ENV;
var glob = require('glob')
var fs = require('fs')
var mime = require('mime')
var md5 = require('md5')
exports.assetsPath = function (_path) {
  var assetsSubDirectory = process.env.NODE_ENV === 'prod'
      ? config[env].assetsSubDirectory
      : config[env].assetsSubDirectory
  return path.posix.join(assetsSubDirectory, _path)
}

exports.cssLoaders = function (options) {
  options = options || {}

  var cssLoader = {
    loader: 'css-loader',
    options: {
      minimize: process.env.NODE_ENV !== 'dev',
      sourceMap: options.sourceMap
    }
  }

  // generate loader string to be used with extract text plugin
  function generateLoaders (loader, loaderOptions) {
    var loaders = [cssLoader]
    if (loader) {
      loaders.push({
        loader: loader + '-loader',
        options: Object.assign({}, loaderOptions, {
          sourceMap: options.sourceMap
        })
      })
    }

    if (options.extract) {
      return ExtractTextPlugin.extract({
        use: loaders,
        fallback: 'vue-style-loader'
      })
    } else {
      return ['vue-style-loader'].concat(loaders)
    }
  }
  function generateSassResourceLoader() {
    var loaders = [
      cssLoader,
      'sass-loader',
      {
        loader: 'sass-resources-loader',
        options: {
          resources: [
            path.resolve(__dirname, '../src/styles/utils/_mixin.scss'),
            path.resolve(__dirname, '../src/styles/utils/_variables.scss'),
            path.resolve(__dirname, '../src/styles/function/_color.scss')
          ]
        }
      }
    ]
    if (options.extract) {
      return ExtractTextPlugin.extract({
        use: loaders,
        fallback: 'vue-style-loader'
      })
    } else {
      return ['vue-style-loader'].concat(loaders)
    }
  }
  // https://vue-loader.vuejs.org/en/configurations/extract-css.html
  return {
    css: generateLoaders(),
    postcss: generateLoaders(),
    less: generateLoaders('less'),
    sass: generateSassResourceLoader('sass', { indentedSyntax: true }),
    scss: generateSassResourceLoader('sass'),
    stylus: generateLoaders('stylus'),
    styl: generateLoaders('stylus')
  }
}

// Generate loaders for standalone style files (outside of .vue)
exports.styleLoaders = function (options) {
  var output = []
  var loaders = exports.cssLoaders(options)
  for (var extension in loaders) {
    var loader = loaders[extension]
    output.push({
      test: new RegExp('\\.' + extension + '$'),
      use: loader
    })
  }
  return output
}

exports.getEntry = function(globPath) {
  var entries = {}, pathname;
  glob.sync(globPath).forEach(function (entry) {
    pathname = path.basename(entry, path.extname(entry));
    entries[pathname] = entry;
  });
  return entries;
}

exports.getChunks = function() {
  var chunks = [];
  glob.sync(path.resolve(__dirname, '../src/page/**/*.js')).forEach(function (entry) {
    var pathname = path.basename(entry, path.extname(entry));
    chunks.push(pathname);
  });
  return chunks;
}

exports.resolveImg = function () {
  glob.sync(config[ env ].assetsRoot + path.sep + 'static' + path.sep + 'images' + path.sep + '**').forEach(function (file) {
    if (!fs.statSync(file).isFile()) {
      return;
    }
    var text = fs.readFileSync(file);
    if (text.length < 5 * 1024) {
      var base64 = 'data:' + mime.lookup(file) + ';base64,' + text.toString("base64");
      glob.sync(config[ env ].assetsRoot + path.sep + '**').forEach(function (distFile) {
        if (!fs.statSync(distFile).isFile()) {
          return;
        }
        var distText = fs.readFileSync(distFile, 'utf-8');
        var regExp = new RegExp(file.substr(config[ env ].assetsRoot.length), 'g');
        if (regExp.test(distText)) {
          fs.writeFileSync(distFile, distText.replace(regExp, base64));
        }
      })
      fs.unlinkSync(file);
      return;
    }
    var imgName = file.substr(config[ env ].assetsRoot.length);
    var md5Name = md5(fs.readFileSync(file, 'utf-8'));
    md5Name = md5Name.substr(7) + path.extname(file);
    var dirName = imgName.substr(1, imgName.lastIndexOf(path.sep));
    glob.sync(config[ env ].assetsRoot + path.sep + '**').forEach(function (file) {
      if (!fs.statSync(file).isFile()) {
        return;
      }
      var text = fs.readFileSync(file, 'utf-8');
      var regExp = new RegExp(imgName, 'g');
      if (regExp.test(text)) {
        fs.writeFileSync(file, text.replace(regExp, config[ env ].assetsPublicPath + dirName + md5Name));
      }
    })
    fs.renameSync(file, config[ env ].assetsRoot + path.sep + dirName + md5Name);
  })
}

exports.syncGit = function () {
  var childProcess = require('child_process');
  var child = childProcess.exec('sh shell' + path.sep + 'sync_' + env + '.sh', { encoding: 'utf8' }, function (error, stdout, stderr) {
    if (error) {
      console.log(error.stack);
      console.log('Error Code: ' + error.code);
      console.log('Error Signal: ' + error.signal);
    }
    console.log('Results: \n' + stdout);
    if (stderr.length) {
      console.log('Errors: ' + stderr);
    }
  })
  child.on('exit', function (code) {
    console.log('sync complate with code: ' + code);
  });
}
