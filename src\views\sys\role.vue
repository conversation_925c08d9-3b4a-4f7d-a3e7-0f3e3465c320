<template>
    <div class="role-content">
        <el-row v-loading="loading">
            <el-col class="role-menu">
                <el-row class="role-title-wrap">
                    <span class="role-title">角色管理</span>
                    <!--<el-button size="mini" type="primary" icon="el-icon-plus" @click="addRole">添加角色</el-button>-->
                </el-row>
                <el-tabs ref="roleTab" tab-position="left" @tab-click="handleTab" :value="tabValue">
                    <el-tab-pane :label="role.roleName" :name="`${role.id}`" v-for="role in roleList"
                                 :key="role.id">
                        <div class="role-intro">
                            <div class="role-intro-name">
                                {{role.roleName}}
                                <!--<i class="el-icon-edit-outline" @click="toEdit(role)"></i>-->
                            </div>
                            <div class="role-intro-desc">{{role.description}}</div>
                        </div>
                        <div class="data-form">
                            <div class="content-title">
                                权限设置
                            </div>
                            <el-tabs class="function-list" type="card" v-loading="loadingRole">
                                <el-tab-pane label="功能权限">
                                    <div class="tip"><i class="iconfont icon-dengpao"></i>设置当前角色对平台功能模块</div>
                                    <div class="module-name">功能模块</div>
                                    <el-tree :ref="`role${role.id}`"
                                             v-if="menuList && menuList.length"
                                             :props="props"
                                             :data="menuList"
                                             show-checkbox
                                             node-key="menuId">
                                    </el-tree>
                                    <!--<el-button size="small" type="primary" :loading="saving"
                                               @click="saveMenu(role)">{{saving ? '正在提交...' : '保存'}}
                                    </el-button>-->
                                </el-tab-pane>
                            </el-tabs>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </el-col>
        </el-row>
        <el-dialog
                :title="roleInfo.id ? '编辑角色信息' : '添加角色'"
                :visible.sync="showDialog"
                width="460px">
            <el-form class="dialog-form" :model="roleInfo" :rules="rules" ref="dataForm" label-width="90px"
                     label-position="left" size="small">
                <el-form-item label="角色名称" prop="roleName">
                    <el-input v-model="roleInfo.roleName"></el-input>
                </el-form-item>
                <el-form-item label="角色描述" prop="description">
                    <el-input v-model="roleInfo.description"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-checkbox v-if="roleInfo.id" v-model="roleInfo.delete" :true-label="1"
                             :false-label="0">删除角色</el-checkbox>
    <el-button size="small" @click="showDialog = false">取 消</el-button>
    <el-button size="small" type="primary" :loading="sending"
               @click="save">{{sending ? '正在提交...' : '确定'}}</el-button>
    </span>
        </el-dialog>
    </div>
</template>
<script>
    import api from 'api'

    export default {
        name: 'Home',
        data() {
            return {
                sending: false,
                loading: false,
                saving: false,
                roleList: [],
                props: {
                    label: 'menuName',
                    children: 'children',
                },
                tabValue: '',
                menuList: [],
                permissionMap: {},
                loadingRole: false,
                showDialog: false,
                roleInfo: {},
                rules: {
                    roleName: [
                        { required: true, message: '请填写角色名称', trigger: 'blur' },
                    ]
                },
                allMenu: [],
            }
        },
        watch: {
            tabValue() {
                this.$nextTick(() => {
                    this.handleTab({ name: this.tabValue })
                })
            },
        },
        async mounted() {
            this.$store.dispatch('sideBar/setSideBarState', false);
            this.$store.dispatch('breadcrumb/clearPath');
            this.$store.dispatch('breadcrumb/addPath', {
                title: '我的帐户',
                action: 'role'
            });
            this.loadRole();
            const navRes = await api.menu.list();
            if (navRes.code === 200) {
                this.allMenu = navRes.data;
                const menuList = [];
                navRes.data.forEach(nav => {
                    if (nav.parentId == 0) {
                        nav.children = [];
                        nav.disabled = true
                        menuList.push(nav)
                    }
                })
                navRes.data.forEach(nav => {
                    menuList.forEach(menu => {
                        if (nav.parentId === menu.menuId) {
                            nav.disabled = true
                            menu.children.push(nav)
                        }
                    })
                })
                console.log(JSON.stringify(menuList))
                this.menuList = menuList;
            } else {
                this.$message.error(navRes.message);
            }
        },
        methods: {
            async loadRole() {
                this.loading = true;
                const result = await api.role.list();
                this.loading = false;
                if (result.code == 200) {
                    this.roleList = result.data;
                    if (this.roleList.length) {
                        this.tabValue = `${this.roleList[ 0 ].id}`;
                        this.$refs.roleTab.setCurrentName(this.tabValue)
                    }
                } else {
                    this.$message.error(result.msg);
                }
            },
            addRole() {
                this.roleInfo = {};
                this.showDialog = true;
            },
            toEdit(role) {
                this.roleInfo = {
                    id: role.id,
                    roleName: role.roleName,
                    description: role.description,
                    delete: false,
                }
                this.showDialog = true;
            },
            async saveMenu(role) {
                const checkedKeys = this.$refs[ 'role' + role.id ][ 0 ].getCheckedKeys();
                const checkedMenu = [];
                checkedKeys.forEach(key => {
                    this.allMenu.some(menu => {
                        if(key === menu.menuId){
                            checkedMenu.push(menu);
                            return true;
                        }
                    })
                })
                this.saving = true;
                const result = await api.role.update(role.id, {
                    menuList: checkedMenu,
                })
                this.saving = false;
                if (result.code === 200) {
                    this.$message.success('保存成功');
                } else {
                    this.$message.error(result.msg);
                }
            },
            async handleTab(tab) {
                this.loadingRole = true;
                const id = tab.name;
                const result = await api.role.get(id)
                this.loadingRole = false;
                if (result.code === 200) {
                    const menuIds = [];
                    result.data.menuList.forEach(menu => {
                        menuIds.push(menu.menuId)
                    })
                    this.$nextTick(() => {
                        this.$refs[ 'role' + id ][ 0 ].setCheckedKeys(menuIds);
                    })
                } else {
                    this.$message.error(result.msg);
                }
            },
            async save() {
                if(this.roleInfo.delete){
                    this.$confirm('是否删除该角色', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(async (action ) => {
                        if(action === 'confirm'){
                            const result = await api.role.remove(this.roleInfo.id)
                            if (result.code == 200) {
                                this.showDialog = false;
                                this.$message.success('移除成功');
                                this.loadRole();
                            } else {
                                this.$message.error(result.msg);
                            }
                        }
                    })
                    return false;
                }
                this.$refs.dataForm.validate(async (valid) => {
                    if (valid) {
                        this.sending = true;
                        let result;
                        if (this.roleInfo.id) {
                            result = await api.role.update(this.roleInfo.id, {
                                roleName: this.roleInfo.roleName,
                                description: this.roleInfo.description
                            })
                        } else {
                            result = await api.role.add(this.roleInfo)
                        }
                        this.sending = false;
                        if (result.code === 200) {
                            this.showDialog = false;
                            if (this.roleInfo.id && !this.roleInfo.delete) {
                                this.$message.success('修改成功');
                                this.roleList.forEach(role => {
                                    if (role.id === this.roleInfo.id) {
                                        role.roleName = this.roleInfo.roleName;
                                        role.description = this.roleInfo.description;
                                    }
                                })
                            }
                            if (this.roleInfo.id && this.roleInfo.delete) {
                                this.$message.success('删除成功');
                                this.loadRole();
                            }
                            if (!this.roleInfo.id) {
                                this.$message.success('添加成功');
                                this.loadRole();
                            }
                        } else {
                            this.$message.error(result.msg);
                        }
                    } else {
                        return false;
                    }
                });
            },
        }
    }
</script>
<style lang="scss" rel="stylesheet/scss">
    .role-content {
        padding-left: 30px;
        .role-menu {
            .role-title-wrap {
                padding-top: 10px;
                display: flex;
                align-items: center;
                .role-title {
                    width: 64px;
                    font-size: 16px;
                    margin-right: 15px;
                }
                .el-button {
                    padding: 7px;
                }
            }
            .el-tabs__nav-wrap {
                box-sizing: border-box !important;
                &:after {
                    display: none !important;;
                }
            }
            .el-tabs__header {
                padding-top: 0;
                border-right: none;
            }
            .el-tabs__active-bar {
                display: none !important;;
                right: auto;
                left: 0;
                width: 3px;
            }
            .el-tabs__nav .el-tabs__item {
                width: 176px !important;;
                height: 41px !important;
                line-height: 41px !important;
                text-align: left;
                border-top: 1px solid $extra-black;
                border-left: 1px solid $extra-black;
                border-right: 1px solid $extra-black;
                box-sizing: border-box;
                &.is-active {
                    border-left: 3px solid $color-primary !important;
                    background-color: #fff;
                    &:after {
                        display: none;
                    }
                }
                &:last-child {
                    border-bottom: none;
                    border-bottom: 1px solid $extra-black;
                }
            }
            .el-tabs__content {
                padding-top: 0;
            }
            .role-intro {
                min-height: 55px;
                padding: 15px 30px;
                border: 1px solid $extra-black;
                line-height: 1.5;
                .role-intro-name {
                    font-size: 16px;
                    .el-icon-edit-outline {
                        margin-left: 10px;
                        cursor: pointer;
                        font-size: 18px;
                        color: #999;
                    }
                }
                .role-intro-desc {
                    padding-top: 8px;
                    color: #999;
                }
            }
            .data-form {
                padding-left: 0;
                padding-top: 20px;
                .content-title {
                    padding-left: 5px;
                    margin-bottom: 20px;
                    font-size: 14px !important;
                }
                .function-list {
                    padding-top: 0;
                    .el-tree {
                        margin-bottom: 30px;
                        margin-top: 10px;
                        padding-top: 10px;
                        padding-bottom: 10px;
                        width: 326px;
                        border: 1px solid $extra-black;
                    }
                    .el-tabs__header {
                        float: none;
                    }
                    .el-tabs__content {
                        padding: 30px;
                        border-right: 1px solid $extra-black;
                        border-left: 1px solid $extra-black;
                        border-bottom: 1px solid $extra-black;
                    }
                    .el-tabs__nav {
                        border-top: none;
                        border-right: none;
                        border-bottom: none;
                    }
                    .el-tabs__item {
                        border-bottom: none;
                        height: 30px !important;
                        line-height: 30px !important;
                        width: 90px !important;
                        padding: 0;
                        font-size: 13px !important;
                        text-align: center;
                        &.is-active {
                            border-left: none !important;
                            border-bottom: none;
                            top: 1px;
                            color: $black;
                        }
                    }
                    .tip {
                        display: flex;
                        align-items: center;
                        font-size: 13px;
                        .iconfont {
                            margin-right: 10px;
                            font-size: 26px;
                            color: #82C5C2;
                        }
                    }
                    .module-name {
                        padding-top: 10px;
                        font-size: 14px;

                    }
                }
            }
        }
    }

</style>
