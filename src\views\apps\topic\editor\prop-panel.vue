<template>
  <div class="topic-editor-prop">
    <section class="topic-editor-prop-page">
      <div>
        <slot name="page"></slot>
      </div>
    </section>

    <template v-if="cell">
      <section class="topic-editor-prop-common">
        <div class="topic-editor-prop-title">基础属性</div>
        <div class="topic-editor-prop-body">
          <div class="topic-editor-prop-common-header">
            <div class="topic-editor-prop-common-header-index">
              <span>{{index + 1}}</span>
            </div>
            <div @dblclick="editShow()" class="topic-editor-prop-common-header-floor">
              <div class="topic-editor-prop-common-header-floor-block">
                <div class="topic-editor-prop-common-header-floor-title">{{cell.title}}</div>
                <div class="topic-editor-prop-common-header-floor-hint">{{cell.name}}</div>
              </div>
              <div class="topic-editor-prop-common-header-floor-block">
                <el-row :gutter="0">
                  <el-col :span="4">
                    <el-tooltip :content="(cell.styles || {}).height" :open-delay="600"
                                placement="top" effect="light" v-if="cell.name != 'orderCoupons'">
                      <div>
                        <span>高度：</span>
                        <span class="topic-editor-prop-common-header-floor-hint">{{(cell.styles && cell.styles.height) | px2num}}</span>
                      </div>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="10">
                    <el-tooltip :content="(cell.styles || {}).padding" :open-delay="600"
                                placement="top" effect="light" v-if="cell.name != 'orderCoupons'">
                      <div>
                        <span>内边距(上,右,下,左)：</span>
                        <span class="topic-editor-prop-common-header-floor-hint">{{(cell.styles && cell.styles.padding) | px2num}}</span>
                      </div>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="10">
                    <el-tooltip :content="(cell.styles || {}).margin" :open-delay="600"
                                placement="top" effect="light" v-if="cell.name != 'orderCoupons'">
                      <div>
                        <span>外边距(上,右,下,左)：</span>
                        <span class="topic-editor-prop-common-header-floor-hint">{{(cell.styles && cell.styles.margin) | px2num}}</span>
                      </div>
                    </el-tooltip>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div class="topic-editor-prop-common-header-btn-group">
              <div class="topic-editor-prop-common-header-buttons" v-if="cell.name != 'orderCoupons'">
                <el-tooltip content="上移"
                            placement="top" >
                  <el-button size="small" icon="el-icon-caret-top" :disabled="index === 0||isNewLayout===1 || isNewLayout===3|| isNewLayout===5 || isNewLayout===4 "
                             @click="onMoveup()" plain></el-button>
                </el-tooltip>
                <el-tooltip content="编辑" placement="top">
                  <el-button type="primary" size="small" icon="el-icon-edit" @click="editShow()" v-if="cell.name != 'orderCoupons'"
                             plain></el-button>
                </el-tooltip>
              </div>
              <div class="topic-editor-prop-common-header-buttons">
                <el-tooltip content="下移" v-if="cell.name != 'orderCoupons'">
                  <el-button size="small" icon="el-icon-caret-bottom"
                             :disabled="index === coreLength - 1||isNewLayout===1 || isNewLayout===3|| isNewLayout===5 || isNewLayout===4 " @click="onMovedown()"
                             plain></el-button>
                </el-tooltip>
                <el-tooltip content="删除" v-if="isNewLayout != 4&& isNewLayout!=5">
                  <el-button type="danger"
                             size="small"
                             :disabled="isNewLayout===1 || isNewLayout===3 || isNewLayout===4"
                             icon="el-icon-delete" v-popover:delPov
                             plain></el-button>
                </el-tooltip>
                <el-popover
                  trigger="click"
                  placement="top"
                  ref="delPov"
                  width="80">
                  <p>确定删除此楼层吗？</p>
                  <div class="mr-10 dialog-footer">
                    <el-button type="text" @click="onRemove()">确定</el-button>
                  </div>
                </el-popover>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section class="topic-editor-prop-content">
        <div class="topic-editor-prop-title">内容属性</div>
        <div class="topic-editor-prop-body">
          <component
            v-if="propContentName"
            ref="content"
            :is="`c-${propContentName}`"
            :value="cell.content"
            :topic="topic"
            :categoryList="categoryList"
            :core="core"
            @input="onUpdateContent">
          </component>
          <div v-else style="padding:50px 0;text-align:center">未启用的组件</div>
        </div>
      </section>

      <!-- 设置属性弹窗 -->
      <prop-edit ref="propEdit" :params="edit" @commit="editCommit" @close="editClose"></prop-edit>
    </template>
  </div>
</template>
<script>
  import event_bus from "../../../../utils/eventbus"
  import propEdit from './prop-edit';
  import propComponents from '../../topic-prop-components';

  const componentList = {
    propEdit
  }, componentKeys = {};
  Object.keys(propComponents).forEach(key => {
    propComponents[key].forEach(propComponent => {
      propComponent.children.forEach(component => {
        if (!componentKeys[component.name]) {
          componentKeys[component.name] = component.component;
          componentList['c-' + component.name] = component.component;
        }
      });
    });
  });
  export default {
    props: {
      core: Object,
      cell: Object,   // 组件数据
      index: Number,  // 当前选中组件的index
      coreLength: Number,
      topic: Object,
      isNewLayout:{
        type:Number,
        default:2
      },
      categoryList: Array
    },
    data() {
      return {
        // showPropPanel: false,
        loading: false,
        edit: {
          base: {}
        },
        defaultStyles: {     //楼层默认style
          'searchBox': {      //首页搜索+轮播
            height: '94pt',
            padding: '0pt 0pt 0pt 0pt',
            margin: '0pt 0pt 0pt 0pt',
          },
          'banner': {         //轮播图
            height: '128pt',
            padding: '0pt 0pt 0pt 0pt',
            margin: '0pt 0pt 0pt 0pt',
          },
          'fastEntry': {      //快捷入口
            height: '108pt',
            margin: '20pt 0pt 0pt 0pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'headline': {       //药头条
            padding: '0pt 0pt 0pt 0pt',
            height: '44pt',
            margin: '10pt 10pt 10pt 10pt',
          },
          'homeTab': {       //选项卡
            padding: '0pt 0pt 0pt 0pt',
            height: '44pt',
            margin: '0pt 10pt 0pt 10pt'
          },
          'brand-h': {        //多图广告位
            height: '90pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'seckill': {        //秒杀
            height: '250pt',
            padding: '0pt 0pt 0pt 0pt',
            margin: '0pt 0pt 0pt 0pt',
          },
          'productExhibition': {  //单品展示模块
            height: '308pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'recommendList': {  //集合推荐
            height: '90pt',
            padding: '0pt 10pt 0pt 10pt',
            margin: '10pt 10pt 10pt 10pt',
          },
          'floorSpacing': {   //楼层间隔
            height: '10pt',
            padding: '0pt 0pt 0pt 0pt',
            margin: '0pt 0pt 0pt 0pt',
          },
          'streamer': {       //横幅广告
            height: '120pt',
            padding: '0pt 0pt 0pt 0pt',
            margin: '0pt 0pt 0pt 0pt',
          },
          'topList': {       //商品列表
            height: '600pt',
            padding: '10pt 10pt 10pt 10pt',
            margin: '0pt 0pt 0pt 0pt',
          },
          'activityTips': {    //活动提醒
            height: '100pt',
            padding: '0pt 0pt 0pt 0pt',
            margin: '0pt 0pt 0pt 0pt',
          },
          'moreActive': {    //文字标题
            height: '44pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'wonderActive': {    //图片标题
            height: '44pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'adOne': {       //一图
            height: '125pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'adTwo': {     //二图
            height: '90pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'adThree': {      //三图
            height: '145pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'adFour': {       //四图
            height: '120pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'onePlusTwo': {       //1+2
            height: '185pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'onePlusThree': {       //1+3
            height: '205pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'onePlusFour': {       //1+4
            height: '205pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
          'twoPlusTwo': {       //2+2
            height: '185pt',
            margin: '10pt 10pt 10pt 10pt',
            padding: '0pt 0pt 0pt 0pt',
          },
        }
      };
    },
    watch: {
      common: {
        deep: true,
        handler(value) {
          this.onUpdateCommon(value);
        }
      }
    },
    computed: {
      propContentName() { // 配置面板对应当前组件的content组件
      console.log(this.cell, "Cecll")
        return !this.cell ? null : _.get(this.getComponentProp(this.cell.name), 'name');
      }
    },
    methods: {
      getComponentProp(name) {
        let current;
        Object.keys(propComponents).forEach(key => {
          propComponents[key].some(propComponent => {
            return propComponent.children.some(component => {
              if (component.name === name)
                current = component;
              return component.name === name;
            })
          })
        });
        return current;
      },
      onRemove() {
        this.$emit('remove');
      },
      onMoveup() {
        this.$emit('moveup');
      },
      onMovedown() {
        this.$emit('movedown');
      },
      onUpdateCommon(common) {
        this.$emit('update-common', common);
      },
      onUpdateContent(content) {
        this.$emit('update-content', content);
      },
      // 提供给view-prop使用的custom方法, 用于更新custom数据
      custom(data) {
        _.invoke(this, '$refs.content.custom', data);
      },
      // 生成楼层
      createCell(component) {
        return {
          instance_id: `${Date.now()}${_.random(1000, 9999)}`,
          title: component.title,
          name: component.name,
          styles: component.styles || {},
          common: {
            name: component.title
          },
          content: component.component.contentDefault || {}
        };
      },
      /**
       * 将style属性字符值，转为Object使用
       * @param style
       * @returns {*}
       */
      style2Obj(style) {
        if (!style)
          return style;
        for (let k in style) {
          if (!style[k])
            continue;
          let stl = style[k].split(' ').map(item => {
            return parseFloat(item);
          });
          style[k] = (stl.length == 1) ? stl[0] : stl;
        }
        return style;
      },
      style4Obj(style) {
        if (!style)
          return style;
        if (style.height)
          style.height += style.heightUnit;
        else
          delete style.height;
        // if (style.margin.reduce((c, v) => c += Number(v || 0), 0))  //内部值总和，是否 > 0
        //     style.margin = style.margin.map(v => (v || 0) + style.marginUnit).join(' ');
        // else
        //     delete style.margin;
        if (style.margin.length)  //内部值总和，是否 > 0
          style.margin = style.margin.map(v => (v || 0) + style.marginUnit).join(' ');
        else
          delete style.margin;
        // if (style.padding.reduce((c, v) => c += Number(v || 0), 0))
        //     style.padding = style.padding.map(v => (v || 0) + style.paddingUnit).join(' ');
        // else
        //     delete style.padding;
        if (style.padding.length)
          style.padding = style.padding.map(v => (v || 0) + style.paddingUnit).join(' ');
        else
          delete style.padding;
        delete style.heightUnit;
        delete style.marginUnit;
        delete style.paddingUnit;
        return style;
      },
      editShow(ref = 'propEdit') {
        if (this.cell) {
          let baseEdit = _.cloneDeep(_.pick(this.cell, ['common', 'styles']));
          this.style2Obj(baseEdit.styles);
          Object.assign(this.edit.base, baseEdit);
        }
        this.$refs[ref].show();
      },
      editClose() {

      },
      editCommit(data) {
        this.style4Obj(data.styles);     //属性过滤
        for (let k in data) {
          this.$set(this.cell, k, data[k]);   //拷贝数据（实时刷新）
        }
        // Object.assign(this.cell, data); //拷贝数据
      }
    },
    mounted() {
      if (!this.cell)
        return;
      this.cell.styles = _.merge(this.defaultStyles[this.cell.name], this.cell.styles);   //设置styles默认值
      event_bus.$on("change_default_height", (val) => {
        this.$set(this.cell, "common", {name: "快捷入口"});
        this.$set(this.cell, "styles",
          {
            height: val,
            margin: "10pt 10pt 10pt 10pt"
          });
      })
    },
    components: componentList,
    filters: {
      px2num(px) {
        if (!px)
          return '--';
        let pxs = px.split(' ').map(item => parseFloat(item));
        return pxs.join();
      }
    }
  }
</script>
<style lang="scss" scoped rel="stylesheet/scss">


  .topic-editor-prop {
    .topic-editor-prop-title {
      font-size: 16px;
      line-height: 30px;
      text-align: center;
      color: #fff;
      background: $color-primary;
    }

    .topic-editor-prop-body {
      min-height: 20px;
      border-bottom: $border-base;
    }

    .topic-editor-prop-common {
      .topic-editor-prop-common-header {
        display: flex;
        justify-content: space-between;
        overflow: hidden;
        height: 60px;
        padding: 5px;
        line-height: 30px;

        .topic-editor-prop-common-header-index {
          float: left;
          min-width: 30px;
          margin-right: 5px;
          border-right: $border-base;
          font-size: 14px;
          line-height: 60px;
          color: #333;
          text-align: center;
        }

        .topic-editor-prop-common-header-floor {
          overflow: hidden;
          width: 100%;
          margin: 0 5px;
          white-space: nowrap;
          text-overflow: ellipsis;

          .topic-editor-prop-common-header-floor-block {
            display: block;
            border-bottom: $border-base;

            .topic-editor-prop-common-header-floor-title {
              overflow: hidden;
              height: 16px;
              line-height: 16px;
              font-size: 16px;
              color: #333;
            }

            .topic-editor-prop-common-header-floor-hint {
              overflow: hidden;
              height: 14px;
              line-height: 14px;
              font-size: 12px;
              color: #aaa;
            }
          }
        }

        .topic-editor-prop-common-header-btn-group {
          float: right;
          width: 70px;
          margin-left: 5px;

          .topic-editor-prop-common-header-buttons {
            display: flex;
            padding: 1px 0;
            text-align: center;

            button {
              width: 30px;
              height: 30px;
              padding: 0;
              min-width: auto;
              font-size: 20px;
              font-weight: bold;
            }
          }
        }
      }
    }

    &-content {
      .topic-editor-prop-body {
        padding: 5px;
      }
    }
  }
</style>
