<template>
    <div class="topic-image">
        <div class="topic-image-tips">
            注：大图请使用具有切片功能的<b>起始页</b>组件，此组件只适合于不想切片的<b>小图</b>（gif动图、二维码图、需长按保存的图），上传体积不允许超过<b>2M</b>。
        </div>
        <el-upload
                class="topic-image-upload"
                ref="upload"
                :max-size="2"
                :skip-rule="true"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                :show-file-list="false"
                :before-upload="() => {loading = true; return true;}"
                :on-success="onUploadImageOther">
            <el-button class="btn-block" type="primary" :loading="loading">上传单图(异形屏)</el-button>
            <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
        </el-upload>
        <!-- <el-radio-group v-model="content.link.startTimeTypeOne" style="text-align: left;width: 100%;margin-top: 10px;">
                <el-radio :value="0" style="padding: 6px 10px;">默认有效期内全天展示</el-radio><br>
                <el-radio :value="1" style="padding: 6px 10px;">设置周期循环 <el-button style="background-color: rgb(19,194,194);color: white;">配置</el-button> </el-radio>
         </el-radio-group> -->
         <el-radio v-model="content.startTimeTypeOne" :label="1" style="padding: 3px 10px;margin-top: 10px;">默认有效期内全天展示</el-radio><br>
         <el-radio v-model="content.startTimeTypeOne" :label="2" style="padding: 3px 10px;" >设置周期循环 <el-button @click="toloopcirculateTime(0)" style="background-color: rgb(19,194,194);color: white;">配置</el-button></el-radio>
        <el-upload
                class="topic-image-upload"
                ref="upload"
                :max-size="2"
                :skip-rule="true"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                :show-file-list="false"
                :before-upload="() => {loading = true; return true;}"
                :on-success="onUploadImage">
            <el-button class="btn-block" type="primary" :loading="loading">上传单图(普通屏)</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
        <el-radio v-model="content.startTimeTypeTwo" :label="1" style="padding: 3px 10px;margin-top: 10px;">默认有效期内全天展示</el-radio><br>
        <el-radio v-model="content.startTimeTypeTwo" :label="2" style="padding: 3px 10px;">设置周期循环 <el-button @click="toloopcirculateTime(1)" style="background-color: rgb(19,194,194);color: white;">配置</el-button></el-radio>
        <span>链接类型</span>
        <el-select v-model="content.link.linkType" placeholder="请选择" @change="changeLink">
            <el-option
              v-for="item in linkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        <div class="topic-image-info">
            <!-- <div class="name">{{content.link.page_name}}</div> -->
            <el-input  class="name" style="" size="small" placeholder="页面名称" v-model="content.link.page_name"></el-input>
            <el-input placeholder="链接地址" v-model="content.link.page_url"  @input="urlChange">
            </el-input>
            <div class="desc">{{linkDesc}}</div>
            <div class="data"></div>
            <div class="del el-icon-delete" @click="onResetLink()"></div>
        </div>
        <div class="topic-image-picker">跳转链接</div>
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
        <!-- //设置周期循环 -->
        <loopcirculateTime ref="loopcirculateTime" @loopcirculateTimeBack="loopcirculateTimeBack"></loopcirculateTime>
    </div>
</template>

<script>
    import loopcirculateTime from '../../components/loopcirculateTime.vue';
    import base from '../base'
    import api from "api";
    import { getUrlParam } from "config";
    export default {
        name:'startPage',
        extends: base,
        contentDefault: {
            image: '',
            imageOther : '',
            link: {
                    page_url: '',
                    page_name:'',
                },
            startTimeTypeOne:1,
            startTimeTypeTwo:1,
            circulateTimeOne:{},
            circulateTimeTwo:{},
        },
        components:{
            loopcirculateTime
        },
        data() {
            return {
              linkOptions: [
                  {
                    value: "topic",
                    label: "专题页链接"
                  },
                  {
                    value: "stores",
                    label: "店铺页链接"
                  },
                  {
                    value: "dynamic",
                    label: "动态商品链接"
                  }
            ],
              editData: {
                        linkType: "topic",
                        title: "",
                        entry: "",
                        frontColor: "#000000",
                        operation: "",
                        timevalue: [],
                    link: {
                        meta: {
                        page_url: ""
                    }
              },
        crowdType: 1,
        entryLocation: "",
        crowdValue: "",
        crowdId: "",
        timeType:'1',
        circulateTime:{}
      },
                typeNow:0,
                pickerOptions0: {
                    shortcuts: [
                    {
                        text: "未来一周",
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                        picker.$emit("pick", [start, end]);
                        }
                    },
                    {
                        text: "未来一个月",
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                        picker.$emit("pick", [start, end]);
                        }
                    },
                    {
                        text: "未来三个月",
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                        picker.$emit("pick", [start, end]);
                        }
                    },
                    {
                        text: "未来六个月",
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                        picker.$emit("pick", [start, end]);
                        }
                    },
                    {
                        text: "未来一年",
                        onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                        picker.$emit("pick", [start, end]);
                        }
                    }
                    ]
                },
                loading: false
            }
        },
        computed: {
            // startTimeTypeTwo(){
            //     return this.content.link.sarTtimeType || 0
            // },
            linkName() {
                return this.content.link.page_name || '未填链接'
            },
            linkDesc() {
                return ''
            }
        },
        created() {
            console.log(this.content,222)
            if(!this.content.startTimeTypeOne||!this.content.circulateTimeOne){
                this.content.startTimeTypeOne=1
                this.content.circulateTimeOne={}
            }
            if(!this.content.startTimeTypeTwo||!this.content.circulateTimeTwo){
                this.content.startTimeTypeTwo=1
                this.content.circulateTimeTwo={}
            }
            this.debounce = _.debounce(this.changeLink, 1000);
        },
        methods: {
            //打开周期
            toloopcirculateTime(type){
                this.typeNow=type
                if(type==0){
                    if(this.content.circulateTimeOne){
                        this.$refs.loopcirculateTime.circulateTime=JSON.parse(JSON.stringify(this.content.circulateTimeOne))
                    }else{
                        this.$refs.loopcirculateTime.circulateTime={}
                    }
                }else{
                    if(this.content.circulateTimeTwo){
                        this.$refs.loopcirculateTime.circulateTime=JSON.parse(JSON.stringify(this.content.circulateTimeTwo))
                    }else{
                        this.$refs.loopcirculateTime.circulateTime={}
                    }
                }
                this.$refs.loopcirculateTime.showVisible=true
            },
            loopcirculateTimeBack(data){
                data=JSON.parse(JSON.stringify(data))
                debugger
                if(this.typeNow==0){
                    this.content.circulateTimeOne=data
                }else{
                    this.content.circulateTimeTwo=data
                }
            },
            urlChange(){
      this.content.link.page_url=this.content.link.page_url.trim()
    },
            onSetLink(link) {
                console.log(link)
                Object.assign(this.content.link, link.meta)
                // this.content.link = link.meta
            },
            onResetLink() {
                this.content.link =
                     {
                        linkType:'',
                        page_url: '',
                        page_name:''
                    }
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.image = res.data.url
            },
            async onUploadImageOther(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.imageOther = res.data.url
            },
            async changeLink() {
                this.content.link.page_url=this.content.link.page_url.trim()
                if (this.content.link.page_url&&this.content.link.linkType=="topic") {
                    if (!new RegExp("^(ybmpage://commonh5activity|ybmpage://homeSteadyChannel|ybmpage://searchresult).*$").test(this.content.link.page_url)) {
                        if (this.content.link.page_url.includes('ybmpage://searchresult')) {
                            return false;
                        }
                        this.$message.error('跳转链接格式不正确');
                        this.content.link.page_url = '';
                    } else {
                        let linkPageUrl = getUrlParam(this.content.link.page_url, 'url');
                        const result = await api.topic.checkPageUrl({ url: linkPageUrl });
                        if (((result || {}).data || {}).status != 200) {
                        this.$message.error('跳转链接不存在');
                        this.content.link.page_url = '';
                        }
                    }
                }
            }
        },
        //监听input输入值变化
        watch:{
        'content.link.page_url': {
            handler(val, oldVal) {
            console.log(this.content.link.page_name,val,111)
            if (val) {
                // this.debounce();
                // if(!this.content.link.page_name&&val){
                //     this.content.link.page_name=""
                // }
            }else{
                // if(!this.content.link.page_name&&!val){
                //     this.content.link.page_name="未填链接"
                // }
            }
            }
        }
        }
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .topic-image-tips {
        padding: 5px 0;
        font-size: 12px;
        color: #999;

        b {
            color: $color-danger;
        }
    }

    .topic-image-info {
        position: relative;
        overflow: hidden;
        height: 62px;
        padding-bottom: 10px;
        margin: 5px 0;
        border: $border-base;
        font-size: 12px;
        .name {
            position: relative;
            overflow: hidden;
            height: 30px;
            // padding: 0 5px;
            margin-bottom: 3px;
            font-size: 14px;
            line-height: 30px;
            border-bottom: $border-base;
        }
        .desc {
            position: relative;
            overflow: hidden;
            height: 16px;
            padding: 0 5px;
            line-height: 16px;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .data {
            position: relative;
            overflow: hidden;
            height: 16px;
            padding: 0 5px;
            line-height: 16px;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #999;
        }
        .del {
            position: absolute;
            top: 0;
            right: 0;
            padding: 7px;
            height: 16px;
            border-left: $border-base;
            background: #fff;
            cursor: pointer;
            &:hover {
                background: $color-base-silver;
                color: #fff;
            }
        }

    }

    .topic-image-picker {
        line-height: 40px;
        text-align: center;
        background: $color-base-gray;
        color: $border-color-hover;
    }

</style>
<style>
    .topic-image-upload .el-upload {
        width: 100%;
    }
</style>
