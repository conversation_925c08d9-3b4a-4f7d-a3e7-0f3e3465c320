<template>
  <div class="product-link">
    <el-row v-if="!inSelScope.eq" class="mb-10">
      <el-alert :title="inSelScope.tip" :closable="false" type="warning" center show-icon></el-alert>
    </el-row>

    <!-- 查询商品区 -->
    <el-row class="mb-10" v-loading="loading">
      <el-col :span="autoCommit ? 24 : 17">
        <el-upload
          :limit="maxFs"
          :auto-upload="false"
          :on-change="fileChange"
          :on-remove="fileRm"
          :on-exceed="uploadExceed"
          ref="upload"
          class="upload-demo"
          accept=".xlsx, .xls"
        >
          <el-button slot="trigger" size="mini" type="success" plain>选取文件</el-button>
          <el-tooltip :open-delay="600" placement="top" effect="light">
            <div slot="content">
              <ul class="tips ml-20">
                <li v-for="(file, uid) in workbook.imp">
                  <span>文件《{{ workbook.dict.fid[uid] }}》</span>
                  <ul class="tips-decimal ml-20">
                    <li v-for="(sheet, name) in file">
                      <span>工作表“{{ name }}”</span>
                      <ul class="tips-disc ml-20 mb-10">
                        <li v-for="(rows, k) in sheet">
                          <b>{{ workbook.dict[k] }}{{ !rows ? null : '：' }}</b>
                          <ul v-if="!!rows" class="tips-circle ml-20 mtb-5">
                            <li v-for="(row, idx) in rows">第 {{ +idx + 2 }} 行，编码“{{ row.barcode }}”</li>
                          </ul>
                        </li>
                      </ul>
                    </li>
                  </ul>
                </li>
              </ul>
            </div>
            <b v-show="workbook.importCount">
              <el-tag
                size="mini"
                type="info"
                class="ml-10"
              >已导入{{ workbook.importCount }}条，获取到{{ list.length }}条</el-tag>
            </b>
          </el-tooltip>
          <el-tooltip placement="top" :open-delay="500">
            <div slot="content">
              <h4>小提示：</h4>
              <ul class="tips ml-20">
                <li>支持多文件、多工作表选取；</li>
                <li>表头须有“编码”（或“barcode”）列；</li>
                <li>后续选取的文件，将自动去除重复“编码”；</li>
                <li>当表头“排序”（或“order”）列有值时，以此值优先排序；</li>
                <li>商品列表可拖动排序；</li>
                <li>输入商品信息，筛选已选项；</li>
                <li>
                  点击提交按钮“
                  <i class="el-icon-check"></i>”，提交已选项；
                </li>
                <li>商品列表中，未勾选的商品不提交。</li>
              </ul>
            </div>
            <i class="ml-10 el-icon-info"></i>
          </el-tooltip>
          <i slot="tip" class="el-upload__tip">只能选取Excel</i>
        </el-upload>
      </el-col>
      <el-col :span="7" v-if="!autoCommit">
        <div class="dialog-footer">
          <el-tooltip content="清空" placement="top" :open-delay="500">
            <el-button @click="closeAddGoods" icon="el-icon-close" type="info" size="mini" plain></el-button>
          </el-tooltip>
          <el-tooltip content="确定" placement="top" :open-delay="500">
            <el-button @click="confirm" icon="el-icon-check" type="primary" size="mini"></el-button>
          </el-tooltip>
        </div>
        <div v-if="selectData.length" class="mt-10 el-dialog__title">
          <el-tooltip :content="`已选 ${selectData.length} 项：`" placement="left" :open-delay="800">
            <el-input
              v-model="searchSel.search"
              @keyup.enter.native="searchSels()"
              size="mini"
              placeholder="关键字筛选"
              clearable
            />
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <el-table
      ref="selTab"
      :data="list"
      @selection-change="onSelect"
      :row-class-name="tabRowCla"
      v-loading="loading"
      :max-height="tabHeight.sel"
      class="custom-table selected-tab"
      size="mini"
      highlight-current-row
      border
    >
      <el-table-column v-if="selectData.length < maxSel" type="selection" width="35%"></el-table-column>
      <el-table-column type="index" width="45%" align="right"></el-table-column>
      <el-table-column
        prop="id"
        v-if="!briefColumn"
        :show-overflow-tooltip="true"
        label="ID"
        width="60%"
        align="right"
      ></el-table-column>
      <el-table-column prop="branchCode" v-if="!briefColumn" label="区域" width="90%" align="right">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.branchCode" placement="top" :open-delay="600">
            <span>{{ $options.filters.getBranchName(scope.row.branchCode, branchs) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="code"
        v-if="!briefColumn"
        :show-overflow-tooltip="true"
        label="条码"
        width="120%"
        align="left"
      ></el-table-column>
      <el-table-column
        prop="barcode"
        v-if="!briefColumn"
        :show-overflow-tooltip="true"
        label="编码"
        width="100%"
        align="left"
      ></el-table-column>
      <el-table-column prop="showName" label="商品名称" min-width="210%"></el-table-column>
      <el-table-column label="图片" width="80%" align="center">
        <template slot-scope="scope">
          <el-popover placement="right-end" trigger="focus">
            <img :src="scope.row.imageUrl" class="avatar" alt="图片" />
            <img slot="reference" :src="scope.row.imageUrl" class="avatar goods-img-min" />
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="availableQty" label="库存数" align="right" width="70%"></el-table-column>
      <el-table-column
        prop="spec"
        v-if="!briefColumn"
        :show-overflow-tooltip="true"
        label="规格"
        width="120%"
        align="center"
      ></el-table-column>
      <el-table-column prop="fob" v-if="!briefColumn" label="原 / 售价(￥)" width="110" align="right">
        <template slot-scope="scope">
          <el-tag size="mini" type="success">{{ scope.row.fob }}</el-tag>
          <el-tag size="mini">{{ scope.row.retailPrice }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isPromotion" label="促销" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isPromotion(scope.row.isPromotion)}}</template>
      </el-table-column>
      <el-table-column prop="isFragileGoods" label="易碎品" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isFragileGoods(scope.row.isFragileGoods)}}</template>
      </el-table-column>
      <el-table-column prop="productType" label="秒杀" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isProductType(scope.row.productType)}}</template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="80%" align="center">
        <template slot-scope="scope">{{ $options.filters.statusTip(scope.row.status, status) }}</template>
      </el-table-column>
      <el-table-column prop="channelCodes" label="渠道" align="center" width="100%">
        <template
          slot-scope="scope"
        >{{ $options.filters.channelCodesFilter(scope.row.channelCodes) }}</template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        v-if="!briefColumn"
        :show-overflow-tooltip="true"
        label="创建时间"
        width="100%"
      >
        <template slot-scope="scope">{{ scope.row.createTime | dateFmt }}</template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import api from "api";
import XLSX from "xlsx";
import Sortable from "sortablejs";
import { fetch, getDate } from "../../../utils/time-format";

export default {
  props: {
    /**
     * @param autoCommit {@link Boolean}：单选时，是否自动提交，默认自动提交；
     * @param reqMethod {@link String}：request请求商品时，调用的方法名称；
     * @param minSel {@link Number}：最少选择数，默认“0”；
     * @param maxSel {@link Number}：最多选择数，默认“1000”；
     * @param maxFs {@link Number}：最多选取文件数，默认“100”；
     * @param briefColumn {@link Boolean}：是否简洁显示列表（部分列不展示），默认“false”；
     * @param pageSize {@link Number}：搜索页显示数量；
     * @param tabHeight {@link Object}：设置各列表高度；
     * @param search {@link Object}：初始搜索条件；
     * @param data {@link Object}：初始选中数据；
     */
    params: {
      type: Object,
      default() {
        return {};
      }
    },
    is_close: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // let seledShow = (this.params.seledShow == null) ? true : !!this.params.seledShow;
    let briefColumn =
      this.params.briefColumn == null ? false : !!this.params.briefColumn;
    let minSel =
        this.params.minSel == null
          ? 0
          : this.params.minSel < 0
          ? 0
          : this.params.minSel,
      maxSel =
        this.params.maxSel == null
          ? 1000
          : this.params.maxSel < minSel
          ? minSel
          : this.params.maxSel;
    let tabHeight = this.params.tabHeight || {};
    tabHeight.sel = tabHeight.sel || 300;
    tabHeight.seled = tabHeight.seled || 180;
    return {
      loading: false,
      search: {},
      searchSel: {},
      list: [],
      workbook: {
        //工作表导入信息
        importCount: 0, //已导入总条数
        imp: {}, //导入信息
        dict: {
          ok: "导入成功",
          not: "无数据",
          notGet: "未获取",
          repeat: "重复",
          repeatAll: "全部重复",
          fid: {} //文件uid字典
        }
      },
      status: null,
      branchs: null,
      tabHeight,
      minSel,
      maxSel,
      // seledShow,
      briefColumn,
      maxFs: this.params.maxFs || 100,
      autoCommit: !!this.params.autoCommit,
      reqMethod: this.params.reqMethod || "selectGoods",
      selectIds: [],
      selectData: []
    };
  },
  computed: {
    inSelScope() {
      let r = {
        eq: true
      };
      if (
        this.minSel &&
        (!this.selectData || this.selectData.length < this.minSel)
      ) {
        r.eq = false;
        r.tip = `请最少选择 ${this.minSel} 个商品`;
      } else if (
        this.maxSel &&
        (!this.selectData || this.selectData.length > this.maxSel)
      ) {
        r.eq = false;
        r.tip = `请最多选择 ${this.maxSel} 个商品`;
      }
      return r;
    }
  },
  methods: {
    fileChange(f, fs) {
      this.loading = true;
      this.workbook.dict.fid[f.uid] = f.name; //添加文件uid字典
      let imp = (this.workbook.imp[f.uid] = Object.create(null));
      let redABS = false; //是否将文件读取为二进制字符串
      let reader = new FileReader();
      reader.onload = e => {
        let prs = [];
        let sheets = reader.read2JS();
        for (let name in sheets) prs.push(reqGoods(sheets, name));
        Promise.all(prs.map(item => item.catch(e => e))).then(res =>
          resAll(res)
        );
      };
      redABS
        ? reader.readAsArrayBuffer(f.raw)
        : reader.readAsBinaryString(f.raw);

      let resAll = res =>
        res.forEach(rs => {
          if (!rs || !rs.length) return (this.loading = false);
          /*if (f.startIdx != null) {
            f.endIdx += rs.length;
          } else {
            f.startIdx = this.list.length;  //起始位置索引
            f.endIdx = f.startIdx + rs.length - 1;
          }*/
          this.list.push(...rs);
          this.selectData.push(...rs);
          rs.forEach(item => this.$refs.selTab.toggleRowSelection(item));
          this.loading = false;
        });
      let reqGoods = async (sheets, name) => {
        let sht = (imp[name] = Object.create(null));
        let rows = sheets[name];
        if (!rows || !rows.length) {
          sht.not = null;
          return this.$notify.warning(`工作表“${name}”未导入：无导入数据。`);
        }
        rows.forEach(item => {
          //修改属性名称
          item.order = item.order || item["排序"];
          item.barcode = String(item.barcode || item["编码"]);
        });
        let dtRows = distinct(rows, sht);
        if (!dtRows.length) {
          delete sht.repeat;
          sht.repeatAll = null;
          return this.$notify.warning(`工作表“${name}”未导入：所有数据重复。`);
        }
        this.workbook.importCount += dtRows.length;
        let barcodes = order(dtRows).map(item => item.barcode);
        (f.barcodes = f.barcodes || []).push(
          ...barcodes.map(item => String(item))
        );
        let rs = await this.req({ barcodes });
        {
          //添加导入记录
          let notGet = notImport(rows, rs, sht);
          if (Object.keys(notGet).length)
            //有未导入的商品，则添加"notGet"属性
            sht.notGet = notGet;
          if (!Object.keys(sht).length) sht.ok = null;
        }
        return orderContrast(dtRows, rs);
      };

      /**
       * 对照排序
       */
      function orderContrast(olds, goods) {
        if (!olds || !goods || !goods.length) return goods;
        let k = 0; //已排序的当前定位
        for (let i = 0, len = olds.length; i < len; i++) {
          let old = olds[i];
          for (let j = k; j < goods.length; j++)
            if (old.barcode == goods[j].barcode) {
              if (j != k) swap(goods, j, k);
              k++;
            }
        }
        return goods;
      }

      /**
       * 正序排序
       */
      function order(rows) {
        if (!rows) return rows;
        for (let i = 0, un = 0, len = rows.length; i < len; i++) {
          //un：无"排序"指针
          rows[i].order = rows[i].order || ++un; //没有"排序"值，则以默认顺序
          for (let j = i; j > 0 && rows[j].order < rows[j - 1].order; )
            swap(rows, j, --j);
        }
        return rows;
      }

      function swap(js, i, j) {
        let proxy = js[i];
        js[i] = js[j];
        js[j] = proxy;
      }

      /* 过滤重复商品编码（不从当前sheet中过滤） */
      let distinct = (rows, sht) =>
        rows.filter((item, i) => {
          let exist = f.barcodes && f.barcodes.indexOf(item.barcode) >= 0; //先从当前表格过滤
          for (let i = 0, len = this.list.length; !exist && i < len; i++) {
            //再从当前列表（所有表格）中过滤
            if ((exist = this.list[i].barcode == item.barcode)) break;
          }
          if (exist)
            //有重复信息，则添加"repeat"属性
            (sht.repeat = sht.repeat || {})[i] = item;
          return !exist; //返回不重复的编码
        });
      /* 获取未导入编码 */
      let notImport = (rows, rs, sht) => {
        let notGet = Object.create(null); //模仿数组对象
        let rcs = (rs || []).map(item => item.barcode);
        rows.forEach((item, i) =>
          (sht.repeat &&
            sht.repeat[i] &&
            item.barcode == sht.repeat[i].barcode) || //当前编码在“重复”编码中，则不再提示“未获取”
          rcs.indexOf(item.barcode) >= 0
            ? null
            : (notGet[i] = item)
        );
        return notGet;
      };
    },
    fileRm(f, fs) {
      delete this.workbook.imp[f.uid];
      delete this.workbook.dict.fid[f.uid];
      if (!f.barcodes || !f.barcodes.length) {
        this.workbook.importCount = new Number(this.workbook.importCount); //此处为引起VUE的数据监听
        return;
      }
      this.workbook.importCount -= f.barcodes.length;
      /* 查找删除（若导入时未去重，则会删除后续文件的商品！） */
      delItem(this.list);
      delItem(this.selectData);

      function delItem(arr) {
        //区间算法，分段批量删除
        for (var i = 0, idx = i; i < arr.length; i++) {
          let item = arr[i];
          if (f.barcodes.indexOf(item.barcode) == -1) {
            if (i > idx) {
              let m = i - idx;
              arr.splice((i = idx), m);
            }
            idx++;
          }
        }
        if (idx < arr.length)
          //删除末尾元素
          arr.splice(idx, arr.length - idx);
      }

      /* 当某行数据被Sortable拖动后，起始、结束索引会失效
        if (f.startIdx == null || f.endIdx == null || f.startIdx < 0 || f.endIdx < 0)
          return;
        resetIdx(fs);
        let delGos = this.list.splice(f.startIdx, f.endIdx - f.startIdx + 1);
        delGos.forEach(item => {    //删除已选中数据
          for (let i = 0, len = this.selectData.length; i < len; i++)
            if (item.id == this.selectData[i].id) {
              this.selectData.splice(i, 1);
              break;
            }
        });*/
      /**
         * 重置索引值
         * @param fs {@link Array}：files；
         function resetIdx(fs) {
					if (!fs || !fs.length)
						return;
					let idx = 0;
					fs.forEach(f => {
						if (f.startIdx == null || f.endIdx == null || f.startIdx < 0 || f.endIdx < 0)
							return;
						let diff = f.endIdx - f.startIdx;   //索引差值
						f.startIdx = idx;
						f.endIdx = f.startIdx + diff;
						idx = f.endIdx + 1;
					});
				} */
    },
    uploadExceed(f, fs) {
      this.$notify.warning(`最多允许上传 ${fs.length} 个文件`);
    },
    async req(pms) {
      pms = Object.assign(pms || {}, this.search);
      let res = await api.goods.select4barcodesAndBranchCodes(pms);
      if (res.code == 200) {
        if (this.search.needFilter && this.search.status) {
          let skuIdList = [];
          //第一次过滤掉商品状态不符合的商品id
          res.data.forEach(data => {
            if (data.status == this.search.status) skuIdList.push(data.id);
          });
          //由于productBusinessApi.selectSkuByBarcodeAndBranchCode
          //处理已售罄的商品时返回的状态有误，手动再次查询商品信息过滤
          let pms2 = {
            pageSize: skuIdList.length,
            pageNum: 1,
            skuIdList: skuIdList,
            branchCode: this.search.branchCode
          };
          let res2 = await api.goods.selectSKUsByOrderId(pms2);
          let dateTemp = [];
          res2.data.forEach((data, index) => {
            if (data.status == this.search.status) {
              if (res.data[index].id === data.id) {
                data.channelCodes = res.data[index].channelCodes;
              } else {
                res.data.forEach(res_data => {
                  if (res_data.id === data.id) {
                    data.channelCodes = res_data.channelCodes;
                  }
                });
              }
              dateTemp.push(data);
            }
          });
          return dateTemp;
        }
        return res.data;
      }
      this.$notify.error({
        message: res.msg,
        dangerouslyUseHTMLString: true, //允许html
        offset: 100, //偏移
        duration: 60000
      });
      return null;
    },
    /**
     * 已选商品搜索
     */
    searchSels() {
      let srch = this.searchSel.search;
      if (!srch || !(srch = srch.trim()))
        //搜索条件
        return;
      let attrs = ["code", "barcode", "showName", "spec"];
      let srs = this.selectData.filter((item, i) => {
        for (let j = 0, len = attrs.length; j < len; j++)
          if (item[attrs[j]] && `${item[attrs[j]]}`.indexOf(srch) != -1)
            return true;
        this.$nextTick(() => this.$refs.selTab.toggleRowSelection(item)); //会移除"this.selectData"相应元素
        return false;
      });
      if (!srs.length) return this.$message.info("无相关匹配项。");
      this.selectData = srs;
      this.$message.success(`已为您匹配成功 ${srs.length} 项。`);
    },
    /**
     * 多选事件
     */
    onSelect(objs) {
      let gos = Object.assign([], this.list);
      for (let i = 0; i < objs.length; i++) {
        let obj = objs[i];
        let id = obj.id;
        for (let j = 0; j < gos.length; j++) {
          let go = gos[j];
          if (!go) continue;
          if (id == go.id) {
            gos[j] = undefined; //清空相同元素
            break;
          }
        }
        if (this.selIdxOf(id) == -1)
          //已选中列表中不存在此id，则添加
          this.selectData.push(obj);
      }
      /* 将剩余不同元素（未选中），从this.selectData剔除 */
      for (let i = 0; i < gos.length; i++) {
        let go = gos[i];
        if (!go) continue;
        let idx = this.selIdxOf(go.id);
        if (idx >= 0) this.selectData.splice(idx, 1);
      }
    },
    /**
     * 查询所在已选ID列表的索引位置。
     * 不在列表中，则返回"-1"
     * @param id {@link Number}：查询元素；
     * @param sels {@link Array}：被查找数组；
     * @returns {number}
     */
    selIdxOf(id, sels) {
      sels = sels || this.selectData;
      if (!id || !sels || !sels.length) return -1;
      for (let i = 0; i < sels.length; i++) {
        let sel = sels[i];
        if (id == sel.id) return i;
      }
      return -1;
    },
    async dict() {
      let status = await api.goods.status();
      if (status.code == 200) this.$nextTick(() => (this.status = status.data));
      else this.$message.error(status.msg);

      if (!this.briefColumn) {
        //显示全部列时，获取区域信息
        let bho = await api.dict.branchHasOpen();
        if (bho.code == 200) this.$nextTick(() => (this.branchs = bho.data));
        else this.$message.error(bho.msg);
      }
    },
    /**
     * 初始化加载
     */
    init() {
      let search = (this.params.search = this.params.search || {}); //初始搜索条件
      bc: if (!search.branchCodes || !search.branchCodes.length) {
        if (search.branchCode) search.branchCodes = [search.branchCode];
        else if (this.params.branchCode)
          search.branchCodes = [this.params.branchCode];
        else if (this.params.branchCodes && this.params.branchCodes.length)
          search.branchCodes = this.params.branchCodes;
        if (search.branchCodes && search.branchCodes.length) break bc;

        this.loading = true;
        this.inSelScope.eq = false;
        this.inSelScope.tip = "<b>未能获取“区域编码”</b>，请联系开发者";
        this.$notify.error({
          message: this.inSelScope.tip,
          dangerouslyUseHTMLString: true, //允许html
          offset: 300, //偏移
          duration: 0
        });
        return false;
      }
      this.search = Object.assign(this.search, search);
      return true;
    },
    sortableInit() {
      let tbodys = document.querySelectorAll(".selected-tab table > tbody");
      if (!tbodys || !tbodys.length) return;
      tbodys.forEach(tbody =>
        Sortable.create(tbody, {
          scrollSpeed: 10, // 滚动速度。
          scrollSensitivity: 80, // 鼠标必须靠近边缘多少px才能开始滚动。
          fallbackTolerance: 50, // 以像素为单位指定鼠标在被视为拖动之前应移动多远。
          touchStartThreshold: 50, // 在多少像素移动范围内可以取消延迟拖动事件。
          /**
           * 拖拽被选中时的回调
           */
          onChoose: e => {
            if (this.tabHeight.sel >= 350) return;
            if (this.timId) clearTimeout(this.timId);
            this.tabHeight.selCache = this.tabHeight.sel; //缓存原数值
            this.tabHeight.sel = 350; //放大拖拽区域
          },
          /**
           * 拖拽完成后回调
           * @description: 拖拽直接操作DOM完成后，并不会引起Vue的虚拟DOM变化。
           * 所以后续双向更新数据时，Vue根据Diff算法，会重新再次渲染真实DOM，引起冲突。
           * 参见：https://www.jianshu.com/p/d92b9efe3e6a
           */
          onEnd: e => {
            if (e.newIndex != e.oldIndex) {
              /* 还原直接操作的真实DOM，将DOM操作交还给Vue */
              let tr = tbody.children[e.newIndex];
              let oldTr = tbody.children[e.oldIndex];
              tbody.insertBefore(
                tr,
                e.newIndex > e.oldIndex ? oldTr : oldTr.nextSibling
              );

              let vals = this.list.splice(e.oldIndex, 1);
              this.list.splice(e.newIndex, vals.length - 1, ...vals);
            }
            this.$nextTick(() => {
              if (this.tabHeight.selCache && this.tabHeight.selCache < 350)
                this.timId = setTimeout(() => {
                  //拖拽完成后还原区域视图
                  this.tabHeight.sel = this.tabHeight.selCache;
                  delete this.tabHeight.selCache;
                  this.timId = clearTimeout(this.timId);
                }, 3000);
            });
          }
        })
      );
    },
    tabRowCla({ row, i }) {
      if (row.status != 1 && row.status != 3 && row.status != 5)
        return "bgc-warn";
      return "";
    },
    /**
     * 设置选中的ID组
     * @returns {Array}
     */
    changeSelId() {
      this.selectIds.length = 0;
      if (!this.selectData) return this.selectIds;
      for (let i = 0; i < this.selectData.length; i++) {
        let sel = this.selectData[i];
        this.selectIds.push(sel.id);
      }
      return this.selectIds;
    },
    /**
     * 确定
     */
    confirm() {
      this.loading = true;
      if (!this.inSelScope.eq) {
        this.$notify.warning({
          message: this.inSelScope.tip,
          dangerouslyUseHTMLString: true, //允许html
          offset: 200 //偏移
        });
      } else {
        let r = {
          tag: "importGoods", //此返回值的自定义标识（以便区分其它组件的返回值）
          ids: _.cloneDeep(this.changeSelId()),
          data: _.cloneDeep(this.selectData)
        };
        this.$emit("select", r);
      }
      this.loading = !this.loading;
    },
    /**
     * 取消
     */
    closeAddGoods() {
      this.list = [];
      this.selectIds = [];
      this.selectData = [];
      this.searchSel = {}; //清空搜索
      this.workbook.imp = {};
      this.workbook.dict.fid = {};
      this.workbook.importCount = 0;
      this.$refs.upload.clearFiles();
      this.$refs.selTab.clearSelection();
    }
  },
  beforeMount() {
    this.dict();
  },
  mounted() {
    this.sortableInit();
  },
  created() {
    if (!this.init()) return;
    /**
     * 读取Excel
     * @return {@link Object}：Excel文档对象。
     */
    FileReader.prototype.read2JS = function() {
      let redABS = false; //是否将文件读取为二进制字符串
      let bytes = new Uint8Array(this.result); //无符号整型数组
      if (redABS) {
        // let fix = new TextDecoder(encode || 'UTF-8').decode(bytes);
        let fix = fixdata(bytes);
        let b2a = btoa(fix);
        var wb = XLSX.read(b2a, {
          type: "base64"
        });
      } else {
        let len = bytes.byteLength;
        let binarys = new Array(len); //创建定长数组，存储文本
        for (let i = 0; i < len; i++)
          binarys[i] = String.fromCharCode(bytes[i]);
        let binary = binarys.join("");
        var wb = XLSX.read(binary, {
          type: "binary"
        });
      }
      let r = {};
      wb.SheetNames.forEach(name => {
        //遍历每张纸数据
        r[name] = XLSX.utils.sheet_to_json(wb.Sheets[name]); //excel导入的数据
      });
      return r;
    };
    /* 重写，为读取Excel方式 */
    FileReader.prototype.readAsBinaryString = function(f) {
      if (!this.onload)
        this.onload = e => {
          this.jsData = this.read2JS();
        };
      this.readAsArrayBuffer(f); //内部会回调this.onload方法
    };

    /**
     * char值转String
     * @param data {@link Array}：char值；
     * @return {@link String}
     */
    function fixdata(data) {
      let w = 1024 << 0; //每次读取1M字节
      let len = Math.floor(data.byteLength / w);
      let o = new Array(len);
      for (var i = 0; i < len; i++)
        o[i] = String.fromCharCode.apply(
          null,
          new Uint8Array(data.slice(i * w, (i + 1) * w))
        );
      o[i] = String.fromCharCode.apply(null, new Uint8Array(data.slice(i * w)));
      return o.join("");
    }
  },
  filters: {
    //渠道
    channelCodesFilter(val) {
      if (Array.isArray(val)) {
        if (val.length == 0) {
          return "无";
        }
        if (val.indexOf("1") != -1 && val.indexOf("2") != -1) {
          return "b2b,壹块钱";
        } else if (val.indexOf("1") != -1) {
          return "b2b";
        } else if (val.indexOf("2") != -1) {
          return "壹块钱";
        }
        return "未知";
      }
      return "无";
    },
    statusTip(status, dict) {
      return !dict ? "未知" : dict[status];
    },
    isPromotion(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isFragileGoods(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isProductType(val) {
      if (val == 2) {
        return "是";
      } else {
        return "否";
      }
    },
    dateFmt(date) {
      return date ? getDate(date) : "";
    },
    getBranchName(code, branchs) {
      let branchName = "";
      if (!code || !branchs || !branchs.length) return branchName;
      for (let i = 0, len = branchs.length; i < len; i++) {
        let branch = branchs[i];
        if (branch.branchCode == code) {
          branchName = branch.branchName;
          break;
        }
      }
      return branchName;
    }
  },
  watch: {
    is_close() {
      this.closeAddGoods();
    }
  }
};
</script>
<style lang="scss" scoped rel="stylesheet/scss">
.product-link {
  border: 1px solid #0cdcdc;
  padding: 3px;
}

.tips > li {
  list-style: square;
}

.tips-disc > li {
  list-style: disc;
}

.tips-circle > li {
  list-style: circle;
}

.tips-decimal > li {
  list-style: decimal;
}

.goods-img-min {
  max-width: 100%;
  max-height: 30px;
}

.el-table .bgc-warn {
  background: oldlace;
}

.el-table .bgc-success {
  background: #f0f9eb;
}
</style>
