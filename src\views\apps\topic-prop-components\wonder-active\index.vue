<template>
    <div class="topic-image">
        <div style="margin: 10px 0" v-if="this.categoryList[0].title != '综合页（主会场，广告位，商品流）'">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-radio-group v-model="content.activeKey">
                        <el-radio :label="index" v-for="(item,index) in menu" :key="index" :disabled="index>1">
                            {{item}}
                        </el-radio>
                    </el-radio-group>
                </el-col>
            </el-row>
        </div>
        <!--模块有效时间设置-->
        <el-row :gutter="20">
            <div class="title">模块有效时间设置</div>
            <el-col :span="24">
                <el-date-picker
                        v-model="content.timevalue"
                        type="datetimerange"
                        :picker-options="pickerOptions0"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        align="right">
                </el-date-picker>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <div class="title">模块背景颜色</div>
            <el-col :span="8">
                <div class="block">
                    <div>
                        <el-upload
                                class="topic-image-upload"
                                ref="upload"
                                accept="image/jpeg,image/jpg,image/png,image/gif"
                                :show-file-list="false"
                                :on-success="onUploadImg">
                            <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
                            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                        </el-upload>
                    </div>
                </div>
            </el-col>
            <el-col :span="8">
                <div class="block">
                    <span class="demonstration">主标题文字颜色</span>
                    <div>
                        <el-color-picker v-model="content.main_title_color" size="mini"></el-color-picker>
                    </div>
                </div>
            </el-col>
            <el-col :span="8">
                <div class="block">
                    <span class="demonstration">副标题文字颜色</span>
                    <div>
                        <el-color-picker v-model="content.sub_title_color" size="mini"></el-color-picker>
                    </div>
                </div>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <div class="title">标题设置</div>
            <el-col :span="24" class="headline">
                <el-input placeholder="请输入内容" v-model="content.main_title">
                    <template slot="prepend">主标题:</template>
                </el-input>
            </el-col>
            <el-col :span="24">
                <el-input placeholder="请输入内容" v-model="content.sub_title">
                    <template slot="prepend">副标题:</template>
                </el-input>
            </el-col>
        </el-row>
        <!--活动链接设置-->
        <el-row :gutter="20">
            <div class="title">活动链接设置</div>
            <div class="topic-image-picker">
                <span>链接类型：</span>
                <el-select v-model="content.linkType" placeholder="请选择" @change="changeLinkType">
                <el-option
                    v-for="item in linkOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                ></el-option>
                </el-select>
            </div>

            <div class="topic-image-picker" v-if="content.linkType !== 'dynamic'">
            <el-input placeholder="请设置活动链接" v-model.trim="content.url">
                <template slot="prepend">跳转链接</template>
            </el-input>
            </div>
            <div v-if="content.linkType === 'dynamic'">
                <div class="topic-image-picker">
                    <el-input style="width: 300px; margin: 10px 0" placeholder="输入跳转id" v-model="content.dynamicId">
                    <template slot="prepend">跳转id</template>
                    </el-input>
                    <el-button type="primary" @click="putDynamicLink()">生成链接</el-button>
                </div>
                <el-input placeholder="请设置活动链接" v-model.trim="content.url">
                    <template slot="prepend">跳转链接</template>
                </el-input>
            </div>
            <el-col :span="24">
                <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
            </el-col>
        </el-row>
    </div>
</template>
<script>
    import base from "../base";
    import api from "api";
    import { getUrlParam } from "config";
    export default {
        name: 'wonder-active',
        extends: base,
        contentDefault: {
            timevalue: '',
            bgRes: 'http://upload.ybm100.com/ybm/applayoutbanner/cb244fd0-80cd-4685-bb21-cac896c077d5.png',
            main_title_color: '#ffffff',
            sub_title_color: '#ffffff',
            main_title: '精彩活动',
            sub_title: '全场限时满减等你来抢',
            activeKey: 1,
            url: "",
            linkType: 'topic',
            dynamicId: '',
        },
        data() {
            return {
                pickerOptions0: {
                    shortcuts: [
                        {
                            text: "未来一周",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来一个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来三个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来六个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来一年",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                                picker.$emit("pick", [start, end]);
                            }
                        }
                    ],
                },
                loading: false,
                linkOptions: [{
                    value: "topic",
                    label: "专题页链接"
                    }, {
                    value: "stores",
                    label: "店铺页链接"
                    }, {
                    value: "dynamic",
                    label: "动态商品链接"
                    },
                    {
                    value: "redEnvelope",
                    label: "绑定微信链接"
                    }],
            }
        },
        computed: {},
        created() {
            this.debounce = _.debounce(this.changeLink, 1000);
        },
        methods: {
            changeLinkType() {
                this.debounce();
            },
            putDynamicLink() {
                if (!this.content.dynamicId) {
                this.$message({
                    message: "请输入跳转id再点击生成链接",
                    type: "warning"
                });
                return false;
                }
                this.content.url = `ybmpage://homeSteadyChannel?strategyId=${this.content.dynamicId}&title=${this.content.main_title}`;
            },
            onSetLink(link) {
                this.content.url = link.meta.page_url
            },
            async onUploadImg(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.bgRes = res.data.url;
            },
            async changeLink() {
                if (this.content.linkType === 'topic' && this.content.url) {
                    if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.content.url)) {
                        this.$message.error('链接格式不正确');
                        this.content.url = '';
                    } else {
                        let linkPageUrl = getUrlParam(this.content.url, 'url');
                        const result = await api.topic.checkPageUrl({ url: linkPageUrl });
                        if (((result || {}).data || {}).status != 200) {
                        this.$message.error('链接不存在');
                        this.content.url = '';
                        }
                    }
                }
            }
        },
        //监听input输入值变化
        watch:{
        'content.url': {
            handler(val, oldVal) {
            if (val) {
                this.debounce();
            }
            }
        }
        }
    }
</script>

<style lang="scss">
    .el-row {
        text-align: center;

        .title {
            text-align: left;
            line-height: 30px;
            background-color: #f2f2f2;
            margin: 10px 0;
            padding-left: 10px;
        }

    }
</style>
<style lang="scss" scoped>
    .headline {
        margin-bottom: 10px;
    }
    .topic-image-picker {
        margin-bottom: 10px;
    }
</style>