<script>
	import {common} from 'api'

	export default {
		data() {
			return {
				form: {name: ""},
				selectItem: [],
				loading: false,
				tabs: [
					{label: '活动页', value: 'page'},
					{label: '商品组', value: 'goodsGroup'}
				],
				tabName: '',
				activeName: '',
				currentContent: [],
				currentTabIndex: 0,
				goodsIds: [],
				isBrand: false
			}
		},
		watch: {
			'content.list': {
				deep: true,
				handler(val) {
					if (val.content) {
						this.currentContent = val[this.currentTabIndex].content
					}
				}
			}
		},
		methods: {
			addTab() {
				if (!this.tabName) {
					this.$message('请输入选项卡名称')
					return
				}
				this.content.list.push({name: this.tabName, isBrand: this.isBrand, content: []})
			},
			removeTab(targetName) {
				const index = common.getRepeatResult('name', targetName, this.content.list);
				this.content.list.splice(index, 1)
			},
			tabClick() {
				this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.list);
				this.currentContent = this.list[this.currentTabIndex].content
			},
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},
			onSetLink(link) {
				if (this.selectItem.length === 0 || this.selectItem.length !== 1) {
					this.$message.warning('请先选中1个标签')
					return
				}
				if (!this.selectItem[0].title) {
					//选中的是品牌
					const index = this.content.brand.indexOf(this.selectItem[0])
					let {page_name, page_url, id} = link.meta
					this.content.brand[index].page_name = page_name
					this.content.brand[index].page_url = page_url
					this.content.brand[index].id = id
					return
				}
				const labelArray = this.currentContent.map(item => {
					return item.title
				})
				const index = labelArray.indexOf(this.selectItem[0]['title'])
				if (link.label === "活动页") {
					let {page_name, page_url, id} = link.meta;
					this.currentContent[index].page_name = page_name
					this.currentContent[index].page_url = page_url
					this.currentContent[index].id = id
					return
				}
				let {data} = link
				this.goodsIds = this.currentContent[index].goodsIds = data.goods
				this.currentContent[index].goodsName = data.name
			},
			handleAdd(formName) {
				// 添加子菜单
				if (this.activeName === '0') {
					this.$message.error('请选中选项卡，再添加')
					return
				}
				this.currentContent.push({
					title: this.form.name,
					page_name: '',
					page_url: '',
					id: '',
					goodsName: '',
					goodsIds: []
				})
			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = common.getRepeatResult('title', item.title, this.currentContent)
					this.currentContent.splice(index, 1)
				})
			},
			getImage(data) {
				if (data.image) {
					this.currentContent[data.index].image = data.image
				}
			}

		}

	}
</script>

<style>
	.blank_20 {
		height: 20px
	}
</style>