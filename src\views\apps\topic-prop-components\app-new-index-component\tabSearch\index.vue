<template>
  <div class="topic-search">
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="12">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
          <img v-if="content.bgImg" :src="content.bgImg" alt="">
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <div>
            <el-button @click="imgOnclick">清除背景图</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">背景色</span>
          <el-color-picker v-model="content.bgRes" size="mini" @change="onSelect"></el-color-picker>
        </div>
      </el-col>
    </el-row>
    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块楼层信息配置</div>
      <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">搜索框</span>
          <div>
            <!-- <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'search_box_url')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="content.search_box_url" :src="content.search_box_url" alt="">
            </el-upload> -->
          </div>
        </div>
        <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.search_box_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.search_box_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div>
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('search_box')">清空重置</el-button>
        </div>
      </el-col>
      <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">搜索图</span>
          <div>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'search_image_url')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="content.search_image_url" :src="content.search_image_url" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
          </div>
        </div>
        <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.search_image_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.search_image_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div>
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('search_image')">清空重置</el-button>
        </div>
      </el-col>
      <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">消息按钮</span>
          <div>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'message_button_url')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="content.message_button_url" :src="content.message_button_url" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
          </div>
        </div>
        <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.message_button_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.message_button_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div>
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('message_button')">清空重置</el-button>
        </div>
      </el-col>
      <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">消息角标</span>
          <div>
            <!-- <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'message_sub_url')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="content.message_sub_url" :src="content.message_sub_url" alt="">
            </el-upload> -->
          </div>
        </div>
        <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.message_sub_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.message_sub_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div>
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('message_sub')">清空重置</el-button>
        </div>
      </el-col>
      <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">热词文字</span>
        </div>
        <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.scan_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.scan_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div>
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('scan_')">清空重置</el-button>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">绑定首页模板id</div>
      <el-col :span="12">
        <div class="block">
          <div class="templateIdDiv">
            <span class="demonstration">首页id</span>
            <el-input v-model="homeTemplateId"></el-input>
            <el-button class="homeIdBtn" type="primary" @click="checkHomeId">确认</el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import base from "../../base";
import api from 'api';
export default {
  name: 'searchBox',
  extends: base,
  contentDefault: {
    search_image_transparency: 100,
    search_box_transparency: 100,
    message_button_transparency: 100,
    message_sub_transparency: 100,
    scan_transparency: 100,
    tab_bg_color1: "",
    tab_bg_color2: "",
    pro_obj: {
      pro_type: "longBar",
      pro_auto: 0,
      pro_align_type: "center",
      default_color: "#ffffff",
      default_opacity: 30,
      active_color: "#555555",
      active_opacity: 100,
      component_name: "searchBox", //区分模块的标识
    },
    list: [],
    search_text: "您常搜",
    active_icon_color: "#ffffff",
    default_icon_color: "#ffffff",
    // top_bgRes: "#00B377",
    // hotWord_bgRes: "#00B377",
    // meddle_bgRes: "#00B377",
    // bottom_bgRes: "#00B377",
    // refresh_bgRes: "#00B377"
    top_bgRes: "#fff",
    hotWord_bgRes: "#fff",
    meddle_bgRes: "#fff",
    bottom_bgRes: "#fff",
    refresh_bgRes: "#fff"
  },
  data() {
    return {
      loading: false,
      homeTemplateId: ''
    }
  },
  mounted() {
    this.homeTemplateId = (this.content||{}).homeTemplateId || '';
  },
  methods: {
    async checkHomeId() {
      let result;
      const params = {
          page_type: 'new_home',
          category: 'app',
        };
      result = await api.topic.list(params);
      if (result.code == 200) {
        let flag = false;
        if ((result.data.rows || []).findIndex(item => item.page_id == this.homeTemplateId) > -1) {
          this.content.homeTemplateId = this.homeTemplateId;
          result.data.rows.forEach(i => {
            if (i.page_id == this.homeTemplateId) {
              i.layout.forEach(k => {
                if (k.name == "newIndexSearch") {
                  this.content = {...this.content, ...k.content};
                }
              })
            }
          })
        } else {
          this.$message.warning("当前页面id不存在！");
        }
      }
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.$set(this.content, 'bgImg', res.data.url);
    },
    imgOnclick() {
      this.content.bgImg = null;
    },
    // 上传banner对应的头部区域背景图片
    async UploadTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.content, type, res.data.url);
    },
    onSelect(val, isEditEntry) {
      if (val) {
        this.content.bgRes = val;
      } else {
        this.content.bgRes = "#eee";
      }
    },
    clear_bgs(type) {
      this.content[type + '_url'] = '';
      this.content[type + '_color'] = '';
      this.content[type + '_transparency'] = 100;
      
      // this.content.top_bgRes = "#fff";
      // this.content.meddle_bgRes = "#fff";
      // this.content.bottom_bgRes = "#fff";
      // this.content.hotWord_bgRes = "#fff"
    },
  }
}
</script>
<style lang="scss" scoped>

.topic-search {
  .add-color-back {
    display: flex;
    align-items: center;
    .el-button {
      height: 30px;
    }
    .upload-demo {
      margin-right: 15px;
      // width: 180px;
      .el-upload {
        display: flex;
        align-items: center;
        
        img {
          width: 60px;
          margin-left: 15px;
        }
      }
    }
  }
  .el-col {
    display: flex;
    >div {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .topic-item {
      display: flex;
      margin-right: 10px;
      margin-bottom: 10px;
      flex: 1;
      .demonstration {
        min-width: 80px;
        margin-right: 15px;
        white-space: nowrap;
      }
    }
    .topic-item-title {
      flex: auto;
      width: 275px;
      display: flex;
      justify-content: flex-start;
    }
  }
}
.tableBox {
  width: 100%;
}
.carouselFlexBox {
  padding-right: 10px;
  .carouselFlex {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
    padding-right: 20px;
    span {
      min-width: 100px;
      text-align: right;
    }
  }
  .el-date-editor {
    width: 400px !important;
    height: 30px !important;
    .el-range__icon, .el-range-separator {
      line-height: 21px;
    }
  }
  .carouselButton {
    text-align: right;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }
}

.container-table {
  margin: 10px auto;
  padding-bottom: 10px;
  display: flex;
  justify-content: space-around;

  .img {
    width: 50%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .button-list {
    width: 45%;
  }
}

.topic-image-upload {
  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 200px;
    height: 200px;
    line-height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    font-size: 50px;
  }
}

.topic-image-upload .el-upload {
  width: 100%;
}

.el-row {
  text-align: center;

  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
  .tabBox {
    display: flex;
    margin: 20px;
    border-bottom: 1px solid #F1F1F4;
    cursor: pointer;
    div {
      border: 1px solid #F1F1F4;
      border-bottom: none;
      padding: 5px 10px;
    }
    .activeTab {
      color: #13c2c2;
    }
  }
}
.block {
  width: 100%;
  img {
    width: 60px;
  }
}
.templateIdDiv {
  display: flex;
  align-items: center;
  .demonstration {
    white-space: nowrap;
    margin-right: 10px;
  }
  .homeIdBtn {
    margin-left: 10px;
  }
}
</style>
<style  lang="scss">
.topic-search {
  .upload-demo {
    width: 180px;
    .el-upload {
      display: flex;
      align-items: center;
      .el-button {
        height: 30px;
      }
      img {
        width: 60px;
        margin-left: 15px;
      }
    }
  }
  .banner-dialog {
    .el-input {
      width: 400px;
    }
  }
  .el-date-editor {
    .el-input__icon, .el-range-separator {
      line-height: 21px;
    }
  }
  .crowdInput {
    .el-input {
      width: 200px !important;
      margin-right: 20px;
    }
  }
  .tableBox {
    .el-button {
      margin-left: 0;
      margin-right: 10px;
    }
    .preview-img {
      width: 60px;
    }
  }
}
</style>