<template>
  <div class="product-link">
    <el-row v-if="!inSelScope.eq" class="mb-10">
      <el-alert :title="inSelScope.tip" :closable="false" type="warning" center show-icon></el-alert>
    </el-row>
    <el-collapse v-if="seledShow" v-show="selectData.length" v-model="collShow">
      <el-collapse-item name="goods">
        <template slot="title">
          <i class="header-icon el-icon-goods ml-10"></i>
          <el-tooltip placement="top" :open-delay="500">
            <div slot="content">
              <h4>小提示：</h4>
              <ul class="tips ml-20">
                <li>按住图片放大；</li>
                <li v-if="!radio">已选列表可拖动排序；</li>
                <li>已选列表中输入商品信息，进行筛选；</li>
                <li v-if="!autoCommit">
                  点击提交按钮“
                  <i class="el-icon-check"></i>”，提交已选项。
                </li>
              </ul>
            </div>
            <b class="el-message-box__content">
              <i class="el-icon-info"></i>
              <span>已选商品</span>
            </b>
          </el-tooltip>
          <el-badge :value="selectData.length" type="success"></el-badge>
        </template>

        <el-table
          v-show="deal_selectData.length"
          :data="deal_selectData"
          :max-height="300"
          class="custom-table"
          size="mini"
          highlight-current-row
          border
        >
          <el-table-column align="center" width="130%">
            <template slot="header" slot-scope="scope">
              <el-input
                v-model="searchSel.showName"
                @keyup.enter.native="searchSels()"
                size="mini"
                placeholder="关键字筛选"
                clearable
              />
            </template>
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                @click="del(scope.row,'deal_selectData')"
              ></el-button>
            </template>
          </el-table-column>
          <el-table-column type="index" width="120">
            <template slot-scope="scope">
              <el-input
                v-model="sort_arr[scope.$index]"
                size="mini"
                @change="input_sort(scope.$index)"
              ></el-input>
              <!--<el-input-number v-model="sort_arr[scope.$index]"-->
              <!--@change="input_sort(scope.$index)"-->
              <!--:min="1"-->
              <!--:max="selectData.length" label="描述文字"></el-input-number>-->
            </template>
          </el-table-column>

          <el-table-column
            prop="barcode"
            v-if="!briefColumn"
            :show-overflow-tooltip="true"
            label="编码"
            width="200"
            align="left"
          ></el-table-column>
          <el-table-column
            prop="showName"
            :show-overflow-tooltip="true"
            label="商品名称"
            min-width="210%"
          ></el-table-column>

          <el-table-column
            prop="spec"
            v-if="!briefColumn"
            :show-overflow-tooltip="true"
            label="规格"
            width="120%"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="fob"
            v-if="!briefColumn"
            label="原 / 售价(￥)"
            width="110"
            align="right"
          >
            <template slot-scope="scope">
              <el-tag size="mini" type="success">{{ scope.row.fob }}</el-tag>
              <el-tag size="mini">{{ scope.row.retailPrice }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="id"
            v-if="!briefColumn"
            :show-overflow-tooltip="true"
            label="ID"
            width="60%"
            align="right"
          ></el-table-column>
          <el-table-column
            prop="branchCode"
            v-if="!briefColumn"
            label="区域"
            width="90%"
            align="right"
          >
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.branchCode" placement="top" :open-delay="600">
                <span>{{ $options.filters.getBranchName(scope.row.branchCode, branchs) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="code"
            v-if="!briefColumn"
            :show-overflow-tooltip="true"
            label="条码"
            width="120%"
            align="left"
          ></el-table-column>
          <el-table-column label="图片" width="80%" align="center">
            <template slot-scope="scope">
              <el-popover placement="right-end" trigger="focus">
                <img :src="scope.row.imageUrl" class="avatar" alt="图片" />
                <img slot="reference" :src="scope.row.imageUrl" class="avatar goods-img-min" />
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="availableQty" label="库存数" align="right" width="70%"></el-table-column>

          <el-table-column prop="isPromotion" label="促销" align="center" width="80%">
            <template slot-scope="scope">{{ $options.filters.isPromotion(scope.row.isPromotion)}}</template>
          </el-table-column>
          <el-table-column prop="isFragileGoods" label="易碎品" align="center" width="80%">
            <template
              slot-scope="scope"
            >{{ $options.filters.isFragileGoods(scope.row.isFragileGoods)}}</template>
          </el-table-column>
          <el-table-column prop="productType" label="秒杀" align="center" width="80%">
            <template slot-scope="scope">{{ $options.filters.isProductType(scope.row.productType)}}</template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center" width="80%">
            <template slot-scope="scope">{{ $options.filters.statusTip(scope.row.status, status) }}</template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            v-if="!briefColumn"
            :show-overflow-tooltip="true"
            label="创建时间"
            width="100%"
          >
            <template slot-scope="scope">{{ scope.row.createTime | dateFmt }}</template>
          </el-table-column>
        </el-table>
        <el-table
          v-show="!deal_selectData.length"
          :data="selectData"
          :max-height="300"
          class="custom-table"
          size="mini"
          highlight-current-row
          border
        >
          <el-table-column align="center" width="130%">
            <template slot="header" slot-scope="scope">
              <el-input
                v-model="searchSel.showName"
                @keyup.enter.native="searchSels()"
                size="mini"
                placeholder="关键字筛选"
                clearable
              />
            </template>
            <template slot-scope="scope">
              <el-button size="mini" type="danger" icon="el-icon-delete" @click="del(scope.row)"></el-button>
            </template>
          </el-table-column>
          <el-table-column type="index" width="120">
            <template slot-scope="scope">
              <el-input
                v-model="sort_arr[scope.$index]"
                size="mini"
                @change="input_sort(scope.$index)"
              ></el-input>
              <!--<el-input-number v-model="sort_arr[scope.$index]"-->
              <!--@change="input_sort(scope.$index)"-->
              <!--:min="1"-->
              <!--:max="selectData.length" label="描述文字"></el-input-number>-->
            </template>
          </el-table-column>

          <el-table-column
            prop="barcode"
            v-if="!briefColumn"
            :show-overflow-tooltip="true"
            label="编码"
            width="200"
            align="left"
          ></el-table-column>
          <el-table-column
            prop="showName"
            :show-overflow-tooltip="true"
            label="商品名称"
            min-width="210%"
          ></el-table-column>

          <el-table-column
            prop="spec"
            v-if="!briefColumn"
            :show-overflow-tooltip="true"
            label="规格"
            width="120%"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="fob"
            v-if="!briefColumn"
            label="原 / 售价(￥)"
            width="110"
            align="right"
          >
            <template slot-scope="scope">
              <el-tag size="mini" type="success">{{ scope.row.fob }}</el-tag>
              <el-tag size="mini">{{ scope.row.retailPrice }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="id"
            v-if="!briefColumn"
            :show-overflow-tooltip="true"
            label="ID"
            width="60%"
            align="right"
          ></el-table-column>
          <el-table-column
            prop="branchCode"
            v-if="!briefColumn"
            label="区域"
            width="90%"
            align="right"
          >
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.branchCode" placement="top" :open-delay="600">
                <span>{{ $options.filters.getBranchName(scope.row.branchCode, branchs) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="code"
            v-if="!briefColumn"
            :show-overflow-tooltip="true"
            label="条码"
            width="120%"
            align="left"
          ></el-table-column>
          <el-table-column label="图片" width="80%" align="center">
            <template slot-scope="scope">
              <el-popover placement="right-end" trigger="focus">
                <img :src="scope.row.imageUrl" class="avatar" alt="图片" />
                <img slot="reference" :src="scope.row.imageUrl" class="avatar goods-img-min" />
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="availableQty" label="库存数" align="right" width="70%"></el-table-column>
          <el-table-column prop="isPromotion" label="促销" align="center" width="80%">
            <template slot-scope="scope">{{ $options.filters.isPromotion(scope.row.isPromotion)}}</template>
          </el-table-column>
          <el-table-column prop="isFragileGoods" label="易碎品" align="center" width="80%">
            <template
              slot-scope="scope"
            >{{ $options.filters.isFragileGoods(scope.row.isFragileGoods)}}</template>
          </el-table-column>
          <el-table-column prop="productType" label="秒杀" align="center" width="80%">
            <template slot-scope="scope">{{ $options.filters.isProductType(scope.row.productType)}}</template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center" width="80%">
            <template slot-scope="scope">{{ $options.filters.statusTip(scope.row.status, status) }}</template>
          </el-table-column>
          <el-table-column prop="channelCodes" label="渠道" align="center" width="100%">
            <template
              slot-scope="scope"
            >{{ $options.filters.channelCodesFilter(scope.row.channelCodes) }}</template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            v-if="!briefColumn"
            :show-overflow-tooltip="true"
            label="创建时间"
            width="100%"
          >
            <template slot-scope="scope">{{ scope.row.createTime | dateFmt }}</template>
          </el-table-column>
        </el-table>
      </el-collapse-item>
    </el-collapse>

    <!-- 查询商品区 -->
    <el-row class="mb-10">
      <el-col :span="autoCommit ? 24 : 17">
        <el-popover placement="top" trigger="hover" width="400" :open-delay="500">
          <el-row class="mb-10">
            <el-col align="center">
              <h4>更多搜索</h4>
            </el-col>
          </el-row>
          <el-row :gutter="5">
            <el-col :span="12" class="mb-10">
              <el-input v-model.trim="search.code" size="mini" placeholder="搜索条码" clearable>
                <template slot="prepend">条码</template>
              </el-input>
            </el-col>
            <el-col :span="12" class="mb-10">
              <el-input v-model.trim="search.barcode" size="mini" placeholder="搜索编码" clearable>
                <template slot="prepend">编码</template>
              </el-input>
            </el-col>
            <el-col :span="12">
              <el-select
                v-model.number="search.status"
                size="mini"
                placeholder="选择状态"
                default-first-option
                filterable
              >
                <el-option v-for="(v, k, i) in status" :label="v" :value="Number(k)"></el-option>
              </el-select>
            </el-col>
          </el-row>
          <div slot="reference">
            <el-input
              v-model.trim="search.barcode"
              @keyup.enter.native="getList()"
              size="mini"
              placeholder="编码搜索"
              clearable
            >
              <el-button slot="append" icon="el-icon-refresh" @click="cancel()"></el-button>
              <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
            </el-input>
          </div>
        </el-popover>
      </el-col>
      <el-col :span="7" v-if="!autoCommit">
        <div class="dialog-footer">
          <el-tooltip content="取消所选" placement="top" :open-delay="500">
            <el-button @click="closeAddGoods" icon="el-icon-close" type="info" size="mini" plain></el-button>
          </el-tooltip>
          <el-tooltip content="确定" placement="top" :open-delay="500">
            <el-button @click="confirm" icon="el-icon-check" type="primary" size="mini"></el-button>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <el-table
      ref="selTab"
      :data="list"
      @selection-change="onSelect"
      @current-change="onRadio"
      :row-class-name="tabRowCla"
      v-loading="loading"
      :max-height="tabHeight.sel"
      class="custom-table"
      size="mini"
      highlight-current-row
      border
    >
      <el-table-column v-if="!radio && selectData.length < maxSel" type="selection" width="35%"></el-table-column>
      <el-table-column type="index" width="37%" align="right"></el-table-column>

      <el-table-column
        prop="barcode"
        v-if="!briefColumn"
        :show-overflow-tooltip="true"
        label="编码"
        width="100%"
        align="left"
      ></el-table-column>

      <el-table-column prop="showName" label="商品名称" min-width="210%"></el-table-column>

      <el-table-column
        prop="spec"
        v-if="!briefColumn"
        :show-overflow-tooltip="true"
        label="规格"
        width="120%"
        align="center"
      ></el-table-column>

      <el-table-column prop="fob" v-if="!briefColumn" label="原 / 售价(￥)" width="110" align="right">
        <template slot-scope="scope">
          <el-tag size="mini" type="success">{{ scope.row.fob }}</el-tag>
          <el-tag size="mini">{{ scope.row.retailPrice }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="id"
        v-if="!briefColumn"
        :show-overflow-tooltip="true"
        label="ID"
        width="60%"
        align="right"
      ></el-table-column>
      <el-table-column prop="branchCode" v-if="!briefColumn" label="区域" width="90%" align="right">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.branchCode" placement="top" :open-delay="600">
            <span>{{ $options.filters.getBranchName(scope.row.branchCode, branchs) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="code"
        v-if="!briefColumn"
        :show-overflow-tooltip="true"
        label="条码"
        width="120%"
        align="left"
      ></el-table-column>
      <el-table-column label="图片" width="80%" align="center">
        <template slot-scope="scope">
          <el-popover placement="right-end" trigger="focus">
            <img :src="scope.row.imageUrl" class="avatar" alt="图片" />
            <img slot="reference" :src="scope.row.imageUrl" class="avatar goods-img-min" />
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="availableQty" label="库存数" align="right" width="70%"></el-table-column>

      <el-table-column prop="isPromotion" label="促销" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isPromotion(scope.row.isPromotion)}}</template>
      </el-table-column>
      <el-table-column prop="isFragileGoods" label="易碎品" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isFragileGoods(scope.row.isFragileGoods)}}</template>
      </el-table-column>
      <el-table-column prop="productType" label="秒杀" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isProductType(scope.row.productType)}}</template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="80%" align="center">
        <template slot-scope="scope">{{ $options.filters.statusTip(scope.row.status, status) }}</template>
      </el-table-column>
      <el-table-column prop="channelCodes" label="渠道" align="center" width="100%">
        <template
          slot-scope="scope"
        >{{ $options.filters.channelCodesFilter(scope.row.channelCodes) }}</template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        v-if="!briefColumn"
        :show-overflow-tooltip="true"
        label="创建时间"
        width="100%"
      >
        <template slot-scope="scope">{{ scope.row.createTime | dateFmt }}</template>
      </el-table-column>
    </el-table>

    <el-pagination
      small
      :total="pagination.total"
      :page-size="pagination.size"
      :current-page="pagination.current"
      layout="prev, pager, next, jumper"
      @current-change="getList"
    ></el-pagination>
  </div>
</template>

<script>
import api from "api";
import Sortable from "sortablejs";
import { fetch, getDate } from "../../../utils/time-format";

export default {
  props: {
    /**
     * @param autoCommit {@link Boolean}：单选时，是否自动提交，默认自动提交；
     * @param reqMethod {@link String}：request请求商品时，调用的方法名称；
     * @param minSel {@link Number}：最少选择数，默认“0”；
     * @param maxSel {@link Number}：最多选择数，默认“1000”；
     * @param briefColumn {@link Boolean}：是否简洁显示列表（部分列不展示），默认“false”；
     * @param radio {@link Boolean}：是否单选，默认“多选”；
     * @param seledShow {@link Boolean}：是否显示已选列表；
     * @param collShow {@link Boolean}：是否展开已选列表；
     * @param pageSize {@link Number}：搜索页显示数量；
     * @param tabHeight {@link Object}：设置各列表高度；
     * @param search {@link Object}：初始搜索条件；
     * @param data {@link Object}：初始选中数据；
     */
    params: {
      type: Object,
      default() {
        return {};
      }
    },
    is_close: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let radio = !!this.params.radio;
    let autoCommit =
      radio &&
      (this.params.autoCommit == null ? true : !!this.params.autoCommit);
    let seledShow =
      this.params.seledShow == null ? true : !!this.params.seledShow;
    let briefColumn =
      this.params.briefColumn == null ? false : !!this.params.briefColumn;
    let minSel =
        this.params.minSel == null
          ? 0
          : this.params.minSel < 0
          ? 0
          : this.params.minSel,
      maxSel =
        this.params.maxSel == null
          ? 1000
          : this.params.maxSel < minSel
          ? minSel
          : this.params.maxSel;
    let tabHeight = this.params.tabHeight || {};
    tabHeight.sel = tabHeight.sel || 300;
    tabHeight.seled = tabHeight.seled || 180;
    return {
      loading: false,
      search: {
        status: null
      },
      searchSel: {},
      list: [],
      status: null,
      branchs: null,
      pagination: {
        total: 0,
        current: 1,
        size: this.params.pageSize || 5
      },
      tabHeight,
      minSel,
      maxSel,
      radio,
      seledShow,
      autoCommit,
      briefColumn,
      reqMethod: this.params.reqMethod || "selectSingleGoods",
      collShow: !this.params.collShow ? "" : "goods", //折叠面板展开，取el-collapse-item的name属性值
      selectIds: [],
      deal_selectData: [],
      selectData: [],
      sort_arr: []
    };
  },
  computed: {
    inSelScope() {
      let r = {
        eq: true
      };
      if (
        this.minSel &&
        (!this.selectData || this.selectData.length < this.minSel)
      ) {
        r.eq = false;
        r.tip = `请最少选择 ${this.minSel} 个商品`;
      } else if (
        this.maxSel &&
        (!this.selectData || this.selectData.length > this.maxSel)
      ) {
        r.eq = false;
        r.tip = `请最多选择 ${this.maxSel} 个商品`;
      }
      return r;
    }
  },
  methods: {
    input_sort(index) {
      console.log(
        parseInt(this.sort_arr[index]).toString().length !==
          this.sort_arr[index].length
      );
      if (
        parseInt(this.sort_arr[index]).toString().length !==
        this.sort_arr[index].length
      ) {
        this.$notify.error({
          title: "警告",
          message: "只能输入数字",
          type: "warning"
        });
        return;
      }

      let container = this.selectData[index];
      if (this.sort_arr[index] >= this.sort_arr.length) {
        this.$set(
          this.selectData,
          index,
          this.selectData[this.sort_arr.length - 1]
        );
        this.$set(this.selectData, this.sort_arr.length - 1, container);
      } else {
        this.$set(
          this.selectData,
          index,
          this.selectData[this.sort_arr[index] - 1]
        );
        this.$set(this.selectData, this.sort_arr[index] - 1, container);
      }

      console.log(this.selectData);
    },
    async getList(page = 1) {
      this.pagination.current = page;
      this.loading = true;

      let pms = Object.assign(
        {
          offset: this.pagination.current,
          limit: this.pagination.size
        },
        this.search
      );
      const r = await api.goods[this.reqMethod](pms);
      this.loading = false;
      if (r.code == 200) {
        let page = r.data;
        this.$nextTick(() => {
          this.list = page.list;
          this.pagination.total = page.total;
          /* 设置选中 */
          if (
            !this.list ||
            !this.list.length ||
            !this.selectData ||
            !this.selectData.length
          )
            return;
          this.list.map((v, i) => {
            this.selectData.map((sv, j) => {
              if (sv && v.id == sv.id) {
                return setTimeout(() => {
                  let row = this.$refs.selTab;
                  if (!row) return;
                  row[!this.radio ? "toggleRowSelection" : "setCurrentRow"](v); //设置选中
                }, 100);
              }
            });
          });
        });
      } else {
        this.$notify.error({
          message: r.msg,
          dangerouslyUseHTMLString: true, //允许html
          offset: 100, //偏移
          duration: 60000
        });
      }
    },
    async getSels() {
      this.loading = true;
      let data = this.params.data;
      if (
        !data ||
        ((!data.ids || !data.ids.length) &&
          (!data.selectData || !data.selectData.length))
      )
        //"selectData"和"ids"，二者有其一
        return;
      if (!data.selectData) {
        //没有实体数据，则根据ids组请求获得
        let pms = {
          skuIdList: data.ids
        };
        let r = await api.goods[this.reqMethod](pms);
        this.loading = !this.loading;
        if (r.code == 200) {
          data.selectData = r.data.list || [];
        } else {
          this.$notify.error({
            message: r.msg,
            dangerouslyUseHTMLString: true, //允许html
            offset: 100, //偏移
            duration: 30000
          });
        }
      }
      this.selectData = data.selectData;
    },
    /**
     * 已选商品搜索
     */
    searchSels() {
      let srch = this.searchSel.showName;
      if (!srch || !(srch = srch.trim()))
        //搜索条件
        return;
      let srs = this.selectData.filter(
        item =>
          ["code", "barcode", "showName", "spec"].filter(
            attr => item[attr] && `${item[attr]}`.indexOf(srch) != -1
          ).length
      );
      if (!srs.length) return this.$message.info("无相关匹配项。");
      //0828
      // this.selectData = srs;
      this.deal_selectData = srs;
      //0828
      this.$message.success(`已为您匹配成功 ${srs.length} 项。`);
    },
    /**
     * 单选事件
     */
    onRadio(val, oldVal) {
      if (this.radio && val) {
        this.selectData = [val];
        if (this.autoCommit) this.confirm();
      }
    },
    /**
     * 多选事件
     */
    onSelect(objs) {
      if (this.radio) return;
      let gos = Object.assign([], this.list);
      for (let i = 0; i < objs.length; i++) {
        let obj = objs[i];
        let id = obj.id;
        for (let j = 0; j < gos.length; j++) {
          let go = gos[j];
          if (!go) continue;
          if (id == go.id) {
            gos[j] = undefined; //清空相同元素
            break;
          }
        }
        if (this.selIdxOf(id) == -1)
          //已选中列表中不存在此id，则添加
          this.selectData.push(obj);
      }
      /* 将剩余不同元素（未选中），从this.selectData剔除 */
      for (let i = 0; i < gos.length; i++) {
        let go = gos[i];
        if (!go) continue;
        let idx = this.selIdxOf(go.id);
        if (idx >= 0) this.selectData.splice(idx, 1);
      }
    },
    /**
     * 查询所在已选ID列表的索引位置。
     * 不在列表中，则返回"-1"
     * @param id {@link Number}：查询元素；
     * @param sels {@link Array}：被查找数组；
     * @returns {number}
     */
    selIdxOf(id, sels) {
      sels = sels || this.selectData;
      if (!id || !sels || !sels.length) return -1;
      for (let i = 0; i < sels.length; i++) {
        let sel = sels[i];
        if (id == sel.id) return i;
      }
      return -1;
    },
    /**
     * 设置选中的ID组
     * @returns {Array}
     */
    changeSelId() {
      this.selectIds.length = 0;
      if (!this.selectData) return this.selectIds;
      for (let i = 0; i < this.selectData.length; i++) {
        let sel = this.selectData[i];
        this.selectIds.push(sel.id);
      }
      return this.selectIds;
    },
    del(row, type) {
      let idx = this.selIdxOf(row.id);
      if (idx >= 0) this.selectData.splice(idx, 1);

      let pgIdx = this.selIdxOf(row.id, this.list);
      if (pgIdx >= 0) {
        //删除当前页的选中
        if (!this.radio) this.$refs.selTab.toggleRowSelection(this.list[pgIdx]);
        //反选
        else this.$refs.selTab.setCurrentRow();
      }

      if (this.radio && this.autoCommit) this.confirm();
      //0828
      if (type === "deal_selectData") {
        let deal_index = this.deal_selectData.findIndex(val => {
          return val.id === row.id;
        });
        if (deal_index >= 0) {
          this.deal_selectData.splice(deal_index, 1);
        }
      }
      //0828
    },
    async dict() {
      let status = await api.goods.status();
      if (status.code == 200) this.$nextTick(() => (this.status = status.data));
      else this.$message.error(status.msg);

      if (!this.briefColumn) {
        //显示全部列时，获取区域信息
        let bho = await api.dict.branchHasOpen();
        if (bho.code == 200) this.$nextTick(() => (this.branchs = bho.data));
        else this.$message.error(bho.msg);
      }
    },
    /**
     * 初始化加载
     */
    init() {
      /*            	if (this.params.radio)   //是否单选
                                    this.radio = this.params.radio;
                                if (this.params.maxSel)   //最多选择数
                                    this.maxSel = this.params.maxSel;*/
      if (this.params.search)
        //初始搜索条件
        this.search = Object.assign(this.search, this.params.search);
      this.getSels();
    },
    sortableInit() {
      if (this.radio || !this.seledShow) return;
      let tbodys = document.querySelectorAll(".sort-tab table > tbody");
      if (!tbodys || !tbodys.length) return;
      tbodys.forEach(tbody =>
        Sortable.create(tbody, {
          scrollSpeed: 10, // 滚动速度。
          scrollSensitivity: 80, // 鼠标必须靠近边缘多少px才能开始滚动。
          fallbackTolerance: 50, // 以像素为单位指定鼠标在被视为拖动之前应移动多远。
          touchStartThreshold: 50, // 在多少像素移动范围内可以取消延迟拖动事件。
          /**
           * 拖拽被选中时的回调
           */
          onChoose: e => {
            if (this.tabHeight.seled >= 350) return;
            if (this.timId) clearTimeout(this.timId);
            this.tabHeight.seledCache = this.tabHeight.seled; //缓存原数值
            this.tabHeight.seled = 350; //放大拖拽区域
          },
          /**
           * 拖拽完成后回调
           * @description: 拖拽直接操作DOM完成后，并不会引起Vue的虚拟DOM变化。
           * 所以后续双向更新数据时，Vue根据Diff算法，会重新再次渲染真实DOM，引起冲突。
           * 参见：https://www.jianshu.com/p/d92b9efe3e6a
           */
          onEnd: e => {
            if (e.newIndex != e.oldIndex) {
              /* 还原直接操作的真实DOM，将DOM操作交还给Vue */
              let tr = tbody.children[e.newIndex];
              let oldTr = tbody.children[e.oldIndex];
              tbody.insertBefore(
                tr,
                e.newIndex > e.oldIndex ? oldTr : oldTr.nextSibling
              );

              let vals = this.selectData.splice(e.oldIndex, 1);
              this.selectData.splice(e.newIndex, vals.length - 1, ...vals);
            }
            this.$nextTick(() => {
              if (this.tabHeight.seledCache && this.tabHeight.seledCache < 350)
                this.timId = setTimeout(() => {
                  //拖拽完成后还原区域视图
                  this.tabHeight.seled = this.tabHeight.seledCache;
                  delete this.tabHeight.seledCache;
                  this.timId = clearTimeout(this.timId);
                }, 3000);
            });
          }
        })
      );
    },
    cancel() {
      this.search = Object.assign((this.search = {}), this.params.search);
      this.getList();
    },
    tabRowCla({ row, i }) {
      if (row.status == 1 || row.status == 3 || row.status == 5)
        return "bgc-success";
      return "";
    },
    /**
     * 确定
     */
    confirm() {
      //0828
      this.deal_selectData = [];
      this.searchSel.showName = "";
      //0828
      this.loading = true;
      if (!this.inSelScope.eq) {
        this.$notify.warning({
          message: this.inSelScope.tip,
          dangerouslyUseHTMLString: true, //允许html
          offset: 200 //偏移
        });
      } else {
        let r = {
          tag: "goods", //此返回值的自定义标识（以便区分其它组件的返回值）
          ids: _.cloneDeep(this.changeSelId()),
          data: _.cloneDeep(this.selectData)
        };
        if (this.radio) {
          //单选
          if (!r.data || !r.data.length) {
            r.data = {};
          } else {
            r.data = r.data[0];
            r.id = r.data.id;
          }
        }
        this.$emit("select", r);
      }
      this.loading = !this.loading;
    },
    /**
     * 取消
     */
    closeAddGoods() {
      this.selectIds = [];
      this.selectData = [];
      this.searchSel = {}; //清空搜索
      this.$refs.selTab[!this.radio ? "clearSelection" : "setCurrentRow"]();
    }
  },
  beforeMount() {
    this.dict();
  },
  mounted() {
    this.init();
    this.sortableInit();
    this.getList();
  },
  filters: {
    //渠道
    channelCodesFilter(val) {
      if (Array.isArray(val)) {
        if (val.length == 0) {
          return "无";
        }
        if (val.indexOf("1") != -1 && val.indexOf("2") != -1) {
          return "b2b,壹块钱";
        } else if (val.indexOf("1") != -1) {
          return "b2b";
        } else if (val.indexOf("2") != -1) {
          return "壹块钱";
        }
        return "未知";
      }
      return "无";
    },
    statusTip(status, dict) {
      return !dict ? "未知" : dict[status];
    },
    isPromotion(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isFragileGoods(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isProductType(val) {
      if (val == 2) {
        return "是";
      } else {
        return "否";
      }
    },
    dateFmt(date) {
      return date ? getDate(date) : "";
    },
    getBranchName(code, branchs) {
      let branchName = "";
      if (!code || !branchs || !branchs.length) return branchName;
      for (let i = 0, len = branchs.length; i < len; i++) {
        let branch = branchs[i];
        if (branch.branchCode == code) {
          branchName = branch.branchName;
          break;
        }
      }
      return branchName;
    }
  },
  watch: {
    selectData(new_val, old_val) {
      this.sort_arr = new_val.map((item, index) => {
        return index + 1;
      });
      console.log(this.sort_arr);
    },
    is_close() {
      this.closeAddGoods();
    }
  }
};
</script>
<style lang="scss" scoped rel="stylesheet/scss">
.product-link {
  border: 1px solid #0cdcdc;
  padding: 3px;
}

.tips li {
  list-style: square;
}

.goods-img-min {
  max-width: 100%;
  max-height: 30px;
}

.el-table .bgc-success {
  background: #f0f9eb;
}
</style>
