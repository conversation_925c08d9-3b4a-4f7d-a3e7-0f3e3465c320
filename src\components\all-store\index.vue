<template>
  <div class="page-link">
    <div style="display: flex;">
      <el-select
        size="mini"
        v-if="params.page_type === 'control-h5' || params.componentName == 'carefullySelectedShops'"
        v-model="params.content.shopType"
        placeholder="请选择"
        @change="handleChangeType"
      >
        <el-option
          v-for="item in typeOptions"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        ></el-option>
      </el-select>

      <el-input
        style="width: 150px"
        size="mini"
        v-if="params.page_type === 'control-h5' && params.content.shopType === 2"
        placeholder="请输入店铺名称"
        v-model="shopName"
        clearable>
      </el-input>

      <el-input
        style="flex:1; width: 1px;"
        size="mini"
        class="mb-10"
        v-model="key"
        @keyup.enter.native="getList()"
        :placeholder="params.page_type === 'control-h5' && params.content.shopType === 2 ? '请输入店铺编码' : '请输入店铺编码或名称'"
        clearable
      >
        <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
      </el-input>
    </div>
    <el-table size="mini" :data="list" style="margin-bottom:5px" v-loading="loading">
      <el-table-column type="index" width="50"></el-table-column>
      <el-table-column label="店铺编码">
        <template slot-scope="scope">
          <p>{{ scope.row.shopCode }}</p>
        </template>
      </el-table-column>
      <el-table-column label="店铺名称">
        <template slot-scope="scope">
          <p>{{ scope.row.showName }}</p>
        </template>
      </el-table-column>
      <el-table-column label="logo" width="80">
        <template slot-scope="scope">
          <img :src="scope.row.appLogoUrl" class="title-image" />
        </template>
      </el-table-column>

      <el-table-column label="店铺标签">
        <template slot-scope="scope">
          <span>{{ scope.row.shopTags }}</span>
        </template>
      </el-table-column>

      <el-table-column label="链接">
        <template slot-scope="scope">
          <span>{{ scope.row.appLink }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            type="danger"
            plain
            v-if="is_have(scope.row)"
            size="small"
            @click="emit_store(scope.row, 'del')"
          >取消关联</el-button>
          <el-button
            v-else
            type="primary"
            plain
            size="small"
            @click="emit_store(scope.row, 'add')"
          >关联</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="change_size"
      @current-change="change_curPage"
      :page-sizes="[10, 20, 30, 40]"
      layout="sizes, prev, pager, next"
      :current-page="pagination.current"
      :page-size="pagination.size"
      :total="pagination.total"
    ></el-pagination>
  </div>
</template>

<script>
import api from "api";

export default {
  props: {
    params: Object,
    stores: Array
  },
  data() {
    return {
      shopName: '',
      key: "",
      list: [],
      pagination: {
        size: 10,
        current: 1,
        total: 0
      },
      loading: false
    };
  },
  created() {
    this.getList();
  },
  computed:{
    typeOptions() {
      if(this.params.componentName == 'carefullySelectedShops') {
        return [
          { name: "自然人", value: 1},
          { name: "pop", value: 2 },
          { name: "自营店铺", value: 3 }
        ]
      } else {
        return [
          { name: "自然人", value: 1 },
          { name: "pop", value: 2 }
        ]
      }
    }
  },
  methods: {
    is_have(store) {
      return this.stores.some(item => {
        return item.shopCode === store.shopCode;
      });
    },
    change_size(pageSize) {
      this.pagination.size = pageSize;
      this.getList();
    },
    change_curPage(cur_page) {
      this.pagination.current = cur_page;
      this.getList();
    },
    handleChangeType() {
      this.pagination = {
        size: 10,
        current: 1,
        total: 0
      };
      this.key = '';
      this.shopName = '';
      // this.params.content.list = [];
      // this.params.content.store_list =[];
      this.getList();
    },
    async getPopDate() {
      this.loading = true;
      const params = {
        pageNum: this.pagination.current,
        pageSize: this.pagination.size,
        shopCode: this.key,
        name: this.shopName,
        branchCode: this.params.branchCode
      };
      const result = await api.stores.selectPop(params);
      this.loading = false;
      if (result.data && result.data.status === "success") {
        this.$nextTick(() => {
          this.list = result.data.data.shopInfos;
          this.pagination.total = result.data.data.totalCount;
          if (this.list.length === 0) {
            this.$message.info("没有查到店铺，请重新输入！");
          }
        });
      } else {
        this.$message.error(result ? result.msg : "接口错误！");
      }
    },
    async getList() {
      if (this.params.content && this.params.content.shopType === 2 && this.params.componentName !== 'carefullySelectedShops') {
        this.getPopDate();
        return false;
      }
      this.loading = true;
      let shopPatternCode = "personal";
      switch (this.params.page_type) {
        case "controlMallHome":
          shopPatternCode = "personal";
          break;
        case "YKQ":
          shopPatternCode = "ykq";
          break;
        default:
          shopPatternCode = this.params.content.shopType == 1 ? "personal" : this.params.content.shopType == 2 ? "pop" : this.params.content.shopType == 3 ? "ybm" : "";
      }
      const params = {
        shopPatternCodes: [shopPatternCode],
        pageNum: this.pagination.current,
        pageSize: this.pagination.size,
        branchCode: this.params.branchCode,
        keyword: this.key
      };
      const result = await api.stores.selectStore(params);
      this.loading = false;
      if (result.data && result.data.status === "success") {
        this.$nextTick(() => {
          this.list = result.data.data.shopInfos;
          this.pagination.total = result.data.data.totalCount;
          if (this.list.length === 0) {
            this.$message.info("没有查到店铺，请重新输入！");
          }
        });
      } else {
        this.$message.error(result ? result.msg : "接口错误！");
      }
    },
    emit_store(row, type) {
      let text =
        type === "add" ? `是否要关联${row.showName}` : `是否要取消关联${row.showName}`;
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      })
        .then(() => {
          this.$emit("emitStore", {
            type: type,
            data: row
          });
          this.$message({
            type: "success",
            message: "成功!"
          });
        })
        .catch(() => {});
    }
  }
};
</script>
<style lang="scss" scoped rel="stylesheet/scss">
.title-image {
  max-width: 100%;
  max-height: 100%;
}
</style>
