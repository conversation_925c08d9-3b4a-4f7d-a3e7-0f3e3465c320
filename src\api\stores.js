import * as http from 'utils/http';
export default {
  /**
   * 查询店铺列表
   * @param pms
   * @returns {Promise<*>}
   */
  async selectStore(pms) {
    const obj={
      headers: {
        "isAdmin": true,
        "Content-Type":"application/x-www-form-urlencoded"
      },
      method:"post",
      url:"/cms/shop/listShopsByParam",
      data:pms,
      transformRequest:[function(data) {
        let ret = '';
        for (let key in data) {
          ret += encodeURIComponent(key) + '=' + encodeURIComponent(data[key]) + '&'
        }
        return ret
      }]
    };
    return await http.putRequest(obj);
  },

  async selectPop(pms) {
    const obj={
      headers: {
        "isAdmin": true,
        "Content-Type":"application/x-www-form-urlencoded"
      },
      method:"post",
      url:"/cms/shop/listPopShopsByParam",
      data:pms,
      transformRequest:[function(data) {
        let ret = '';
        for (let key in data) {
          ret += encodeURIComponent(key) + '=' + encodeURIComponent(data[key]) + '&'
        }
        return ret
      }]
    };
    return await http.putRequest(obj);
  },

  async pullShops(pms) {
    const obj={
      headers: {
        "isAdmin": true,
      },
      method:"post",
      url:"/cms/shop/listPopShopInfos",
      data:pms,
    };
    return await http.putRequest(obj);
  },

  async pullStores(pms) {
    const obj={
      headers: {
        "isAdmin": true,
      },
      method:"post",
      url:"/cms/shop/listShopInfos",
      data:pms,
    };
    return await http.putRequest(obj);
  },

  async pullStoresName(pms) {
    const obj={
      headers: {
        "isAdmin": true,
      },
      method:"post",
      url:"/cms/shop/getShopNamesByExhibitionIds",
      data:pms,
    };
    return await http.putRequest(obj);
  },

  async searchCoupon(pms) {
    const obj={
      headers: {
        "isAdmin": true,
      },
      method:"post",
      url:"/cms/coupon/listCouponByIds",
      data:pms,
    };
    return await http.putRequest(obj);
  },
  async checkCoupon(pms) {
    const obj = {
      headers: {
        "isAdmin": true,
      },
      method:"get",
      url:"/cms/coupon/couponCheck",
      params:pms,
    };
    return await http.putRequest(obj);
  },
  async couponCheck(params) {
    const obj={
      headers: {
        "isAdmin": true,
      },
      method:"get",
      url:"/cms/coupon/couponCheck",
      params:params,
    };
    return await http.putRequest(obj);
  },

}
