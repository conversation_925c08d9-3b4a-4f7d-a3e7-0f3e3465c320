<template>
    <section>
        <!--模块背景设置-->
        <el-row :gutter="20">
            <div class="title">内外背景设置</div>
            <el-col :span="12">
                <div class="block">
                    <span class="demonstration">上传外背景图</span>
                    <div>
                        <!-- 背景图片上传 -->
                        <el-upload class="topic-pic-upload" ref="upload"
                                   accept="image/jpeg,image/jpg,image/png,image/gif"
                                   :max-size="1"
                                   :show-file-list="false"
                                   :before-upload="() => {loading = true; return true;}"
                                   :on-success="onUploadImage">
                            <el-button class="btn-block"
                                       size="small"
                                       type="warning" :loading="loading">上传外背景图
                            </el-button>
                            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                        </el-upload>
                    </div>
                </div>
            </el-col>


            <el-col :span="12">
                <div class="block">
                    <span class="demonstration">外背景色</span>
                    <div>
                        <el-color-picker :value="content.bgRes.length>10?null:content.bgRes"
                                         @change="change_bgRes"
                                         size="small">
                        </el-color-picker>
                    </div>
                </div>
            </el-col>

            <!--<el-col :span="8">-->
            <!--<div class="block">-->
            <!--<span class="demonstration">清除外背景</span>-->
            <!--<div>-->
            <!--<el-button type="warning" size="small" @click="clear_bg">清除外背景图</el-button>-->
            <!--</div>-->
            <!--</div>-->
            <!--</el-col>-->

            <!--<el-col :span="8">-->
            <!--<div class="block">-->
            <!--<span class="demonstration">清除外背景</span>-->
            <!--<div>-->
            <!--<el-button type="warning" size="small" @click="clear_bg">清除外背景图</el-button>-->
            <!--</div>-->
            <!--</div>-->
            <!--</el-col>-->


            <el-col :span="12">
                <div class="block">
                    <span class="demonstration">上传内背景图</span>
                    <div>
                        <!-- 背景图片上传 -->
                        <el-upload class="topic-pic-upload" ref="upload"
                                   accept="image/jpeg,image/jpg,image/png,image/gif"
                                   :max-size="1"
                                   :show-file-list="false"
                                   :before-upload="() => {loading = true; return true;}"
                                   :on-success="onUploadInside_bgImage">
                            <el-button class="btn-block"
                                       size="small"
                                       type="warning" :loading="loading">上传内背景图
                            </el-button>
                            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                        </el-upload>
                    </div>
                </div>
            </el-col>


            <el-col :span="12">
                <div class="block">
                    <span class="demonstration">内背景色</span>
                    <div>
                        <el-color-picker :value="content.inside_bgRes.length>10?null:content.inside_bgRes"
                                         @change="change_inside_bgRes"
                                         size="small">
                        </el-color-picker>
                    </div>
                </div>
            </el-col>

        </el-row>
        <!--模块外背景设置-->
        <el-row :gutter="20">
            <div class="title">药头条设置</div>
            <el-col :span="12">
                <div class="block">
                    <span class="demonstration"></span>
                    <div>
                        <!-- icon上传 -->
                        <el-upload class="topic-pic-upload" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"
                                   :max-size="1"
                                   :show-file-list="false" :before-upload="() => {return true;}"
                                   :on-success="onUploadIcon">
                            <el-button class="btn-block" type="warning" :loading="loading">上传icon</el-button>
                            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                        </el-upload>
                    </div>
                </div>
            </el-col>

            <el-col :span="12">
                <div class="block">
                    <span class="demonstration"></span>
                    <div>
                        <el-button class="add-button" @click="handleAdd" type="primary">添加药头条</el-button>
                    </div>
                </div>
            </el-col>
        </el-row>


        <!--<a @click="content.bgImage=''" style="cursor: pointer"></a>-->
        <!-- 药头条列表 -->

        <el-table :data="pageData" :row-key="getRowKeys" border style="width: 100%" v-if="dataList.length>0">
            <el-table-column label="开始日期" width="150">
                <template slot-scope="scope">
                    {{timeFormat(scope.row.startTime)}}
                </template>
            </el-table-column>
            <el-table-column label="结束日期" width="150">
                <template slot-scope="scope">
                    {{timeFormat(scope.row.endTime)}}
                </template>
            </el-table-column>
            <el-table-column prop="content" label="头条标题">
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                    <el-button @click="handleEdit(scope.row)" type="text" size="small">编辑</el-button>
                    <el-button type="text" size="small" @click="handleCancle(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-size="pageSize"
                layout="prev, pager, next, jumper"
                :total="totalNum">
        </el-pagination>
        <el-dialog
            v-if="dialogFormVisible"
                :close-on-click-modal="false"
                :title="flag==='edit'?'编辑药头条':'添加药头条'" :visible.sync="dialogFormVisible">
            <edit-headline @give-form='getFormData' :flag='flag' :editData="editData"
                           :params="{branchCode: topic.branchCode}"></edit-headline>
        </el-dialog>
    </section>
</template>
<script>
    import base from "../base";
    import editHeadline from './edit-headline'

    export default {
        extends: base,
        contentDefault: {
            list: [],
            bgRes: "#F7F7F8",
            inside_bgRes: '#F7F7F8',
            icon: 'http://upload.ybm100.com/ybm/applayoutbanner/b07eda96-029d-457a-9c13-8d449cd719b4.png'
        },
        data() {
            return {
                loading: false,
                dialogFormVisible: false,
                editData: null,
                editIndex: 0,
                flag: '',
                currentPage: 1,
                pageSize: 10,
                id: 1
            }
        },
        computed: {
            dataList() {
                var list = _.get(this, 'content.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
            pageData() {
                this.$nextTick(function () {
                    this.setSort();
                });
                return this.dataList.slice((this.currentPage - 1) * this.pageSize, (this.currentPage) * (this.pageSize))
            },
            totalNum() {
                return this.dataList.length
            },
        },
        components: {
            editHeadline
        },
        methods: {
            change_inside_bgRes(val) {
                this.content.inside_bgRes = val
            },
            change_bgRes(val) {
                this.content.bgRes = val
            },
            getFormData(data) {
                ++this.id;
                let dataJson = Object.assign({}, data.data);
                dataJson.id = this.id;
                delete dataJson.date;
                dataJson = Object.assign(dataJson, {startTime: data.data.date[0], endTime: data.data.date[1]})
                if (this.flag === 'add') {
                    //添加
                    if (this.dataList.indexOf(data.data) >= 0) {
                        this.$message.error('您添加的内容已存在，请直接编辑')
                        return false
                    }
                    this.dataList.push(dataJson)

                } else {
                    //编辑
                    this.dataList.splice(this.editIndex, 1, dataJson)
                }
                this.dialogFormVisible = false;
                this.init()

            },
            init() {
                this.flag = ""
                this.editIndex = 0
                this.editData = null
            },
            addTamp(m) {
                return m < 10 ? '0' + m : m
            },
            timeFormat(timestamp) {
                if (timestamp === '') {
                    return ''
                }
                var time = new Date(timestamp);
                var year = time.getFullYear();
                var month = time.getMonth() + 1;
                var date = time.getDate();
                var hours = time.getHours();
                var minutes = time.getMinutes();
                var seconds = time.getSeconds();
                return year + '-' + this.addTamp(month) + '-' + this.addTamp(date) + ' ' + this.addTamp(hours) + ':' +
                    this.addTamp(minutes) + ':' + this.addTamp(seconds);
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: "warning"
                    });
                    return;
                }
                this.content.bgRes = res.data.url;
            },
            onUploadInside_bgImage(res) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: "warning"
                    });
                    return;
                }
                this.content.inside_bgRes = res.data.url;
            },
            async onUploadIcon(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: "warning"
                    });
                    return;
                }
                this.content.icon = res.data.url;
            },
            handleAdd() {
                //添加药头条
                this.editData = null;
                this.dialogFormVisible = true;
                this.flag = "add"
            },
            handleEdit(row) {
                this.flag = "edit"
                this.editData = Object.assign({}, row);
                this.editIndex = this.dataList.indexOf(row);
                this.dialogFormVisible = true;
            },
            handleCancle(row) {
                let _self = this;
                return function () {
                    const index = _self.dataList.indexOf(row)
                    _self.dataList.splice(index, 1)
                    _self.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }.confirm(_self)()
            },
            handleCurrentChange(val) {
                this.currentPage = val
                // console.log(`当前页: ${val}`);
            }
        }
    }
</script>
<style lang="scss">
    .el-row {
        text-align: center;
        margin-bottom: 10px;

        img {
            width: 100%;
        }

        .title {
            text-align: left;
            line-height: 30px;
            background-color: #f2f2f2;
            margin: 10px 0;
            padding-left: 10px;
        }
    }
</style>
