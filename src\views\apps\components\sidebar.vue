<template>
  <header class="sidebar" v-loading="loading">
    <el-menu
      :style="{ minHeight: contentHeight }"
      :defaultActive="activeIndex"
      class="side-menu-list"
      :router="true"
      :unique-opened="true"
      @select="handleSelect"
    >
      <template v-for="(nav, index) in navList">
        <template v-if="!nav.children.length">
          <el-menu-item :key="index" :index="nav.actionUrl">
            <i class="iconfont" :class="nav.icon"></i>
            {{ nav.menuName }}
          </el-menu-item>
        </template>
        <template v-else>
          <el-submenu :key="index" :index="nav.actionUrl">
            <template slot="title">
              <img class="menu-icon-active" src="/static/images/menu-active.png" />
              <img class="menu-icon" src="/static/images/menu.png" />
              <span>{{ nav.menuName }}</span>
            </template>
            <el-menu-item
              :index="subMenu.actionUrl"
              v-for="subMenu in nav.children"
              :key="subMenu.menuId"
            >
              <!--<img class="menu-icon-active" src="/static/images/menu-active.png">-->
              <!--<img class="menu-icon" src="/static/images/menu.png">-->
              {{ subMenu.menuName }}
            </el-menu-item>
          </el-submenu>
        </template>
      </template>
    </el-menu>
  </header>
</template>

<script>
import api from "api";
import bus from "utils/eventbus";

export default {
  name: "Sidebar",
  data() {
    return {
      activeIndex: location.hash
        .substr(1)
        .split("/")
        .splice(0, 3)
        .join("/"),
      contentHeight: window.innerHeight - 50 + "px",
      loading: false
    };
  },
  computed: {
    isShow() {
      return this.$store.getters["sideBar/isShow"];
    },
    navList() {
      return this.$store.getters["sideBar/navList"];
    }
  },
  async mounted() {
    this.loading = true;
    const navRes = await api.menu.list();
    if (navRes.code == 200) {
      this.loading = false;
      this.$store.dispatch("sideBar/updateNavList", navRes.data);
      this.$store.dispatch("sideBar/updateAccountNavList", navRes.data);
    } else {
      this.$message.error(navRes.msg);
    }
    this.handleSelect(this.activeIndex);
  },
  methods: {
    handleSelect(key, keyPath) {
      if (key.indexOf("category=") > 0) {
        let sts = key.split("category=");
        if (sts[1] && sts[1] == "app") {
          bus.$emit("menu_type", "app");
          // bus.$emit("change_type", "app-index");
        } else if (sts[1] && sts[1] == "pc") {
          bus.$emit("menu_type", "pc");
          // bus.$emit("change_type", "pc-h5");
        } else {
          bus.$emit("menu_type", "other");
        }
      } else {
        for (let v in this.navList) {
          if (this.navList[v].actionUrl == key) {
            bus.$emit("menu_type", "other-" + this.navList[v].menuName);
          }
        }
      }
    }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss">
.sidebar {
  position: relative;
  background-color: #fff;
  .side-menu-list {
    padding-top: 10px;
    box-sizing: border-box;
    color: #ffffff;
    .el-submenu__title {
      height: 36px;
      line-height: 36px;
      font-size: 13px;
    }
    .el-menu-item {
      display: flex;
      align-items: center;
      padding-left: 25px;
      margin-top: 6px;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-weight: 400;
      .iconfont {
        font-size: 16px;
        height: 16px;
        line-height: 1;
        margin-right: 5px;
      }
      .menu-icon,
      .menu-icon-active {
        width: 18px;
        margin-right: 10px;
      }
      .menu-icon {
        display: inline-block;
      }
      .menu-icon-active {
        display: none;
      }
      &.is-active {
        .menu-icon {
          display: none;
        }
        .menu-icon-active {
          display: inline-block;
        }
      }
    }
    .el-submenu {
      .menu-icon,
      .menu-icon-active {
        width: 18px;
        margin-right: 10px;
      }
      .menu-icon {
        display: inline-block;
      }
      .menu-icon-active {
        display: none;
      }
      &.is-active {
        font-weight: 700;
        .menu-icon {
          display: none;
        }
        .menu-icon-active {
          display: inline-block;
        }
      }
    }
  }
}
</style>
