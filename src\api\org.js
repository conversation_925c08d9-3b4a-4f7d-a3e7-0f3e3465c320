import * as http from 'utils/http'

export default {

    list() {
        return http.get('org')
    },
    get(id) {
        return http.get(
            `org/${id}`
        )
    },
    add(params) {
        return http.post(
            'org/add', params
        )
    },
    update(id, params) {
        return http.post(
            `org/${id}/update`, params
        )
    },
    remove(id, params) {
        return http.post(
            `org/${id}/remove`, params
        )
    },
}
