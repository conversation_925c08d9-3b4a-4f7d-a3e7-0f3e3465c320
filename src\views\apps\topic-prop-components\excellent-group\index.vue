<template>
	<div>
		<!--优+套餐-->
		<el-row>
			<el-col :span="8">
				<el-input type="text" size="mini" v-model="content.code"
				          autocomplete="off" placeholder="请输入套餐编号"></el-input>
			</el-col>
			<el-col :span="16">
				<el-button type="primary" size="mini" @click="handleSearch" style="margin-left:10px;">查询</el-button>
			</el-col>
		</el-row>
		<p class="blank_20"></p>
		选择套餐样式：
		<el-radio-group v-model="content.styleNum" @change="styleChange">
			<el-radio :label="item" v-for="(item,index) in styleList" :key="index">{{`样式${item}`}}</el-radio>
		</el-radio-group>
		<div v-show="content.styleNum===2">
			<p class="blank_20"></p>
			是否显示标题：
			<el-checkbox v-model="content.isTitle">{{content.isTitle?'隐藏':'显示'}}</el-checkbox>
		</div>
		<p class="blank_10"></p>
		<el-row :gutter="10" v-show="content.styleNum===3">
			<el-col :span="6">
				<el-upload
					  class="topic-image-upload"
					  ref="upload"
					  accept="image/jpeg,image/jpg,image/png,image/gif"
					  :show-file-list="false"
					  :on-success="onUploadImg">
					<el-button size="mini" class="btn-block" type="primary">上传背景图</el-button>
					<div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
				</el-upload>
			</el-col>
			<el-col :span="6">
				<el-upload
					  class="topic-image-upload"
					  ref="upload"
					  accept="image/jpeg,image/jpg,image/png,image/gif"
					  :show-file-list="false"
					  :on-success="onUploadFlag">
					<el-button size="mini" class="btn-block" type="primary">上传角标背景图</el-button>
					<div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
				</el-upload>
			</el-col>
		</el-row>
		<p class="blank_10"></p>
	</div>
</template>
<script>
	import base from "../base";
	import {activePackage} from 'api'

	export default {
		self: this,
		extends: base,
		name: "excellentGroup",
		contentDefault: {
			code: 0,
			styleNum: 3,
			isTitle: false,
			bgImage: 'http://upload.ybm100.com/ybm/applayoutbanner/6e41f728-4f27-4b57-8f16-c883a17f1158.png',
			flag: 'http://upload.ybm100.com/ybm/applayoutbanner/f2e8af0f-268b-4481-bf2d-a33b94879bd1.png',
			searchNum: 1
		},
		data() {
			return {
				styleList: [1, 2, 3]
			}
		},

		methods: {
			styleChange(){
				if(this.content.searchNum!==1)
					this.content.searchNum++;
				else
					this.content.searchNum=1
			},
			onUploadImg(res, file) {
				if (res.code !== 200) {
					this.$message({
						message: `[${res.code}]${res.msg}`,
						type: 'warning'
					})
					return;
				}
				this.content.bgImage = res.data.url;
			},
			onUploadFlag(res, file) {
				if (res.code !== 200) {
					this.$message({
						message: `[${res.code}]${res.msg}`,
						type: 'warning'
					})
					return;
				}
				this.content.flag = res.data.url;
			},
			handleSearch() {
				if (!this.content.code || isNaN(parseInt(this.content.code))) {
					this.$message.error('请输入正确的查询编码')
					return
				}
				//package_id:1195
				let params = {
					'packageId': this.content.code,
					'branchCode': this.topic.branchCode,
					'effectiveStatus': 1
				};
				activePackage.select(params).then(res => {
					if (res.status !== 'success') {
						this.content.searchNum=1
						this.$message.warning('查询套餐已失效，请重新输入')
						return
					}
					if (!res.resultPage.rows || res.resultPage.rows.length === 0) {
						this.content.searchNum=1
						this.$message.warning('套餐无效')
						return
					}
					this.content.searchNum++;
				}).catch(err => {
					this.content.searchNum=1
					console.log(err)
					this.$message.error('查询错误,请重新输入')
				})


			}
		}
	}
</script>

<style scoped>

</style>
