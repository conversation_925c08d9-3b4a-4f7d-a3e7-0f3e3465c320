/**
 * 封装请求
 */
import 'es6-promise/auto'
import {API_SERVER} from 'config'
import diversion from './diversion'
import handleError from './error'
import axios from 'axios'
import eventbus from 'utils/eventbus';

function defaults() {
  return {
    baseURL: `${API_SERVER}/`,
    withCredentials: true,
    headers: {
      'token': window.localStorage.getItem('token')
    },
  }
}

async function fetch() {
  let customOptions = [].pop.apply(arguments)
  console.log(window.localStorage.getItem('token'), "toekne获取")
  let options = defaults()
  if (customOptions) {
    if (customOptions.baseURL) {
      options.baseURL = customOptions.baseURL
    }
    if (customOptions.params) {
      options.params = customOptions.params
    }
  }
  let method = [].shift.apply(arguments)
  const result = await diversion(() => {
    return axios[method](...arguments, options).catch((err) => {
      handleError(err)
      throw err
    })
  })
  if (result.code === 403) {
    eventbus.$emit('accessTokenExpire')
  }
  return result;
}

export const get = (url, params, options) => {
  return fetch('get', url, Object.assign(options || {}, {params}))
};
export const post = (url, data, options) => {
  return fetch('post', url, data, options)
};
export const put = (url, data, options) => {
  return fetch('put', url, data, options)
};
export const del = (url, options) => {
  return fetch('delete', url, options)
};

function new_default_option() {
  let base_url = "https://app-v4.ybm100.com";
  switch (window.origin) {
    case ("http://localhost:9090"):
      // base_url = "https://new-app.dev.ybm100.com";
      base_url = "https://new-app.test.ybm100.com";
      break;
    case ("https://admin-cms.ybm100.com"):
      base_url = "https://app-v4.ybm100.com";
      break;
    case ("https://admin-cms-new.stage.ybm100.com"):
      base_url = "https://app-new.stage.ybm100.com";
      break;
    case ("https://admin-cms.test.ybm100.com"):
      base_url = "https://new-app.test.ybm100.com";
      break;
    case ("https://admin-cms.dev.ybm100.com"):
      base_url = "https://new-app.dev.ybm100.com";
      break;
    case ("https://admin-cms2.test.ybm100.com"):
      base_url = "https://app2.test.ybm100.com/";
      break;
  }
  return {
    baseURL: base_url,
    timeout : 10000,
  }
}

export const putRequest = (options={}) => {
  let option = new_default_option();
  console.log("puttt", option)
  option=Object.assign(option,options);
  return axios(option).catch(err=>{
    console.log(err)
  });

};

