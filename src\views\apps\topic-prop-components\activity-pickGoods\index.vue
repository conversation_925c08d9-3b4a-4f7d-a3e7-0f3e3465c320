<template>
	<div>
		<div class="edit-title">
			<p class="blank_20"></p>
			<header class="edit-header">编辑公共组件</header>
			<p class="blank_20"></p>
			<el-radio-group v-model="pubComponent">
				<el-radio :label="item.name" v-for="(item,index) in pubComponentList" :key="index">{{item.label}}
				</el-radio>
			</el-radio-group>
		</div>
		<p class="blank_20"></p>
		<streamer
			  :page-data="content.streamer"
			  v-show="pubComponent==='streamer'"
			  :branchCode="topic.branchCode"
			  label="streamer"
			  @listenData="getData"></streamer>
		<p class="blank_20"></p>
		<swiper-group
			  :page-data="content.swiper"
			  v-show="pubComponent==='swiper'"
			  :branchCode="topic.branchCode"
			  label="swiper"
			  @listenData="getData"></swiper-group>
		<p class="blank_20"></p>
		<tab-switch
			  :page-data="content.switch"
			  v-show="pubComponent==='switch'"
			  label="switch"
			  @listenData="getData"
			  @listenCurrentRow="getEditRow"
			  :componentsList="componentsList"></tab-switch>
		<p class="blank_20"></p>
		<el-dialog title="编辑当前页" :visible.sync="dialogVisible" width="40%">
			<p class="blank_20">选择组件:</p>
			<el-checkbox-group v-model="selectComponent" @change="setComponent">
				<el-checkbox :label="item.name" v-for="(item,index) in componentsList" :key="index">{{item.label}}
				</el-checkbox>
			</el-checkbox-group>
			<p class="blank_20">编辑组件:</p>
			<el-radio-group v-model="currentComponent">
				<el-radio :label="item.name" v-for="(item,index) in selectComponentList" :key="index">{{item.label}}
				</el-radio>
			</el-radio-group>
			<p class="blank_20"></p>
			<header class="edit-header">{{textArr(currentComponent)}}</header>
			<swiper-group
				  v-if="isShow.static"
				  v-show="currentComponent==='static'"
				  :page-data="content.static"
				  :branchCode="topic.branchCode"
				  label="static"
				  :clearNum="clearNum"
				  @listenData="getData"></swiper-group>
		</el-dialog>
	</div>
</template>

<script>

	import tabSwitch from '../../components/public/tab-switch'
	import streamer from '../../components/public/streamer'
	import swiperGroup from '../../components/public/swiper-goods-group'
	import base from "../base";
	import activityPage from '../../mixins/activityPage'

	export default {
		name: "activityMargin",
		extends: base,
		mixins: [activityPage],
		contentDefault: {
			switch: {
				color: '#2A2A2A',
				hoverColor: '#333',
				lineColor: '#00DC82',
				bgColor: '#EBEBEB',
				isBtn: false,
				list: [
					{
						"name": "挑好货",
						"content": [
							{
								"title": "同品推荐"
							},
							{
								"title": "药食同源"
							},
							{
								"title": "高毛优选"
							}
						]
					}
				]
			},
			streamer: {
				image: '',
				bgRes: '',
				color: '#000000',
				link: {
					page_url: '',
					page_name: ''
				},
				timevalue: ''
			},
			swiper: {
				bgColor: '#fff',
				list: []
			},
			static: {
				bgColor: '#fff',
				list: []
			},
			list: [],
			currentRow: []
		},
		data() {
			return {
				isShow: {
					static: false,
				},
				clearNum: 1,
				componentsList: [{name: 'static', label: '静态商品组'}],
				pubComponent: '',
				pubComponentList: [{name: 'streamer', label: '横福广告'}, {name: 'swiper', label: '滚动商品组'}, {
					name: 'switch',
					label: '选项卡'
				}]
			}
		},
		mounted() {
			this.pubComponent = this.pubComponentList[0].name
		},
		components: {
			swiperGroup, tabSwitch, streamer
		},
		methods: {
			getData(data) {
				// 返回组件的数据
				data = _.cloneDeep(data);
				//填入选项卡数据
				if (data.key === 'static') {
					if (this.content.currentRow.length === 0) return;
					if (!this.content.list[this.content.currentRow[0]]) {
						this.$set(this.content.list, this.content.currentRow[0], [])
					}
					if (!this.content.list[this.content.currentRow[0]][this.content.currentRow[1]]) {
						this.$set(this.content.list[this.content.currentRow[0]], this.content.currentRow[1], {})
					}

					this.$set(this.content.list[this.content.currentRow[0]][this.content.currentRow[1]], data.key, data.data)
					return
				}
				this.content[data.key] = data.data;
			},
			getEditRow(data) {
				// data为选中的当前行
				if (!this.content) return
				if (data.key === 'tab') {
					this.content.currentRow = data.data
					return
				}
				this.content.currentRow = data.data;
				// 填充组件数据
				this.setConfig();
				this.showComponents()
			},
			setConfig() {
				//填入数据
				this.clearData();
				if (this.content.list.length > 0 && this.content.list[this.content.currentRow[0]]) {
					if (this.content.list[this.content.currentRow[0]][this.content.currentRow[1]]) {
						this.content.static = _.cloneDeep(this.content.list[this.content.currentRow[0]][this.content.currentRow[1]]['static']);
					}
				}
			},
			clearData() {
				//清空数据
				this.clearNum++;
				this.content.static.list.length > 0 ?
					this.content.static.list.splice(0, this.content.static.list.length)
					: null;
				this.selectComponent.splice(0, this.selectComponent.length)
			}

		}
	}
</script>
<style>
	.edit-title {
		background-color: #E8E8E8;
		padding-left: 10px;
		padding-bottom: 20px;
	}
</style>
