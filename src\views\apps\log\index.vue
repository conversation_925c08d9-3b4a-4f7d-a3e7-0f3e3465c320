<template>
  <div class="main-content">
    <el-row class="mb-10">
      <!--<el-col :span="12">
                <el-button type="primary" size="mini" icon="el-icon-plus" @click="showModal()">添加产品</el-button>
      </el-col>-->
      <el-col :span="24">
        <el-row class="search-wrap" type="flex" :gutter="10">
          <el-popover placement="top" trigger="hover">
            <el-button
              @click="cancel()"
              :loading="loading"
              type="info"
              icon="el-icon-refresh"
              size="mini"
              plain
            >清空</el-button>
            <el-button
              @click="changeSize()"
              :loading="loading"
              slot="reference"
              type="primary"
              icon="el-icon-search"
              size="mini"
              plain
            >查询</el-button>
          </el-popover>
          <el-date-picker
            v-model="dataTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="mini"
          ></el-date-picker>
          <el-select v-model="searchParam.type" placeholder="选择操作动作" size="mini">
            <el-option
              v-for="item in logType"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
          <!-- <el-input v-model="searchParam.url" placeholder="输入操作动作" size="mini" clearable></el-input> -->
          <el-input v-model="searchParam.userId" placeholder="输入操作人账号" size="mini" clearable></el-input>
          <el-input v-model="searchParam.pageName" placeholder="输入页面配置名称" size="mini" clearable></el-input>
        </el-row>
      </el-col>
    </el-row>
    <el-table :data="dataList" v-loading="loading" class="custom-table" size="mini" border>
      <div slot="empty" class="empty-wrap">
        <i class="iconfont icon-tishi"></i>
        <span>未获取到日志信息</span>
      </div>

      <el-table-column label="ID" :show-overflow-tooltip="true">
        <template slot-scope="scope">{{scope.row._id}}</template>
      </el-table-column>

      <el-table-column prop="url" :show-overflow-tooltip="true" label="客户端" width="80" >
        <template slot-scope="scope">{{ scope.row.category }}</template>
      </el-table-column>

      <el-table-column :show-overflow-tooltip="true" label="业务线" width="120">
        <template slot-scope="scope">{{scope.row.businessLine}}</template>
      </el-table-column>

      <el-table-column  :show-overflow-tooltip="true" label="区域" >
        <template slot-scope="scope">{{scope.row.branchCode}}</template>
      </el-table-column>

      <el-table-column  :show-overflow-tooltip="true" label="页面配置名称" >
        <template slot-scope="scope">{{scope.row.pageName}}</template>
      </el-table-column>

      <el-table-column  :show-overflow-tooltip="true" label="页面类型" >
        <template slot-scope="scope">{{scope.row.pageType}}</template>
      </el-table-column>

      <el-table-column  :show-overflow-tooltip="true" label="操作动作" >
        <template slot-scope="scope">{{scope.row.type}}</template>
      </el-table-column>

      <el-table-column  :show-overflow-tooltip="true" label="操作账号" >
        <template slot-scope="scope">{{scope.row.userId}}</template>
      </el-table-column>

      <el-table-column  label="操作人姓名" width="150%">
        <template slot-scope="scope">{{scope.row.userName}}</template>
      </el-table-column>

      <el-table-column  label="操作时间" width="150%">
        <template slot-scope="scope">{{scope.row.createTime}}</template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-show="totalSize > 10"
      :current-page.sync="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      @size-change="changeSize"
      @current-change="changePage"
      layout="total, sizes, slot, jumper, prev, pager, next"
      :total="totalSize"
    ></el-pagination>
  </div>
</template>
<script>
import api from "api";
import bus from "utils/eventbus";

export default {
  name: "Log",
  data() {
    return {
      currentPage: 1,
      pageFrom: 1,
      pageSize: 10,
      totalSize: 10,
      loading: false,
      searchParam: {
        category: "app",
        type: '',
        userId: '',
        pageName: '',
        startTime: '',
        endTime: '',
      },
      dataTime: '',
      dataList: [],
      logType: [],
    };
  },

  created() {
    bus.$on("change_log_type", this.changeLogType);
    // bus.$on("change_depearment", this.changeDepearment);
    bus.$on("change_branch", this.changeBranch);
  },

  mounted() {
    // this.loadData()
    this.$store.dispatch("breadcrumb/clearPath");
    this.$store.dispatch("breadcrumb/addPath", {
      title: "日志管理",
      subTitle: "日志管理",
      action: "log"
    });
    bus.$emit("menu_type", "other-日志管理");
    api.log.getLogType().then((res) => {
      if (res.code === 200) {
        this.logType = res.data;
      }
    })
  },

  methods: {
    changeLogType(item) {
      this.searchParam.category = item.includes("PC") ? "pc" : "app";
      this.pageFrom = 1;
      this.pageSize = 10;
      this.loadData();
    },

    changeDepearment(item) {
      this.searchParam.department = item.id;
      this.pageFrom = 1;
      this.pageSize = 10;
      this.loadData();
    },

    changeBranch(item) {
      this.searchParam.branchCode = item.branchCode;
      this.pageFrom = 1;
      this.pageSize = 10;
      this.loadData();
    },

    async loadData() {
      this.loading = true;
      const params = {
        pageFrom: this.pageFrom,
        pageSize: this.pageSize,
      };
      if (this.dataTime) {
        params.startTime = this.dataTime[0];
        params.endTime = this.dataTime[1];
      } else {
        params.startTime = '';
        params.endTime = '';
      }
      const result = await api.log.list(
        Object.assign(this.searchParam, params)
      );
      this.loading = false;
      if (result.code == 200) {
        this.$nextTick(() => {
          this.dataList = result.data.rows;
          this.totalSize = result.data.total;
        });
      } else {
        this.$message.error(result.msg);
      }
    },

    changePage(pageNo) {
      this.pageFrom = pageNo;
      this.loadData();
    },

    changeSize(pageSize) {
      this.currentPage = 1;
      this.pageSize = pageSize || this.pageSize;
      this.pageFrom = 1;
      this.loadData();
    },

    cancel() {
      const type = this.searchParam.category;
      this.searchParam = {
        category: type
      };
      this.dataTime = '';
      this.changeSize();
    }
  },

  destroyed() {
    bus.$off("change_log_type", this.changeLogType);
    bus.$off("change_depearment", this.changeDepearment);
    bus.$off("change_branch", this.changeBranch);
  }
};
</script>
<style lang="scss" rel="stylesheet/scss">
</style>
