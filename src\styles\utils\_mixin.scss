@import "../function/color";

@mixin ellipsis() {
  overflow: hidden;
  white-space: nowrap;
  word-break: keep-all;
  text-overflow: ellipsis;
}

@mixin translate($left: -50%, $top: -50%) {
  transform: translate($left, $top);
}

@mixin translateX($left: 0) {
  transform: translateX($left);
}

@mixin translateY($top: 0) {
  transform: translateY($top);
}

@mixin middle-center($position: absolute) {
  position: $position;
  left: 50%;
  top: 50%;
  @include translate(-50%, -50%);
}

@mixin middle-center-x($position: absolute) {
  position: $position;
  left: 50%;
  @include translateX(-50%);
}

@mixin middle-center-y($position: absolute) {
  position: $position;
  top: 50%;
  @include translateY(-50%);
}

@mixin multi-ellipsis($line-number: 2) {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line-number;
}

@mixin background-image($url: '') {
  background: url($url) center no-repeat;
  background-size: contain;
}
@mixin flexbox($direction:row,
  $rowRank:space-between,
  $columnRank:center,
  $wrap:nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $rowRank;
  align-items: $columnRank;
  flex-flow: $wrap
}
