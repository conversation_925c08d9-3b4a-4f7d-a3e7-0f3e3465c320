<template>
  <div class="topic-menu-list">
    <el-row :gutter="20">
      <div class="title">模块背景配置</div>
      <div class="block btitle">
        <span class="demonstration">标题名称：</span>
        <div>
          <el-input
            size="mini"
            :maxLength="10"
            v-model="content.title"
            placeholder="请输入标题"
          />
        </div>
      </div>
      <el-col
        style="
          width: 100%;
          display: flex;
          align-items: center;
          padding: 10px;
          padding-left: 20px;
        "
      >
        <div class="block" style="margin-right: 40px">
          <span class="demonstration">模块背景颜色</span>
          <div>
            <el-color-picker v-model="content.list_body_color" size="mini" />
          </div>
        </div>
        <div class="block">
          <el-upload
            style="display: inline-block; margin-right: 10px"
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="
              () => {
                loading = true;
                return true;
              }
            "
            :on-success="onUploadImage"
          >
            <el-button
              size="mini"
              class="btn-block"
              type="primary"
              :loading="loading"
              >上传背景图</el-button
            >
            <div slot="tip" class="el-upload__tip">
              支持类型：png/jpg/jpeg/gif
            </div>
          </el-upload>
          <el-button
            size="mini"
            @click="
              () => {
                content.bgImage = '';
              }
            "
            >清除背景图</el-button
          >
        </div>
      </el-col>
    </el-row>
    <!-- 模块有效时间设置 -->
    <el-row :gutter="20" class="brand-time">
      <div class="title">模块有效时间设置</div>
      <el-col :span="24">
        <el-date-picker
          v-model="content.timevalue"
          @change="change_time"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        >
        </el-date-picker>
      </el-col>
    </el-row>
    <el-row :gutter="20" style="height: 10px;">
      <!-- <div class="title">列表基础配置</div>
      <div class="block btitle">
        <span class="demonstration">排序策略：</span>
        <div>
          <el-radio-group v-model="content.sort">
            <el-radio :label="1">大搜排序逻辑</el-radio>
            <el-radio :label="2">拼团专属排序逻辑</el-radio>
          </el-radio-group>
        </div>
      </div> -->
    </el-row>

    <el-row :gutter="20">
      <div v-if="goods_group.length">
        <div class="title" style="margin-bottom: 0">预览已选商品组</div>
        <el-col :span="24">
          <el-table :data="goods_group" height="120" ref="multipleTable">
            <el-table-column prop="name" label="组名" />
            <el-table-column prop="code" label="编号" />
            <el-table-column prop="branchCode" label="区域号" />
          </el-table>
        </el-col>
      </div>
    </el-row>
    <!--选择商品-->
    <all-link
      ref="all_link"
      @select="onSetLink"
      :tabs="tabs"
      :params="{
        goodsGroup: {
          seledShow: false,
          minSel: 1,
          search: {
            state: 1,
            branchCode: topic.branchCode,
            indexType: 1,
          },
        },
      }"
    />
  </div>
</template>

<script>
import base from "../../base";
export default {
  name: "categiory-flow",
  extends: base,
  contentDefault: {
    list: [],
    goods_group: [],
    list_body_color: "#fff",
    title: "",
    bgImage: "",
    sort: 1,
  },
  created() {
    if (this.content.sort === undefined) {
      this.content.sort = 1;
    }
  },
  data() {
    return {
      loading: false,
      tabs: [{ label: "商品组", value: "goodsGroup" }],
      pickerOptions: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  computed: {
    list() {
      let list = _.get(this, "content.list");
      if (list) {
        return list;
      } else {
        return [];
      }
    },
    goods_group() {
      let list = _.get(this, "content.goods_group");
      if (list) {
        return list;
      } else {
        return [];
      }
    },
  },
  methods: {
    // 模块有效时间设置
    change_time() {
      for (let item of this.content.list) {
        this.$set(item, "time", this.content.timevalue);
      }
      this.content.list = this.content.list.map((item) => {
        return item;
      });
    },
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.bgImage = res.data.url;
    },
    onSetLink(link) {
      function handle_arr(arr = []) {
        return arr.map((item) => {
          let obj = {};
          obj.init_img_url = item.init_img_url;
          obj.imageUrl = item.imageUrl;
          obj.productName = item.productName;
          obj.showName = item.showName;
          obj.mediumPackageTitle = item.mediumPackageTitle;
          obj.fob = item.fob;
          obj.id = item.id;
          return obj;
        });
      }
      if (link.tag === "goods" || link.tag === "importGoods") {
        let _self_arr = handle_arr(link.data);
        this.content.list = [..._self_arr];
        this.content.goods_group = [];
      } else if (link.tag === "goodsGroup") {
        let obj = {};
        obj.name = link.data.name;
        obj.branchCode = link.data.branchCode;
        obj.code = link.data.code;
        this.content.goods_group.splice(0, 1, obj);
        this.content.list = [];
      }
    },
  },
};
</script>
<style scoped lang="scss">
.el-row {
  text-align: center;
  .title {
    text-align: left;
    line-height: 30px;
    color: #13c2c2;
    padding-left: 10px;
    margin: 10px;
  }
  .btitle {
    display: flex;
    align-items: center;
    padding: 10px;
    padding-top: 0;
    padding-left: 20px;
  }
}
</style>
