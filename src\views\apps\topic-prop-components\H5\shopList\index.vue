<template>
  <div>
    <el-row :gutter="20">
      <div class="title">模版背景设置</div>
      <div class="cont">
        <div class="flex">
          <div class="left flex">
            店铺背景色:
            <el-color-picker
              v-model="content.shopBackgroundColor"
              @active-change="onSelectShopBackgroundColor"
            ></el-color-picker>
          </div>
          <div class="right flex">
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/png,image/gif"
              :max-size="2"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadShopBackgroundImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传店铺背景图</el-button>
            </el-upload>
            <el-button class="btn-block" @click="content.shopBackgroundImg = ''">清除店铺背景图</el-button>
          </div>
        </div>

        <div class="flex" style="margin-top: 20px">
          <div class="left flex">
            商品背景色:
            <el-color-picker
              v-model="content.goodBackgroundColor"
              @active-change="onSelectGoodBackgroundColor"
            ></el-color-picker>
          </div>
          <div class="right flex">
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/png,image/gif"
              :max-size="2"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadGoodBackgroundImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传商品背景图</el-button>
            </el-upload>
            <el-button class="btn-block" @click="content.goodBackgroundImg = ''">清除商品背景图</el-button>
          </div>
        </div>
      </div>
      <div class="title">关联店铺设置</div>
      <div class="cont">
        <div class="flex alginCenter justify">
          　关联店铺方式：
          <div>
            <el-select
              v-model.number="content.associatedShopType"
              size="mini"
              placeholder="请选择"
              default-first-option
              filterable
            >
              <el-option label="系统自动-品质店铺" :value="1"></el-option>
              <el-option label="指定关联店铺" :value="2"></el-option>
            </el-select>  
          </div> 
        </div> 
      </div>
      <div v-if="content.associatedShopType === 2">
        <div class="title">关联店铺</div>
        <div class="cont flex justify">
          <el-button style="width: 100px;" class="btn-block" type="primary" @click="toEdit('add')">关联店铺</el-button>
        </div>
      </div>

      <div v-if="content.associatedShopType === 2">
        <div class="title">关联店铺列表</div>
        <div class="cont">
          <el-table :data="content.list" :lazy="true" size="mini" :row-key="(row) => row.groupShopsName">
            <el-table-column label="序号" width="50" type="index" />
            <el-table-column label="店铺组名称">
              <template slot-scope="scope">
                <span>{{ scope.row.groupShopsName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="指定人群">
              <template slot-scope="scope">
                <span>{{ scope.row.crowdValue || '全部人群' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="关联店铺数">
              <template slot-scope="scope">
                <span>{{ (scope.row.shopsList || []).length }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="toEdit('edit', scope.row, scope.$index)">编辑</el-button>
                <el-popover
                  placement="top"
                  v-model="scope.row.visible"
                >
                  <p>确定删除吗？</p>
                  <div style="text-align: right; margin: 0">
                    <el-button size="mini" type="text" @click="scope.row.visible = false">取消</el-button>
                    <el-button type="primary" size="mini" @click="toDelete(scope.row, scope.$index)">确定</el-button>
                  </div>
                  <el-button slot="reference" @click="scope.row.visible = true" type="danger" size="mini">删除</el-button>
                </el-popover>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-row>
    <shop-dialog
      v-if="add_editContent"
      :shopItemData="editData"
      :content="content"
      :topic="topic"
      :visible="add_editContent"
      :bindCsuOrProductGroup="true"
      @confirmEditContent="confirmEditContent"
    />
  </div>
</template>

<script>
import base from "../../base";
import shopDialog from '../../AppIndex/carefullySelectedShops/shopDialogCur.vue';

export default {
  name: 'shopList',
  extends: base,
  contentDefault: {
    shopBackgroundColor: '#ffffff',
    shopBackgroundImg: '',
    goodBackgroundColor: '#ffffff',
    goodBackgroundImg: '',
    associatedShopType: 1,
    list: [],
  },
  components: {
    shopDialog
  },
  data() {
    return {
      loading: false,
      editData: {
        groupShopsName: '',
        crowdType: 1,
        crowdValue: '',
        shopsList: [],
        visible: false,
      },
      add_editContent: false,
    }
  },
  methods: {
    toEdit(type, row, index) {
      this.add_editContent = true;
      this.editType = type;
      if(type == 'edit') {
        console.log(2, row)
        this.editData = JSON.parse(JSON.stringify(row));
        this.nowIndex = index;
      } else {
        this.editData = {
          groupShopsName: '',
          crowdType: 1,
          crowdValue: '',
          shopsList: [],
          visible: false,
        };
      }
    },
    toDelete(row, index) {
      this.content.list.splice(index, 1);
      row.visible = false;
    },
    closeEditContent() {
      this.add_editContent = false;
    },
    confirmEditContent(data) {
      if(this.editType == 'add') {
        this.content.list.push(data);
      } else {
        this.content.list.splice(this.nowIndex, 1, data);
      }
    },
    onSelectShopBackgroundColor(val) {
      this.content.shopBackgroundColor = this.toColor16(val);
    },
    async onUploadShopBackgroundImg(res) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
           type: 'warning'
        })
        return;
      }
      this.content.shopBackgroundImg = res.data.url;
    },
    onSelectGoodBackgroundColor(val) {
      this.content.goodBackgroundColor = this.toColor16(val);
    },
    async onUploadGoodBackgroundImg(res) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
           type: 'warning'
        })
        return;
      }
      this.content.goodBackgroundImg = res.data.url;
    },
  }
}
</script>

<style lang="scss">
  .el-row {
    text-align: center;
    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
    .cont {
      padding: 20px;
      .flex {
        display: flex;
        justify-content: space-around;
        .left {
          flex-shrink: 0;
          align-items: center;
        }
        .right {
          flex: 1;
          button {
            width: auto;
          }
        }
      }
    }
    .alginCenter {
      align-items: center;
    }
    .justify {
      justify-content: center !important;
    }
    .spaceAround {
      justify-content: space-around;
    }
  }
</style>