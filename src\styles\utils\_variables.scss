
//Transition
$all-transition: all .3s cubic-bezier(.645,.045,.355,1);
$fade-transition: opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
$fade-linear-transition: opacity 200ms linear;
$md-fade-transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1) 100ms, opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) 100ms;
$border-transition-base: border-color .2s cubic-bezier(.645,.045,.355,1);
$color-transition-base: color .2s cubic-bezier(.645,.045,.355,1);

// Border

$border-width-base: 1px;
$border-style-base: solid;
$border-color-base: rgb(191, 217, 215);
$border-color-hover: rgb(131, 165, 164);
$border-base: 1px solid rgb(191, 217, 215);
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-circle: 100%;

// Box-shadow

$box-shadow-base: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .12);

// Fill

$fill-base: #fff;