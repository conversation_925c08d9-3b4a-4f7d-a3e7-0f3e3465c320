import proxy from './proxy';
import { AppWebsite } from '../config/index';

/**
 * 套餐
 */
export default {
	/**
	 * 查询套餐
	 * @param pm
	 * @returns {Promise<*>}
	 */
	async select(pm) {
		let pms = {
			url: AppWebsite + 'app/activityPackage/select',
			dataType: 'json',
			data: pm,
			head: {
				terminalType: 1,
				'Content-Type': 'application/json;charset=UTF-8'
			}
		};
		let res = await proxy.post(pms);    //代理请求，外部接口
		return res
		// return (res.code != 1) ? {
		// 		code: 500,
		// 		msg: res.msg
		// 	} : {
		// 		code: 200,
		// 		data: res.result
		// 	};
	},
	/**
	 * 套餐类型
	 * @returns {Promise<undefined>}
	 */
	async type() {
		let pms = {
			url: AppWebsite + 'app/activityPackage/type',
			dataType: 'json',
			head: {
				terminalType: 1,
				'Pragma': `public, max-age=${12 * 60 * 60}`,         //允许使用缓存（单位：秒），兼容http-1.0
				'Cache-Control': `public, max-age=${12 * 60 * 60}`  //允许使用缓存（单位：秒）
			}
		};
		let res = await proxy.post(pms);    //代理请求，外部接口
		return (!res.code) ? {
				code: 200,
				data: res
			} : {
				code: 500,
				msg: '获取套餐类型失败。'
			};
	},
	/**
	 * 套餐标识
	 * @returns {Promise<undefined>}
	 */
	async mark() {
		let pms = {
			url: AppWebsite + 'app/activityPackage/mark',
			dataType: 'json',
			head: {
				terminalType: 1,
				'Pragma': `public, max-age=${12 * 60 * 60}`,         //允许使用缓存（单位：秒），兼容http-1.0
				'Cache-Control': `public, max-age=${12 * 60 * 60}`  //允许使用缓存（单位：秒）
			}
		};
		let res = await proxy.post(pms);    //代理请求，外部接口
		return (!res.code) ? {
				code: 200,
				data: res
			} : {
				code: 500,
				msg: '获取套餐标识失败。'
			};
	},
	/**
	 * 新增启动页时间校验（app端）
	 */
	async timeVerification(params) {
		let pms = {
            url: AppWebsite + 'cms/startPage/timeVerification',
            dataType: 'json',
            data: params,
            head: {
                terminalType: 1,
                'Content-Type': 'application/json;charset=UTF-8'
            }
		};
		let res = await proxy.post(pms);    //代理请求，外部接口
		return res
	}
}
