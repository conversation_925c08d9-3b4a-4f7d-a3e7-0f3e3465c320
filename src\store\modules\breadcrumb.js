import * as types from '../mutation-types'
// initial state
const state = {
  pathList: [],
}
// getters
const getters = {
  pathList: state => state.pathList,
}
// actions
const actions = {
  setPathList({ commit, state }, list) {
    commit(types.SET_PATH_LIST, list)
  },
  clearPath({ commit, state }) {
    commit(types.CLEAR_PATH)
  },
  addPath({ commit, state }, path) {
    commit(types.ADD_PATH, path)
  },
  removePath({ commit, state }, path) {
    commit(types.REMOVE_PATH, path)
  },
}
// mutations
const mutations = {
  [types.SET_PATH_LIST](state, list) {
    state.pathList = list;
    localStorage.setItem('current_path', JSON.stringify(state.pathList))
  },
  [types.CLEAR_PATH](state) {
    state.pathList = [];
    localStorage.setItem('current_path', JSON.stringify(state.pathList))
  },
  [types.ADD_PATH](state, dPath) {
    dPath.subTitle = dPath.subTitle || dPath.title;
    dPath.path = dPath.path || window.location.hash.substr(1);
    let flag = false, currentIndex = 0;
    state.pathList.forEach((path, index) => {
      if (dPath.action === path.action) {
        flag = true;
        currentIndex = index;
      }
    })
    if (flag) {
      state.pathList.splice(currentIndex + 1)
    } else {
      state.pathList.push(dPath)
    }
    localStorage.setItem('current_path', JSON.stringify(state.pathList))
  },
  [types.REMOVE_PATH](state, dPath) {
    state.pathList.forEach((path, index) => {
      if (dPath.action === path.action) {
        state.pathList.splice(index, 1);
      }
    })
    localStorage.setItem('current_path', JSON.stringify(state.pathList))
  },
}
export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
