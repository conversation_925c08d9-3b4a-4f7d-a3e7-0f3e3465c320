<template>
  <div></div>
</template>

<script>
  import _ from 'lodash';

  export default {
    // content对象初始化的数据结构
    contentDefault: {},
    props: {
      value: Object,
      topic: Object,
      categoryList: Array
    },
    data() {
      return {
        content: null,
        menu: ["首页", "活动专区", "爆款推荐", "常购清单"],
        // menu: ["首页", "活动专区"],
      }
    },
    watch: {
      // 双向更新
      value: {
        deep: true,
        handler(value) {
          this.content = _.cloneDeep(value);
        }
      },
      content: {
        deep: true,
        handler(value) {
          if (JSON.stringify(value) !== JSON.stringify(this.value)) {
            this.$emit('input', value)
          }
        }
      }
    },
    created() {
      console.log(this.topic, "vlkk")
      this.content = _.cloneDeep(this.value);
      this.content.branchCode = this.topic.branchCode
    }
  }
</script>
