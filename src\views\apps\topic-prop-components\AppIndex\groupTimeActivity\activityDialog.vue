<template>
  <el-dialog class="banner-dialog" :title="isEdit ? '编辑活动' : '添加活动'" :visible.sync="visible" :before-close="closeEditContent">
    <el-form ref="form" :rules="rules" :model="editData" label-width="100px">
      <el-form-item label="活动名称：" prop="activityName">
        <el-input v-model="editData.activityName" class="entry-name" :maxlength="20" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="人群范围：" prop="crowdType">
        <el-radio-group v-model="editData.crowdType" @change="changeCrowdType">
          <el-radio :label="1">全部人群</el-radio>
          <el-radio :label="2">指定人群</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="指定人群：" v-if="editData.crowdType===2">
        <el-select
          v-model="editData.crowdValue"
          :loading="selectLoading"
          filterable
          :filter-method="optionFilter"
          placeholder="请输入人群id"
          clearable
          @clear="options = []"
          @change="selectCrowd"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <!-- <el-autocomplete
          style="width: 300px"
          class="inline-input"
          v-model="editData.crowdValue"
          :fetch-suggestions="querySearchCrowd"
          placeholder="请输入人群id"
          :trigger-on-focus="false"
          @select="handleSelectCrowd"
          @input="changeCrowdValue"
        ></el-autocomplete> -->
      </el-form-item>
      <el-form-item label="展示时间：" prop="mode">
        <el-radio-group v-model="editData.mode">
          <el-radio :label="1">
            固定时段
            <el-date-picker v-model="editData.timevalue" type="datetimerange" :picker-options="pickerOptions" range-separator="至"  start-placeholder="开始日期" end-placeholder="结束日期" align="right" />
          </el-radio>
          <br>
          <el-radio :label="2">
            周期循环
            <el-button style="marginTop: 10px" :disabled="editData.mode === 1" @click="settingTime" type="primary" size="mini">配置</el-button>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="上传主标题：">
        <el-upload
          class="topic-image-upload"
          ref="upload"
          accept="image/jpeg,image/jpg,image/png,image/gif"
          :show-file-list="false"
          :on-success="uploadMainTitleImg"
        >
          <div v-if="editData.mainTitleUrl" class="imgBox">
            <img :src="editData.mainTitleUrl" class="image" />
          </div>
          <i v-else v-loading="loading" class="el-icon-plus uploader-icon"></i>
          <i class="el-icon-circle-close" @click.stop="clearMaintitle"></i>
          <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="上传副标题：">
        <el-upload
          class="topic-image-upload"
          ref="upload"
          accept="image/jpeg,image/jpg,image/png,image/gif"
          :show-file-list="false"
          :on-success="uploadSubtitleImg"
        >
          <div v-if="editData.subtitleUrl" class="imgBox">
            <img :src="editData.subtitleUrl" class="image" />
          </div>
          <i v-else v-loading="loading" class="el-icon-plus uploader-icon"></i>
          <i class="el-icon-circle-close" @click.stop="clearSubtitle"></i>
          <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
      </el-form-item>

      <el-form-item label="入口名称：">
        <el-input class="entryNameBox" placeholder="请输入内容" v-model="editData.entryName" :maxlength="4" />
      </el-form-item>
      <el-form-item label="跳转链接：">
        <el-input placeholder="请输入内容" v-model="editData.jumpLink" @input="urlChange"/>
      </el-form-item>

      <el-form-item label="选品方式：" prop="selectProductType">
        <el-select size="small" @change="changeProductsType" v-model="editData.selectProductType" placeholder="请选择">
          <el-option label="指定商品" value="appointProduct" />
          <el-option label="指定商品组" value="appointProductGroup" />
          <el-option label="系统自动" value="systemAuto" />
        </el-select>
      </el-form-item>
      <el-form-item label="商品组ID：" v-if="editData.selectProductType === 'appointProductGroup'">
        <el-input style="width: 200px" size="small" placeholder="请输入内容" v-model="editData.selectProductGroupId" />
      </el-form-item>
      <el-table
        v-if="editData.selectProductType === 'appointProduct'"
        :data="editData.selectProducts"
        size="mini"
      >
        <el-table-column label="显示序号" type="index" width="100" />
        <el-table-column label="指定商品ID">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row"
              size="mini"
              placeholder="请输入指定商品ID"
              clearable
              style="width: 200px"
              @input="changeInput($event, scope.$index)"
            />
          </template>
        </el-table-column>
      </el-table>

    </el-form>
    <loop-by-week
      v-if="showTimeVis"
      :weekData="editData.weekData"
      @cancelModal="showTimeVis = false"
      @confirmSetLoopTime="confirmSetLoopTime"
    >
    </loop-by-week>

    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="closeEditContent">取 消</el-button>
      <el-button size="small" type="primary" @click="add_confirmEdit">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
  import { AppWebsite, getUrlParam } from "config";
  import api from 'api';
  import LoopByWeek from '../../../components/loopByWeek.vue';
  export default {
    name: 'adDialog',
    props:[ "visible", "branchCode", "adItemData", "editIndex", "pageType" ],
    components: {
      LoopByWeek,
    },
    data() {
      return {
        loading: false,
        maxNumber: 100, 
        // productGroupId: '',
        showTimeVis: false,
        options: [],
        selectLoading: false,
        editData: {
          activityName: "",
          crowdType: 1,
          crowdValue: '',
          crowdId: '',
          entryName: '',
          jumpLink: 'ybmpage://commonh5activity?cache=0&url=https://app.ybm100.com/public/2021/6/16401.html',
          subtitleUrl: '',
          mainTitleUrl: '',
          mode: 1,
          timevalue: [],
          weekData: [],
          selectProductType: '',
          selectProducts: [],
          selectProductGroupId: '',
        },
        pickerOptions: {
          shortcuts: [
            {
              text: "未来一周",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来三个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来六个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一年",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                picker.$emit("pick", [start, end]);
              }
            }
          ]
        },
        rules: {
          activityName: [
            { required: true, message: '请输入活动名称', trigger: 'blur' },
            { max: 20, message: '最多20个字符', trigger: 'blur' }
          ],
          crowdType: [
            { required: true, message: '请选择人群范围', trigger: 'blur' },
          ],
          mode: [
            { required: true, message: '请选择展示时间', trigger: 'blur' },
          ],
          selectProductType: [
            { required: true, message: '请选择选品方式', trigger: 'change' },
          ]
        }
      };
    },
    mounted () { 
      if(this.isEdit) {
        this.editData = this.adItemData;
      }
    },
    computed: {
      isEdit() {
        return Object.keys(this.adItemData).length > 0 ? true : false;
      }
    },
    methods: {
      urlChange(){
      this.editData.jumpLink=this.editData.jumpLink.trim()
    },
      changeInput(val, index) {
        this.$set(this.editData.selectProducts, index, Number(val.trim()));
      },
      clearMaintitle() {
        this.editData.mainTitleUrl = '//upload.ybm100.com/ybm/app/layout/cmsimages/2022-3/1e5040fd3138993a621060e1d4503947.png';
      },
      clearSubtitle() {
        this.editData.subtitleUrl = '';
      },
      // changeCrowdValue(e) {
      //   if (!e) {
      //     this.editData.crowdId = '';
      //   }
      //   this.$forceUpdate();
      // },
      changeCrowdType() {
        this.editData.crowdId = '';
        this.editData.crowdValue = '';
      },
      async optionFilter(val) {
        this.selectLoading = true;
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          this.selectLoading = false;
          this.options = [{
            label: data.name,
            value: val,
          }]
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCrowd(e) {
        if (e) {
          this.editData.crowdId = Number(this.options[0].value.trim());
          this.editData.crowdValue = this.options[0].label;
        } else {
          this.editData.crowdId = '';
          this.editData.crowdValue = '';
        }
        this.$forceUpdate();
      },
      // async querySearchCrowd(queryString, cb) {
      //   const pms = {
      //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
      //     dataType: "json",
      //     data: {},
      //     head: {
      //       "Content-Type": "application/json;charset=UTF-8"
      //     }
      //   };
      //   const res = await api.proxy.post(pms);
      //   if (res.success) {
      //     const { data } = res;
      //     cb([{
      //       id: queryString,
      //       value: data.name || ""
      //     }]);
      //     return false;
      //   }
      // },
      // handleSelectCrowd(item) {
      //   this.editData.crowdId = item.id;
      // },
      changeProductsType() {
        this.editData.selectProducts = Array.from({length: 6}, v => '');
        this.editData.selectProductGroupId = '';
        // this.productGroupId = '';
      },
      confirmSetLoopTime(data) {
        this.$set(this.editData, 'weekData', data);
        this.showTimeVis = false;
      },
      settingTime() {
        // if ((this.editData.timevalue || []).length === 0) {
        //   this.$message.error('请先设置开始结束时间');
        //   return;
        // }
        this.showTimeVis = true;
      },
      
      async uploadMainTitleImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.editData.mainTitleUrl = res.data.url;
      },
      async uploadSubtitleImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.editData.subtitleUrl = res.data.url;
      },
      
      closeEditContent() {
        this.$parent.closeEditContent();
      },

      // 校验绑定商品
      async checkBindCsuOrProductGroup() {
        let canSave = true;
        (this.editData.selectProducts || []).forEach((item) => {
          if (isNaN(item) || item < 0) {
            canSave = false;
          }
        });
        if (!canSave) {
          this.$message.error('指定商品ID只能输入数字');
          return;
        }
        const params = {
          type: this.editData.selectProductType === 'appointProduct' ? 1: 2,
          exhibitionId: this.editData.selectProductGroupId,
          csuIds: this.editData.selectProducts.filter(i => i).map(Number),
        }
        const result = await api.topic.checkBindCsuOrProductGroup(params);
        if ((result.data.data || {}).checkResult) {
          // if (this.editData.selectProductType === 'appointProduct' ) {
          //   this.$set(this.editData, 'selectProducts', this.editData.selectProducts)
          // } else {
          //   this.editData.selectProductGroupId = this.productGroupId;
          // }
          this.$message.success('绑定成功')
          this.confirm();
        } else {
          // this.editData.selectProductGroupId = '';
          if (this.editData.selectProductType === 'appointProduct' ) {
            this.$message.error(`以下商品id绑定失败：${((result.data.data || {}).failureCsuIds || []).join()}`)
          } else {
            this.$message.error(result.data.msg)
          }
        }
      },
      
      //确定添加活动
      add_confirmEdit() {
        this.$refs.form.validate(async(valid) => {
          if (valid) {
            if (!this.editData.crowdType || (this.editData.crowdType === 2 && !this.editData.crowdId)) {
              this.$message.warning("请选择正确的人群");
              return false;
            }
            if ((this.editData.mode == 1 && this.editData.timevalue.length == 0) || this.editData.mode == 2 && this.editData.weekData.length == 0) {
              this.$message.warning("请选择有效时间");
              return false;
            }
            let linkErrMsg = '';
            if (this.editData.jumpLink) {
              if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.editData.jumpLink)) {
                linkErrMsg = '跳转链接格式不正确，请检查';
              } else {
                let linkPageUrl = getUrlParam(this.editData.jumpLink, 'url');
                const result = await api.topic.checkPageUrl({ url: linkPageUrl });
                if (((result || {}).data || {}).status != 200) {
                  linkErrMsg = '跳转链接不存在，请检查';
                }
              }
            }
            if (linkErrMsg) {
              this.$message.error(linkErrMsg);
              return false;
            }

            if (this.editData.selectProductType === 'systemAuto') {
              this.confirm();
            } else {
              this.checkBindCsuOrProductGroup();
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      confirm() {
        this.closeEditContent();
        this.psData = {
          ...this.editData,
          mainTitleUrl: this.editData.mainTitleUrl || '//upload.ybm100.com/ybm/app/layout/cmsimages/2022-3/1e5040fd3138993a621060e1d4503947.png',
        }
        if (this.isEdit) {
          this.$emit('saveDialog','edit', this.psData, this.editIndex )
        } else {
          this.$emit("saveDialog", 'add', this.psData);
        }
        console.log('this.psData', this.psData);
      }

    }
  };

</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .topic-image-upload {
    width: 100px;
    height: 100px;
    position: relative;
    .imgBox {
      position: relative;
    }
    .image {
      display: block;
      width: 100%;
      z-index: 10;
    }
    .uploader-icon {
      width: 100px;
      height: 100px;
      line-height: 100px;
      border: 1px solid $border-base;
      font-size: 30px;
    }
  }
  .el-icon-circle-close {
    position: absolute;
    top: -8px;
    right: -16px;
    font-size: 20px;
    z-index: 100;
  }
  .el-upload__tip {
    position: absolute;
    top: 18px;
    left: 134px;
    width: 180px;
  }
  .hr {
    height: 1px;
    background: #E8E6E6;
  }
  .infoItem {
    margin-top: 10px;
  }
  .topic-image-picker {
    padding-bottom: 10px;
  }
</style>
