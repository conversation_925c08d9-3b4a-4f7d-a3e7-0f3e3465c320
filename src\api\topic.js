import * as http from 'utils/http';
import { AppWebsite } from "config";

export default {
  list(params) {
    return http.post(
      'topic', params, { contentType: 'application/json; charset=UTF-8' }
    )
  },
  get(id) {
    return http.get(
      `topic/${id}`
    )
  },
  /**埋点发布页面事件 */
  async publishPage(id) {
    const obj={
      headers: {
        "isAdmin": true,
      },
      method:"get",
      url:"/cms/coupon/publishPage",
      params:id,
    };
    return await http.putRequest(obj);
  },
  copy(id, params) {
    return http.post(
      `topic/${id}/copy`, params
    )
  },
  add(params) {
    return http.post(
      'topic/add', params, { contentType: 'application/json; charset=UTF-8' }
    )
  },
  addMore(params) {
    return http.post(
      'topic/addMore', params, { contentType: 'application/json; charset=UTF-8' }
    )
  },
  update(id, params) {
    return http.post(
      `topic/${id}/update`, params
    )
  },
  rollback(id, params) {
    return http.post(
      `topic/${id}/rollback`, params
    )
  },
  publish(id) {
    return http.post(
      `topic/${id}/publish`
    )
  },
  fetch(id) {
    return http.post(
      `topic/${id}/fetch`
    )
  },
  remove(id) {
    return http.post(
      `topic/${id}/remove`
    )
  },
  removeMore(params) {
    return http.post(
      `topic/removeMore`,params
    )
  },
  synchronous() {
    return http.post(
      `topic/synchronous`
    )
  },
  checkPageUrl(params) {
    return http.post(
      `topic/checkPageUrl`, params, { contentType: 'application/json; charset=UTF-8' }
    )
  },
  updatePriority(params) {
    return http.post(
      `topic/updatePriority`, params, { contentType: 'application/json; charset=UTF-8' }
    )
  },
  /**
   * 查询
   * @param pms
   * @returns {Promise<*>}
   */
  async query(pms) {
    return await http.post('/topic/query', pms, {
      contentType: 'application/json; charset=UTF-8'
    });
  },
  /**
   * 状态
   */
  topicState() {
    return http.post('/dict/topicState')
  },
  /**
   * 使用状态
   */
  topicUsingState() {
    return http.post('/dict/topicUsingState')
  },
  /**
   * 类型
   */
  topicCategory(pms) {
    return http.post('/dict/topicCategory', pms)
  },
  /**
   * 类型
   */
  topicPageType(pms) {
    return http.post('/dict/topicPageType', pms)
  },
  /**
   * 获取菜单
   */
  getTabList(params) {
    return http.get('/menu/top', params);
  },
  /**
   * 操作日记记录
   */
  logInsert(params) {
    return http.post('/log/insert', params);
  },
  /**
   * pc正式链接
   */
  getPublishUrl(id) {
    return http.post(`/topic/${id}/getPublishUrl`);
  },

  // 保存cms自动生成商品展示组
  async saveExhibitionGroup(params) {
    const obj = {
      headers: {
        "isAdmin": true,
      },
      method: "post",
      url: "/cms/saveCmsSubjectProductExhibitionGroup",
      data: params,
    };
    return await http.putRequest(obj);
  },

  // 批量上传套餐
  uplodaSetMeal(params) {
    return http.post(
      AppWebsite + 'cms/importActivityPackage', params, {
      "Content-Type": "application/x-www-form-urlencoded",
      terminalType: 1,
      isadmin: true,
    }
    )
  },
  // 新弹框更新优先级
  async batchUpdatePriority(params) {
    return await http.post('/topic/dialogV2/batchUpdatePriority', params, {
      contentType: 'application/json; charset=UTF-8',
      isadmin: true,
    });
  },

  // 校验绑定店铺商品
  async checkBindShopCsu(pms) {
    const obj = {
      headers: {
        "isAdmin": true,
        "Content-Type": "application/x-www-form-urlencoded",
        "terminalType": 1
      },
      method: "post",
      url: "/cms/checkBindShopCsu",
      data: pms,
      transformRequest: [function (data) {
        let ret = '';
        for (let key in data) {
          ret += encodeURIComponent(key) + '=' + encodeURIComponent(data[key]) + '&'
        }
        return ret
      }]
    };
    return await http.putRequest(obj);
  },

  // 校验商品id/商品组id是否存在
  async checkBindCsuOrProductGroup(params) {
    const obj = {
      headers: {
        "isAdmin": true,
      },
      method: "post",
      url: "/cms/checkBindCsuOrProductGroup",
      data: params,
    };
    return await http.putRequest(obj);
  },
  // cms活动类型枚举
  async getActivityTypeEnum(params) {
    const obj = {
      headers: {
        "isAdmin": true,
        "Content-Type": "application/x-www-form-urlencoded",
      },
      method: "post",
      url: `/cms/activity/typeEnum?scenes=${params.scenes}`,
      data: params,
    };
    return await http.putRequest(obj);
  },
  // cms活动信息查询
  async getActivityInfos(params) {
    const obj = {
      headers: {
        "isAdmin": true,
      },
      method: "get",
      url: `/cms/activity/get?id=${params.id}&scene=${params.scene}`,
    };
    return await http.putRequest(obj);
  },
  async uploadShopInfo(params) {
    const obj = {
      method: "post",
      url: `/cms/shop/batchQueryShopInfo`,
      data: params,
    };
    return await http.putRequest(obj);
  },
  // uploadShopInfo(params) {
  //   return http.post(
  //     `/shop/batchQueryShopInfo`, params, { contentType: 'application/json; charset=UTF-8' }
  //   )
  // },
}
