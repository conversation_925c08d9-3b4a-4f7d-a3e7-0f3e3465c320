<template>
  <div>
    <!-- 新增 -->
    <el-dialog
      class="feed-scroll-dialog"
      :title="`${isEdit ? '编辑' : '新建'}`"
      :before-close="addDialogCancel"
      :visible.sync="addDialog"
    >
      <el-form
        label-position="right"
        ref="addRuleForm"
        :model="addForm"
        :rules="addRuleForm"
        size="small"
        :disabled="isInfo"
        label-width="100px"
        label-suffix="："
      >
        <el-form-item label="活动名称" prop="activityName">
          <el-input
            v-model="addForm.activityName"
            maxlength="20"
            size="mini"
            placeholder="请输入热词组名称，20个字符以内"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="指定人群" prop="crowdType">
          <el-radio-group
            v-model="addForm.crowdType"
            @change="changeCrowdType"
            style="width: 100%"
          >
            <el-radio :label="1" style="width: 100%"
              >该页面已选中人群</el-radio
            >
            <el-radio
              :label="2"
              style="width: 100%; margin-top: 10px"
              class="cowdtype-radio"
            >
              <div class="cowdtype-radio">
                <div>指定人群</div>
                <el-select
                  v-if="addForm.crowdType === 2"
                  style="margin-left: 10px"
                  v-model.trim="addForm.crowdValue"
                  :loading="selectLoading"
                  filterable
                  :filter-method="optionFilter"
                  placeholder="请输入人群id"
                  clearable
                  @clear="options = []"
                  @change="selectCrowd"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <div v-if="addForm.crowdType === 2" style="margin-left: 5px">
                  <!-- 展示人群包名称 -->
                </div>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="展示时间">
          <el-radio v-model="addForm.timeType" :label="1">固定时段</el-radio>
          <el-date-picker
            v-model="addForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :picker-options="{
              disabledDate: (time) => {
                const times =
                  new Date(new Date().toLocaleDateString()).getTime() +
                  1095 * 8.64e7 -
                  1;
                return (
                  time.getTime() < Date.now() - 8.64e7 || time.getTime() > times
                ); // 如果没有后面的-8.64e7就是不可以选择今天的
              },
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker
          ><br />
          <el-radio v-model="addForm.timeType" :label="2">周期循环</el-radio>
          <el-button
            style="margintop: 10px"
            @click="toloopcirculateTime"
            type="primary"
            size="mini"
            >配置</el-button
          >
          <br>
          <div v-for="(item,index) in addForm.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[addForm.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{addForm.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
        </el-form-item>
        <el-form-item label="选品方式" prop="selectProductType">
          <el-select
            size="small"
            @change="changeProductsType"
            v-model="addForm.selectProductType"
            placeholder="请选择"
          >
            <!-- <el-option label="指定商品" value="appointProduct" /> -->
            <el-option label="指定商品组" value="appointProductGroup" />
            <el-option label="系统自动" value="systemAuto" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="商品组ID"
          v-if="addForm.selectProductType === 'appointProductGroup'"
          prop="selectProductGroupId"
        >
          <el-input
            style="width: 200px"
            size="small"
            placeholder="请输入内容"
            v-model="addForm.selectProductGroupId"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" v-if="!isInfo" @click="addDialogConfirm"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <loopcirculateTime
      ref="loopcirculateTime"
      @loopcirculateTimeBack="loopcirculateTimeBack"
    ></loopcirculateTime>
  </div>
</template>


<script>
import loopcirculateTime from "../../../../components/loopcirculateTime.vue";
import swiperPoint from "views/apps/components/public/swiper-point";
import api from "api";
import { AppWebsite, getUrlParam } from "config";
export default {
  components: { swiperPoint, loopcirculateTime },
  props: {
    value: Object,
    topic: Object,
    categoryList: Array,
    isInfo:Boolean
  },
  data() {
    return {
      addRuleForm:{
        activityName: [
          { required: true, message: "请填写活动名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" },
        ],
        crowdType: [
          { required: true, message: "请选择指定人群", trigger: "change" },
        ],
        crowdValue: [
          { required: true, message: "请填写人群名称", trigger: "blur" },
        ],
        selectProductType:[
          { required: true, message: "请选择选品方式", trigger: "change" },
        ]
      },
      isEdit: false,
      addDialog: false,
      options: [],
      selectLoading: false,
      isShowHrefDialog: false,
      addForm: {
        activityId: "",
        activityName: "",
        crowdType: 1,
        crowdId: "",
        crowdValue: "",
        timeType: 1,
        validityTime: [],
        circulateTime: {},
        selectProductType: "",
        selectProductGroupId: "",
      },
      addFormSelectLink: "",
      bannerLocationList: [
        {
          id: 1,
          name: "第一帧",
        },
        {
          id: 2,
          name: "第二帧",
        },
        {
          id: 3,
          name: "第三帧",
        },
        {
          id: 4,
          name: "第四帧",
        },
        {
          id: 5,
          name: "第五帧",
        },
        {
          id: 6,
          name: "第六帧",
        },
        {
          id: 7,
          name: "第七帧",
        },
        {
          id: 8,
          name: "第八帧",
        },
        {
          id: 9,
          name: "第九帧",
        },
      ],
    };
  },
  methods: {
   
    open(row, isEdit) {
    
      this.isEdit = isEdit;
      if (this.isEdit) {
        let keys = Object.keys(row);
        for (let index = 0; index < keys.length; index++) {
          const key = keys[index];
          this.addForm[key] = row[key];
        }
      }
      this.addDialog = true;
      this.$nextTick(()=>{
        this.$refs['addRuleForm'].clearValidate()
      })
    },

    onSetLink(link) {
      this.addFormSelectLink = link.meta.page_url;
    },

    selectCrowd(e) {
      if (e) {
        this.addForm.crowdId = Number(this.options[0].value.trim());
        this.addForm.crowdValue = this.options[0].label;
      } else {
        this.addForm.crowdId = "";
        this.addForm.crowdValue = "";
      }
      this.$forceUpdate();
    },

    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    changeProductsType() {
      this.addForm.selectProductGroupId = "";
      // this.productGroupId = '';
    },
    //打开时间循环
    toloopcirculateTime() {
      this.$refs.loopcirculateTime.circulateTime=this.addForm.circulateTime
          this.$refs.loopcirculateTime.editInit()
      this.$refs.loopcirculateTime.showVisible = true;
    },
    changeCrowdType() {
      this.addForm.crowdId = "";
      this.addForm.crowdValue = "";
    },
    //循环时间回调
    loopcirculateTimeBack(data) {
      this.addForm.circulateTime = data;
    },
    addDialogCancel() {
      this.resetAddForm();
      this.$refs['addRuleForm'].clearValidate()
      this.addDialog = false;
    },

    hrefCancel() {
      this.addFormSelectLink = "";
      this.isShowHrefDialog = false;
    },
    hrefConfirm() {
      this.addForm.hrefUrl = this.addFormSelectLink;
      this.isShowHrefDialog = false;
    },
    resetAddForm() {
      this.addForm = {
        activityId: "",
        activityName: "",
        crowdType: 1,
        crowdId: "",
        crowdValue: "",
        timeType: 1,
        validityTime: [],
        circulateTime: {},
        selectProductType: "",
        selectProductGroupId: "",
      };
    },
    async addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        if(this.addForm.crowdType==2&&!this.addForm.crowdId){
          this.$message.warning("请添人群ID");
          return false;
        }
        if (this.addForm.timeType==2&&(!this.addForm.circulateTime||Object.keys(this.addForm.circulateTime).length==0||!this.addForm.circulateTime.circulateList||this.addForm.circulateTime.circulateList.length==0)) {
          this.$message.warning("请添加[周期循环] 时间段。");
          return false;
        }
        if(this.addForm.timeType==1&&(!this.addForm.validityTime||this.addForm.validityTime.length==0)){
          this.$message.warning("请添加时段");
          return false;
        }
        this.$emit('done', this.addForm);
        //this.addDialogCancel();
      });
    },
  },
};
</script>

<style scoped>
.cowdtype-radio {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>