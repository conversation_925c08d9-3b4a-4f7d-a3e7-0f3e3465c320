<template>
  <div class="topic-search">
    <!--模块背景设置-->
    <!-- <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="12">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :show-file-list="false"
              :before-upload="
                () => {
                  loading = true;
                  return true;
                }
              "
              :on-success="onUploadImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading"
                >上传背景图</el-button
              >
              <div slot="tip" class="el-upload__tip">
                支持类型：png/jpg/jpeg/gif
              </div>
            </el-upload>
          </div>
          <img v-if="content.bgImg" :src="content.bgImg" alt="" />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <div>
            <el-button @click="imgOnclick">清除背景图</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">背景色</span>
          <el-color-picker
            v-model="content.bgRes"
            size="mini"
            @change="onSelect"
          ></el-color-picker>
        </div>
      </el-col>
    </el-row> -->
    <el-row :gutter="20">
      <div class="title">信息配置</div>
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="活动id:">
            <el-input placeholder="请输入内容" v-model="queryParams.activityId">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="活动名称:">
            <el-input
              placeholder="请输入内容"
              v-model="queryParams.activityName"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="展示时间:">
            <el-date-picker
              v-model="queryParams.validityTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="人群id:">
            <el-select
              style="margin-left: 10px"
              v-model.trim="queryParams.crowdValue"
              :loading="selectLoading"
              filterable
              :filter-method="optionFilter"
              placeholder="请输入人群id"
              clearable
              @clear="options = []"
              @change="selectCrowd"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态:">
            <el-select
              v-model="queryParams.status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="item in status"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="carouselButton">
          <el-button type="primary" @click="addList" size="mini"
            >新增</el-button
          >
          <el-button type="primary" @click="searchList" size="mini"
            >查询</el-button
          >
          <el-button type="primary" @click="resetList" size="mini"
            >重置</el-button
          >
        </el-col>
      </el-form>
      <el-table
        :data="dataList"
        size="mini"
        class="tableBox"
        style="margin: 0 0 20px"
        ref="tableBox"
        :row-key="(row) => row.id"
      >
        <el-table-column
          label="id"
          width="150"
          prop="activityId"
        ></el-table-column>
        <el-table-column
          label="活动名称"
          width="150"
          prop="activityName"
        ></el-table-column>
        <el-table-column label="顺序">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.sort"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            @blur="changeSort(scope,dataList)"
            @keyup.enter.native="changeSort(scope,dataList)"
          ></el-input>
        </template>
      </el-table-column>
        <!-- <el-table-column
          label="顺序"
          width="150"
          prop="sort"
        >
      <template slot-scope="scope">
        <el-popover placement="top" trigger="click">
              <div class="priorityCheck" style="text-align: center;">
                <el-input size="mini" style="width: 160px;" maxlength="11" @input="popoverValue = popoverValue.replace(/[^\d]/g, '')"
                  v-model="popoverValue" />
                <el-button style="width: 150px;height: 30px;text-align: center !important;" type="primary"
                  icon="el-icon-check" size="mini" @click="changePriority(scope.$index, scope.row)"></el-button>
                <el-button style="width: 150px;height: 30px;text-align: center;" type="info" icon="el-icon-close"
                  size="mini" @click="hidePriority()"></el-button>
              </div>
              <div  style="width: 150px !important;height: 20px;cursor: pointer;" size="mini" slot="reference"
                @click="setPriority(scope.row.sort)">{{ scope.row.sort }}</div>
            </el-popover>
      </template>
      </el-table-column> -->
        <el-table-column label="人群" show-overflow-tooltip width="150">
          <template slot-scope="scope">
            <p v-if="scope.row.crowdType == 2">
              {{ scope.row.crowdId + "/" + scope.row.crowdValue || "该页面已选人群" }}
            </p>
            <p v-else>该页面已选中人群</p>
          </template>
        </el-table-column>
        <el-table-column label="展示时间" width="300">
          <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
            </div>
            <div v-else> 
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div>
              {{
                ["未开始", "上线", "已结束", "下线"][scope.row.status - 1] ||
                "-"
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
           <div style="display: flex;">
            <el-button
              size="mini"
              v-if="scope.row.status == 1 ||scope.row.status == 3 || scope.row.status == 4"
              @click="toEdit(scope.row, scope.$index)"
              type="text"
              >编辑
            </el-button>
            <el-button
              size="mini"
              v-if="scope.row.status == 4"
              @click="toRemove(scope.row)"
              type="text"
              >删除</el-button
            >
           </div>
            <div style="display: flex;margin-top: 10px;">
              <el-button
              size="mini"
              v-if="scope.row.status == 4"
              @click="online(scope)"
              type="text"
              >上线</el-button
            >
            <el-button
              size="mini"
              @click="outline(scope)"
              v-if="scope.row.status == 2"
              type="text"
              >下线</el-button
            >
            <el-button size="mini" v-if="scope.row.status == 2" @click="toEdit(scope.row, scope.$index,true)" type="text">详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <lrGoodsPositionAlert
      ref="lrGoodsPositionAlert"
      :isEdit="isEdit"
      :topic="topic"
      :isInfo="isInfo"
      @done="lrGoodsPositionAlertDone"
    ></lrGoodsPositionAlert>
  </div>
</template>

<script>
import lrGoodsPositionAlert from "./components/lr_goods_position_alert.vue";
import base from "../../base";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";

export default {
  name: "lrGoodsPosition",
  extends: base,
  components: { swiperPoint, lrGoodsPositionAlert },
  contentDefault: {
    bgImg: "",
    bgRes: "",
    list: [],
  },
  props: {
    core: Object,
  },
  data() {
    return {
      isInfo:false,
      popoverValue:"",
      isEdit: false,
      loading: false,
      queryParams: {
        activityId: "",
        activityName: "",
        validityTime: [],
        crowdValue: "",
        crowdId: "",
        status: "",
      },
      options: [],
      selectLoading: false,
      status: [
        { id: "", name: "全部" },
        { id: 1, name: "未开始" },
        { id: 2, name: "上线" },
        { id: 3, name: "已结束" },
        { id: 4, name: "下线" },
      ],
      addForm: {
        frontColor: "",
      },
       dataList: [],
      currentDataIndex: undefined,
    };
  },
  mounted() {
    this.initData();
    this.initDataStatus();
    // this.rowDrop()
  },
  // computed:{
  //   dataList(){
  //     return this.content.list
  //   }
  // },

  methods: {
    async changePriority(index, row,newData) {
       console.log(index, row,newData)
       this.sort(row.activityId,this.popoverValue)
    },
    setPriority(value) {
      this.popoverValue = value
    },
    hidePriority() {
      document.body.click()
    },
    changeSort(scope,val) {
      let dataLists=val
      let ind = scope.$index;
      if (dataLists[ind].sort <= 0) {
        this.$message.warning("请输入大于0的数字！");
        return;
      }
      if (dataLists[ind].sort >= dataLists.length) {
        dataLists.splice(dataLists.length, 0, dataLists[ind]);
        dataLists.splice(ind, 1);
      } else {
        if (dataLists[ind].sort > ind) {
          dataLists.splice(dataLists[ind].sort, 0, dataLists[ind]);
          dataLists.splice(ind, 1);
        } else {
          dataLists.splice(
            dataLists[ind].sort - 1,
            0,
            dataLists[ind]
          );
          dataLists.splice(ind + 1, 1);
        }
      }
      dataLists.forEach((item, index) => {
        item.sort = index + 1;
      });
    },
    sort(id,newData){
        if(newData<=0||newData>9999){
          this.$message.error("排序只允许1-9999")
          this.hidePriority()
          return
        }
          let nextData={}
          nextData={
            activityId:id,
            sort:newData
          }
          let nextDatas;
              do{
                nextDatas= this.content.list.find(item=>nextData.sort==item.sort)
                if(nextDatas){
                  nextDatas=JSON.parse(JSON.stringify(nextDatas))
                }
                let findData= this.content.list.find(item=>nextData.activityId==item.activityId)
                findData.sort=nextData.sort
                if(nextDatas){
                nextData=nextDatas
                nextData.sort=Number(nextData.sort)+1
                }
              }
              while(nextDatas)
          this.hidePriority()
    },
    onSelect(val) {
      if (val) {
        this.content.bgRes = val;
      } else {
        this.content.bgRes = "#ffffff";
      }
    },
    initDataStatus() {
      this.dataList = this.setStatusInitList(this.dataList);
    },
    setStatusInitList(data) {
      if (!data) {
        return;
      }
      data.forEach((item, index) => {
        // if(item.status==4){
        //   return
        // }
        // // item.sort = index + 1;
        this.$set(item, "sort", index + 1);
        // // 1:"月 ",2:"周 ",3:"日 "
        // if (item.status != 4) {
          if (item.timeType == 2) {
            // if (Array.isArray(item.circulateTime.circulateList)) {
            //   let _date = new Date().toLocaleTimeString("en-US", {
            //     hour12: false,
            //   });
            //   let dateTime = new Date().getTime();
            //   if (item.circulateTime.circulateType == 3) {
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       }
            //       else if (_date >= element.selectTimeData[1]) {
            //         item.status = 3; //已结束
            //       }
            //       else if(_date<element.selectTimeData[0]) {
            //          item.status = 1; //未开始
            //       }
            //     });
            //   }
            //   if (item.circulateTime.circulateType == 1) {
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         new Date().getDate() == element.weekOrday &&
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       } else if (
            //         dateTime > new Date(element.selectTimeData[1]).getTime()
            //       ) {
            //         item.status = 3; //已结束
            //       } else if(_date<element.selectTimeData[0]) {
            //          item.status = 1; //未开始
            //       }
            //     });
            //   }
            //   if (item.circulateTime.circulateType == 2) {
            //     const dayOfWeek = [
            //       "周日",
            //       "周一",
            //       "周二",
            //       "周三",
            //       "周四",
            //       "周五",
            //       "周六",
            //     ][new Date().getDay()];
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         dayOfWeek == element.weekOrday &&
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       } else if (
            //         dateTime > new Date(element.selectTimeData[1]).getTime()
            //       ) {
            //         item.status = 3; //已结束
            //       } else if(_date<element.selectTimeData[0]) {
            //          item.status = 1; //未开始
            //       }
            //     });
            //   }
            // }
            if(item.status!=4){
              item.status=2
            }
          } else {
            if (new Date() * 1 < new Date(item.validityTime[0]) * 1) {
               item.status = 1; // 未开始
            } else if (
              new Date() * 1 > new Date(item.validityTime[0]) * 1 &&
              new Date() * 1 < new Date(item.validityTime[1]) * 1&&item.status!=4
            ) {
              item.status = 2; //上线
            } else if (new Date() * 1 > new Date(item.validityTime[1]) * 1) {
              item.status = 3;
            } else {
              item.status = 4;
            }
          }
        // }
      });
      return data;
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.$set(this.content, "bgImg", res.data.url);
    },
    imgOnclick() {
      this.content.bgImg = null;
    },
    toEdit(val, index,isInfo) {
            if(isInfo){
        this.isInfo=true
      }else{
         this.isInfo=false
      }
      this.currentDataIndex = index;
      this.isEdit = true;
      this.$refs.lrGoodsPositionAlert.open(val, true);
    },
    toRemove(data) {
      let _self = this;
      return function () {
        _self.content.list.splice(
          _self.content.list.findIndex(
            (item) => item.activityId == data.activityId
          ),
          1
        );
        // _self.dataList.splice(_self.dataList.findIndex((item) => item.activityId == data.activityId), 1)
        _self.initData();
        _self.initDataStatus();
        _self.$message({
          type: "success",
          message: "删除成功!",
        });
      }.confirm(_self)();
    },
    online(scope) {
      this.$confirm("确定要执行上线操作吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        //this.content.list[scope.$index].status = 2;
        this.content.list.find(item=>item.activityId==scope.row.activityId).status=2
        this.$message.success("操作成功！");
        this.initData();
      });
    },
    outline(scope) {
      this.$confirm("确定要执行下线操作吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.content.list.find(item=>item.activityId==scope.row.activityId).status=4
        this.$message.success("操作成功！");
        this.initData();
      });
    },
    addList() {
      this.isEdit = false;
      this.isInfo=false
      this.$refs.lrGoodsPositionAlert.open();
    },
    searchList() {
      this.dataList = this.content.list;
      if (Array.isArray(this.queryParams.validityTime)&&this.queryParams.validityTime.length) {
        this.dataList = this.dataList.filter((item, index) => {
          return new Date(this.queryParams.validityTime[0]) * 1 >= new Date(item.validityTime[0]) * 1 && new Date(this.queryParams.validityTime[1]) * 1 <= new Date(item.validityTime[1]) * 1;
        })
      }
      if (this.queryParams.activityId) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.activityId == item.activityId;
        })
      }
      if (this.queryParams.classType) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.classType == item.classType;
        })
      }
      
      if (this.queryParams.activityName) {
        this.dataList = this.dataList.filter((item, index) => {
          //return item.activityName.indexOf(this.queryParams.activityName) > -1;
          return new RegExp(this.queryParams.activityName).test(item.activityName)
        })
      }
      if (this.queryParams.crowdValue) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.crowdValue == item.crowdId;
        })
      }
      if (this.queryParams.status) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.status == item.status;
        })
      }
      this.initDataStatus();



    },
    lrGoodsPositionAlertDone(val) {
      if (val.selectProductType === "systemAuto") {
        this.confirm(val);
      } else {
         this.checkBindCsuOrProductGroup(val);
      }
    },
    // 校验绑定商品
    async checkBindCsuOrProductGroup(addForm) {
      this.confirm(addForm);
      return true
      let canSave = true;
      (addForm.selectProducts || []).forEach((item) => {
        if (isNaN(item) || item < 0) {
          canSave = false;
        }
      });
      if (!canSave) {
        this.$message.error("指定商品ID只能输入数字");
        return;
      }
      const params = {
        type: addForm.selectProductType === "appointProduct" ? 1 : 2,
        exhibitionId: addForm.selectProductGroupId,
        // csuIds: this.addForm.selectProducts.filter(i => i).map(Number),
      };
      const result = await api.topic.checkBindCsuOrProductGroup(params);
      if ((result.data.data || {}).checkResult) {
        this.$message.success("绑定成功");
        this.confirm(addForm);
      } else {
        if (addForm.selectProductType === "appointProduct") {
          this.$message.error(
            `以下商品id绑定失败：${(
              (result.data.data || {}).failureCsuIds || []
            ).join()}`
          );
          return
        } else {
          this.$message.error(result.data.msg);
          return
        }
      }
    },
    confirm(addForm) {
      
      // //校验重复
      // if(this.isEdit){
      //   arr.splice(arr.findIndex(item => item.activityId == addForm.activityId),1)
      // }
      // let classFlag = false;
      // if (arr.findIndex(item => item.crowdId == addForm.crowdId) > -1 
      // || (arr.findIndex(item => item.crowdType == 1) > -1 && !this.core.crowdId)
      // || (arr.length && addForm.crowdType == 1 && !this.core.crowdId)
      // ) {
      //   if (addForm.timeType == 1) {
      //     const start_form = new Date(addForm.validityTime[0]).getTime();          
      //     const end_form = new Date(addForm.validityTime[1]).getTime();          
      //     arr.forEach(item => {
      //       const start = new Date(item.validityTime[0]).getTime();
      //       const end = new Date(item.validityTime[1]).getTime();
      //       if (start_form <= start && end_form >= end) {
      //         classFlag = true;
      //       } else if ((start_form >= start && start_form <= end) || (end_form >= start && end_form <= end)) {
      //         classFlag = true;
      //       }
      //     })
      //   } else if (addForm.timeType == 2) {
      //     // 1:周 2:月 3:日
      //     arr.forEach(item => {
      //       if(Array.isArray(addForm.circulateTime.circulateList)){
      //         // let _date =  new Date().toLocaleTimeString('en-US', {hour12: false});  
      //         if (item.timeType == 2) {
      //           let _date = addForm.circulateTime.circulateList[0].selectTimeData;     
      //           if(addForm.circulateTime.circulateType==3){
      //             item.circulateTime.circulateList.forEach(element => {
      //               if ((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1])) {
      //                 classFlag = true;
      //               }
      //             });
      //           }
      //           if(item.circulateTime.circulateType==1 || item.circulateTime.circulateType==2){
      //             item.circulateTime.circulateList.forEach(element => {
      //               if (addForm.circulateTime.circulateList[0].weekOrday==element.weekOrday&&((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1]))) {
      //                 classFlag = true;
      //               }
      //             });
      //           }
      //         }
      //       }
      //     })
      //   }
      // }
      // if (classFlag) {
      //   this.$message.error("展示时间不能包含列表已存在数据时间！");
      //   return;
      // }
      let arr = [...this.content.list];
      if (this.isEdit) {
        //this.$set(this.content.list, this.currentDataIndex, addForm);
        this.$set(this.content.list,this.content.list.findIndex(item=>item.activityId==addForm.activityId),addForm)
       // this.content.list[this.content.list.findIndex(item=>item.activityId==addForm.activityId)]=addForm
      } else {
        let id = 0;
      id = Math.floor(Math.random() * 90000) + 10000;
      this.$set(addForm, "activityId", id);
      if (this.content.list.findIndex((item) => item.activityId == id) > -1||!addForm.activityId) {
        this.$message("id错误，请重新添加！");
        return;
      }
   
    
        arr.splice(0, 0, addForm);
        this.$set(this.content, "list", arr);
        this.initData();
        this.initDataStatus();
      }
      this.$refs.lrGoodsPositionAlert.addDialogCancel();
      this.$message.success(`${this.isEdit ? "编辑" : "添加"}成功！`);
     // this.sort(id,1)
      this.resetList()
      //this.searchList()

    },
    initData() {
      this.dataList = this.content.list;
    },
    resetList() {
      this.dataList = this.content.list;
      this.resetQueryParams();
      this.initDataStatus();
    },
    resetQueryParams() {
      this.queryParams = {
        activityId: "",
        activityName: "",
        validityTime: [],
        banner_location: "",
        sort: "",
        crowd_value: "",
        status: "",
        crowdName: "",
      };
      this.searchList()
    },

    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    selectCrowd(e) {
      if (e) {
        this.queryParams.crowdId = Number(this.options[0].value.trim());
        this.queryParams.crowdValue = this.options[0].label;
      } else {
        this.queryParams.crowdId = "";
        this.queryParams.crowdValue = "";
      }
      this.$forceUpdate();
    },
  },
};
</script>

<style scoped>
.title {
  text-align: left;
  line-height: 30px;
  background-color: #f2f2f2;
  margin: 10px 0;
  padding-left: 10px;
}

.carouselButton {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}
.is-required .el-form-item__label::after {
  content: "*";
  color: #ff0000;
  margin-left: 4px;
}

.avatar {
  width: 150px !important;
  height: 150px !important;
  display: block;
}
</style>