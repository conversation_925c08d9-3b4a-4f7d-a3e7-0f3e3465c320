import api from 'api'
import * as types from '../mutation-types'

// initial state
const state = {
  currentAccount: {},
  currentMenuList: [],
}

// getters
const getters = {
  corp: state => state.corp,
  currentAccount: state => state.currentAccount,
  currentMenuList: state => state.currentMenuList
}

// actions
const actions = {
  async updateCurrentMember ({ commit, state }, account) {
    commit(types.SET_CURRENT_ACCOUNT, account)
  },
  async updateCurrentMenuList ({ commit, state }, menuList) {
    commit(types.SET_CURRENT_MENULIST, menuList)
  },
  reset ({commit}) {
    commit(types.RESET_CORP)
  }
}

// mutations
const mutations = {
  [types.SET_CURRENT_ACCOUNT] (state, account) {
      state.currentAccount = account
  },
  [types.SET_CURRENT_MENULIST] (state, menuList) {
      state.currentMenuList = menuList
  },
  [types.RESET_CORP] (state) {
      state.currentAccount = {};
      state.currentMenuList = []
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
