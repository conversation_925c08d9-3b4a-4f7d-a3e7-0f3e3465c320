<template>
  <!-- 添加内容弹层 -->
  <el-dialog class="banner-dialog" width="70%" v-loading="uploadLoading" title="关联店铺" :visible.sync="visible" :before-close="closeEditContent">
    <div style="margin: 10px 0">
      <span style="fontSize: 15px">店铺组名称：（不在用户侧展示）</span>
      <el-input style="width: 300px" size="small" v-model="editData.groupShopsName" />
    </div>
    <div style="margin: 10px 0">
      <span style="fontSize: 15px">人群范围：</span>
      <el-radio-group v-model="editData.crowdType" @change="changeCrowdType">
        <el-radio :label="1">全部人群</el-radio>
        <el-radio :label="2">指定人群</el-radio>
      </el-radio-group>
    </div>
    <div style="margin: 10px 0" class="shop-dialog-cur-row">
      <span style="fontSize: 15px">一键设置有效时间：</span>
      <div class="shop-dialog-cur-row">
        <el-date-picker
              v-model="pageListTime"
              type="datetimerange"
              :picker-options="pickerOptions"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="left"
              @change="setPageListTime"
            ></el-date-picker>
      </div>
    </div>
    <div style="margin: 10px 0; display: flex;">
      <span style="fontSize: 15px">店铺范围：</span>
      <div class="shop-dialog-cur-row">
        
        <el-button class="uploadBtn" type="primary" size="mini">导入店铺<input type="file" ref="uploadinput" @change="handleFileUpload" accept=".xlsx, .xls"/></el-button>
        
        <el-button type="primary" size="mini" style="margin-left: 10px;" @click="downLoadExcel">下载模板</el-button>
      </div>
    </div>
    <div v-if="editData.crowdType===2">
      <span style="fontSize: 15px">指定人群：</span>
      <el-select
        v-model="editData.crowdValue"
        :loading="selectLoading"
        filterable
        :filter-method="optionFilter"
        placeholder="请输入人群id"
        clearable
        @clear="options = []"
        @change="selectCrowd"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <!-- <el-autocomplete
        style="width: 300px"
        class="inline-input"
        size="small"
        v-model.trim="editData.crowdValue"
        :fetch-suggestions="querySearchCrowd"
        placeholder="请输入人群id"
        :trigger-on-focus="false"
        @select="handleSelectCrowd"
        @input="changeCrowdValue"
      ></el-autocomplete> -->
    </div>
    <div slot="footer" class="dialog-footer">
      <!-- el-table放到footer里的原因：Dialog 的内容是懒渲染的，但footer是实时的。放外面的话拿不到DOM无法操作拖拽 -->
      <el-table :data="editData.shopsList" size="mini" :row-key="(row) => row.shopCode" style="margin: 0 0 20px">
        <el-table-column label="序号" width="50" type="index" />
        <el-table-column label="店铺logo">
          <template slot-scope="scope">
            <img
              v-if="scope.row.appLogoUrl"
              :src="scope.row.appLogoUrl"
              alt="图"
              class="title-image"
            />
            <i v-else class="el-icon-circle-plus-outline no-img"></i>
          </template>
        </el-table-column>
        <el-table-column label="店铺名称">
          <template slot-scope="scope">
            <el-input v-model="scope.row.customShopName" size="mini" @input="changeInput" />
          </template>
        </el-table-column>
        <el-table-column label="展示商品" v-if="!hideDisplayGoods">
          <template slot-scope="scope">
            <div ref="closepopover">
              <el-popover placement="top" trigger="click">
                <div class="priorityCheck">
                  <el-input size="mini" @input="popoverValue = !bindCsuOrProductGroup ? popoverValue.replace(/[^\d]/g, '') : popoverValue" v-model="popoverValue" />
                  <el-button type="primary" icon="el-icon-check" size="mini" @click="submitCsuId(scope.$index,scope.row)"></el-button>
                  <el-button type="info" icon="el-icon-close" size="mini" @click="hidePopover()"></el-button>
                </div>
                <el-button v-if="scope.row.csuId || scope.row.productGroupId" type="text" size="mini" slot="reference" @click="showCsuId(scope.row.csuId || scope.row.productGroupId)">{{ scope.row.csuId || scope.row.productGroupId }}</el-button>
                <el-button v-else type="text" size="mini" slot="reference" @click="showCsuId()" style="color:red;">点击输入</el-button>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="引导语">
          <template slot-scope="scope">
            <el-input v-model="scope.row.customMessage" size="mini" />
          </template>
        </el-table-column> -->
        <el-table-column label="店铺状态">
          <template slot-scope="scope">
            <b v-if="scope.row.status === 2" style="color: #67C23A">已上线</b>
            <b v-if="scope.row.status === 1" style="color: red">待上线</b>
            <b v-if="scope.row.status === 3" style="color: red">已下线</b>
            <b v-if="scope.row.status === 4" style="color: red">已关闭</b>
          </template>
        </el-table-column>
        <el-table-column label="展示状态">
          <template slot-scope="scope">
            <span v-html="format_text(scope.row.timevalue)"></span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="有效时间" width="450">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.timevalue"
              @input="changeInput"
              type="datetimerange"
              :picker-options="pickerOptions"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="left"
            ></el-date-picker>
            <el-button
              type="danger"
              @click="del_store(scope.row.shopCode)"
              icon="el-icon-delete"
              circle
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
      <allStore
        ref="isAllStore"
        @emitStore="handle_store"
        :stores="editData.shopsList"
        :params="{
          branchCode: topic.branchCode,
          page_type: topic.page_type,
          content: content,
          componentName: 'carefullySelectedShops'
        }"
      />
      <div style="marginTop: 20px">
        <el-button size="small" @click="closeEditContent">取消</el-button>
        <el-button size="small" type="primary" @click="confirmDialog">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
  import api from "api";
  import Sortable from 'sortablejs';
  import { AppWebsite } from "config";
  import * as XLSX from 'xlsx';
  import { uploadShopInfo } from "@/api/topic";
  export default {
    name: 'shopDialog',
    props:[ "shopItemData", "topic", "content", "visible", "bindCsuOrProductGroup", "hideDisplayGoods" ],
    data() {
      return {
        uploadLoading: false,
        excelData: [],
        pageListTime:'',
        popoverValue: '',
        sortable: null,
        selectLoading: false,
        options: [],
        editData: {
          groupShopsName: '',
          crowdType: 1,
          crowdValue: '',
          crowdId: '',
          shopsList: [],
        },
        pickerOptions: {
          shortcuts: [
            {
              text: "未来一周",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来三个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来六个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一年",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                picker.$emit("pick", [start, end]);
              }
            }
          ]
        },
      };
    },
    mounted () { 
      this.rowDrop();
      if(this.isEdit) {
        this.editData = this.shopItemData;
      }
    },
    computed: {
      isEdit() {
        return Object.keys(this.shopItemData).length > 0 ? true : false;
      }
    },
    methods: {
      handleFileUpload (event) {
        console.log(event,'evnet')
        const file = event.target.files[0]  //获取上传的文件

        if (file) {
          this.uploadLoading = true;
          const reader = new FileReader() //创建FileReader对象，说明：它通常用于处理本地文件的读取操作，例如读取文本文件、图像文件、或像前面示例中的Excel文件一样的二进制文件

          reader.onload = async (event) => {   // 设置事件监听器
            const data = event.target.result
            /*
            使用XLSX库的XLSX.read方法解析文件数据
          'array'（默认值）: 这是最常见的类型。它用于读取二进制数据数组，通常是通过 FileReader 读取的文件数据。这是用于读取二进制格式文件，如 Excel 文件的一种常见类型。
          'binary': 用于读取二进制字符串。这可以用于将二进制数据传递为二进制字符串。
          'base64': 用于读取 base64 编码的数据。如果你有一个 base64 编码的文件内容，你可以使用这个类型来读取它。
          'buffer': 用于 Node.js 环境，可以读取 Node.js Buffer 对象中的数据。
          'file': 用于在浏览器中直接读取文件对象。这个选项通常用于读取用户选择的文件而不需要先通过 FileReader 将其读取为数组。
            不同的 type 选项允许你根据数据的来源和格式来选择适当的类型，以便 XLSX 库能够正确解析数据。在大多数情况下，使用 'array' 是最常见的，因为它适用于通过 FileReader 读取的文件数据，这是处理文件上传的典型用例。
            * */
            const workbook = XLSX.read(data, { type: 'array' })

            const firstSheetName = workbook.SheetNames[0]
            const worksheet = workbook.Sheets[firstSheetName]
            const fileData = XLSX.utils.sheet_to_json(worksheet);
            
            if (this.checkExcelConfig(fileData)) {
              var shopCode = [];
              fileData.forEach(item => {
                const val = item['*店铺编码'] || item['店铺编码'] || '';
                if (val) {
                  shopCode.push(val);
                }
              })
              let params = {
                shopCode: shopCode.join(",")
              }
              const h = this.$createElement;
              uploadShopInfo(params).then(res => {
                if (res && res.data.code === 1000) {
                  const { failureNum, successNum, successShopDetailList, failureSHopDetailList } = res.data.data.data;
                  let successArr = [];
                  successShopDetailList.map(item => {
                    item.customShopName = item.showName;
                    if (!successArr.filter(k => k.shopCode == item.shopCode).length) {
                      successArr.push(item);
                    }
                  })
                  let shopArr = [];
                  this.editData.shopsList = successArr;
                  this.editData.shopsList.forEach(item => {
                    if (!shopArr.filter(k => k.shopCode === item.shopCode).length) {
                      shopArr.push(item);
                    }
                  })
                  this.editData.shopsList = shopArr;
                  this.uploadLoading = false;
                  this.$msgbox({
                    title: '上传文件反馈',
                    message: h('p', null, [
                      h('div', null, `共成功上传${successNum}个店铺，失败${failureNum}条数据`),
                      h('span', `${failureNum ? '下载失败文件：' : ''}`),
                      failureNum ? h('a', { on: {click: () => this.downloadFailExcel(failureSHopDetailList)} , style: 'color:#4183d5;cursor: pointer;' }, `上传店铺失败文件.xlsx`) : '']),
                    confirmButtonText: '确定',
                  }).then(() => {
                    this.$refs.isAllStore.getList();
                  }).catch(() => {
                    this.$refs.isAllStore.getList();
                  });
                } else {
                  this.$alert(res.message || '导入失败，请重新上传！', { type: 'error' });
                  this.uploadLoading = false;
                }
              })
            }
          }
          reader.readAsArrayBuffer(file);
          this.$refs.uploadinput.value = null;
        }
      },
      downLoadExcel() {
        let header = ['店铺编码'];
        let name = '批量导入店铺模板.xlsx';
        const tabelData = [header];
        const ws = XLSX.utils.aoa_to_sheet(tabelData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb,ws,name)
        XLSX.writeFile(wb,`${name}`)
      },
      downloadFailExcel(data) {
        let header = [{name: '店铺编码', key: 'shopCode'}, {name: '错误原因', key: 'failMessage'}];
        let name = '上传店铺失败文件.xlsx';
        let tableHeader = [];
        header.forEach(item => tableHeader.push(item.name));
        const tableData = [tableHeader];
        data.forEach(item=>{
          const rowData = [];
          header.forEach(itemH=>{
            rowData.push(item[itemH.key]);
          })
          tableData.push(rowData);
        })
        console.log(tableData, 'downLoadData');
        const ws = XLSX.utils.aoa_to_sheet(tableData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb,ws,name)
        XLSX.writeFile(wb,`${name}`)
      },
      checkExcelConfig(exceldata) {
        if (exceldata && exceldata.length) {
          let excelHeaderKeys = Object.keys(exceldata[0]);
          if (excelHeaderKeys && excelHeaderKeys[0].indexOf("店铺编码") < 0) {
            this.$message.warning('模板错误!');
            this.uploadLoading = false;
            return false;
          }
          let reg = /\s/;
          exceldata.forEach(item => {   //判断店铺列表是否包含空格，如果包含，则替换为空；
            for(let i in item) {
              if (i.indexOf("店铺编码") > -1) {
                if (item[i]) {
                  if (reg.test(item[i])) {
                    item[i].replace(reg, "");
                  }
                } else {
                  // delete item;
                }
              }
            }
          })
        }
        return true;
      },
      setPageListTime(){
        if(!this.pageListTime){
          this.$message.warning('请先点选择时间');
          return
        }
        let shoplistCopy = this.editData.shopsList?_.cloneDeep(this.editData.shopsList):[];

        for (let index = 0; index < shoplistCopy.length; index++) {
          const element = shoplistCopy[index];
          element.timevalue = this.pageListTime
        }
        this.editData.shopsList = [];
        this.editData.shopsList = shoplistCopy;
      },

      // changeCrowdValue(e) {
      //   if (!e) {
      //     this.editData.crowdId = '';
      //   }
      //   this.$forceUpdate();
      // },
      rowDrop() {
        const _this = this;
        const tbody = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[1];
        Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
          ghostClass: "sortable-ghost",
          onEnd: evt => {
            const currRow = ((_this.editData || {}).shopsList || []).splice(evt.oldIndex, 1)[0];
            ((_this.editData || {}).shopsList || []).splice(evt.newIndex, 0, currRow);
          }
        });
      },
      async optionFilter(val) {
        this.selectLoading = true;
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          this.selectLoading = false;
          this.options = [{
            label: data.name,
            value: val,
          }]
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCrowd(e) {
        if (e) {
          this.editData.crowdId = Number((this.options[0].value).trim());
          this.editData.crowdValue = this.options[0].label;
        } else {
          this.editData.crowdId = '';
          this.editData.crowdValue = '';
        }
        this.$forceUpdate();
      },
      // async querySearchCrowd(queryString, cb) {
      //   const pms = {
      //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
      //     dataType: "json",
      //     data: {},
      //     head: {
      //       "Content-Type": "application/json;charset=UTF-8"
      //     }
      //   };
      //   const res = await api.proxy.post(pms);
      //   if (res.success) {
      //     const { data } = res;
      //     cb([{
      //       id: queryString,
      //       value: data.name || ""
      //     }]);
      //     return false;
      //   }
      // },
      // handleSelectCrowd(item) {
      //   this.editData.crowdId = item.id;
      // },
      format_text(data) {
        if (!data) {
          return "<b style='color: red'>请设置时间</b>";
        }
        if (!data.length) {
          return "<b style='color: red'>请设置时间</b>";
        }
        const _date = new Date().getTime();
        const start = new Date(data[0]).getTime();
        const end = new Date(data[1]).getTime();
        if (_date <= end && _date >= start) {
          return "<b style='color: #67C23A'>展示中</b>";
        } else if (_date < start) {
          return `<b style='color: #000000'>即将在${new Date(
            start
          ).toLocaleDateString()}展示</b>`;
        } else {
          return "<b style='color: #000000'>已过期</b>";
        }
      },
      changeInput() {
        this.$forceUpdate();
      },
      changeCrowdType() {
        this.editData.crowdId = '';
        this.editData.crowdValue = '';
      },
      showCsuId(value) {
        this.popoverValue = value || '';
      },
      hidePopover() {
        document.body.click();
      },
      async bindCsuOrProductGroupFn(row) {
        if (this.popoverValue) {
          const params = {
            type: 2,
            exhibitionId: this.popoverValue,
            csuIds: [],
          }
          const result = await api.topic.checkBindCsuOrProductGroup(params);
          if (((result.data || {}).data || {}).checkResult) {
            this.editData.shopsList.forEach((val, index) => {
              if (val.shopCode === row.shopCode) {
                this.$set(row, 'productGroupId', this.popoverValue)
              }
            });
            this.hidePopover();
          } else {
            this.$message.error((result.data || {}).msg);
          }
        } else {
          this.editData.shopsList.forEach((val, index) => {
            if (val.shopCode === row.shopCode) {
              this.$set(row, 'productGroupId', '')
            }
          });
          this.hidePopover();
        }
      },
      async submitCsuId(index, row) {
        if (this.bindCsuOrProductGroup) {
          // this.$set(row, 'productGroupId', this.popoverValue)
          this.bindCsuOrProductGroupFn(row);
          return false;
        }
        // return
        if(this.popoverValue) {
          let res = await api.topic.checkBindShopCsu({ "shopCode": row.shopCode, "csuId": this.popoverValue || '' });
          if (((res.data || {}).data || {}).checkResult) {
            this.editData.shopsList.forEach((val, index) => {
              if (val.shopCode === row.shopCode) {
                this.$set(row, 'csuId', this.popoverValue)
              }
            });
            this.hidePopover();
          }
          else { this.$message.error((res.data || {}).msg); }
        } else {
          this.editData.shopsList.forEach((val, index) => {
            if (val.shopCode === row.shopCode) {
              this.$set(row, 'csuId', '')
            }
          });
          this.hidePopover();
        }
      },
      handle_store(data) {
        if (data.type === "add") {
          this.editData.shopsList.push(Object.assign(data.data, { timevalue: null, customShopName: data.data.showName }));
        } else {
          let cur_index = 0;
          this.editData.shopsList.forEach((val, index) => {
            if (val.shopCode === data.data.shopCode) {
              cur_index = index;
            }
          });
          this.editData.shopsList.splice(cur_index, 1);
        }
      },
      del_store(shopCode) {
        const text = `是否取消该关联`;
        this.$confirm(text, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "info"
        }).then(() => {
          let cur_index = 0;
          this.editData.shopsList.forEach((val, index) => {
            if (val.shopCode === shopCode) {
              cur_index = index;
            }
          });
          this.editData.shopsList.splice(cur_index, 1);
          this.$message({
            type: "success",
            message: "成功!"
          });
        })
        .catch(() => {});
      },
      confirmDialog() {
        
        if (this.editData.crowdType === 2 && !this.editData.crowdId) {
          this.$message.error('请选择正确的人群');
          return;
        }
        let hasEmpty = false;
        this.editData.shopsList.forEach((item) => {
          if(!item.customShopName || !item.timevalue) {
            this.$message.error('请检查店铺名称/有效时间是否填写完整');
            hasEmpty = true;
          }
          // if(!item.csuId&&!item.productGroupId) {
          //   this.$message.error('请将展示商品填写完整');
          //   hasEmpty = true;
          // }
        })
        
        if(!hasEmpty) {
          this.closeEditContent();
          this.$emit('confirmEditContent', this.editData)
        }
      },
      closeEditContent() {
        this.$parent.closeEditContent();
      },
    }
  };
 
</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .banner-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }
    .image {
      width: 64px;
      height: 64px;
    }
  }
  .title-image {
    width: 64px;
    height: 64px;
  }
  .no-img {
    font-size: 35px;
    display: block;
    color: #caccd0;
  }
  .priorityCheck {
    display: flex;
    justify-content: space-around;
    .el-input {
      margin-right: 10px;
    }
  }
  .shop-dialog-cur-row{
    display: flex;
    flex-direction: row;
    align-items: center;
    .uploadBtn {
      position: relative;
      input {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
      }

    }
  }
</style>
