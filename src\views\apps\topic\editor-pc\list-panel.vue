<template>
  <div class="topic-editor-list">
    <div class="category" v-if="core.isNewLayout!==1">
      <div class="category-block" v-for="cat in categoryList" :key="cat.title">
        <!-- <div class="category-block-title" v-if="cat.title">{{cat.title}}</div> -->
        <el-tabs v-model="activeName" type="card" @tab-click="handleClickTab">
          <el-tab-pane label="模块" name="module" />
          <el-tab-pane label="页面" name="page" v-if="core.page_type != 'diaNewLog'" />
        </el-tabs>
        <ul v-if="activeName === 'module'">
          <li
            class="grid"
            v-for="item in cat.children"
            :key="item.name"
            @mousedown="e => onDrag(e, item)"
          >
            <span class="grid-title">{{item.title}}</span>
            <span class="grid-name">{{item.name}}</span>
          </li>
        </ul>
        <div v-if="activeName === 'page'" class="configs">
          <div class="page-config">
            <div class="config">
              <span>页面背景色：</span>
              <el-color-picker v-model="core.background" @change="onSetBackground" size="mini"></el-color-picker>
              <span class="show">
                <el-checkbox v-model="core.checkedColor" @change="onCheckedColor">显示</el-checkbox>
              </span>
            </div>
            <div class="gray">若背景色设置不生效可尝试删除背景图</div>
          </div>
          <div class="page-padding">
            页头下边距10像素：
            <el-switch v-model="core.pagePadding" active-text="开启" inactive-text="关闭" @change="onSwitchPagePadding"></el-switch>
          </div>
          <div class="page-backImg">
            <p>页面背景图：</p>
            <div class="cont">
              <div class="updatedImg">
                <img :src="core.backgroundImageUrl" />
              </div>
              <div class="buttons">
                <el-upload
                  class="topic-image-upload"
                  ref="upload"
                  accept="image/jpeg,image/jpg,image/png,image/gif"
                  :show-file-list="false"
                  :before-upload="beforeUpload"
                  :on-success="onUploadImg">
                  <el-button type="primary" :loading="loading" size="mini">更换图片</el-button>
                  <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                </el-upload>
                <div class="btn">
                  <el-button type="info" plain size="mini" @click="handleDeleteBgImg">删除</el-button>
                </div>
                <div class="info">
                  <p>文件格式：GIF，JPG，PNG</p>
                  <p>文件大小：200k以内</p>
                </div>
              </div>
            </div>
          </div>
          <div class="page-repeat">
            <p>背景显示：</p>
            <el-radio-group size="mini" v-model="core.backgroundRepeat" class="repeatCont" @change="handleBackgroundRepeat">
              <el-radio-button label="repeat">平铺</el-radio-button>
              <el-radio-button label="repeat-y">纵向平铺</el-radio-button>
              <el-radio-button label="repeat-x">横向平铺</el-radio-button>
              <el-radio-button label="no-repeat">不平铺</el-radio-button>
            </el-radio-group>
          </div>
          <div class="page-position">
            <p>背景对齐：</p>
            <el-radio-group size="mini" v-model="core.backgroundPosition" class="repeatCont" @change="handleBackgroundPosition">
              <el-radio-button label="left">左对齐</el-radio-button>
              <el-radio-button label="center">居中</el-radio-button>
              <el-radio-button label="right">右对齐</el-radio-button>
            </el-radio-group>
          </div>
          <div class="page-warning">
            <i class="el-icon-warning-outline"></i>
            <span>以上设置默认只应用到当前页面</span>
          </div>
          <div class="page-reset">
            <el-button type="primary" size="mini" @click="handlePageReset">重置当前页</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="sorry" v-else>新布局不允许改动模块,仅可以配置模块里的内容。</div>
    <div
      class="grid prey"
      v-show="prey.el"
      :style="{
        transform : `translate(${prey.mx-prey.ox-1}px, ${prey.my-prey.oy-1}px)`
      }"
    >
      <template v-if="prey.el">
        <span class="grid-title">{{prey.el.title}}</span>
        <span class="grid-name">{{prey.el.name}}</span>
      </template>
    </div>
  </div>
</template>

<script>
import categoryList from "../../topicPc-prop-components";

export default {
  props: {
    core: {
      type: Object,
      default: {}
    }
  },

  data() {
    return {
      activeName: 'module',
      backgroundColor: '#ffffff',
      checkedColor: false,
      pagePadding: false,
      backgroundImageUrl: '',
      backgroundRepeat: 'repeat',
      backgroundPosition: 'center',
      loading: false,
      prey: {
        el: null,
        // 偏移坐标
        ox: 0,
        oy: 0,
        // 位移坐标
        mx: 0,
        my: 0
      }
    };
  },

  computed: {
    categoryList() {
      if (this.core.page_type == "diaNewLog") {
        // dialogType==1代表弹框类型是自定义，2代表弹框类型是优惠券；自定义类型时需隐藏“popUpCoupon”组件
        if (this.core.dialogType == 1) {
          let newChildren = categoryList.pc_diaNewLog[0].children.filter(item => { return item.name !== "popUpCoupon" });
          let core_categoryList = this.core.categoryList[0]
          let newCategoryList = [{title: core_categoryList.title, children: newChildren}];
          return newCategoryList || categoryList[this.core.category + '_' + this.core.page_type]
        }
      }
      // this.core.page_type = 'h5';
      // this.core.category = 'pc';
      // if (!this.core.categoryList) return false;
      // if (this.core.page_type == "h5") {
      //   let core_categoryList = this.core.categoryList[0];
      //   let newCategoryList = [
      //     { title: core_categoryList.title, children: [] }
      //   ];
      //   let categoryList_h5 =
      //     categoryList[this.core.category + "_" + this.core.page_type];
      //   if (core_categoryList && core_categoryList.children.length > 0) {
      //     for (var i = 0; i < core_categoryList.children.length; i++) {
      //       HandleCategoryList(
      //         core_categoryList.children[i],
      //         i,
      //         categoryList_h5,
      //         newCategoryList[0].children
      //       );
      //     }
      //   }
      //   return (
      //     newCategoryList ||
      //     categoryList[this.core.category + "_" + this.core.page_type]
      //   );
      // }

      // function HandleCategoryList(_item, _ind, _core, _arr) {
      //   for (var i = 0; i < _core.length; i++) {
      //     _core[i].children.find((val, ind) => {
      //       if (val.name === _item.name) {
      //         _arr.push(val);
      //       }
      //     });
      //   }
      // }
      return categoryList[this.core.category + "_" + this.core.page_type] || [];
    }
  },

  methods: {
    handleClickTab(tab) {
      this.activeName = tab.name;
    },

    onCheckedColor(value) {
      if (value) {
        this.core.background = this.backgroundColor;
        return false
      }
      this.core.background = this.toColor16("#ffffff");
    },

    toColor16(str) {
      if (/^(rgb|RGB)/.test(str)) {
        var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
        var strHex = "#";
        for (var i = 0; i < aColor.length; i++) {
          var hex = Number(aColor[i]).toString(16);
          if (hex === "0") {
            hex += hex;
          }
          strHex += hex;
        }

        if (strHex.length !== 7) {
          strHex = str;
        }
        return strHex.toUpperCase();
      } else {
        return str;
      }
    },

    onSetBackground(value) {
      if (value) {
          this.backgroundColor = this.toColor16(value);
          if (this.checkedColor) this.core.background = this.backgroundColor;
      } else {
          this.backgroundColor = this.toColor16("#ffffff");
      }
    },

    onSwitchPagePadding(value) {
      this.core.pagePadding = value;
      // if (value) {
      //   this.core.pagePadding = '10px'
      // } else {
      //   this.core.pagePadding = '0px'
      // }
    },

    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
          this.$message({
              message: `[${res.code}]${res.msg}`,
              type: 'warning'
          })
          return;
      }
      this.backgroundImageUrl = res.data.url;
      this.core.backgroundImageUrl = this.backgroundImageUrl;
    },

    beforeUpload(file) {
      const isLt2k = file.size < (200 * 1024);
      if (!isLt2k) {
        this.$message.error('图片大小不能超过 200k!');
      }
      return isLt2k;
    },

    handleDeleteBgImg() {
      this.backgroundImageUrl = '';
      this.core.backgroundImageUrl = '';
    },

    handleBackgroundRepeat(value) {
      this.backgroundRepeat = value;
      this.core.backgroundRepeat = value;
    },

    handleBackgroundPosition(value) {
      this.backgroundPosition = value;
      this.core.backgroundPosition = value;
    },

    handlePageReset() {
      this.core.background = this.backgroundColor = '#ffffff';
      this.checkedColor = false;
      this.core.pagePadding = this.pagePadding = false;
      this.core.backgroundImageUrl = this.backgroundImageUrl = '';
      this.core.backgroundRepeat = this.backgroundRepeat = 'repeat';
      this.core.backgroundPosition = this.backgroundPosition = 'center';
    },

    getComponentByName(name) {
      return _.find(this.list, { name });
    },

    onDrag(e, item) {
      this.prey = {
        el: item,
        ox: e.offsetX,
        oy: e.offsetY,
        mx: e.clientX,
        my: e.clientY
      };
      window.addEventListener("mousemove", this.onMove);
      window.addEventListener("mouseup", this.onDrop);
      document.body.style.cssText =
        "-webkit-touch-callout:none;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;";
      this.$emit("drag", item);
    },

    onMove(e) {
      this.prey.mx = e.clientX;
      this.prey.my = e.clientY;
    },

    onDrop(e) {
      document.body.style.cssText = "";
      window.removeEventListener("mousemove", this.onMove);
      window.removeEventListener("mouseup", this.onDrop);
      this.prey.el = null;
    }
  }
};
</script>

<style lang="scss" scoped rel="stylesheet/scss">
.topic-editor-list {
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 800px;

  .category {
    .category-block {
      margin-bottom: 10px;

      .category-block-title {
        overflow: hidden;
        padding: 0 5px;
        height: 30px;
        font-size: 14px;
        line-height: 30px;
        background: $color-primary;
        color: #fff;
      }

      ul {
        display: flex;
        flex-flow: wrap; /* justify-content:center; */
        justify-content: space-between;
        padding: 5px;
        padding-left: 0;
      }
    }
  }

  .sorry {
    text-align: left;
    color: red;
    line-height: 20px;
    font-size: 14px;
  }
  .grid {
    width: 100px;
    height: 40px;
    padding: 5px;
    margin: 5px;
    border: $border-base;
    text-align: center;
    white-space: nowrap;
    background: #fff;
    cursor: pointer;

    .grid-title {
      display: block;
      overflow: hidden;
      height: 24px;
      font-size: 14px;
      line-height: 24px;
      color: #333;
      pointer-events: none;
    }

    .grid-name {
      display: block;
      overflow: hidden;
      height: 16px;
      font-size: 12px;
      line-height: 16px;
      color: #aaa;
      pointer-events: none;
    }
  }

  .prey {
    position: fixed;
    top: 0;
    left: 0;
    margin: 0;
    pointer-events: none;
    background: rgba(0, 0, 0, 0.5);

    .grid-title {
      color: #fff;
    }

    .grid-name {
      color: #ccc;
    }
  }
}

.el-tabs {
  padding-top: 0;
  height: 38px;
}

.topic-editor-list .el-tabs--border-card >.el-tabs__content {
  padding: 0;
}

.page-config {
  padding-top: 15px;
  .config {
    display: flex;
    align-items: center;
    .show {
      margin-left: 20px;
    }
  }
  .gray {
    color: #999;
    margin-top: 10px;;
  }
}

.page-padding {
  padding: 20px 0 0 0;
  display: flex;
}

.page-backImg {
  padding: 20px 0 0 0;
  p {
    margin: 0 0 15px 0;
  }
  .cont {
    display: flex;
    .updatedImg {
      width: 80px;
      height: 80px;
      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }
    .buttons {
      flex: 1;
      padding: 0 0 0 15px;
      .topic-image-upload {
        display: inline-block;
      }
      cursor: pointer;
      position: relative;
      overflow: hidden;
      .btn {
        width: 40%;
        margin: 0 0 10px 0;
        display: inline-block;
      }
      .btn:last-child {
        margin-left: 20px;
      }
    }
    .info {  
      p {
        color: #999;
        font-size: 12px;
        margin: 0;
      }
    }

    .el-upload:hover {
      border-color: #409EFF;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 128px;
      height: 128px;
      line-height: 128px;
      text-align: center;
    }
    .avatar {
      width: 128px;
      height: 128px;
      display: block;
    }
  }
}

.page-repeat {
  padding: 20px 0 0 0;
  p {
    padding-bottom: 10px;
  }
}

.page-position {
  padding: 20px 0 0 0;
  p {
    padding-bottom: 0px;
  }
  .buttons {
    .btn-block {
      width: 25%;
      display: inline-block;
    }
  }
  .buttons:last-child {
    margin-top: 10px;
  }
}

.page-warning {
  padding-top: 20px;
  color: red;
  display: flex;
  align-items: center;
}

.page-reset {
  padding: 20px 15px 0 0;
  text-align: right;
}

</style>
