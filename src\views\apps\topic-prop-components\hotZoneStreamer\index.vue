<template>
  <div class="topic-image">
    <div class="topic-image-tips">
      注：大图请使用具有切片功能的<b>横幅广告</b>组件，此组件只适合于不想切片的<b>小图</b>（gif动图、二维码图、需长按保存的图），上传体积不允许超过<b>1M</b>。
    </div>
    <!--添加广告入口-->
    <div class="block">
        <el-button class="btn-block" type="primary" @click="addAdList">添加广告</el-button>
    </div>

    <el-table :data="list" size="mini" :row-key="row => row.entry" style="width: 100%">
      <el-table-column type="index" width="50"></el-table-column>
      <el-table-column prop="entry" label="广告名称">
        <template slot-scope="scope">
          <span v-html="scope.row.entry"></span>
          <!-- <img v-if="scope.row.title" :src="scope.row.title" alt="图" class="title-image" />
          <i v-else class="el-icon-circle-plus-outline no-img"></i> -->
        </template>
      </el-table-column>
      <el-table-column label="人群名称">
        <template slot-scope="scope">
          <span>{{scope.row.crowdValue || '全部人群'}}</span>
        </template>
      </el-table-column>

      <el-table-column prop="timevalue" label="有效时间" width="200px">
        <template slot-scope="scope">
          <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
              <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
            </template>
            </div>
          <div v-else> {{format_date(scope.row.timevalue[0])}}~{{format_date(scope.row.timevalue[1])}}</div>
         
        </template>
      </el-table-column>

      <el-table-column prop="timevalue" label="状态">
        <template slot-scope="scope">
          <b v-if="scope.row.timeType&&scope.row.timeType==2"  style='color: #67C23A'>展示中</b>
            <div v-else>
              <span v-html="format_text(scope.row.timevalue)"></span>
            </div>
         
        </template>
      </el-table-column>

      <el-table-column prop="link" label="链接" :show-overflow-tooltip="true" width="120px">
        <template slot-scope="scope">
          <span>{{(scope.row.hotZoneInfoList && scope.row.hotZoneInfoList[0] || {}).page_url}}</span>
          <!-- <el-input
            type="text"
            size="mini"
            v-model="(scope.row.hotZoneInfoList && scope.row.hotZoneInfoList[0] || {}).page_url"
          >{{(scope.row.hotZoneInfoList && scope.row.hotZoneInfoList[0] || {}).page_url}}</el-input> -->
        </template>
      </el-table-column>

      <el-table-column label="链接类型">
        <template slot-scope="scope">
          <span>{{scope.row.hotZoneInfoList && (scope.row.hotZoneInfoList[0] || {}).linkType === 'topic' ? '专题页链接' : scope.row.hotZoneInfoList && (scope.row.hotZoneInfoList[0] || {}).linkType === 'stores' ? '店铺页链接': '动态商品链接'}}</span>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="120px">
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="toEdit(scope.row, scope.$index)"
            type="primary"
          >编辑</el-button>
          <el-button size="mini" @click="handleCancle(scope.row, scope.$index)" type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <ad-dialog
      ref="adDialog"
      v-if="add_editContent"
      :visible="add_editContent"
      :branchCode="topic.branchCode"
      :adItemData="adItemData"
      :editIndex="editIndex"
      :pageType="topic.page_type"
      @saveDialog="saveDialog"
    ></ad-dialog>
  </div>
</template>
<script>
  import base from "../base";
  import adDialog from './adDialog';
  import bus from "utils/eventbus";
  export default {
    name: 'streamer',
    extends: base,
    components: {
      adDialog
    },
    data() {
      return {
        loading: false,
        add_editContent: false,
        adItemData: {},
        editIndex: '',
        // list: [],
      };
    },
    computed: {
      list() {
        var list = _.get(this, "content.list");
        if (list) {
          this.$nextTick(function() {
            this.setSort();
          });
          let arr=list
          console.log(list)
        
          let noDateArr=arr.filter(item=>{
          if(item.timeType==2){return item}else
        return this.format_text(item.timevalue)!="<b style='color: #000000'>已过期</b>"
        })
        let dateArr=arr.filter(item=>{
         
        return this.format_text(item.timevalue)=="<b style='color: #000000'>已过期</b>"&&item.timeType!=2
        })
        console.log(noDateArr.concat(dateArr))
         this.content.list=noDateArr.concat(dateArr)
        return noDateArr.concat(dateArr)
        //return list
        } else {
          return [];
        }
      }
    },

    methods: {
      saveDialog(type, psData, index) {
        console.log(psData,111111)
        this.closeEditContent();
        if (type == 'edit') {
          this.content.list.splice(index, 1, psData)
        } else {
          let list = _.get(this, "content.list");
          if (list) {
            this.content.list.push(psData);
          } else {
            this.$set(this.content, 'list', []);
            this.content.list.push(psData);
          }
        }
      }, 
      format_text(data) {
        if (!data) {
          return "<b style='color: red'>请设置时间</b>";
        }
        const _date = new Date().getTime();
        const start = new Date(data[0]).getTime();
        const end = new Date(data[1]).getTime();
        if (_date <= end && _date >= start) {
          return "<b style='color: #67C23A'>展示中</b>";
        } else if (_date < start) {
          return `<b style='color: #000000'>即将在${new Date(
            start
          ).toLocaleDateString()}展示</b>`;
        } else {
          return "<b style='color: #000000'>已过期</b>";
        }
      },
      addTamp(m) {
        return m < 10 ? '0' + m : m
      },
      format_date(timestamp) {
        if (timestamp === '') {
          return ''
        }
        var time = new Date(timestamp);
        var year = time.getFullYear();
        var month = time.getMonth() + 1;
        var date = time.getDate();
        var hours = time.getHours();
        var minutes = time.getMinutes();
        var seconds = time.getSeconds();
        return year + '-' + this.addTamp(month) + '-' + this.addTamp(date) + ' ' + this.addTamp(hours) + ':' +
          this.addTamp(minutes) + ':' + this.addTamp(seconds);
      },
      //编辑
      toEdit(data, index) {
        this.add_editContent = true;
        if(!data.timeType){
        data.timeType="1"
      }
      this.adItemData = JSON.parse(JSON.stringify(data));
      console.log(data,1111)
     this.$nextTick(()=>{
      if(this.adItemData.timeType==2&&this.adItemData.circulateTime){
        this.$refs.adDialog.$refs.loopcirculateTime.circulateTime=this.adItemData.circulateTime
        this.$refs.adDialog.$refs.loopcirculateTime.editInit()

      }
     })
        this.editIndex = index;
      },
      //删除
      handleCancle(row,index) {
        let _self = this;
        return function () {
            const index = _self.content.list.indexOf(row)
            _self.content.list.splice(index, 1)
            _self.$message({
                type: 'success',
                message: '删除成功!'
            });
        }.confirm(_self)()
      },
      closeEditContent() {
        this.add_editContent = false;
      },
      addAdList(){
      
        this.add_editContent = true;
        this.$nextTick(()=>{
          this.$refs.adDialog.$refs.loopcirculateTime.circulateTime={}
        })
        this.adItemData = {};
        this.editIndex = '';
        // if(this.content.list.length >= 10){
        //   this.add_editContent=false;
        //   this.$message({
        //     message: '最多可添加10条广告',
        //     type: "warning"
        //   });
        // }
      },
    },
  };

</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .topic-image-tips {
    padding: 5px 0;
    font-size: 12px;
    color: #999;
    b {
      color: $color-danger;
    }
  }
  .btn-block {
    display: block;
    width: 90%;
    margin:10px auto 20px;
  }
  .el-form-item {
    margin-bottom: 12px;
  }
  .topic-image-upload {
    .image {
      display: block;
      width: 100%;
    }
    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }
  .banner-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }
    .image {
      width: 64px;
      height: 64px;
    }
  }
  .topic-image-picker {
    padding: 10px 0;
  }
  .title-image {
    width: 64px;
    height: 64px;
  }
  .el-table {
    .cell {
      text-align: center;
      padding: 0;
    }

    th .cell {
      color: #606266;
    }
  }
  .el-button--mini {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
  }

.fast-entry {
  .container {
    display: flex;
    align-items: center;

    .img {
      width: 65%;

      img {
        display: block;
        width: 100%;
      }
    }

    .button-list {
      margin-left: 10px;
    }
  }

  .content-setting {
    color: #fff;
    background-color: #13c2c2;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }
  .entry-name {
    width: 70%;
  }

  // single-upload
  .uploader-btn-state {
    text-align: center;
  }
  .no-img {
    font-size: 35px;
    display: block;
    color: #caccd0;
  }
}

.el-loading-spinner {
  top: auto !important;
  margin-top: auto !important;
}

.el-row {
  text-align: center;

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}

  .topic-image-info {
    position: relative;
    overflow: hidden;
    height: 62px;
    padding-bottom: 10px;
    margin: 5px 0;
    border: $border-base;
    font-size: 12px;

    .name {
      position: relative;
      overflow: hidden;
      height: 26px;
      padding: 0 5px;
      margin-bottom: 3px;
      font-size: 14px;
      line-height: 26px;
      border-bottom: $border-base;
    }

    .data {
      position: relative;
      overflow: hidden;
      height: 16px;
      padding: 0 5px;
      line-height: 16px;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: #999;
    }

    .del {
      position: absolute;
      top: 0;
      right: 0;
      padding: 7px;
      border-left: $border-base;
      background: #fff;
      cursor: pointer;

      &:hover {
        background: $color-base-silver;
        color: #fff;
      }
    }
  }

</style>
