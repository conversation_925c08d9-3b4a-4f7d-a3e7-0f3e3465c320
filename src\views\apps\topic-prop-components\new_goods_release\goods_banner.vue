<template>
    <div>

            <div style="margin: 15px 0">
                背景颜色:
                <el-color-picker v-model="content.banner.bgColor" size="mini"></el-color-picker>
                页面名颜色:
                <el-color-picker v-model="content.banner.subject_title_color" size="mini"></el-color-picker>
                标语说明颜色:
                <el-color-picker v-model="content.banner.subject_introduce_color" size="mini"></el-color-picker>
            </div>
            <div>
                <el-input placeholder="请输入内容" v-model="content.banner.subject_title">
                    <template slot="prepend">页面名</template>
                </el-input>

                <el-input placeholder="请输入内容" v-model="content.banner.subject_introduce" style="margin-top: 5px">
                    <template slot="prepend">标语说明</template>
                </el-input>
            </div>
            <div style="margin: 5px 0">
                <span>统一的截止时间:</span>
                <el-date-picker
                        v-model="all_limit_time"
                        type="date"
                        placeholder="选择日期" style="margin-top: 5px;width: 100%">
                </el-date-picker>
            </div>
            <el-table :data="banner_list" style="width: 100%;margin-top: 5px" height="500" v-if="banner_list.length>0"
                      ref="multipleTable">
                <el-table-column fixed label="图片" width="80">
                    <template slot-scope="scope">
                        <img :src="scope.row.imageUrl" :alt="scope.row.productName" style="width:100%;max-height:50px;">
                    </template>
                </el-table-column>
                <el-table-column prop="productName" label="药名" width="120">
                    <template slot-scope="scope">
                        <span v-if="scope.row.productName">{{scope.row.productName}}</span>
                        <span v-else>{{scope.row.showName}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="规格" width="80">
                    <template slot-scope="scope">
                        {{scope.row.mediumPackageTitle}}
                    </template>
                </el-table-column>
                <el-table-column prop="fob" label="价格" width="80">
                    <template slot-scope="scope">
                        {{scope.row.fob}}
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="160">
                    <template slot-scope="scope">
                        <div class="edit-button">
                            <el-button @click="banner_handleDelete(scope.row)" type="warning" size="mini">删除</el-button>
                        </div>
                        <div class="edit-button" style="margin-top: 5px">
                            <span>截止时间:</span>
                            <el-date-picker
                                    v-model="scope.row.limit_time"
                                    type="date"
                                    placeholder="选择日期" style="margin-top: 5px;width: 100%"
                                    @change="change_time_limit(scope.row)">
                            </el-date-picker>
                        </div>

                    </template>
                </el-table-column>
            </el-table>

        <!--选择商品-->
        <all-link @select="onSetLink" :tabs="tabs" :params="{
                productlink: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                importGoods: {
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                goodsGroup: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
            }"></all-link>
        </div>
</template>

<script>
    import base from "../base";
    import api from 'api'
    import tabSwitch from './tab'
    import {common} from 'api'
    import {AppWebsite} from 'config/index';

    export default {
        components: {
            tabSwitch
        },
        extends: base,
        contentDefault: {
            banner: {
                list: [],
                bgColor: "#578EE1",
                subject_title: "值得买",
                subject_title_color: "#ffffff",
                subject_introduce: "上新限时特惠",
                subject_introduce_color: "",
                subject_more_url: "",
            }
        },
        data() {
            return {
                tabs: [
                    {label: '商品', value: 'productlink'},
                    {label: '导入商品', value: 'importGoods'},
                    {label: '商品组', value: 'goodsGroup'}
                ],
                all_limit_time: null
            }
        },
        watch: {
            all_limit_time(new_val) {
                if (new_val) {
                    for (let item of this.content.banner.list) {
                        this.$set(item, "limit_time", new_val)
                    }
                }
            },
        },
        computed: {
            banner_list() {
                let list = _.get(this, 'content.banner.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
        },
        methods: {
            change_time_limit(row) {
                let index = this.content.banner.list.indexOf(row);
                this.$set(this.content.banner.list[index], "limit_time", row.limit_time)

            },
            onSetLink(link) {
                function handle_arr(arr=[]){
                    return arr.map((item)=>{
                        let obj={};
                        obj.init_img_url=item.init_img_url;
                        obj.imageUrl=item.imageUrl;
                        obj.productName=item.productName;
                        obj.showName=item.showName;
                        obj.mediumPackageTitle=item.mediumPackageTitle;
                        obj.fob=item.fob;
                        obj.id=item.id;
                        return obj
                    });
                }

                    if (link.tag === "goods" || link.tag === "importGoods") {
                        let _self_arr= handle_arr(link.data);
                        if (this.content.banner.list.length > 0) {
                            this.content.banner.list = [...api.common.removeRepeat(this.content.banner.list, _self_arr)]
                        } else {
                            this.content.banner.list = [..._self_arr]
                        }
                    } else if (link.tag === "goodsGroup") {
                        let _self_arr= handle_arr(link.data._goods);
                        this.content.banner.list = [..._self_arr]
                    }

            },
            banner_handleDelete(row) {
                const index = this.content.banner.list.indexOf(row)
                this.content.banner.list.splice(index, 1)
            },

        },

    }
</script>

<style scoped>

</style>
