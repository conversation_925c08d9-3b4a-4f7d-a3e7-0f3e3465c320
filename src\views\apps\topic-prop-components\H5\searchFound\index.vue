<template>
  <div class="searchFound">
    <el-row :gutter="20">
      <div class="title">添加词条</div>
      <el-col :span="24">
        <el-button class="addSearch" type="primary" @click="addWords">添加词条</el-button>
      </el-col>
    </el-row>
    
    <div class="wordsList">
      <el-table :data="content.wordsList" :lazy="true" size="mini">
        <el-table-column type="index" width="30"></el-table-column>
        <el-table-column label="词条名称">
          <template slot-scope="scope">
            <span>{{ scope.row.entryName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="有效时间">
          <template slot-scope="scope">
            {{scope.row.timevalue|dateFilter}}
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <span v-html="format_text(scope.row.timevalue)"></span>
          </template>
        </el-table-column>
        <el-table-column label="链接">
          <template slot-scope="scope">
            <el-input
              type="text"
              size="mini"
              v-model="scope.row.page_url"
            >{{ scope.row.page_url }}</el-input>
          </template>
        </el-table-column>
        <el-table-column label="链接类型">
          <template slot-scope="scope">
            <span>{{ {'topic': '专题页链接', 'stores': '店铺页链接', 'dynamic': '动态商品链接'}[scope.row.linkType] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="150px">
          <template slot-scope="scope">
            <el-button
              size="mini"
              @click="toEdit(scope.row, scope.$index)"
              type="primary"
            >编辑</el-button>
            <el-button size="mini" @click="handleCancle(scope.row, scope.$index)" type="danger">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <words-dialog
      v-if="add_editContent"
      :visible="add_editContent"
      :branchCode="topic.branchCode"
      :adItemData="adItemData"
      :editIndex="editIndex"
      @saveDialog="saveDialog"
    ></words-dialog>
  </div>
</template>

<script>
import base from "views/apps/topic-prop-components/base.vue";
import api from "api";
import { AppWebsite } from "config";
import wordsDialog from './wordsDialog';

export default {
  name: 'SetMeal',
  extends: base,
  contentDefault: {
    timevalue: '',
    wordsList: [],
  },
  components: {
    wordsDialog
  },
  data() {
    return {
      currentMealIndex: -1,
      index: 0,
      add_editContent: false,
      adItemData: {},
      editIndex: '',
      pickerOptions0: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
    }
  },
  mounted () {
    // this.index = this.content.setMealList.length;
  },
  filters: {
    dateFilter(date) {
      function formatDate(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return year + '-' + (String(month).length > 1 ? month : '0' + month) + '-' +
          (String(day).length > 1 ? day : '0' + day) + ' ' + (String(hour).length > 1 ? hour : '0' + hour) + ':' + (String(minute).length > 1 ? minute : '0' + minute)
          + ':' + (String(second).length > 1 ? second : '0' + second)
      }
      if (date) {
        let date1 = formatDate(new Date(date[0]));
        let date2 = formatDate(new Date(date[1]));
        // const nS=new Date(date).getTime()
        return date1 + "至" + date2
      } else {
        return " "
      }
    },
  },
  methods: {
    format_text(data) {
      if (!data) {
        return "<b style='color: red'>请设置时间</b>";
      }
      const _date = new Date().getTime();
      const start = new Date(data[0]).getTime();
      const end = new Date(data[1]).getTime();
      if (_date <= end && _date >= start) {
        return "<b style='color: #67C23A'>展示中</b>";
      } else if (_date < start) {
        return `<b style='color: #000000'>即将在${new Date(
          start
        ).toLocaleDateString()}展示</b>`;
      } else {
        return "<b style='color: #000000'>已过期</b>";
      }
    },
    addWords() {
      this.add_editContent = true;
      this.adItemData = {};
      this.editIndex = '';
    },
    //编辑
    toEdit(data, index) {
      this.add_editContent = true;
      this.adItemData = JSON.parse(JSON.stringify(data));
      this.editIndex = index;
    },
    //删除
    handleCancle(row,index) {
      let _self = this;
      return function () {
        const index = _self.content.wordsList.indexOf(row)
        _self.content.wordsList.splice(index, 1)
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    saveDialog(type, psData, index) {
      this.closeEditContent();
      if (type == 'edit') {
        this.content.wordsList.splice(index, 1, psData)
      } else {
        this.content.wordsList.push(psData);
      }
    },
    closeEditContent() {
      this.add_editContent = false;
    },
  }
}
</script>

<style lang="scss" scoped>
  .searchFound {
    .addSearch {
      width: 80%;
    }
    .wordsList {
      margin-top: 20px;
    }
  }
  .el-row {
    text-align: center;
    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
  }
</style>