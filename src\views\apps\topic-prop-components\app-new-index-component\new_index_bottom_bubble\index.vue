<template>
<div>
    <el-table
      :data="dataList"
      style="width: 100%"
      height="250"
      v-if="dataList.length > 0"
      ref="multipleTable"
    >
     <el-table-column label="位置" width="150" prop="position">
        <template slot-scope="scope">
          <p>首页底部Tab</p>
        </template>
      </el-table-column>
      <el-table-column label="模块" width="200" prop="bubbleSelectMode">
        <template slot-scope="scope">
            <el-select
            v-model="scope.row.bubbleSelectMode"
            placeholder="请选择按钮模块"
            size="mini"
            clearable
            :disabled="scope.row.bubbleStatus === 1"  
            >
            <el-option
                v-for="item in moduleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            ></el-option>
            </el-select>
        </template>
      </el-table-column>
      <el-table-column label="气泡提示文案" prop="bubbleTips">
        <template slot-scope="scope">
            <el-input
                type="text"
                size="mini"
                v-model="scope.row.bubbleTips"
                maxlength="20"
                clearable
                :disabled="scope.row.bubbleStatus === 1" 
            >
            </el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="50" prop="bubbleStatus">
        <template slot-scope="scope">
          <el-button 
            type="text" 
            @click="handleStatusChange(scope.row.bubbleStatus,scope.$index)">
            {{ scope.row.bubbleStatus === 1 ? "下线" : "上线" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
</div>
</template>

<script>
import base from "../../base";
export default {
    extends: base,
    contentDefault: {
        list: [],
        color: "#a0a0a0", //文字颜色
        hoverColor: "#00dc82", //文字选中颜色,
        bgColor: "#FFFFFF", //背景颜色,
        bgImage: ""
    },
    name:'new_index_bottom_bubble',
    data() {
        return {
             moduleOptions: [
                { value: 1, label: '一键补货' },
                { value: 2, label: '购物车' },
                { value: 3, label: '订单' },
                { value: 4, label: '我的' }
            ]
        }
    },
    computed: {
     dataList() {
      var dataList = _.get(this, "content.list");
      console.log(_.get(this, "content.core"),"jjj")
        if (dataList) {
            return dataList;
        } else {
            return [];
        }
      }
    },
    created() {
        if (this.content.list.length === 0) {
            this.initDataList();
        }
    },
    methods: {
        initDataList() {
            this.dataList.push({
                bubbleTips: "",
                bubbleStatus: 0,
                bubbleId: `${new Date().getTime()}_${Math.random().toString(36).substr(2, 5)}`,
                bubbleSelectMode: "",
            })
        },
        /**
         * 修改气泡提示文案上下线状态 status为1时 上线 为 0 时下线 
         * 下线弹出提示框 确认之后改为下线 
         * @param {number | undefined} status - 目前这条记录的上下线状态
         * @param {number} index - 当前行索引
         */
        handleStatusChange(status,index) {
            if(status === 1) {
                this.$confirm("确定要下线吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                .then(() => {
                    this.dataList[index].bubbleStatus = 0;
                    this.content.list = this.dataList;
                })
                .catch(() => {});
            } else {
                this.$confirm("确定要上线吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                .then(() => {
                    this.dataList[index].bubbleStatus = 1;
                    this.dataList[index].bubbleId = `${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
                    this.content.list = this.dataList;
                    this.$message({
                        message: "上线成功",
                        type: "success"
                    });
                })
                .catch(() => {});
            }
        }
    },
}
</script>

<style lang='scss' scoped>

</style>