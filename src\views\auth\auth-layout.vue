<template>
  <div class="auth-page">
    <div id="particles" class="particles"></div>
    <slot></slot>
  </div>
</template>

<script>
  import loadScript from 'utils/load-script'

  export default {
    name: 'AuthLayout',
    props: {
      // 高度
      logoUrl: {
        type: String,
        default: '/static/images/logo_auth.png'
      }
    },
    computed: {
      logoStyle() {
        let result = {
          backgroundColor: '#C82433',
          backgroundImage: 'url(' + this.logoUrl + ')'
        }
        if (!this.logoUrl) {
          result.backgroundImage = 'url(/static/images/logo_auth.png)'
        }
        return result
      }
    },
    async mounted() {
      await loadScript('https://cdn.bootcss.com/particles.js/2.0.0/particles.min.js')
      particlesJS.load('particles', 'ASSET_PUBLIC_PATHstatic/particles.json', function () {
        console.log('callback - particles-js config loaded')
      })
    }
  }
</script>

<style lang="scss" rel="stylesheet/scss">
  .auth-page {
    background: url(/static/images/main_bg.jpg) no-repeat top;
    overflow-y: auto;
    height: 100%;
  }
  .particles {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
</style>
