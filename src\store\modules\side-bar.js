import * as types from '../mutation-types'
// initial state
const state = {
  isShow: true,
  activeNav: window.localStorage.getItem('activeNav') || '',
  navList: [],
  accountMenu: [],
}
// getters
const getters = {
  isShow: state => state.isShow,
  activeNav: state => state.activeNav,
  navList: state => state.navList,
  accountMenu: state => state.accountMenu,
}
// actions
const actions = {
  setActiveNavId({ commit, state }, activeNav) {
    commit(types.SET_ACTIVE_NAV, activeNav)
  },
  updateNavList({ commit, state }, navList) {
    commit(types.UPDATE_SIDE_NAV_LIST, navList)
  },
  updateAccountNavList({ commit, state }, navList) {
    commit(types.UPDATE_ACCOUNT_NAV_LIST, navList)
  },
  setSideBarState({ commit, state }, status) {
    commit(types.SET_SIDE_BAR_STATE, status)
  },
  reset({ commit }) {
    commit(types.RESET_SIDEBAR_STATE)
  }
}
// mutations
const mutations = {
  [types.SET_ACTIVE_NAV](state, nav) {
    window.localStorage.setItem('activeNav', nav)
    state.activeNav = nav
  },
  [types.UPDATE_ACCOUNT_NAV_LIST](state, navList) {
    const menuList = [];
    navList.forEach(nav => {
      if (nav.parentId == 2) {
        menuList.push(nav)
      }
    })
    state.accountMenu = menuList;
  },
  [types.UPDATE_SIDE_NAV_LIST](state, navList) {
    const menuList = [];
    navList.forEach(nav => {
      if (nav.parentId == 0 && nav.menuId !== 2) {
        !nav.children?nav.children = []:nav.children;
        menuList.push(nav)
      }
    })
    navList.forEach(nav => {
      menuList.forEach(menu => {
        if (nav.parentId == menu.menuId) {
          menu.children.push(nav)
        }
      })
    })
    state.navList = menuList;
  },
  [types.SET_SIDE_BAR_STATE](state, isShow) {
    state.isShow = isShow
  },
  [types.RESET_SIDEBAR_STATE](state) {
    state.isShow = true
  }
}
export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
