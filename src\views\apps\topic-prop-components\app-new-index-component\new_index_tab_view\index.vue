
<template>
  <div class="topic-search">
    <!-- 分类配置 -->
    <div class="title">分类配置</div>
    <el-row :gutter="20">
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="分类名称:">
            <el-input placeholder="请输入内容" v-model="content.type_name">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态:">
            <el-select
              v-model="content.status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="(v, k, i) in status"
                :label="v"
                :key="v"
                :value="k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="展示时间:">
            <el-input placeholder="请输入内容" v-model="content.type_time">
            </el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <div class="three-button">
      <el-button type="primary" @click="searchList" size="mini">查询</el-button>
      <el-button type="primary" @click="resetList" size="mini">重置</el-button>
      <el-button type="primary" @click="openTypeSettingViewAlert" size="mini"
        >新建</el-button
      >
    </div>
    <el-table
      :data="dataList"
      size="mini"
      class="tableBox"
      style="margin-top: 10px"
      ref="tableBox"
      :row-key="(row) => row.id"
    >
      <el-table-column label="id" width="100"></el-table-column>
      <el-table-column label="分类名称" prop="typeName"></el-table-column>
      <el-table-column label="有效期" width="200">
        <template slot-scope="scope">
          {{ scope.row.validityTime[0] }} ~ {{ scope.row.validityTime[1] }}
        </template>
      </el-table-column>

      <el-table-column label="状态">
        <template slot-scope="scope">
          <div>
            {{ scope.row.status || 9 }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="150px">
        <template slot-scope="scope">
          <el-button size="mini" @click="toEdit(scope.row, scope.$index)"
            >编辑
          </el-button>
          <el-button size="mini" @click="toRemove(scope.row)" type="danger"
            >删除</el-button
          >
          <el-button size="mini" @click="online(scope.row)" type="danger"
            >上线</el-button
          >
          <el-button size="mini" @click="outline(scope.row)" type="danger"
            >下线</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- tab背景配置 -->
    <div class="title">背景配置</div>
    <el-row :gutter="20">
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="id:">
            <el-input
              placeholder="请输入内容"
              v-model="content.tab_background_id"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="背景名称:">
            <el-input
              placeholder="请输入内容"
              v-model="content.tab_background_name"
            >
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态:">
            <el-select
              v-model="content.tab_background_status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="(v, k, i) in tabBackgroundStatus"
                :label="v"
                :key="v"
                :value="k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="有效期:">
            <el-input
              placeholder="请输入内容"
              v-model="content.tab_background_validate_time"
            >
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="人群id:">
            <div class="tab_crowd_value">
              <el-input v-model="content.tab_background_crowd_value"></el-input>
              <div>展示人群包名称</div>
            </div>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <div class="three-button">
      <el-button type="primary" @click="tabBackGroundSearchList" size="mini"
        >查询</el-button
      >
      <el-button type="primary" @click="tabBackGroundResetList" size="mini"
        >重置</el-button
      >
      <el-button
        type="primary"
        @click="openTypeBackgroundSettingViewAlert"
        size="mini"
        >新建</el-button
      >
    </div>

    <el-table
      :data="tabBackgroundDataList"
      size="mini"
      class="tableBox"
      style="margin-top: 10px"
      ref="tableBox"
      :row-key="(row) => row.id"
    >
      <el-table-column label="id" width="100"></el-table-column>
      <el-table-column
        label="背景名称"
        prop="tabBackgroundName"
      ></el-table-column>
      <el-table-column label="背景名称" prop="tabBackgroundName">
        <template slot-scope="scope">
          <!-- 这里需要判断是图片还是颜色在进行展示 -->
          <div>未完成</div>
        </template>
      </el-table-column>
      <el-table-column label="有效期" width="200">
        <template slot-scope="scope">
          {{ scope.row.validityTime[0] }} ~ {{ scope.row.validityTime[1] }}
        </template>
      </el-table-column>

      <el-table-column label="状态">
        <template slot-scope="scope">
          <div>
            {{ scope.row.status || 9 }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="150px">
        <template slot-scope="scope">
          <el-button size="mini" @click="toEdit(scope.row, scope.$index)"
            >编辑
          </el-button>
          <el-button size="mini" @click="toRemove(scope.row)" type="danger"
            >删除</el-button
          >
          <el-button size="mini" @click="online(scope.row)" type="danger"
            >上线</el-button
          >
          <el-button size="mini" @click="outline(scope.row)" type="danger"
            >下线</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- tab配置 -->
    <div class="title">tab配置</div>
    <el-row :gutter="20">
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="id:">
            <el-input placeholder="请输入内容" v-model="content.tab_setting_id">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="tab名称:">
            <el-input
              placeholder="请输入内容"
              v-model="content.tab_setting_name"
            >
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="人群id:">
            <div class="tab_crowd_value">
              <el-input v-model="content.tab_setting_crowd_value"></el-input>
              <div>展示人群包名称</div>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="tab类型:">
            <el-select
              v-model="content.tab_setting_type"
              placeholder="选择类型"
              default-first-option
              filterable
            >
              <el-option
                v-for="(v, k, i) in tabSettingTypeStatus"
                :label="v"
                :key="v"
                :value="k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="位置:">
            <el-select
              v-model="content.tab_setting_position"
              placeholder="选择类型"
              default-first-option
              filterable
            >
              <el-option :label="'左'" :value="1"></el-option>
              <el-option :label="'右'" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态:">
            <el-select
              v-model="content.tab_setting_status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="(v, k, i) in tabSettingStatus"
                :label="v"
                :key="v"
                :value="k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="有效期:">
            <el-input placeholder="请输入内容" v-model="content.validate_time">
            </el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <div class="three-button">
      <el-button type="primary" @click="tabSettingSearchList" size="mini"
        >查询</el-button
      >
      <el-button type="primary" @click="tabSettingResetList" size="mini"
        >重置</el-button
      >
      <el-button
        type="primary"
        @click="openTabSettingViewAlert"
        size="mini"
        >新建</el-button
      >
    </div>

    <el-table
      :data="tabBackgroundDataList"
      size="mini"
      class="tableBox"
      style="margin-top: 10px"
      ref="tableBox"
      :row-key="(row) => row.id"
    >
      <el-table-column label="id" width="100"></el-table-column>
      <el-table-column
        label="背景名称"
        prop="tabBackgroundName"
      ></el-table-column>
      <el-table-column label="背景名称" prop="tabBackgroundName">
        <template slot-scope="scope">
          <!-- 这里需要判断是图片还是颜色在进行展示 -->
          <div>未完成</div>
        </template>
      </el-table-column>
      <el-table-column label="有效期" width="200">
        <template slot-scope="scope">
          {{ scope.row.validityTime[0] }} ~ {{ scope.row.validityTime[1] }}
        </template>
      </el-table-column>

      <el-table-column label="状态">
        <template slot-scope="scope">
          <div>
            {{ scope.row.status || 9 }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="150px">
        <template slot-scope="scope">
          <el-button size="mini" @click="toEdit(scope.row, scope.$index)"
            >编辑
          </el-button>
          <el-button size="mini" @click="toRemove(scope.row)" type="danger"
            >删除</el-button
          >
          <el-button size="mini" @click="online(scope.row)" type="danger"
            >上线</el-button
          >
          <el-button size="mini" @click="outline(scope.row)" type="danger"
            >下线</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 三个弹窗 -->
    <typeSettingAlert ref="typeSettingAlert" :topic="topic" @done="typeSettingAlertDone"></typeSettingAlert>
    <tabSettingAlert ref="tabSettingAlert" :topic="topic" @done="tabSettingAlertDone"></tabSettingAlert>
  </div>
</template>
<script>
import typeSettingAlert from './components/type_setting_alert.vue'
import tabSettingAlert from './components/tab_setting_alert.vue'
// import tabBackgroundSettingAlert from './components/tab_background_setting_alert.vue'
import base from "../../base";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import Sortable from "sortablejs";
let sortableObject = {};
export default {
  name: "bottomView",
  extends: base,
  components: { swiperPoint, typeSettingAlert, tabSettingAlert },
  contentDefault: {
    type_name: "",
    type_time: [],
    type_id: "",
    tab_background_id: "",
    tab_background_name: "",
    tab_background_status: "",
    tab_background_validate_time: "",
    tab_background_crowd_value: "",
    tab_setting_id: "",
    tab_setting_name: "",
    tab_setting_crowd_value: "",
    tab_setting_type: "",
    tab_setting_position: "",

    banner_location: "",
    sort: "",
    crowd_value: "",
    status: "",
    list: [],
    position: "1",
    positionNum: "1",
    goods_info_id: "",
    goods_info_name: "",
    goods_info_time: [],
    goods_info_crowd_value: "",
    goods_info_status: "",
  },
  data() {
    return {
      isEdit: false,
      isShowHrefDialog: false,
      status: [],
      typeStatus: [],
      tabBackgroundStatus: [],
      tabSettingStatus:[],
      goodInfoStatus: [],
      addDialog: false,
      addFormSelectLink: "",
      addForm: {
        activityName: "",
        activitySort: "",
        crowdId: "",
        crowdValue: "",
        crowdId: "",
        timeType: "1",
        circulateTime: {},
      },
      queryParams: {
        validityTime: "", //有效期
        wordName: "", //词组名称
        crowdValue: "", // 人群
        status: "", //状态
      },
      dataList: [],
      tabBackgroundDataList: [],
      goodInfoDataList: [],
      carouselList: {
        bannerLocation: "",
        crowdValue: "",
        status: "",
      },
      bannerLocationList: [
        {
          id: 1,
          name: "第一帧",
        },
        {
          id: 2,
          name: "第二帧",
        },
        {
          id: 3,
          name: "第三帧",
        },
        {
          id: 4,
          name: "第四帧",
        },
        {
          id: 5,
          name: "第五帧",
        },
        {
          id: 6,
          name: "第六帧",
        },
        {
          id: 7,
          name: "第七帧",
        },
        {
          id: 8,
          name: "第八帧",
        },
      ],
      // 时间不能大于当前时间
      disabledDate: (time) => {
        return time.getTime() > Date.now();
      },
    };
  },
  filters: {
    link(data) {
      return data.meta.page_url;
    },
    dateFilter(date) {
      function formatDate(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return (
          year +
          "-" +
          (String(month).length > 1 ? month : "0" + month) +
          "-" +
          (String(day).length > 1 ? day : "0" + day) +
          " " +
          (String(hour).length > 1 ? hour : "0" + hour) +
          ":" +
          (String(minute).length > 1 ? minute : "0" + minute) +
          ":" +
          (String(second).length > 1 ? second : "0" + second)
        );
      }

      if (date) {
        let date1 = formatDate(new Date(date[0]));
        let date2 = formatDate(new Date(date[1]));
        // const nS=new Date(date).getTime()
        return date1 + "至" + date2;
      } else {
        return " ";
      }
    },
    jumpText(val) {
      if (!val) {
        return "app内部跳转";
      } else {
        if (val === "inLink") {
          return "app内部跳转";
        }
        return "跳转至外部";
      }
    },
  },
  mounted() {
    this.initData();
    this.rowDrop();
    this.changeTab("notInvalid");
    this.getDict();
    // this.searchList()
  },
  computed: {
    /**
     *   获取列的状态名称
     */
    getStatusName() {
      return function (timevalue, type) {
        let item = {};
        if (!timevalue) {
          item = {
            id: 4,
            name: "未设置时间",
          };
        } else {
          const _date = new Date().getTime();
          const start = new Date(timevalue[0]).getTime();
          const end = new Date(timevalue[1]).getTime();
          if (_date <= end && _date >= start) {
            item = {
              id: 1,
              name: "生效中",
            };
          } else if (_date > end) {
            item = {
              id: 3,
              name: "已失效",
            };
          } else if (_date < start) {
            item = {
              id: 2,
              name: "待生效",
            };
          }
        }
        if (type == "id") {
          return item.id;
        } else {
          return item.name;
        }
      };
    },
  },
  methods: {
    async getDict() {
      let status = await api.goods.status();
      if (status.code == 200) this.$nextTick(() => (this.status = status.data));
      else this.$message.error(status.msg);
    },
    resetList() {},
    resetQueryParams() {
      this.queryParams = {
        validityTime: "", //有效期
        wordName: "", //词组名称
        crowdValue: "", // 人群
        status: "", //状态
      };
    },

    //链接去掉空格
    urlChange() {
      this.dataForm.link.meta.page_url =
        this.dataForm.link.meta.page_url.trim();
    },
    initData() {
      this.dataList = this.content.list;
    },
    rowDrop() {
      const _this = this;
      const tbody = document.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      sortableObject = Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        ghostClass: "sortable-ghost",
        onEnd: (evt) => {
          const currRow = (_this.dataList || []).splice(evt.oldIndex, 1)[0];
          (_this.dataList || []).splice(evt.newIndex, 0, currRow);
          const currRowData = (_this.content.list || []).splice(
            evt.oldIndex,
            1
          )[0];
          (_this.content.list || []).splice(evt.newIndex, 0, currRowData);
        },
      });
    },
    changeCrowdValue(e) {
      if (!e) {
        this.dataForm.crowdId = "";
      }
      this.$forceUpdate();
    },
    clear_bgs(type) {
      console.log(this.content, "Cotnet");
      this.content[type + "_url"] = "";
      this.content[type + "_color"] = "";
      this.content[type + "_transparency"] = 100;

      // this.content.top_bgRes = "#fff";
      // this.content.meddle_bgRes = "#fff";
      // this.content.bottom_bgRes = "#fff";
      // this.content.hotWord_bgRes = "#fff"
    },

    // 上传banner对应的头部区域背景图片
    async UploadTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.content, type, res.data.url);
      console.log("添加", this.content, "conetnt");
    },
    UploadhotWord_bgRes(res) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.hotWord_bgRes = res.data.url;
    },
    // 上传banner对应的中间区域背景图片
    async UploadMeddleSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.meddle_bgRes = res.data.url;
    },
    // 上传banner对应的底部区域背景图片
    async UploadBottomSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.bottom_bgRes = res.data.url;
    },
    // 设置轮播链接
    onSetLink(link) {
      this.addFormSelectLink = link.meta.page_url;
    },
    changeCrowdType() {
      this.addForm.crowdId = "";
      this.addForm.crowdValue = "";
    },
    async addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        if (
          this.content.list.findIndex(
            (item) => item.wordName === this.addForm.wordName
          ) > -1
        ) {
          this.$message("热词组名称已存在！");
          return;
        }
        this.$set(this.content.list, this.content.list.length, this.addForm);
        this.$message.success("添加成功！");
        this.addDialog = false;
      });
    },

    // 按照规则排序--排序规则优先级：人群 > 帧位 > 生效时间（生效中>待生效>未设置）
    sortByRule(data) {
      const samePeople = this.content.list.filter((item, index) => {
        return Number(item.crowdId) === Number(this.dataForm.crowdId);
      });
      // 相同人群的逻辑
      if (samePeople.length) {
        const sameLocation = samePeople.filter((item, index) => {
          return (
            Number(item.bannerLocation) === Number(this.dataForm.bannerLocation)
          );
        });
        // 相同人群下，相同帧位的逻辑
        if (sameLocation.length) {
          const sameStatus = sameLocation.filter((item, index) => {
            return (
              this.getStatusName(this.dataForm.timevalue) ===
              this.getStatusName(item.timevalue)
            );
          });
          let tempIndex = undefined;
          if (sameStatus.length) {
            this.content.list.forEach((item, index) => {
              if (item.id === sameStatus[sameStatus.length - 1].id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，相同状态，插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === "生效中") {
            this.content.list.forEach((item, index) => {
              if (sameLocation[0].id === item.id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，生效中插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === "待生效") {
            this.content.list.map((item, index) => {
              if (this.getStatusName(item.timevalue) === "生效中") {
                tempIndex = index + 1;
              }
            });
            if (!tempIndex) {
              // 说明没有生效中，找未设置的
              this.content.list.map((item, index) => {
                if (this.getStatusName(item.timevalue) === "未设置时间") {
                  tempIndex = index;
                }
              });
            }
            if (!tempIndex) {
              // 说明没有生效中未设置的，插到帧位最后面
              this.content.list.map((item, index) => {
                if (sameLocation[sameLocation.length - 1].id === item.id) {
                  tempIndex = index + 1;
                }
              });
            }
            // 相同人群，相同帧位，待生效插到生效中后面或未设置时间的前面或同帧位最后面
            this.content.list.splice(tempIndex, 0, data);
          } else {
            this.content.list.map((item, index) => {
              if (sameLocation[sameLocation.length - 1].id === item.id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，未设置插到后面
            this.content.list.splice(tempIndex + 1, 0, data);
          }
        } else {
          // 相同人群下，不同帧位，比较帧位大小
          let tempIndex = undefined;
          // 找到第一个大于新增帧位的项，有则插入到前面，没有则插入到同人群下最后一位
          let maxItem = samePeople.find((item, index) => {
            return item.bannerLocation > this.dataForm.bannerLocation;
          });
          if (maxItem) {
            this.content.list.map((item, index) => {
              if (item.id === maxItem.id) {
                tempIndex = index;
              }
            });
          } else {
            this.content.list.map((item, index) => {
              if (item.id === samePeople[samePeople.length - 1].id) {
                tempIndex = index + 1;
              }
            });
          }
          // 新增帧位小插到前面；新增帧位大插到后面
          this.content.list.splice(tempIndex, 0, data);
        }
      } else {
        // 新人群，直接添加
        let tempIndex = undefined;
        tempIndex = this.content.list.filter((item) => {
          return this.getStatusName(item.timevalue) !== "已失效";
        }).length;
        this.content.list.splice(tempIndex, 0, data);
      }
      this.$nextTick(() => {
        this.searchList();
      });
      // this.content.list.push(Object.assign({}, data));
      // this.$set(this.dataList, this.dataList.length, data)
    },
    changeTab(type) {
      this.activeTab = type;
      this.carouselList.status = "";
      this.searchList();
    },
    //生成唯一id
    genID(length) {
      return Number(
        Math.random().toString().substr(3, length) + Date.now()
      ).toString(36);
    },
    //查询
    searchList() {
      //只有查询全部的时候允许拖拽
      if (
        this.carouselList.status ||
        this.carouselList.crowdValue ||
        this.carouselList.bannerLocation
      ) {
        sortableObject.option("disabled", true);
      } else {
        sortableObject.option("disabled", false);
      }
      // queryParams: {
      //     validityTime: '', //有效期
      //     wordName: '', //词组名称
      //     crowdValue: '', // 人群
      //     status: '', //状态
      //   },
      this.dataList = this.content.list;
      if (this.queryParams.validityTime.length) {
        this.dataList = this.dataList.filter((item, index) => {
          return (
            new Date(this.queryParams.validityTime[0]) * 1 >=
              new Date(item.validityTime[0]) * 1 &&
            new Date(this.queryParams.validityTime[1]) * 1 <=
              new Date(item.validityTime[1]) * 1
          );
        });
      }
      if (this.wordName) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.wordName === item.wordName;
        });
      }
      if (this.crowdValue) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.crowdValue === item.crowdId;
        });
      }
      if (this.status) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.status === item.status;
        });
      }
      // this.initData()
      console.log(this.content.list, 12123);
    },
    toEdit(data, index) {
      this.currentData = data;
      this.currentDataIndex = index;
      this.currentIndex = this.content.list.findIndex(
        (item) => item.id == data.id
      );
      if (!data.timeType) {
        data.timeType = "1";
      }

      this.dataForm = JSON.parse(JSON.stringify(data));
      if (this.dataForm.timeType == 2 && this.dataForm.circulateTime) {
        this.$refs.loopcirculateTime.circulateTime =
          this.dataForm.circulateTime;
        this.$refs.loopcirculateTime.editInit();
      }
      console.log(this.dataForm);
      this.isEdit = true;
      this.addDialog = true;
    },
    toRemove(data) {
      let _self = this;
      return function () {
        _self.content.list.splice(
          _self.content.list.findIndex((item) => item.id == data.id),
          1
        );
        _self.dataList.splice(
          _self.dataList.findIndex((item) => item.id == data.id),
          1
        );
        _self.$message({
          type: "success",
          message: "删除成功!",
        });
      }.confirm(_self)();
    },
    online() {},
    outline() {},
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    selectCrowd(e) {
      if (e) {
        this.dataForm.crowdId = Number(this.options[0].value.trim());
        this.dataForm.crowdValue = this.options[0].label;
      } else {
        this.dataForm.crowdId = "";
        this.dataForm.crowdValue = "";
      }
      this.$forceUpdate();
    },
    async querySearchCrowd() {
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        this.crowdName = data.name;
      }
    },
    
    //弹窗打开
    openTypeSettingViewAlert() {
        this.$refs.typeSettingAlert.open();
    },
    openTypeBackgroundSettingViewAlert() {},
    openTabSettingViewAlert(){
      this.$refs.tabSettingAlert.open();
    },

    // 弹窗回调
    typeSettingAlertDone(val){},
    tabSettingAlertDone(val){},

    //获取列表
    tabBackGroundSearchList() {},
    tabBackGroundResetList() {},

    tabSettingSearchList(){},
    tabSettingResetList(){},
  },
};
</script>
<style lang="scss" scoped>
.title {
  text-align: left;
  line-height: 30px;
  background-color: #f2f2f2;
  margin: 10px 0;
  padding-left: 10px;
}
.three-button {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
.tableBox {
  width: 100%;
}
.dialog-activity-sort {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.tab_crowd_value {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>