
<template>
  <div class="topic-search">
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="12">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
          <img v-if="content.bgImg" :src="content.bgImg" alt="">
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <div>
            <el-button @click="imgOnclick">清除背景图</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">背景色</span>
          <el-color-picker v-model="content.bgRes" size="mini" @change="onSelect"></el-color-picker>
        </div>
      </el-col>
    </el-row>
    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块楼层信息配置</div>
      <!-- <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">搜索区背景</span>
          <div>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'search_background_url')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="content.search_background_url" :src="content.search_background_url" alt="">
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
        <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.search_background_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.search_background_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div>
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('search_background')">清空重置</el-button>
        </div>
      </el-col> -->
      <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">搜索框</span>
          <div>
            <!-- <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'search_box_url')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="content.search_box_url" :src="content.search_box_url" alt="">
            </el-upload> -->
          </div>
        </div>
        <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.search_box_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.search_box_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div>
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('search_box')">清空重置</el-button>
        </div>
      </el-col>
      <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">搜索图</span>
          <div>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'search_image_url')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="content.search_image_url" :src="content.search_image_url" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
          </div>
        </div>
        <!-- <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.search_image_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.search_image_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div> -->
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('search_image')">清空重置</el-button>
        </div>
      </el-col>
      <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">消息按钮</span>
          <div>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'message_button_url')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="content.message_button_url" :src="content.message_button_url" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
          </div>
        </div>
        <!-- <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.message_button_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.message_button_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div> -->
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('message_button')">清空重置</el-button>
        </div>
      </el-col>
      <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">消息角标</span>
          <div>
            <!-- <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'message_sub_url')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="content.message_sub_url" :src="content.message_sub_url" alt="">
            </el-upload> -->
          </div>
        </div>
        <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.message_sub_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.message_sub_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div>
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('message_sub')">清空重置</el-button>
        </div>
      </el-col>
      <el-col style="display: flex;">
        <div class="topic-item topic-item-title">
          <span class="demonstration">热词文字</span>
        </div>
        <div class="topic-item">
          <span class="demonstration">点击设置纯色</span>
          <div>
            <el-color-picker v-model="content.scan_color" size="mini"></el-color-picker>
          </div>
        </div>
        <div class="topic-item">
          <div>
            <el-popover
              placement="top"
              width="400"
              trigger="hover">
              <el-slider v-model="content.scan_transparency"></el-slider>
              <span slot="reference" class="demonstration">透明度设置</span>
            </el-popover>
          </div>
        </div>
        <div class="topic-item">
          <el-button type="text" @click="clear_bgs('scan_')">清空重置</el-button>
        </div>
      </el-col>
    </el-row>
    <!--table-->
    <el-row :gutter="20">
      <div class="title">搜索热词配置</div>
      <el-row class="carouselFlexBox">
        <el-col :span="11" class="carouselFlex">
          <span>词组名称：</span>
          <el-input v-model="queryParams.wordName" size="mini" clearable></el-input>
        </el-col>
        <el-col  class="carouselFlex crowdInput">
          <span>人群id：</span>
          <el-input v-model="queryParams.crowdValue" @blur="querySearchCrowd" @change="crowdName = ''" size="mini" clearable></el-input>
          <div class="crowdNameDiv">{{ crowdName }}</div>
        </el-col>
        <el-col class="carouselFlex">
          <span>有效期：</span>
          <el-date-picker
            v-model="queryParams.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-col>
        <el-col :span="10" class="carouselFlex">
          <span>状态：</span>
          <el-select v-model="queryParams.status" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in queryStatusOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col :span="10" class="carouselButton">
          <el-button type="primary" @click="addList" size="mini">新增</el-button>
          <el-button type="primary" @click="searchList" size="mini">查询</el-button>
          <el-button type="primary" @click="resetQueryParams" size="mini">重置</el-button>
        </el-col>
      </el-row>
      <el-table :data="dataList" size="mini" class="tableBox" style="margin: 0 0 20px" ref="tableBox" :row-key="row => row.id">
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column label="词组名称" width="150" prop="wordName"></el-table-column>
        <el-table-column label="人群" show-overflow-tooltip width="150">
          <template slot-scope="scope">
            <p v-if="scope.row.crowdType == 2">
              {{scope.row.crowdId + '/' + scope.row.crowdValue || '全部人群'}}
            </p>
            <p v-else>该页面已选人群</p>
          </template>
        </el-table-column>
        <el-table-column label="热词" prop="wordValue"></el-table-column>
        <el-table-column label="顺序" >
          <template slot-scope="scope">
            <el-input v-model="scope.row.sort" onkeyup="value=value.replace(/[^\d]/g,'')" @blur="changeSort(scope)" @keyup.enter.native="changeSort(scope)"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="300">
          <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>

              </template>
            </div>
            <div v-else>
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="样式">
          <template slot-scope="scope">
            <div v-if="scope.row.hotStyle">
              {{ scope.row.hotStyle.styleType == 0 ? '默认' : scope.row.hotStyle.styleType == 1 ? '指定' : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div >
              {{ ['未开始', '上线', '已结束', '下线'][scope.row.status - 1] || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button size="mini" v-if="scope.row.status == 1 || scope.row.status == 3 || scope.row.status == 4" @click="toEdit(scope.row, scope.$index)" type="text">编辑
            </el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="toRemove(scope.row)" type="text">删除</el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="online(scope)" type="text">上线</el-button>
            <el-button size="mini" @click="offline(scope)" v-if="scope.row.status == 2" type="text">下线</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <!-- 新增 -->
    <el-dialog class="banner-dialog" :title="`热词${isEdit ? '编辑': '新建'}`" :before-close="addDialogCancel" :visible.sync="addDialog">
      <el-form label-position="right" ref="addRuleForm"  :model="addForm" :rules="addRules" size="small" label-width="100px" label-suffix="：">
        <el-form-item label="热词组名称" prop="wordName">
          <el-input v-model="addForm.wordName" maxlength="20" size="mini" placeholder="请输入热词组名称，20个字符以内" clearable></el-input>
        </el-form-item>
        <el-form-item label="展示时间"  :prop="addForm.timeType == 1 ? 'validityTime' : 'circulateTime'">
          <el-radio  v-model="addForm.timeType" :label="1">固定时段</el-radio>
          <el-date-picker
            v-if="addForm.timeType == 1"
            v-model="addForm.validityTime"
            :default-time="['00:00:00', '23:59:59']"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            :picker-options="{
              disabledDate: (time) => {
                const times = new Date(new Date().toLocaleDateString()).getTime() + 1095 * 8.64e7 - 1
                return time.getTime() < Date.now() - 8.64e7 || time.getTime() > times// 如果没有后面的-8.64e7就是不可以选择今天的
              }
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker><br>
          <el-radio  v-model="addForm.timeType" :label="2">周期循环</el-radio>
          <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
           <br>
          <div v-for="(item,index) in addForm.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[addForm.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{addForm.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
        </el-form-item>
        <el-form-item label="人群圈选" prop="crowdType">
          <el-radio-group v-model="addForm.crowdType" @change="changeCrowdType">
            <el-radio :label="1">该页面已选中人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="addForm.crowdType===2" prop="crowdValue" label="指定人群">
          <el-select
            v-model.trim="addForm.crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-autocomplete style="width: 100%" class="inline-input" v-model.trim="dataForm.crowdValue" :fetch-suggestions="querySearchCrowd" placeholder="请输入人群id" :trigger-on-focus="false"
            @select="handleSelectCrowd" @input="changeCrowdValue"></el-autocomplete> -->
        </el-form-item>
        <el-form-item label="热词输入" prop="wordValue">
          <el-input v-model="addForm.wordValue" oninput="value=value.replace(/[^'\a-\z\A-\Z0-9\u4E00-\u9FA5\.]/g,'')" maxlength="20" placeholder="请输入热词" size="mini" clearable></el-input>
        </el-form-item>
        <el-form-item label="热词样式">
          <div style="display: flex;align-items: center;gap:20px;">
            <el-select v-model.trim="addForm.hotStyle.styleType">
              <el-option :value="0" label="默认"></el-option>
              <el-option :value="1" label="指定"></el-option>
            </el-select>
            <el-button v-if="addForm.hotStyle.styleType == 1" size="mini" type="text" @click="addForm.hotStyle.scanColor='#000000';addForm.hotStyle.scanTransparency=100;addForm.hotStyle.atmosphereIconText='';">重置样式</el-button>
          </div>
          <el-form-item v-if="addForm.hotStyle.styleType == 1" label="">
            <div style="display: flex;align-items: center;gap:20px;margin: 10px 0;">
              <div style="display: flex;align-items: center;">
                点击设置纯色：
                <el-color-picker v-model="addForm.hotStyle.scanColor" size="mini"></el-color-picker>
              </div>
              <div style="display: flex;align-items: center;">
                透明度设置：
                <el-slider v-model="addForm.hotStyle.scanTransparency" style="width: 120px;margin: 0 10px;"></el-slider>
              </div>
              <div style="display: flex;align-items: center;">
                氛围图标词（红底白字）：
                <el-input v-model="addForm.hotStyle.atmosphereIconText" size="mini" style="width: 120px;" maxlength="12"></el-input>
              </div>
              <div style="display: flex;align-items: center;">
                预览：
                <div v-if="addForm.hotStyle.atmosphereIconText">
                  <span style="border-radius: 3px;padding: 2px 5px;font-size: 12px;color: #ffffff;background-color: #ff0000;">
                    {{ addForm.hotStyle.atmosphereIconText }}
                  </span>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form-item>
        <!-- <el-form-item label="转跳类型" prop="hrefType">
          <el-select
            v-model.trim="addForm.hrefType"
            placeholder="请选择转跳类型"
          >
            <el-option
              v-for="item in hrefOption"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="转跳类型">
          <el-select v-model="addForm.hrefType" placeholder="请选择">
            <el-option
              v-for="item in linkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="addForm.hrefType == 'couponGroupPurchasePage'" label="优惠券ID" prop="couponId" >
          <el-input v-model="addForm.couponId" size="mini" placeholder="只能输入一个优惠券id" style="width: 200px;" clearable></el-input>
        </el-form-item>
      </el-form>
      <div class="topic-image-picker" v-if="addForm.hrefType && addForm.hrefType !== 'dynamic' && addForm.hrefType !== 'couponGroupPurchasePage'">
        <el-input placeholder="链接地址" v-model.trim="addForm.link.meta.page_url" @input="urlChange">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>
      <div v-if="addForm.hrefType === 'dynamic'">
        <div class="topic-image-picker">
          <el-input style="width:200px" placeholder="输入跳转id" v-model="addForm.link.meta.dynamicId">
            <template slot="prepend">跳转id</template>
          </el-input>
          <el-button type="primary" @click="putDynamicLink(addForm)">生成链接</el-button>
        </div>
        <el-input placeholder="链接地址" v-model.trim="addForm.link.meta.page_url"  @input="urlChange">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>
      <div v-if="addForm.hrefType==='topic'">
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addDialogConfirm">确定</el-button>
      </div>
    </el-dialog>
    <!-- 分类引入 -->
    <el-row :gutter="20">
      <div class="title">分类配置</div>
      <el-row class="carouselFlexBox">
        <el-col :span="10" class="carouselFlex crowdInput">
          <span>分类名称：</span>
          <el-input v-model="queryClassParams.className" maxlength="20" size="mini" clearable></el-input>
        </el-col>
        <el-col :span="10" class="carouselFlex">
          <span>状态：</span>
          <el-select v-model="queryClassParams.status" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in queryStatusOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col class="carouselFlex">
          <span>有效期：</span>
          <el-date-picker
            v-model="queryClassParams.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-col>
        <el-col class="carouselButton">
          <el-button type="primary" @click="addClassList" size="mini">新增</el-button>
          <el-button type="primary" @click="searchClassList" size="mini">查询</el-button>
          <el-button type="primary" @click="resetClassQueryParams" size="mini">重置</el-button>
        </el-col>
      </el-row>
      <el-table :data="dataClassList" size="mini" class="tableBox" style="margin: 0 0 20px" ref="tableBox" :row-key="row => row.id">
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column label="分类名称" prop="className" width="150"></el-table-column>
        <el-table-column label="有效期" width="300">
          <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
            </div>
            <div v-else>
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div >
              {{ ['未开始', '上线', '已结束', '下线'][scope.row.status - 1] || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button size="mini" v-if="scope.row.status == 1 || scope.row.status == 3 || scope.row.status == 4" @click="toClassEdit(scope.row, scope.$index)" type="text">编辑
            </el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="toClassRemove(scope.row, scope.$index)" type="text">删除</el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="onClassline(scope)" type="text">上线</el-button>
            <el-button size="mini" @click="offClassline(scope)" v-if="scope.row.status == 2" type="text">下线</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <!-- 新增 -->
    <el-dialog class="banner-dialog" :title="`分类活动${isClassEdit ? '编辑': '新建'}`" :before-close="addClassDialogCancel" :visible.sync="addClassDialog">
      <el-form label-position="right" ref="addClassRuleForm"  :model="addClassForm" :rules="addClassRules" size="small" label-width="100px" label-suffix="：">
        <el-form-item label="分类名称" prop="className">
          <el-input v-model="addClassForm.className" maxlength="20" size="mini" placeholder="请输入分类名称，20个字符以内" clearable></el-input>
        </el-form-item>
        <el-form-item label="展示时间" :prop="addClassForm.timeType == 1 ? 'validityTime' : 'circulateTime'">
          <el-radio  v-model="addClassForm.timeType" :label="1">固定时段</el-radio>
          <el-date-picker
            v-if="addClassForm.timeType == 1"
            v-model="addClassForm.validityTime"
            :default-time="['00:00:00', '23:59:59']"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            :picker-options="{
              disabledDate: (time) => {
                const times = new Date(new Date().toLocaleDateString()).getTime() + 1095 * 8.64e7 - 1
                return time.getTime() < Date.now() - 8.64e7 || time.getTime() > times// 如果没有后面的-8.64e7就是不可以选择今天的
              }
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker><br>
          <el-radio  v-model="addClassForm.timeType" :label="2">周期循环</el-radio>
          <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
           <br>
          <div v-for="(item,index) in addForm.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[addForm.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{addForm.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
        </el-form-item>
        <!-- <el-form-item label="分类背景色" prop="classBackground">
          <el-radio-group v-model="addClassForm.classBackgroundType">
            <el-radio :label="1">纯色</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group>
          <div class="add-color-item" v-if="addClassForm.classBackgroundType == 1">
            <span class="demonstration">点击设置纯色</span>
            <div>
              <el-color-picker v-model="addClassForm.classBackground" size="mini"></el-color-picker>
            </div>
            <div class="block" style="width: 200px;">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider v-model="addClassForm.classBackgroundTransparency" :format-tooltip="formatTooltip"></el-slider>
              </div>
            </div>
          </div>
          <div class="add-color-back" v-else>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTopSearchBg(e, 'classBackground')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addClassForm.classBackgroundType == 2 && addClassForm.classBackground" :src="addClassForm.classBackground" alt="">
            </el-upload>
            <el-button size="small" @click="addClassForm.classBackground = ''">清空重置</el-button>
          </div>
        </el-form-item> -->
        <el-form-item label="分类按钮" prop="classBtnBackground">
          <!-- <el-radio-group v-model="addClassForm.classBtnBackgroundType">
            <el-radio :label="1">纯色</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group> -->
          <!-- <div class="add-color-item" v-if="addClassForm.classBtnBackgroundType == 1">
            <span class="demonstration">点击设置纯色</span>
            <div>
              <el-color-picker v-model="addClassForm.classBtnBackground" size="mini"></el-color-picker>
            </div>
            <div class="block" style="width: 200px;">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider v-model="addClassForm.classBtnBackgroundTransparency" :format-tooltip="formatTooltip"></el-slider>
              </div>
            </div>
          </div> -->
          <div class="add-color-back">
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadClassBg(e, 'classBtnBackground')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addClassForm.classBtnBackground" :src="addClassForm.classBtnBackground" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addClassForm.classBtnBackground = ''">清空重置</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addClassDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addClassDialogConfirm">确定</el-button>
      </div>
    </el-dialog>
    <!-- tab配置 -->
    <el-row :gutter="20">
      <div class="title">tab配置</div>
      <el-row class="carouselFlexBox">
        <el-col  :span="10" class="carouselFlex">
          <span>id：</span>
          <el-input v-model="queryTabParams.id" size="mini" clearable></el-input>
        </el-col>
        <el-col  :span="10" class="carouselFlex">
          <span>tab名称：</span>
          <el-input v-model="queryTabParams.tabName" size="mini" clearable></el-input>
        </el-col>
        <el-col  :span="20" class="carouselFlex crowdInput">
          <span>人群id：</span>
          <el-input v-model="queryTabParams.crowdValue" @blur="queryTabSearchCrowd" @change="tabCrowdName = ''" size="mini" clearable></el-input>
          <div class="crowdNameDiv">{{ tabCrowdName }}</div>
        </el-col>
        <el-col  :span="10" class="carouselFlex">
          <span>tab类别：</span>
          <el-select v-model="queryTabParams.class" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in classOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col  :span="10" class="carouselFlex">
          <span>位置：</span>
          <el-select v-model="queryTabParams.position" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in postionOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col :span="10" class="carouselFlex">
          <span>状态：</span>
          <el-select v-model="queryTabParams.status" placeholder="请选择" size="mini" clearable>
            <el-option v-for="item in queryStatusOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-col>
        <el-col class="carouselFlex">
          <span>有效期：</span>
          <el-date-picker
            v-model="queryTabParams.validityTime"
            :default-time="['00:00:00', '23:59:59']"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-col>
        <el-col class="carouselButton">
          <el-button type="primary" @click="addTabList" size="mini">新增</el-button>
          <el-button type="primary" @click="searchTabList" size="mini">查询</el-button>
          <el-button type="primary" @click="resetQueryTabParams" size="mini">重置</el-button>
        </el-col>
      </el-row>
      <el-table :data="tabConfigList" size="mini" class="tableBox" style="margin: 0 0 20px" ref="tableBox" :row-key="row => row.id">
        <el-table-column label="id" prop="id" width="150"></el-table-column>
        <el-table-column label="tab名称" prop="tabName" width="150"></el-table-column>
        <el-table-column label="tab类别" prop="tabType" width="150">
          <template slot-scope="scope">
            <div >
              {{ scope.row.tabType == 1 ? '文字' : '图片' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="预览" width="150">
          <template slot-scope="scope">
            <img class="preview-img" v-if="scope.row.tabType == 2" :src="scope.row.classImg" alt="">
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="位置" >
          <template slot-scope="scope">
            {{ scope.row.position }}
            <!-- <el-input v-model="scope.row.sort" onkeyup="value=value.replace(/[^\d]/g,'')" @blur="changeSort(scope, 'tab')" @keyup.enter.native="changeSort(scope, 'tab')"></el-input> -->
          </template>
        </el-table-column>
        <el-table-column label="人群" show-overflow-tooltip width="200">
          <template slot-scope="scope">
            <p v-if="scope.row.crowdType == 2">
              {{scope.row.crowdId + '/' + scope.row.crowdValue || '全部人群'}}
            </p>
            <!-- <p v-else>{{ core.crowdId ? core.crowdId + '/' + core.crowdValue : '全部人群' }}</p> -->
            <p v-else>该页面已选人群</p>
          </template>
        </el-table-column>
        <el-table-column label="有效期" width="300">
          <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
            </div>
            <div v-else>
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div >
              {{ ['未开始', '上线', '已结束', '下线'][scope.row.status - 1] || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button size="mini" v-if="scope.row.status == 1 || scope.row.status == 3 || scope.row.status == 4" @click="toTabEdit(scope.row, scope.$index)" type="text">编辑
            </el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="toTabRemove(scope.row)" type="text">删除</el-button>
            <el-button size="mini" v-if="scope.row.status == 4" @click="onTabline(scope)" type="text">上线</el-button>
            <el-button size="mini" @click="offTabline(scope)" v-if="scope.row.status == 2" type="text">下线</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <!-- 新增 -->
    <el-dialog class="banner-dialog" :title="`tab${isTabEdit ? '编辑': '新建'}`" :before-close="addTabDialogCancel" :visible.sync="addTabDialog">
      <el-form label-position="right" ref="addTabRuleForm"  :model="addTabForm" :rules="addTabRules" size="small" label-width="100px" label-suffix="：">
        <el-form-item label="tab名称" prop="tabName">
          <el-input v-model="addTabForm.tabName" maxlength="20" size="mini" placeholder="请输入tab名称，20个字符以内" clearable></el-input>
        </el-form-item>
        <el-form-item label="展示时间" :prop="addTabForm.timeType == 1 ? 'validityTime' : 'circulateTime'">
          <el-radio  v-model="addTabForm.timeType" :label="1">固定时段</el-radio>
          <el-date-picker
            v-if="addTabForm.timeType == 1"
            :default-time="['00:00:00', '23:59:59']"
            v-model="addTabForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            :picker-options="{
              disabledDate: (time) => {
                const times = new Date(new Date().toLocaleDateString()).getTime() + 1095 * 8.64e7 - 1
                return time.getTime() < Date.now() - 8.64e7 || time.getTime() > times// 如果没有后面的-8.64e7就是不可以选择今天的
              }
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker><br>
          <el-radio  v-model="addTabForm.timeType" :label="2">周期循环</el-radio>
          <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
           <br>
          <div v-for="(item,index) in addForm.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[addForm.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{addForm.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
        </el-form-item>
        <el-form-item label="人群圈选" prop="crowdType">
          <el-radio-group v-model="addTabForm.crowdType" @change="changeCrowdType">
            <el-radio :label="1">该页面已选中人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="addTabForm.crowdType===2" prop="crowdValue" label="指定人群">
          <el-select
            v-model.trim="addTabForm.crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectTabCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item  prop="position" label="位置">
          <el-select
            v-model.trim="addTabForm.position"
            placeholder="请选择位置"
            clearable
          >
            <el-option v-for="item in postionOption" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类别" :prop="addTabForm.tabType == 1 ? 'class' : 'classImg'">
          <el-radio-group v-model="addTabForm.tabType">
            <el-radio :label="1">文字</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group>
          <div class="add-color-item" v-if="addTabForm.tabType == 1">
            <el-input v-model="addTabForm.class" maxlength="6" oninput="value=value.replace(/[^'\a-\z\A-\Z0-9\u4E00-\u9FA5\.]/g,'')" size="mini" placeholder="限制6个字，默认居中显示" clearable></el-input>
            <div style="display: flex;align-items:center;margin: 10px 0;">
              <span class="demonstration" style="margin-right: 10px;">点击设置文字纯色</span>
              <el-color-picker v-model="addTabForm.classColor" size="mini"></el-color-picker>
            </div>
            <div class="block" style="width: 200px;">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider v-model="addTabForm.classColorTransparency" :format-tooltip="formatTooltip"></el-slider>
              </div>
            </div>
          </div>
          <div class="add-color-back" v-else>
            <el-upload class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
              :on-success="(e) => UploadTabTopSearchBg(e, 'classImg')">
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addTabForm.tabType == 2 && addTabForm.classImg" :src="addTabForm.classImg" alt="">
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addTabForm.classImg = ''">清空重置</el-button>
          </div>
        </el-form-item>
        <el-form-item  label="是否加醒目标识">
          <el-switch
            v-model="addTabForm.isEyeType"
          >
          </el-switch>
          <el-upload v-if="addTabForm.isEyeType" class="upload-demo" ref="upload" accept="image/jpeg,image/jpg,image/png,image/gif"  :show-file-list="false" :before-upload="() => {loading = true; return true;}"
            :on-success="(e) => UploadTabTopSearchBg(e, 'isEyeImg')">
            <el-button size="small" type="primary">点击上传</el-button>
            <img v-if="addTabForm.isEyeImg" :src="addTabForm.isEyeImg" alt="">
            <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
          </el-upload>
        </el-form-item>
        <el-form-item label="转跳类型" prop="hrefType">
          <el-radio-group @change="hrefTypeChange" v-model="addTabForm.hrefType">
            <el-radio :label="1">链接</el-radio>
            <el-radio :label="2">tab模板页</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="addTabForm.hrefType == 1" label="链接类型" prop="urlType">
          <el-select v-model="addTabForm.urlType" placeholder="请选择">
            <el-option
              v-for="item in linkOptionsArr"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <template v-if="addTabForm.hrefType == 1">
          <div class="topic-image-picker" v-if="addTabForm.urlType && addTabForm.urlType !== 'dynamic'">
            <el-input placeholder="链接地址" v-model.trim="addTabForm.link.meta.page_url" @input="urlTabChange">
              <template slot="prepend">跳转链接</template>
            </el-input>
          </div>
          <div v-if="addTabForm.urlType === 'dynamic'">
            <div class="topic-image-picker">
              <el-input style="width:200px" placeholder="输入跳转id" v-model="addTabForm.link.meta.dynamicId">
                <template slot="prepend">跳转id</template>
              </el-input>
              <el-button type="primary" @click="putDynamicLink(addTabForm)">生成链接</el-button>
            </div>
            <el-input placeholder="链接地址" v-model.trim="addTabForm.link.meta.page_url"  @input="urlTabChange">
              <template slot="prepend">跳转链接</template>
            </el-input>
          </div>
          <div v-if="addTabForm.urlType==='topic'">
            <page-link @select="onTabChooseSetLink" :params="{branchCode: topic.branchCode}"></page-link>
          </div>
        </template>

        <!-- <el-form-item v-if="addTabForm.hrefType == 1" label="路径" prop="hrefUrl">
          <el-input v-model="addTabForm.hrefUrl" size="mini" clearable></el-input>
          <el-button type="primary" size="small" @click="isShowHrefDialog = true">more</el-button>
        </el-form-item> -->
        <el-form-item v-if="addTabForm.hrefType == 2" label="tab模板id" prop="hrefTemplateId">
          <el-input v-model="addTabForm.hrefTemplateId" @input="searchTemplateId" size="mini" clearable></el-input>
          <span v-if="templateName">{{ templateName }}</span>
        </el-form-item>
      </el-form>
      <el-dialog
        title="跳转链接配置"
        :visible.sync="isShowHrefDialog"
        width="30%"
        append-to-body
      >
        <page-link @select="onTabSetLink" :params="{branchCode: topic.branchCode}"></page-link>
        <span slot="footer" class="dialog-footer">
          <el-button @click="hrefTabCancel">取 消</el-button>
          <el-button type="primary" @click="hrefTabConfirm">确 定</el-button>
        </span>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addTabDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addTabDialogConfirm">确定</el-button>
      </div>
    </el-dialog>
    <loopcirculateTime ref="loopcirculateTime" @loopcirculateTimeBack="loopcirculateTimeBack"></loopcirculateTime>
  </div>
</template>
<script>
import loopcirculateTime from '../../../components/loopcirculateTime.vue';
import base from "../../base";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import Sortable from 'sortablejs';
let sortableObject = {}
export default {
  name: 'searchBox',
  extends: base,
  components: { swiperPoint,loopcirculateTime },
  contentDefault: {
    search_box_color: "#eee",
    message_sub_color: "#eee",
    search_image_color: "#eee",
    message_button_color: "#eee",
    scan_color: "#eee",
    search_image_transparency: 100,
    search_box_transparency: 100,
    message_button_transparency: 100,
    message_sub_transparency: 100,
    scan_transparency: 100,
    search_background_color: "",
    list: [],
  },
  props: {
    core: Object,
  },
  data() {
    return {
      linkOptions: [
        {
          value: "",
          label: "不挂链接"
        },
        {
          value: "topic",
          label: "专题页链接"
        },
        {
          value: "stores",
          label: "店铺页链接"
        },
        {
          value: "dynamic",
          label: "动态商品链接"
        },
        {
          value: 'couponGroupPurchasePage',
          label: '优惠券凑单页'
        }
      ],
      addTabFormSelectLink: '',
      tabCrowdName: '',
      templateName: '',
      templateData: [],
      addTabDialog: false,
      isTabEdit: false,
      isClassEdit: false,
      addClassDialog: false,
      dialogClassType: 'addClassForm',
      dialogClassRule: 'addClassRuleForm',
      isShowHrefDialog: false,
      tabConfigList: [],
      classOption: [
        {id: 1, name: '文字'},
        {id: 2, name: '图片'}
      ],
      postionOption: [
        {id: 1, name: '左1'},
        {id: 2, name: '左2'},
        {id: 3, name: '左3'},
        {id: 4, name: '左4'},
        {id: 5, name: '左5'},
        {id: 6, name: '左6'},
        {id: 7, name: '左7'},
        {id: 8, name: '左8'},
        {id: 9, name: '左9'},
      ],
      addTabForm: {
        tabName: '',
        class: '',
        classImg: '',
        position: '',
        classColor: '#000000',
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        className: '', //分类名称
        circulateTime: '',//周期循环时间
        status: 1,
        timeType: 1,//展示时间type,
        classColorTransparency: 100,
        tabType: 1,
        hrefTemplateId: '',
        hrefType: 1,
        hrefUrl: '',
        isEyeType: 1,
        isEyeImg: "",
        link: {
          meta: {
            page_url: ""
          }
        },
      },
      addClassForm: {
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        className: '', //分类名称
        // classBackgroundType: 1,
        // classBackground: '#eee',
        // classBtnBackgroundType: 1,
        classBtnBackground: '',
        circulateTime: '',
        status: 1,
        timeType: 1,
        // classBackgroundTransparency: 100,
        classBtnBackgroundTransparency: 100,
      },
      queryTabParams: {
        id: '',
        tabName: '',
        status: '',
        validityTime: '', //有效期
        crowdValue: '',
        class: '',
        position: '',
      },
      addTabRules: {
        tabName: [
          { required: true, message: "请填写背景名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" }
        ],
        crowdType: [{ required: true, message: "请选择指定人群", trigger: "change" }],
        crowdValue: [
          { required: true, message: "请填写人群名称", trigger: "blur" },
        ],
        position: [
          { required: true, message: "请选择位置", trigger: "change" }
        ],
        validityTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        circulateTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        class: [
          { required: true, message: "请输入类别文字", trigger: "blur" }
        ],
        classImg: [
          { required: true, message: "请选择类别图片", trigger: "change" }
        ],
        hrefType: [
          { required: true, message: "请选择转跳类型", trigger: "change" }
        ],
        urlType: [
          { required: true, message: "请选择链接类型", trigger: "change" }
        ],
        hrefUrl: [
          { required: true, message: "请输入转跳链接", trigger: "blur" }
        ],
        hrefTemplateId: [
          { required: true, message: "请输入转跳模板id", trigger: "blur" }
        ],
      },
      addClassRules: {
        className: [
          { required: true, message: "请填写分类名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" }
        ],
        crowdType: [{ required: true, message: "请选择指定人群", trigger: "change" }],
        crowdValue: [
          { required: true, message: "请填写人群名称", trigger: "blur" },
        ],
        classBackground: [
          { required: true, message: "分类背景色不能为空", trigger: "change" }
        ],
        classBtnBackground: [
          { required: true, message: "分类按钮不能为空", trigger: "change" },
        ],
        validityTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        circulateTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
      },
      addRules: {
        wordName: [
          { required: true, message: "请填写热词组名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" }
        ],
        crowdType: [{ required: true, message: "请选择指定人群", trigger: "change" }],
        crowdValue: [
          { required: true, message: "请填写人群名称", trigger: "blur" },
        ],
        wordValue: [
          { required: true, message: "请填写热词", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" }
        ],
        hrefType: [
          { required: true, message: "请选择转跳类型", trigger: "change" }
        ],
        hrefUrl: [
          { required: true, message: "请填写转跳链接", trigger: "blur" },
        ],
        validityTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        circulateTime: [
          { required: true, message: "展示时间不能为空", trigger: "change" }
        ],
        couponId: [
          { required: true, message: "优惠券ID不能为空", trigger: "change" }
        ]
      },
      // 时间不能大于当前时间
      disabledDate: time => {
        return time.getTime() > Date.now()
      },
      isEdit: false,
      goods: [{ required: true, validator: this.goodsValid, trigger: "blur" }],
      keys: 'id',
      dataList: [], // 查询完的列表
      dataClassList: [],
      addForm: {
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        wordId: '', //词组id
        wordName: '', //词组名称
        crowdValue: '', // 人群
        wordValue: '',
        hrefType: '',
        hrefUrl: '',
        timeType: 1,
        circulateTime: '',
        status: 1,
        hotStyle: {
          styleType: 0,
          scanColor: '#eeeeee',
          scanTransparency: 100,
          atmosphereIconText: '',
        },
        couponId: '',
        link: {
          meta: {
            page_url: ""
          }
        },
      },
      crowdName: '',
      addFormSelectLink: '',
      queryParams: {
        validityTime: '', //有效期
        wordName: '', //词组名称
        crowdValue: '', // 人群
        status: '', //状态
      },
      queryClassParams: {
        validityTime: '', //有效期
        className: '', //词组名称
        status: '', //状态
      },
      queryStatusOption: [
        {id: '', name: '全部'},
        {id: 1, name: '未开始'},
        {id: 2, name: '上线'},
        {id: 3, name: '已结束'},
        {id: 4, name: '下线'},
      ],
      hrefOption: [
        {value: 1, label: '不挂链接'},
        {value: 2, label: '专题页链接'},
        {value: 3, label: '店铺页链接'},
        {value: 4, label: '动态商品页链接'},
      ],
      carouselList: {
        bannerLocation: '',
        crowdValue: '',
        status: ''
      },
      currentData: {},
      currentDataIndex: 0,
      currentId: '',
      currentClassId: '',
      currentTabId: '',
      currentIndex: 0,
      activeTab: 'notInvalid',
      selectLoading: false,
      options: [],
      pickerOptions0: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      pickerOptions1: {
        shortcuts: [{
          text: '未来一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来六个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '未来一年',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      loading: false,
      addDialog: false,
      dataForm: {
        bannerLocation: '',
        bannerType: "inLink",
        image: '',
        link: {
          meta: {
            id: 0,
            page_url: ''
          }
        },
        timevalue: '',
        // bgRes: "#00B377",
        bgRes: "#eee",
        rest_bgRes: "#ffffff",
        crowdType: 1,
        crowdValue: '',
        crowdId: '',
        timeType:'1',
        circulateTime:{}
      },
    }
  },
  filters: {
    link(data) {
      return data.meta.page_url;
    },
    dateFilter(date) {
      function formatDate(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return year + '-' + (String(month).length > 1 ? month : '0' + month) + '-' +
          (String(day).length > 1 ? day : '0' + day) + ' ' + (String(hour).length > 1 ? hour : '0' + hour) + ':' + (String(minute).length > 1 ? minute : '0' + minute)
          + ':' + (String(second).length > 1 ? second : '0' + second)
      }

      if (date) {
        let date1 = formatDate(new Date(date[0]));
        let date2 = formatDate(new Date(date[1]));
        // const nS=new Date(date).getTime()
        return date1 + "至" + date2
      } else {
        return " "
      }
    },
    jumpText(val) {
      if (!val) {
        return "app内部跳转"
      } else {
        if (val === "inLink") {
          return "app内部跳转"
        }
        return "跳转至外部"
      }
    }
  },
  mounted() {
    this.getTemplateData();
    this.initData();
    this.initDataStatus();
    this.initClassList();
    this.initTabConfigList();

    // this.rowDrop()
    this.changeTab('notInvalid')
    // this.searchList()
  },
  computed: {
    linkOptionsArr() {
      return this.linkOptions.filter(item => item.value != "");
    },
    /**
     *   获取列的状态名称
     */
    getStatusName() {
      return function (timevalue, type) {
        let item = {}
        if (!timevalue) {
          item = {
            id: 4,
            name: '未设置时间'
          }
        } else {
          const _date = new Date().getTime();
          const start = new Date(timevalue[0]).getTime();
          const end = new Date(timevalue[1]).getTime();
          if (_date <= end && _date >= start) {
            item = {
              id: 1,
              name: '生效中'
            }
          } else if (_date > end) {
            item = {
              id: 3,
              name: '已失效'
            }
          } else if (_date < start) {
            item = {
              id: 2,
              name: '待生效'
            }
          }
        }
        if (type == 'id') {
          return item.id
        } else {
          return item.name
        }
      }
    },
    // list() {
    //   let list = _.get(this, 'content.list')
    //   if (list) {
    //     if (list.length > 0 && list[0].link.meta) {
    //       this.$nextTick(function () {
    //         this.setSort()
    //       })
    //     }
    //     return list
    //   } else {
    //     return [];
    //   }
    // }
  },
  methods: {
    putDynamicLink(item) {
      if (!item.link.meta.dynamicId) {
        this.$message({
          message: '请输入跳转id再点击生成链接',
          type: 'warning'
        });
        return false;
      }
      item.link.meta.page_url = `ybmpage://homeSteadyChannel?strategyId=${item.link.meta.dynamicId}`
    },
    async UploadTabTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.addTabForm, type, res.data.url);
      this.$refs['addTabRuleForm'].clearValidate()
    },
    toTabRemove(data) {
      let _self = this;
      return function () {
        _self.content.tabConfigList.splice(_self.content.tabConfigList.findIndex((item) => item.id == data.id), 1)
        _self.tabConfigList.splice(_self.tabConfigList.findIndex((item) => item.id == data.id), 1)
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    toTabEdit(data, index) {
      this.addTabForm = {...data};
      this.dialogClassType = 'addTabForm';
      this.dialogClassRule = 'addTabRuleForm'
      this.currentTabId = data.id;
      this.currentTabDataIndex = index;
      this.currentTabIndex = this.content.tabConfigList.findIndex((item) => item.id == data.id);
      if(!data.timeType){
        data.timeType = 1
      }

      if(this.addTabForm.timeType==2&&this.addTabForm.circulateTime){
        this.$refs.loopcirculateTime.circulateTime=this.addTabForm.circulateTime
        this.$refs.loopcirculateTime.editInit()

      }
      this.searchTemplateId();
      this.isTabEdit = true;
      this.addTabDialog = true;
    },
    onTabline(scope) {
      this.$confirm(
        "确定要执行上线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.tabConfigList[this.content.tabConfigList.findIndex(i => i.id == scope.row.id)].status = 2;
        this.initData();
        this.searchTabList();
        this.$message.success("操作成功！")
      });
    },
    offTabline(scope) {
      this.$confirm(
        "确定要执行下线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.tabConfigList[this.content.tabConfigList.findIndex(i => i.id == scope.row.id)].status = 4;
        this.initData();
        this.searchTabList();
        this.$message.success("操作成功！")
      });
    },
    hrefTabConfirm() {
      this.addTabForm.hrefUrl = this.addTabFormSelectLink;
      this.$refs['addTabRuleForm'].clearValidate('hrefUrl');
      this.isShowHrefDialog = false;
    },
    hrefTabCancel() {
      this.addTabFormSelectLink = '';
      this.isShowHrefDialog = false;
    },
    hrefTypeChange() {
      this.$refs['addTabRuleForm'].clearValidate('hrefUrl');
      this.$refs['addTabRuleForm'].clearValidate('hrefTemplateId');
    },
    async queryTabSearchCrowd() {
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${this.queryTabParams.crowdValue}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.code == 1000 && this.queryTabParams.crowdValue) {
        this.tabCrowdName = res.data.name;
      }
    },
    async getTemplateData() {
      let params = {
        category: "app",
        page_type: "tab",
        pageSize:99999
        // department: 0,
      }
      const result = await api.topic.list(params);
      if (result.code == 200) {
        this.templateData = result.data.rows;
      }
    },
    searchTemplateId() {
      let obj = this.templateData.find(item => item.page_id == this.addTabForm.hrefTemplateId) || {};
      if (obj) {
        this.templateName = obj.page_name;
      } else {
        this.templateName = "";
      }
    },
    addTabDialogCancel() {
      this.resetTabAddForm();
      this.addTabDialog = false;
    },
    addTabDialogConfirm() {
      this.$refs.addTabRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        if (!this.isTabEdit) {
          let id = 0;
          id = Math.floor(Math.random() * 90000) + 10000;
          if ((this.content.tabConfigList || []).findIndex(item => item.id == id) > -1) {
            this.$message("id错误，请重新添加！");
            return;
          }
          this.$set(this.addTabForm, 'id', id);
        }
        let classFlag = false;
        let filterArr = JSON.parse(JSON.stringify(this.content.tabConfigList || []));
        if (this.isTabEdit) {
          filterArr = filterArr.filter(item => item.id != this.addTabForm.id);
        }
        filterArr = filterArr.filter(item => item.position == this.addTabForm.position);

        // if (filterArr.findIndex(item => item.crowdId == this.addTabForm.crowdId) > -1
        // || (filterArr.findIndex(item => item.crowdType == 1) > -1 && !this.core.crowdId)
        // || (filterArr.length && this.addTabForm.crowdType == 1 && !this.core.crowdId)
        // )
        if (
          (this.addTabForm.crowdType == 1 && filterArr.findIndex(item => item.crowdType == 1) > -1)
          || this.addTabForm.crowdType != 1 && filterArr.findIndex(item => item.crowdType != 1 && item.crowdId == this.addTabForm.crowdId && item.timeType == this.addTabForm.timeType) > -1
        ) {
          if (this.addTabForm.timeType == 1) {
            const start_form = new Date(this.addTabForm.validityTime[0]).getTime();
            const end_form = new Date(this.addTabForm.validityTime[1]).getTime();
            filterArr.forEach(item => {
              if(item.timeType == 1) {
                const start = new Date(item.validityTime[0]).getTime();
                const end = new Date(item.validityTime[1]).getTime();
                if (start_form <= start && end_form >= end) {
                  classFlag = true;
                } else if ((start_form >= start && start_form <= end) || (end_form >= start && end_form <= end)) {
                  classFlag = true;
                }
              }
            })
          } else if (this.addTabForm.timeType == 2) {
            // 1:周 2:月 3:日
            filterArr.forEach(item => {
              if(Array.isArray(this.addTabForm.circulateTime.circulateList)){
                // let _date =  new Date().toLocaleTimeString('en-US', {hour12: false});
                if (item.timeType == 2) {
                  let _date = this.addTabForm.circulateTime.circulateList[0].selectTimeData;
                  if(this.addTabForm.circulateTime.circulateType==3){
                    item.circulateTime.circulateList.forEach(element => {
                      if ((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])) {
                        classFlag = true;
                      }
                    });
                  }
                  if(item.circulateTime.circulateType==1 || item.circulateTime.circulateType==2){
                    item.circulateTime.circulateList.forEach(element => {
                      if (this.addTabForm.circulateTime.circulateList[0].weekOrday==element.weekOrday&&((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0]))) {
                        classFlag = true;
                      }
                    });
                  }
                }
              }
            })
          }
        }

        if (classFlag) {
          this.$message.warning("展示时间不能包含列表已存在数据时间！");
          return;
        }



        if (this.addTabForm.hrefType == 2 && !this.templateName) {
          this.$message("模板id不存在或输入错误，请检查模板id！");
          return;
        }
        if (!this.content.tabConfigList) {
          this.$set(this.content, 'tabConfigList', []);
        }
        if (this.addTabForm.crowdType == 1) {
          this.addTabForm.crowdId = this.core.crowdId;
          this.addTabForm.crowdValue = this.core.crowdValue;
        }
        let arr = [...this.content.tabConfigList];
        if(this.isTabEdit) {
          this.$set(this.content.tabConfigList, this.content.tabConfigList.findIndex(i => i.id == this.currentTabId), this.addTabForm);
        } else {
          arr.splice(0, 0, this.addTabForm);
          this.$set(this.content, 'tabConfigList', arr);
        }
        this.$message.success(`${this.isTabEdit ? '编辑':'添加'}成功！`);
        this.content.tabConfigList.forEach((item,index) => {
          item.sort = index + 1;
        })
        this.resetTabAddForm();
        this.addTabDialog = false;
        this.initData();
        this.initTabConfigList();
        this.searchTabList();
      });
    },
    resetTabAddForm() {
      this.addTabForm = {
        tabName: '',
        class: '',
        classImg: '',
        position: '',
        classColor: '#000000',
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        className: '', //分类名称
        circulateTime: '',//周期循环时间
        status: 1,
        timeType: 1,//展示时间type,
        classColorTransparency: 100,
        tabType: 1,
        hrefTemplateId: '',
        hrefType: 1,
        hrefUrl: '',
        isEyeType: 1,
        isEyeImg: "",
        link: {
          meta: {
            page_url: ""
          }
        },
      }
      this.templateName = "";
    },
    resetQueryTabParams() {
      this.queryTabParams = {
        id: '',
        tabName: '',
        status: '',
        validityTime: '', //有效期
        crowdValue: '',
        class: '',
        position: '',
      }
      this.tabCrowdName = "";
    },
    searchTabList() {
      this.tabConfigList = this.content.tabConfigList;
      if (this.queryTabParams.validityTime.length) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return new Date(this.queryTabParams.validityTime[0]) * 1 >= new Date(item.validityTime[0]) * 1 && new Date(this.queryTabParams.validityTime[1]) * 1 <= new Date(item.validityTime[1]) * 1;
        })
      }
      if (this.queryTabParams.id) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return this.queryTabParams.id == item.id;
        })
      }
      if (this.queryTabParams.crowdValue) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return (this.queryTabParams.crowdValue == item.crowdId) || (item.crowdType == 1 && this.queryTabParams.crowdValue == this.core.crowdId);
        })
      }
      if (this.queryTabParams.class) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return this.queryTabParams.class == item.tabType;
        })
      }
      if (this.queryTabParams.tabName) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return item.tabName.indexOf(this.queryTabParams.tabName) > -1;
        })
      }
      if (this.queryTabParams.position) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return this.queryTabParams.position == item.position;
        })
      }
      if (this.queryTabParams.status) {
        this.tabConfigList = this.tabConfigList.filter((item, index) => {
          return this.queryTabParams.status == item.status;
        })
      }
      this.initTabConfigList();
    },
    initTabConfigList() {
      this.tabConfigList = this.setStatusInitList(this.tabConfigList);
    },
    initDataStatus() {
      this.dataList = this.setStatusInitList(this.dataList);
    },
    initClassList() {
      this.dataClassList = this.setStatusInitList(this.dataClassList);
    },
    setStatusInitList(data) {
      if (!data) {
        return;
      }
      data.forEach((item, index) => {
        // item.sort = index + 1;
        this.$set(item, 'sort', index + 1);
        // 1:"月 ",2:"周 ",3:"日 "
        // if (item.status != 4) {
          if(item.timeType==2){
            if(item.status!=4){
              item.status=2
            }
          }else{
            if (new Date() * 1 < new Date(item.validityTime[0]) * 1) {
              item.status = 1; // 未开始
            } else if (new Date() * 1 > new Date(item.validityTime[0]) * 1 && new Date() * 1 < new Date(item.validityTime[1]) * 1 && item.status != 4) {
              item.status = 2; //上线
            } else if (new Date() * 1 > new Date(item.validityTime[1]) * 1) {
              item.status = 3;
            } else {
              item.status = 4;
            }
          }
        // }
      })
      return data.sort((a,b)=>a.position - b.position);
    },
    addTabList() {
      this.isTabEdit = false;
      this.addTabDialog = true;
      this.dialogClassType = 'addTabForm'
      this.dialogClassRule = 'addTabRuleForm'
    },
    toClassRemove(data, index) {
      let _self = this;
      return function () {
        _self.content.classList.splice(_self.content.classList.findIndex((item) => item.id == data.id), 1)
        _self.dataClassList.splice(_self.dataClassList.findIndex((item) => item.id == data.id), 1)
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    toClassEdit(data, index) {
      this.addClassForm = {...data};
      this.dialogClassType = 'addClassForm';
      this.dialogClassRule = 'addClassRuleForm'
      this.currentClassId = data.id;
      this.currentClassDataIndex = index;
      this.currentClassIndex = this.content.classList.findIndex((item) => item.id == data.id);
      if(!data.timeType){
        data.timeType = 1
      }

      if(this.addClassForm.timeType==2&&this.addClassForm.circulateTime){
        this.$refs.loopcirculateTime.circulateTime=this.addClassForm.circulateTime
        this.$refs.loopcirculateTime.editInit()

      }
      this.isClassEdit = true;
      this.addClassDialog = true;
    },
    onClassline(scope) {
      this.$confirm(
        "确定要执行上线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.classList[this.content.classList.findIndex(i => i.id == scope.row.id)].status = 2;
        this.initData();
        this.searchClassList();
        this.$message.success("操作成功！")
      });
    },
    offClassline(scope) {
      this.$confirm(
        "确定要执行下线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.classList[this.content.classList.findIndex(i => i.id == scope.row.id)].status = 4;
        this.initData();
        this.searchClassList();
        this.$message.success("操作成功！")
      });
    },
    onSelect(val, isEditEntry) {
      if (val) {
        this.content.bgRes = val;
      } else {
        this.content.bgRes = "#eee";
      }
    },
    imgOnclick() {
      this.content.bgImg = null;
    },
    formatTooltip(val) {
      return val / 100;
    },
    async addClassDialogConfirm() {
      this.$refs.addClassRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        let classFlag = false;
        let filterArr = JSON.parse(JSON.stringify(this.content.classList || []));
        if (this.isClassEdit) {
          filterArr = filterArr.filter(item => item.id != this.addClassForm.id);
        }
        if (this.addClassForm.timeType == 1) {
          const start_form = new Date(this.addClassForm.validityTime[0]).getTime();
          const end_form = new Date(this.addClassForm.validityTime[1]).getTime();
          filterArr.forEach(item => {
            if(item.timeType == 1) {
              const start = new Date(item.validityTime[0]).getTime();
              const end = new Date(item.validityTime[1]).getTime();
              if (start_form <= start && end_form >= end) {
                classFlag = true;
              } else if ((start_form >= start && start_form <= end) || (end_form >= start && end_form <= end)) {
                classFlag = true;
              }
            }
          })
        } else if (this.addClassForm.timeType == 2) {
          // 1:周 2:月 3:日
          filterArr && filterArr.forEach(item => {
            if(Array.isArray(this.addClassForm.circulateTime.circulateList)){
              // let _date =  new Date().toLocaleTimeString('en-US', {hour12: false});
              if (item.timeType == 2) {
                let _date = this.addClassForm.circulateTime.circulateList[0].selectTimeData;
                if(this.addClassForm.circulateTime.circulateType==3){
                  item.circulateTime.circulateList.forEach(element => {
                    if ((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])) {
                      classFlag = true;
                    }
                  });
                }
                if(item.circulateTime.circulateType==1 || item.circulateTime.circulateType==2){
                  item.circulateTime.circulateList.forEach(element => {
                    if (this.addClassForm.circulateTime.circulateList[0].weekOrday==element.weekOrday&&((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0]))) {
                      classFlag = true;
                    }
                  });
                }
              }
            }
          })
        }
        if (classFlag) {
          this.$message.warning("展示时间不能包含列表已存在数据时间！");
          return;
        }

        if (!this.isClassEdit) {
          let id = 0;
          id = Math.floor(Math.random() * 90000) + 10000;
          if ((this.content.tabConfigList || []).findIndex(item => item.id == id) > -1) {
            this.$message("id错误，请重新添加！");
            return;
          }
          this.$set(this.addClassForm, 'id', id);
        }
        // if (this.content.list.findIndex(item => item.wordName === this.addForm.wordName) > -1) {
        //   this.$message("热词组名称已存在！");
        //   return;
        // }
        let arr = [...this.content.classList || []];
        if(this.isClassEdit) {
          this.$set(this.content.classList, this.content.classList.findIndex(i => i.id == this.currentClassId), this.addClassForm);
        } else {
          arr.splice(0, 0, this.addClassForm);
          this.$set(this.content, 'classList', arr);
        }
        this.$message.success(`${this.isClassEdit ? '编辑':'添加'}成功！`);
        this.content.classList.forEach((item,index) => {
          item.sort = index + 1;
        })
        this.initData();
        this.initClassList();
        this.resetClassQueryParams();
        this.resetClassAddForm();
        this.searchClassList();
        this.addClassDialog = false;
      });
    },
    addClassDialogCancel() {
      this.resetClassAddForm();
      this.addClassDialog = false;
    },
    resetClassAddForm() {
      this.addClassForm = {
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        className: '', //分类名称
        // classBackgroundType: 1,
        // classBackground: '#eee',
        // classBtnBackgroundType: 1,
        classBtnBackground: '',
        circulateTime: '',
        status: 1,
        timeType: 1,
        // classBackgroundTransparency: 100,
        classBtnBackgroundTransparency: 100,
      }
    },
    addClassList() {
      this.isClassEdit = false;
      this.addClassDialog = true;
      this.dialogClassType = 'addClassForm'
      this.dialogClassRule = 'addClassRuleForm'
    },
    //查询
    searchClassList() {
      //只有查询全部的时候允许拖拽
      // if (this.carouselList.status || this.carouselList.crowdValue || this.carouselList.bannerLocation) {
      //   sortableObject.option('disabled', true)
      // } else {
      //   sortableObject.option('disabled', false)
      // }
      this.dataClassList = this.content.classList;
      if (this.queryParams.validityTime.length) {
        this.dataClassList = this.dataClassList.filter((item, index) => {
          return new Date(this.queryClassParams.validityTime[0]) * 1 >= new Date(item.validityTime[0]) * 1 && new Date(this.queryClassParams.validityTime[1]) * 1 <= new Date(item.validityTime[1]) * 1;
        })
      }
      if (this.queryClassParams.className) {
        this.dataClassList = this.dataClassList.filter((item, index) => {
          return item.className.indexOf(this.queryClassParams.className) > -1;
        })
      }
      if (this.queryClassParams.status) {
        this.dataClassList = this.dataClassList.filter((item, index) => {
          return this.queryClassParams.status == item.status;
        })
      }
    },
    resetClassQueryParams() {
      this.queryClassParams = {
        validityTime: '', //有效期
        className: '', //分类名称
        status: '', //状态
      }
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.$set(this.content, 'bgImg', res.data.url);
    },
    changeSort(scope, type="search") {
      let ind = scope.$index;
      if (type == 'tab') {
        if (this.tabConfigList[ind].sort <= 0) {
          this.$message.warning("请输入大于0的数字！")
          return;
        }
        if (this.tabConfigList[ind].sort >= this.tabConfigList.length) {
          this.tabConfigList.splice(this.tabConfigList.length, 0, this.tabConfigList[ind]);
          this.tabConfigList.splice(ind, 1)
        } else {
          if (this.tabConfigList[ind].sort > ind) {
            this.tabConfigList.splice(this.tabConfigList[ind].sort, 0, this.tabConfigList[ind]);
            this.tabConfigList.splice(ind, 1);
          } else {
            this.tabConfigList.splice(this.tabConfigList[ind].sort - 1, 0, this.tabConfigList[ind]);
            this.tabConfigList.splice(ind + 1, 1);
          }
        }
        this.tabConfigList.forEach((item, index) => {
          item.sort = index + 1;
        })
      } else {
        if (this.dataList[ind].sort <= 0) {
          this.$message.warning("请输入大于0的数字！")
          return;
        }
        if (this.dataList[ind].sort >= this.dataList.length) {
          this.dataList.splice(this.dataList.length, 0, this.dataList[ind]);
          this.dataList.splice(ind, 1)
        } else {
          if (this.dataList[ind].sort > ind) {
            this.dataList.splice(this.dataList[ind].sort, 0, this.dataList[ind]);
            this.dataList.splice(ind, 1);
          } else {
            this.dataList.splice(this.dataList[ind].sort - 1, 0, this.dataList[ind]);
            this.dataList.splice(ind + 1, 1);
          }
        }
        this.dataList.forEach((item, index) => {
          item.sort = index + 1;
        })
      }
    },
    online(scope) {
      this.$confirm(
        "确定要执行上线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.list[this.content.list.findIndex(i => i.id == scope.row.id)].status = 2;
        this.$message.success("操作成功！")
        this.initData();
        this.searchList();
      });
    },
    offline(scope) {
      this.$confirm(
        "确定要执行下线操作吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        this.content.list[this.content.list.findIndex(i => i.id == scope.row.id)].status = 4;
        this.$message.success("操作成功！")
        this.initData();
        this.searchList();
      });
    },
    addDialogCancel() {
      this.resetAddForm();
      this.addDialog = false;
    },
    resetAddForm() {
      this.addForm = {
        validityTime: '', //有效期
        crowdType: 1, //人群switch
        wordId: '', //词组id
        wordName: '', //词组名称
        crowdValue: '', // 人群
        wordValue: '',
        hrefType: '',
        hrefUrl: '',
        timeType: 1,
        circulateTime: '',
        hotStyle: {
          styleType: 0,
          scanColor: '#000000',
          scanTransparency: 100,
          atmosphereIconText: '',
        },
        hrefType: '',
        couponId: '',
        status: 1,
        link: {
          meta: {
            page_url: ""
          }
        },
      }
    },
    addList() {
      this.isEdit = false;
      this.addDialog = true;
      this.dialogClassType = 'addForm'
      this.dialogClassRule = 'addRuleForm'
    },
    resetQueryParams() {
      this.queryParams = {
        validityTime: '', //有效期
        wordName: '', //词组名称
        crowdValue: '', // 人群
        status: '', //状态
      }
      this.$nextTick(() => {
        this.crowdName = "";
      })
      this.searchList();
    },
    //打开时间循环
    toloopcirculateTime(){
      this.$refs.loopcirculateTime.showVisible=true
    },
    //循环时间回调
    loopcirculateTimeBack(data){
      this.$set(this[this.dialogClassType], 'circulateTime', data);
      this.$refs[this.dialogClassRule].clearValidate('circulateTime')
    },
    //链接去掉空格
    urlChange(){
      this.addForm.link.meta.page_url=this.addForm.link.meta.page_url.trim()
    },
    //链接去掉空格
    urlTabChange(){
      this.addTabForm.link.meta.page_url=this.addTabForm.link.meta.page_url.trim()
    },
    initData() {
      this.dataList = this.content.list;
      this.dataClassList = this.content.classList;
      this.tabConfigList = this.content.tabConfigList;
    },

    rowDrop() {
      const _this = this;
      const tbody = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0];
      sortableObject = Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        ghostClass: "sortable-ghost",
        onEnd: evt => {
          const currRow = (_this.dataList || []).splice(evt.oldIndex, 1)[0];
          (_this.dataList || []).splice(evt.newIndex, 0, currRow);
          const currRowData = (_this.content.list || []).splice(evt.oldIndex, 1)[0];
          (_this.content.list || []).splice(evt.newIndex, 0, currRowData);
        }
      });
    },
    changeCrowdValue(e) {
      if (!e) {
        this.dataForm.crowdId = '';
      }
      this.$forceUpdate();
    },
    clear_bgs(type) {
      this.content[type + '_url'] = '';
      this.content[type + '_color'] = '';
      this.content[type + '_transparency'] = 100;
    },

    // 设置轮播链接
    onSetLink(link) {
      this.addForm.link.meta.page_url = link.meta.page_url;
    },
    onTabChooseSetLink(link) {
      this.addTabForm.link.meta.page_url = link.meta.page_url;
    },
    // 设置轮播链接
    onTabSetLink(link) {
      this.addTabFormSelectLink = link.meta.page_url;
    },

    // 上传轮播图片
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.dataForm.image = res.data.url
    },

    // 上传banner对应的头部区域背景图片
    async UploadTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.content, type, res.data.url);
    },
    // 上传banner对应的头部区域背景图片
    async UploadClassBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.addClassForm, type, res.data.url);
    },
    changeCrowdType() {
      this.addForm.crowdId = '';
      this.addForm.crowdValue = '';
    },
    async addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        if(this.addForm.hrefType && this.addForm.hrefType != 'couponGroupPurchasePage' && !this.addForm.link.meta.page_url) {
          this.$message("请输入跳转链接!");
          return;
        }
        if (!this.isEdit && this.content.list.findIndex(item => item.wordName === this.addForm.wordName) > -1) {
          this.$message("热词组名称已存在！");
          return;
        }
        try {
          if (this.addForm.hrefType == 'couponGroupPurchasePage') {
            const res = await api.stores.checkCoupon({
              couponId: this.addForm.couponId,
              receiveTypes: 1,   //写死   1：用户领取    2：平台发送
              showCenter: 1, //优惠券领取中心是否可展示   1：展示   2：不展示   写死1
            });
            if (!(res.data.code === 1000 && res.data.data.couponCheckInfo.checkFlag)) {
              this.$message.error('优惠券ID校验不通过');
              return
            }
          }

        } catch(err) {
          console.log(err);

          this.$message("接口异常!");
          return
        }
        if(!this.isEdit) {
          let id = 0;
          id = Math.floor(Math.random() * 90000) + 10000;
          if ((this.content.tabConfigList || []).findIndex(item => item.id == id) > -1) {
            this.$message.error("id错误，请重新添加！");
            return;
          }
          this.$set(this.addForm, 'id', id);
        }
        if (this.addForm.crowdType == 1) {
          this.addForm.crowdId = this.core.crowdId;
          this.addForm.crowdValue = this.core.crowdValue;
        }
        let arr = [...this.content.list];

        if(!this.addForm.hrefType){
          this.addForm.link.meta.page_url=""
        }
        if (this.isEdit) {
          await this.$nextTick(); // 确保 DOM 更新完成
          this.$set(this.content.list, this.content.list.findIndex(i => i.id == this.currentId), this.addForm);
        } else {
          arr.splice(0, 0, this.addForm);
          this.$set(this.content, 'list', arr);
        }
        this.$message.success(`${this.isEdit ? '编辑':'添加'}成功！`);
        this.content.list.forEach((item,index) => {
          item.sort = index + 1;
        })
        this.initData();
        this.initDataStatus();
        this.resetAddForm();
        this.searchList();
        this.addDialog = false;
    });
    },

    // 按照规则排序--排序规则优先级：人群 > 帧位 > 生效时间（生效中>待生效>未设置）
    sortByRule(data) {
      const samePeople = this.content.list.filter((item,index) => {
        return Number(item.crowdId) === Number(this.dataForm.crowdId)
      });
      // 相同人群的逻辑
      if (samePeople.length) {
        const sameLocation = samePeople.filter((item, index) => {
          return Number(item.bannerLocation) === Number(this.dataForm.bannerLocation)
        });
        // 相同人群下，相同帧位的逻辑
        if (sameLocation.length) {
          const sameStatus = sameLocation.filter((item, index) => {
            return this.getStatusName(this.dataForm.timevalue) === this.getStatusName(item.timevalue);
          });
          let tempIndex = undefined;
          if (sameStatus.length) {
            this.content.list.forEach((item, index) => {
              if(item.id === sameStatus[sameStatus.length-1].id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，相同状态，插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === '生效中') {
            this.content.list.forEach((item, index) => {
              if (sameLocation[0].id === item.id) {
                tempIndex = index;
              }
            })
            // 相同人群，相同帧位，生效中插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === '待生效') {
            this.content.list.map((item, index) => {
              if(this.getStatusName(item.timevalue) === '生效中') {
                tempIndex = index + 1
              }
            })
            if (!tempIndex) {
              // 说明没有生效中，找未设置的
              this.content.list.map((item, index) => {
                if(this.getStatusName(item.timevalue) === '未设置时间') {
                  tempIndex = index
                }
              })
            }
            if (!tempIndex) {
              // 说明没有生效中未设置的，插到帧位最后面
              this.content.list.map((item, index) => {
                if (sameLocation[sameLocation.length - 1].id === item.id) {
                  tempIndex = index + 1;
                }
              })
            }
            // 相同人群，相同帧位，待生效插到生效中后面或未设置时间的前面或同帧位最后面
            this.content.list.splice(tempIndex, 0, data);
          } else {
            this.content.list.map((item, index) => {
              if (sameLocation[sameLocation.length - 1].id === item.id) {
                tempIndex = index;
              }
            })
            // 相同人群，相同帧位，未设置插到后面
            this.content.list.splice(tempIndex + 1, 0, data);
          }
        } else {
          // 相同人群下，不同帧位，比较帧位大小
          let tempIndex = undefined;
          // 找到第一个大于新增帧位的项，有则插入到前面，没有则插入到同人群下最后一位
          let maxItem = samePeople.find((item, index) => {
            return item.bannerLocation > this.dataForm.bannerLocation
          });
          if (maxItem) {
            this.content.list.map((item, index) => {
              if (item.id === maxItem.id) {
                tempIndex = index
              }
            })
          } else {
            this.content.list.map((item, index) => {
              if (item.id === samePeople[samePeople.length-1].id) {
                tempIndex = index + 1
              }
            })
          }
          // 新增帧位小插到前面；新增帧位大插到后面
          this.content.list.splice(tempIndex, 0, data);
        }
      } else { // 新人群，直接添加
        let tempIndex = undefined;
        tempIndex = this.content.list.filter((item) => {
          return this.getStatusName(item.timevalue) !== '已失效'
        }).length;
        this.content.list.splice(tempIndex, 0, data)
      }
      this.$nextTick(() => {
        this.searchList();
      })
      // this.content.list.push(Object.assign({}, data));
      // this.$set(this.dataList, this.dataList.length, data)
    },
    changeTab(type) {
      this.activeTab = type;
      this.carouselList.status = '';
      this.searchList();
    },
    //生成唯一id
    genID(length) {
      return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36);
    },
    //查询
    searchList() {
      //只有查询全部的时候允许拖拽
      // if (this.carouselList.status || this.carouselList.crowdValue || this.carouselList.bannerLocation) {
      //   sortableObject.option('disabled', true)
      // } else {
      //   sortableObject.option('disabled', false)
      // }
      this.dataList = this.content.list;
      if (this.queryParams.validityTime.length) {
        this.dataList = this.dataList.filter((item, index) => {
          return new Date(this.queryParams.validityTime[0]) * 1 >= new Date(item.validityTime[0]) * 1 && new Date(this.queryParams.validityTime[1]) * 1 <= new Date(item.validityTime[1]) * 1;
        })
      }
      if (this.queryParams.wordName) {
        this.dataList = this.dataList.filter((item, index) => {
          return item.wordName.indexOf(this.queryParams.wordName) > -1;
        })
      }
      if (this.queryParams.crowdValue) {
        this.dataList = this.dataList.filter((item, index) => {
          return (this.queryParams.crowdValue == item.crowdId) || (item.crowdType == 1 && this.queryParams.crowdValue == this.core.crowdId);
        })
      }
      if (this.queryParams.status) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.status == item.status;
        })
      }
      this.initDataStatus();
    },
    toEdit(data, index) {
      this.addForm = {
        link: {
          meta: {
            page_url: ""
          }
        },
        //如果没有新增的属性，则给新增属性加上默认值
        hotStyle: {
          styleType: 0,
          scanColor: '',
          scanTransparency: '',
          atmosphereIconText: '',
        },
        couponId: '',
        //如果有，则data会覆盖默认值
        ...data
      };
      this.dialogClassType = 'addForm';
      this.dialogClassRule = 'addRuleForm'
      this.currentDataIndex = index;
      this.currentId = data.id;
      this.currentIndex = this.content.list.findIndex((item) => item.id == data.id);
      if(!data.timeType){
        data.timeType=1
      }
      if(this.addForm.timeType==2&&this.addForm.circulateTime){
        this.$refs.loopcirculateTime.circulateTime=this.addForm.circulateTime
        this.$refs.loopcirculateTime.editInit()
      }
      this.isEdit = true;
      this.addDialog = true;
    },
    toRemove(data) {
      let _self = this;
      return function () {
        _self.content.list.splice(_self.content.list.findIndex((item) => item.id == data.id), 1)
        _self.dataList.splice(_self.dataList.findIndex((item) => item.id == data.id), 1)
        _self.$message({
          type: 'success',
          message: '删除成功!'
        });
      }.confirm(_self)()
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [{
          label: data.name,
          value: val,
        }]
      } else {
        this.selectLoading = false;
        this.options = []
      }
    },
    selectCrowd(e) {
      if (e) {
        this.addForm.crowdId = Number(this.options[0].value.trim());
        this.addForm.crowdValue = this.options[0].label;
      } else {
        this.addForm.crowdId = '';
        this.addForm.crowdValue = '';
      }
      this.$forceUpdate();
    },
    selectTabCrowd(e) {
      if (e) {
        this.addTabForm.crowdId = Number(this.options[0].value.trim());
        this.addTabForm.crowdValue = this.options[0].label;
      } else {
        this.addTabForm.crowdId = '';
        this.addTabForm.crowdValue = '';
      }
      this.$forceUpdate();
    },
    async querySearchCrowd() {
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${this.queryParams.crowdValue}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.code == 1000 && this.queryParams.crowdValue) {
        this.crowdName = res.data.name;
      }
    },
    // handleSelectCrowd(item) {
    //   this.dataForm.crowdId = item.id;
    // },
  },
  watch:{
    core: {
      deep: true,
      handler() {
        console.log("改变了core", this.core);
      }
    }
    // "dataForm.timeType"(newdata,ordData){

    //   if(newdata==2){
    //     this.dataForm.timevalue=""
    //   }
    //   if(newdata==1){
    //     this.dataForm.circulateTime={}
    //   }
    // },
  }

}
  ;
</script>
<style lang="scss" scoped>

.topic-search {
  .add-color-back {
    display: flex;
    align-items: center;
    .el-button {
      height: 30px;
    }
    .upload-demo {
      margin-right: 15px;
      // width: 180px;
      .el-upload {
        display: flex;
        align-items: center;

        img {
          width: 60px;
          margin-left: 15px;
        }
      }
    }
  }
  .el-col {
    display: flex;
    >div {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .topic-item {
      display: flex;
      margin-right: 10px;
      margin-bottom: 10px;
      flex: 1;
      .demonstration {
        min-width: 80px;
        margin-right: 15px;
        white-space: nowrap;
      }
    }
    .topic-item-title {
      flex: auto;
      width: 275px;
      display: flex;
      justify-content: flex-start;
    }
  }
}
.tableBox {
  width: 100%;
}
 .topic-image-picker {
    padding: 10px 0;
  }
.carouselFlexBox {
  padding-right: 10px;
  .carouselFlex {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
    padding-right: 20px;
    span {
      min-width: 100px;
      text-align: right;
    }
  }
  .el-date-editor {
    width: 400px !important;
    height: 30px !important;
    .el-range__icon, .el-range-separator {
      line-height: 21px;
    }
  }
  .carouselButton {
    text-align: right;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
  }
}

.container-table {
  margin: 10px auto;
  padding-bottom: 10px;
  display: flex;
  justify-content: space-around;

  .img {
    width: 50%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .button-list {
    width: 45%;
  }
}

.topic-image-upload {
  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 200px;
    height: 200px;
    line-height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    font-size: 50px;
  }
}

.topic-image-upload .el-upload {
  width: 100%;
}

.el-row {
  text-align: center;

  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
  .tabBox {
    display: flex;
    margin: 20px;
    border-bottom: 1px solid #F1F1F4;
    cursor: pointer;
    div {
      border: 1px solid #F1F1F4;
      border-bottom: none;
      padding: 5px 10px;
    }
    .activeTab {
      color: #13c2c2;
    }
  }
}
.block {
  width: 100%;
  img {
    width: 60px;
  }
}
</style>
<style  lang="scss">
.topic-search {
  .upload-demo {
    width: 180px;
    .el-upload {
      display: flex;
      align-items: center;
      .el-button {
        height: 30px;
      }
      img {
        width: 60px;
        margin-left: 15px;
      }
    }
  }
  .banner-dialog {
    .el-input {
      width: 400px;
    }
  }
  .el-date-editor {
    .el-input__icon, .el-range-separator {
      line-height: 21px;
    }
  }
  .crowdInput {
    .el-input {
      width: 200px !important;
      margin-right: 20px;
    }
  }
  .tableBox {
    .el-button {
      margin-left: 0;
      margin-right: 10px;
    }
    .preview-img {
      width: 60px;
    }
  }
}
</style>