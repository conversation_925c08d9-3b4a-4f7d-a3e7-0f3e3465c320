import * as http from 'utils/http';
import goods from './goods';

/**
 * 商品组
 */
export default {
	/**
	 * 商品分页处理
	 * @param rs
	 * @param size {@link Number}：页条数；
	 * @returns {*}
	 */
	goods2page(rs, size) {
		if (!rs || !rs.length)
			return rs;
		for (let i = 0; i < rs.length; i++) {
			let r = rs[i];
			let gos = r.goods;
			if (!gos || !gos.length)
				continue;
			r.goods = new Array(Math.ceil(gos.length / size));  //重置长度为总页数
			let pgGos;
			for (let j = 0; j < gos.length; j++) {  //goods商品数组分页
				if (!pgGos)
					r.goods.push(pgGos = new Array(size));
				pgGos.push(gos[j]);
			}
		}
		return rs;
	},
	/**
	 * 查询列表
	 * 商品列表被分页处理
	 * @param pms {@link Object}：查询参数；
	 * @param size {@link Number}：页条数；
	 * @returns {Promise<*>}
	 */
	async selectGoodsPage(pms, {
		size = 10
	} = {}) {
		return this.selectGoods(pms)
			.then(res => {
				if (res.code != 200)
					return res;
				this.goods2page(res.data.rows, size);
				return res;
			});
	},
	/**
	 * 查询列表
	 * 已获取商品信息
	 * @param pms {@link Object}：查询参数；
	 * @param size {@link Number}：页条数；
	 * @returns {Promise<*>}
	 */
	async selectGoods(pms) {
		let res = await this.select(pms);
		if (res.code != 200)
			return res;

		let rs = res.data.rows;
		if (!rs || !rs.length)
			return res;

		let prs = new Array(rs.length);
		for (let i = 0; i < rs.length; i++) {
			let r = rs[i];
			if (!r.goods || !r.goods.length)
				continue;
/*			let gosRes = await goods.selectGoods({
				ids: r.goods
			});
			if (gosRes.code != 200)
				continue;
			let gos = gosRes.data.list;
			if (gos)
				r.goods = gos;*/
			prs[i] = new Promise((res, rej) => {
				return res(goods.selectGoods({
					skuIdList: r.goods
				}));
			}).then(data => {
				if (data.code != 200)
					return null;
				let gos = data.data.list;
				if (gos)
					r.goods = gos;
				return gos;
			}).catch(e => e);
		}
		return Promise.all(prs.map(item => item.catch(e => e)))
			.then(data => res);
	},
	/**
	 * 查询列表
	 * @param pms
	 * @returns {Promise<*>}
	 */
	async select(pms) {
		return await http.post('/goodsGroup/select', pms, {
			contentType: 'application/json; charset=UTF-8'
		});
	},
	/**
	 * 查询商品
	 * @param pms
	 * @returns {Promise<*>}
	 */
	async query(pms) {
		return await http.post('/goodsGroup/query', pms, {
			contentType: 'application/json; charset=UTF-8'
		});
	},
	/**
	 * 保存
	 * @param pms
	 * @returns {Promise<*>}
	 */
	async save(pms) {
		return await http.post('/goodsGroup/save', pms, {
			contentType: 'application/json; charset=UTF-8'
		});
	},
	/**
	 * 修改
	 * @param pm
	 * @returns {Promise<*>}
	 */
	async update(pms) {
		return await http.post('/goodsGroup/update', pms, {
			contentType: 'application/json; charset=UTF-8'
		});
	},
	/**
	 * 删除
	 * @param pm
	 * @returns {Promise<*>}
	 */
	async del(pms) {
		return await http.post('/goodsGroup/del', pms, {
			contentType: 'application/json; charset=UTF-8'
		});
	},
	/**
	 * 商品组状态
	 * @returns {Promise<*>}
	 */
	async state() {
		return await http.post('/dict/goodsGroupState');
	},

	async updateMongGoodGroups() {
		return await http.post('/goodsGroup/updateMongGoodGroups', null, {
			contentType: 'application/json; charset=UTF-8'
		});
	},

	async updateRedisGoodGroups() {
		return await http.post('/goodsGroup/updateRedisGoodGroups', null, {
			contentType: 'application/json; charset=UTF-8'
		});
	}

}
