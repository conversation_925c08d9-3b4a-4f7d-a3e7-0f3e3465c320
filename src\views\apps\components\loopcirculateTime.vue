<template>
    <div>
        <el-dialog
      title="循环限制 "
      :visible.sync="showVisible"
      :before-close="handleClose"
    >
      <div>
        <el-row :gutter="20">
            <el-col :span="4">
                <div class="btnBox">
                    <el-select v-model="circulateTimeClone.circulateType" placeholder="请选择" @change="circulateTimeCloneChange"> 
                        <el-option
                        label="每日"
                        value="3">
                        </el-option>
                        <el-option
                        label="每周"
                        value="2">
                        </el-option>
                        <el-option
                        label="每月"
                        value="1">
                        </el-option>
                </el-select>
               </div>
     
            </el-col>
           
            <el-col :span="20">
                <div class="timeBox">
        <div v-for="(timeItem, index) in circulateTimeClone.circulateList" :key="index" style="margin-bottom: 8px;display: flex;align-items: center;" >
            <el-select v-model="timeItem.weekOrday" placeholder="请选择" @change="" style="width: 120px;" v-if="circulateTimeClone.circulateType!=3" > 
                        <el-option    
                            v-for="items in showOption"
                            :key="items"
                            :label="items"
                            :value="items"
                            >
                        </el-option>
                       
                </el-select>
            <!-- <el-time-picker
                style="margin-left: 10px;"
                v-model="timeItem.selectTimeData[0]"
                  value-format="HH:mm:ss"
                placeholder="请选择">
                 </el-time-picker>&nbsp;—&nbsp;<el-time-picker
                v-model="timeItem.selectTimeData[1]"
               
                  value-format="HH:mm:ss"
                placeholder="请选择">
                 </el-time-picker> -->
                 <el-time-picker
                 style="margin-left: 10px;"
                    is-range
                    value-format="HH:mm:ss"
                    v-model="timeItem.selectTimeData"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围">
                </el-time-picker>
          <span
            style="color: #13c2c2;cursor: pointer;"
            v-if="index === 0"
            @click="addOrDelete(index)"
          >
            +添加时段
          </span>
          <span
            style="color: #EF3035;cursor: pointer;"
            v-else
            @click="addOrDelete(index)"
          >
            -删除时段
          </span>

        </div>
      </div>
     
            </el-col>
        </el-row>
      </div>
      <span class="dialog-footer" slot="footer">
        <el-button size="small" type="text" @click="cancelData">清空</el-button>
        <el-button size="small" @click="handleConfirm">确认</el-button>
      </span>
    </el-dialog>
    </div>
</template>

<script>
export default{
    data() {
        return {
            circulateTime: {
                    "circulateType": "3",//循环周期类型 1:周 2:月 3:日
                    "circulateList": [
                    {
                        "weekOrday":"", //周一 1
                        "selectTimeData": ""
                    }
                    ]
                },
                circulateTimeClone:{
                    "circulateType": "3",//循环周期类型 1:周 2:月 3:日
                    "circulateList": [
                    {
                        "weekOrday":"", //周一 1
                        "selectTimeData": ""
                    }
                    ]
                },
                daySelect:this.getDaySelectOptions(),
                weekSelect:["周一","周二","周三","周四","周五","周六","周日"],
                showOption:[],
                showVisible:false
        }
    },
    methods: {
      handleClose(done){
        // this.circulateTimeClone= {
        //             "circulateType": "3",//循环周期类型 1:周 2:月 3:日
        //             "circulateList": [
        //             {
        //                 "weekOrday":"", //周一 1
        //                 "selectTimeData": ""
        //             }
        //             ]
        //         }
        this.circulateTimeClone=JSON.parse(JSON.stringify(this.circulateTime))
        this.editInit()
                done();
      },
    addOrDelete(index){
        
        if(index==0){
            if( this.circulateTimeClone.circulateList.length>=5){
                this.$message.error("最多可选择5个时间段")
                return
            }
            let data={
                
                        "weekOrday":"", //周一 1
                        "selectTimeData":""
                    
            }
        this.circulateTimeClone.circulateList.push(data)
        }else{
            this.circulateTimeClone.circulateList.splice(index,1)
        }
    },
    cancelData(){
        this.circulateTimeClone.circulateList=[
                    {
                        "weekOrday":"", //周一 1
                        "selectTimeData": ""
                    }
                    ]
    },
    handleConfirm(){
      let data=JSON.parse(JSON.stringify(this.circulateTimeClone))
      for (let i = data.circulateList.length - 1; i >= 0; i--) {
              if(!data.circulateList[i].selectTimeData){
                  data.circulateList[i].selectTimeData=null
                }
                if (!data.circulateList[i].selectTimeData) {
                  data.circulateList.splice(i, 1);
                }
               
              }
              let timeArr=data.circulateList.map((item)=>{return [...item.selectTimeData,item.weekOrday]})
        if(this.checkTime(timeArr)){
            //若选择了周或者月则不能为空
            let isNext=true
            data.circulateList.forEach((item,index)=>{
              
              if(item.selectTimeData&&!item.weekOrday&&data.circulateType!=3){
                isNext=false
              }
            })
            if(!isNext){
              this.$message.error("信息不完整")
              return
            }
            //去除无效时间段
           
            
           this.$emit("loopcirculateTimeBack",data)
           this.circulateTime=data
          console.log(data)
           this.showVisible=false
           }


    },
    getDaySelectOptions(){
        let arr=[]
        for(let i=1;i<=31;i++){
            arr.push(i)
        }
        return arr
    },
    editInit(){
   //zhou
   if(this.circulateTimeClone.circulateType==2){
        this.showOption=this.weekSelect
      }
  
      //月
      if(this.circulateTimeClone.circulateType==1){
        this.showOption=this.daySelect
        
    }
    //日
    if(this.circulateTimeClone.circulateType==3){
        this.showOption=[]
    }
    
    },
    //循环类型发生改变
    circulateTimeCloneChange(){
        //zhou
      if(this.circulateTimeClone.circulateType==2){
        this.showOption=this.weekSelect
      
        this.circulateTimeClone.circulateList=[
                    {
                        "weekOrday":"", //周一 1
                        "selectTimeData": ""
                    }
                    ]
      }
      //月
      if(this.circulateTimeClone.circulateType==1){
        this.showOption=this.daySelect
        this.circulateTimeClone.circulateList=[
                    {
                        "weekOrday":"", //周一 1
                        "selectTimeData": ""
                    }
                    ]
        
    }
    //日
    if(this.circulateTimeClone.circulateType==3){
        this.showOption=[]
        this.circulateTimeClone.circulateList=[
                    {
                        "weekOrday":"", //周一 1
                        "selectTimeData": ""
                    }
                    ]
    }
    },
       // 校验时间是否重叠
       checkTime(intervals) {
      
        // for (let i = 0; i < intervals.length; i++) {
        //     if (!intervals[i][0]||!intervals[i][1]||intervals[i][0]>intervals[i][1]) {
        //         console.log(intervals[i][0]>intervals[i][1])
        //     this.$message.error(`时间段第${i+1}行格式不正确`)
        //     return false; // 存在相交的时间段
        //     }
        // }
        intervals.forEach((element,index) => {
              if(!element){
                intervals.splice(index,1)
              }
            });
       if(intervals.length>=2){
        const sortedIntervals = intervals.sort(this.compareTimeSlots); // 按照左区间排序
        console.log(sortedIntervals)
      
        for (let i = 0; i < sortedIntervals.length; i++) {
        
            for(let j=1+i;j<sortedIntervals.length;j++){
              let time1 = sortedIntervals[i];
              let time2 = sortedIntervals[j];
              if (time1[0] <= time2[1] && time1[1] >= time2[0]) {
                if(time1[2]==time2[2]){
                    this.$message.error(`存在相交的时间段`)
                    return false; // 存在相交的时间段
                    
                }

            }
        }
          
       }   }
     
        return true; // 所有时间段校验通过
   
      },
   compareTimeSlots(timeSlot1, timeSlot2) {
  const startTime1 = new Date(`2000/01/01 ${timeSlot1[0]}`);
  const startTime2 = new Date(`2000/01/01 ${timeSlot2[0]}`);

  if (startTime1 < startTime2) {
    return -1;
  } else if (startTime1 > startTime2) {
    return 1;
  } else {
    return 0;
  }
}
    
},
watch:{
  'circulateTimeClone'(newdata){
    if(Object.keys(this.circulateTimeClone).length==0){
      this.circulateTimeClone= {
                    "circulateType": "3",//循环周期类型 1:周 2:月 3:日
                    "circulateList": [
                    {
                        "weekOrday":"", //周一 1
                        "selectTimeData": ""
                    }
                    ]
                }

    }else if(newdata.circulateList.length==0){
      this.addOrDelete(0)
    }
    this.editInit()
    
  },
  'circulateTime'(){
    this.circulateTimeClone=JSON.parse(JSON.stringify(this.circulateTime))
  }
}
}
</script>

<style lang="scss" scoped rel="stylesheet/scss">
  .topic-image-upload {
    width: 100px;
    height: 100px;
    position: relative;
    .imgBox {
      position: relative;
    }
    .image {
      display: block;
      width: 100%;
      z-index: 10;
    }
    .uploader-icon {
      width: 100px;
      height: 100px;
      line-height: 100px;
      border: 1px solid $border-base;
      font-size: 30px;
    }
  }
  .el-icon-circle-close {
    position: absolute;
    top: -8px;
    right: -16px;
    font-size: 20px;
    z-index: 100;
  }
  .el-upload__tip {
    position: absolute;
    top: 18px;
    left: 134px;
    width: 180px;
  }
  .hr {
    height: 1px;
    background: #E8E6E6;
  }
  .infoItem {
    margin-top: 10px;
  }
  .topic-image-picker {
    padding-bottom: 10px;
  }