import tabSwitch from '../../components/public/tab-switch' //选项卡
import banner from '../../components/public/banner' //轮播
import streamer from '../../components/public/streamer' //横幅广告
import swiperGroup from '../../components/public/swiper-goods-group' //滚动商品组
import staticGroup from '../../components/public/swiper-goods-group' //静态商品组
import topList from '../../components/public/top-list' //商品列表组件
import highTitle from '../../components/public/highmargin-title' //高毛标题组件
import specialArea from '../../components/public/special-area'  //如广云堂专区
import highSort from '../../components/public/sort' //空组件
import mainPromotion from '../../components/public/main-promotion' //高毛专区的促销组件
import brandPromotion from '../../components/public/brand-promotion' //促销滚动
import brandCollection from '../../components/public/brand-collection' //品牌集合


export default{
	components:{tabSwitch,banner,streamer,swiperGroup,staticGroup,topList,highTitle,specialArea,highSort,mainPromotion,brandPromotion,brandCollection},
	componentsList:[
		{name:'tabSwitch',label:'选项卡',num:0},
		{name:'banner',label:'轮播',num:0},
		{name:'streamer',label:'横幅广告',num:0},
		{name:'swiperGroup',label:'滚动商品组',num:0},
		{name:'staticGroup',label:'静态商品组',num:0},
		{name:'topList',label:'商品列表',num:0},
		{name:'highTitle',label:'高毛标题',num:0},
		{name:'specialArea',label:'专区',num:0},
		{name:'highSort',label:'排序',num:0},
		{name:'mainPromotion',label:'套餐/商品组',num:0},
		{name:'brandPromotion',label:'促销滚动',num:0},
		{name:'brandCollection',label:'品牌集合',num:0},
	],
	tabSwitch:{
		color: '#2A2A2A',
		hoverColor: '#333',
		lineColor: '#00DC82',
		bgColor: '#EBEBEB',
		isBtn: true,
		list:[
			{
				"name": "高毛专区",
				"content": [
					{
						"title": "高毛首页",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "心脑血管",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "抗菌消炎",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "消化系统",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "五官用药",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "皮肤外用",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "感冒用药",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "滋补保健",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "妇科用药",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "风湿骨痛",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "辅助器材",
						"componentsName": [],
						"data": {}
					},
					{
						"title": "其他用药",
						"componentsName": [],
						"data": {}
					}
				]
			}
		]
	},
	banner:{
		bgRes: '',
		color: '#000000',
		rotationpointcolor: '#000000',
		image: '',
		list: [],
	},
	streamer:{
		image: '',
		bgRes: '',
		color: '#000000',
		link: {
			page_url: '',
			page_name: ''
		},
		timevalue: ''
	},
	swiperGroup:{
		bgColor: '#fff',
		list: []
	},
	staticGroup:{
		bgColor: '#fff',
		list: []
	},
	topList:{
		list: [],
		color: '#999', //文字颜色
		hoverColor: '#00DC82', //文字选中颜色,
		bgColor: '#ccc', //背景颜色,
		type: 0,
		isBtn: false //添加购物车按钮
	},
	highTitle:{
		text: '高毛新品，每半月上不停',
		size: 16,
		color: '#000000',
		bg_color: '#ffffff',
		align: 'left'
	},
	specialArea:{
		list: [],
		image: '',
		bgImage: '',
		goodsIds: [],
		link: {
			page_url: '',
			page_name: ''
		},
		isBtn:false
	},
	highSort:{
		category: [
			{name: 'smsr.sale_num', title: '综合',isBtn:false},
			{name: 'fob', title: '价格',isBtn:true},
			{name: 'grossProfile', title: '毛利',isBtn:true},
			{name: 'isPromotion', title: '有促销',isBtn:false}
		],
		color: '#333',
		hoverColor: '#00DC82',
		bgColor:'#fff'
	},
	mainPromotion:{
		type: 0,
		code: 0,
		searchNum:1,
		list: [],
		count: 3,
		image: '',
		link: '',
		isBtn:false, //添加购物车按钮
		styleNum:3,
		isTitle:false,
		bgImage:'http://upload.ybm100.com/ybm/applayoutbanner/6e41f728-4f27-4b57-8f16-c883a17f1158.png',
		flag:'http://upload.ybm100.com/ybm/applayoutbanner/f2e8af0f-268b-4481-bf2d-a33b94879bd1.png'
	},
	brandPromotion:{
		list: [],
		count: 3
	},
	brandCollection:{
		list: [],
		title:''
	}

}



