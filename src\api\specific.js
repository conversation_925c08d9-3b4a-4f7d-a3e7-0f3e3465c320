import * as http from 'utils/http';
export default {
  /**
   * 查询店铺列表
   * @param pms
   * @returns {Promise<*>}
   */
  async get_GroupBuyingInfos(pms) {
    const obj={
      headers: {
        "isAdmin": true,
        "Content-Type":"application/x-www-form-urlencoded",
        "terminalType":1
      },
      method:"post",
      url:"/app/sku/listGroupBuyingInfos",
      data:pms,
      transformRequest:[function(data) {
        let ret = '';
        for (let key in data) {
          ret += encodeURIComponent(key) + '=' + encodeURIComponent(data[key]) + '&'
        }
        return ret
      }]
    };
    return await http.putRequest(obj);
  }

}
