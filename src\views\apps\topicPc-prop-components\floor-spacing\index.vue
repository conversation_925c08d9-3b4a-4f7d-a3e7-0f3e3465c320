<template>
  <div class="floor-spacing">
    <!-- <div style="margin: 10px 0">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-radio-group v-model="content.activeKey">
            <el-radio
              :label="index"
              v-for="(item,index) in menu"
              :key="index"
              :disabled="index>1"
            >{{item}}</el-radio>
          </el-radio-group>
        </el-col>
      </el-row>
    </div>-->
    <!-- <section>
      <el-date-picker
        v-model="content.floorSpaceTimeValue"
        type="datetimerange"
        :picker-options="pickerOptions2"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        align="right"
      ></el-date-picker>
    </section>-->
    <div class="panel-common-header">楼层间隔设置</div>
    <div class="block">
      <div class="color">
        <span>间隔颜色：</span>
        <el-color-picker v-model="content.floorSpacingBgRes" size="mini" @active-change="onSelect"></el-color-picker>
      </div>
      <div class="padding">
        <span>楼层间隔高度：</span>
        <el-input
          class="space-height"
          v-model="content.floorSpaceHeight"
          placeholder="请输入高度"
          @change="reviseValue"
        ></el-input>
        <span>px</span>
      </div>
    </div>
  </div>
</template>

<script>
import base from "../base";

export default {
  extends: base,
  contentDefault: {
    floorSpacingBgRes: "",
    floorSpaceHeight: "",
    floorSpaceTimeValue: "",
    activeKey: 1
  },
  data() {
    return {};
  },
  computed: {
    contentBg() {
      var url = _.get(this, "content.");
      if (url) {
        return `url(${url})`;
      } else {
        return "";
      }
    }
  },
  methods: {
    onSelect(val) {
      this.content.floorSpacingBgRes = val;
    },
    reviseValue(val) {
      this.content.floorSpaceHeight = val;
    }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss">
.floor-spacing {
  .panel-common-header {
    height: 35px;
    line-height: 35px;
    background: #f2f2f2;
    padding: 0 0 0 15px;
  }
  .block {
    padding: 20px 15px 20px 15px;
    display: flex;
    align-items: center;
    .color, .padding {
      display: flex;
    }
    .padding {
      padding-left: 50px;
      flex: 1;
      align-items: center;
      .el-input {
        width: auto;
      }
    }
  }
  .space-height {
    width: 70%;
    display: inline-block;
    margin-right: 10px;
  }
}
</style>
