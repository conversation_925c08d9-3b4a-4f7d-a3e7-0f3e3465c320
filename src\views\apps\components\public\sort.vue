<template>
	<div>
		<p class="blank_20"></p>
		<el-row :gutter="5">
			<el-col :span="4">
				<label>文字:</label>
				<el-color-picker
					  v-model="content.color"
					  size="mini"
				></el-color-picker>
			</el-col>
			<el-col :span="6">
				<label>当前文字:</label>
				<el-color-picker
					  v-model="content.hoverColor"
					  size="mini"
				></el-color-picker>
			</el-col>
			<el-col :span="6">
				<label>当前背景:</label>
				<el-color-picker
					  v-model="content.bgColor"
					  size="mini"
				></el-color-picker>
			</el-col>
			<el-col :span="8">
			</el-col>
		</el-row>
	</div>
</template>

<script>
	export default {
		props:{
			pageData:[Array,Object],
			label:String,
			clearNum:{
				type:Number,
				default:1
			}
		},
		data(){
			return {
				content:{}
			}
		},
		mounted(){
			this.content=_.cloneDeep(this.pageData)
		},
		name: "sort",
		watch: {
			'content':{
				deep:true,
				handler(val){
					this.$emit('listenData', {key: this.label, data: val})
				}
			},
			'clearNum':function(){
				this.content=_.cloneDeep(this.pageData)
			}
		}
	}
</script>

<style scoped>

</style>
