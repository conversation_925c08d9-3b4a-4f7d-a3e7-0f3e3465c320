<template>
  <div class="create-topic-modal">
    <el-dialog @close="afterClose" :visible.sync="visible" :title="title">
      <el-form
        :model="newCurrent"
        :rules="validate"
        :ref="validRef"
        label-position="right"
        size="small"
        label-width="100px"
        label-suffix="："
      >
        <el-form-item label="页面ID" prop="page_id">
          <el-input v-model="newCurrent.page_id" :disabled="true" placeholder="编号id自动生成" clearable></el-input>
        </el-form-item>
        <el-form-item label="名称" prop="page_name">
          <el-input v-model="newCurrent.page_name" placeholder="请填写名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="客户端类型" prop="clientType">
          <el-cascader
            v-model="origin.clientTypeNow"
            :options="origin.clientType"
            placeholder="选择客户端 / 类型"
            separator=">"
            filterable
            clearable
            :disabled="true"
          ></el-cascader>
        </el-form-item>

        <el-form-item label="所在业务线">
          <el-select
            v-model="department"
            disabled
            placeholder="选择业务线"
            default-first-option
            filterable
          >
            <el-option
              v-for="(item, i) in departmentList"
              :value="item.id"
              :label="item.name"
              :key="i"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="所在域" prop="branchCode" v-if="((origin || {}).clientTypeNow || [])[1]==='KAHome' || ((origin || {}).clientTypeNow || [])[1]==='KActivity'">
          <el-select
            v-model="newCurrent.branchCode"
            :disabled="false"
            placeholder="选择区域"
            default-first-option
            filterable
          >
            <el-option
              v-for="(item, i) in origin.branchs"
              :value="item.branchCode"
              :label="item.branchName"
              :key="i"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="state">
          <el-radio-group v-model.number="newCurrent.state">
            <el-radio v-for="(v, k) in origin.state" :label="Number(k)" :key="k">{{ v }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="origin.clientTypeNow&&origin.clientTypeNow[0]==='app'&&origin.clientTypeNow[1]==='index'" label="布局模板">
          <el-radio-group v-model="isNewLayout">
            <el-radio :label="3">新首页布局V8.0</el-radio>
            <el-radio :label="1">新首页布局</el-radio>
            <el-radio :label="2">原首页布局</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="((origin || {}).clientTypeNow || [])[1]==='diaNewLog'" label="弹窗内容">
          <el-radio-group v-model="newCurrent.dialogType" @change="changeDialogType">
            <el-radio :label="1">自定义</el-radio>
            <el-radio :label="2">优惠券</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="(origin.clientTypeNow&&((origin.clientTypeNow[0]==='app'&&origin.clientTypeNow[1]==='index')||origin.clientTypeNow[1]==='start'||origin.clientTypeNow[1]==='new_home') && (isNewLayout===1 || isNewLayout===3))
           || (((origin || {}).clientTypeNow || [])[1] === 'diaNewLog' && newCurrent.dialogType === 1)
           || ((origin || {}).clientTypeNow || [])[1] === 'exhibitionPosition' || getClientTypeNowHasTab(origin)
           "
          label="指定人群"
        >
          <el-radio-group v-model="newCurrent.crowdType" @change="changeCrowdType">
            <el-radio :label="1">全部人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="(
            (((((origin || {}).clientTypeNow || [])[1] === 'index' || ((origin || {}).clientTypeNow || [])[1]==='start' || ((origin || {}).clientTypeNow || [])[1]==='new_home') && (isNewLayout===1 || isNewLayout===3))
            || (((origin || {}).clientTypeNow || [])[1] === 'diaNewLog' && newCurrent.dialogType === 1)
            || (((origin || {}).clientTypeNow || [])[1] === 'exhibitionPosition')
          ) || getClientTypeNowHasTab(origin)) && newCurrent.crowdType===2 "
          label="指定人群"
        >
          <el-select
            v-model="crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-autocomplete
            style="width: 100%"
            class="inline-input"
            v-model.trim="crowdValue"
            :fetch-suggestions="querySearchCrowd"
            placeholder="请输入人群id"
            :trigger-on-focus="false"
            @select="handleSelectCrowd"
            @input="changeCrowdValue"
          ></el-autocomplete> -->
        </el-form-item>

        <el-form-item v-if="((origin || {}).clientTypeNow || [])[1]==='diaNewLog'" label="提醒时机">
          <el-radio-group v-model="newCurrent.occasion">
            <el-radio :label="1">仅一次</el-radio>
            <el-radio :label="2">每日一次</el-radio>
            <el-radio :label="3" v-if="newCurrent.sceneType == 6">不限</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="(((origin || {}).clientTypeNow || [])[1]==='diaNewLog')
            || (((origin || {}).clientTypeNow || [])[1]==='exhibitionPosition')"
          label="选择场景"
        >
          <el-radio-group v-model="newCurrent.sceneType"  @change="changeSceneType">
            <el-radio :label="1" v-if="((origin || {}).clientTypeNow || [])[1]==='diaNewLog'">首页</el-radio>
            <!-- <el-radio :label="2">购物车</el-radio> -->
            <el-radio :label="3" v-if="((origin || {}).clientTypeNow || [])[1]==='exhibitionPosition'">个人中心</el-radio>
            <el-radio :label="4" v-if="((origin || {}).clientTypeNow || [])[1]==='exhibitionPosition'">排行榜</el-radio>
            <!-- <el-radio :label="5" v-if="((origin || {}).clientTypeNow || [])[1]==='exhibitionPosition'">搜索发现</el-radio> -->
            <el-radio :label="6" v-if="(((origin || {}).clientTypeNow || [])[1]==='diaNewLog' && newCurrent.dialogType == 1) || ((origin || {}).clientTypeNow || [])[1]==='exhibitionPosition'">支付成功页</el-radio>
            <el-radio :label="7" v-if="((origin || {}).clientTypeNow || [])[1]==='exhibitionPosition'">大搜启动页</el-radio>
            <el-radio :label="8" v-if="((origin || {}).clientTypeNow || [])[1]==='exhibitionPosition'">订单页优惠券</el-radio>
            <el-radio :label="9" v-if="((origin || {}).clientTypeNow || [])[1]==='exhibitionPosition'">订单页轮播</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="页面模板" v-if="origin.isActive && categoryType === 'app'">
          <div>
            <el-select v-model="aPage" placeholder="请选择" class="aPage" value-key="name">
              <el-option
                v-for="cat in template_data"
                :key="cat.name"
                :label="cat.name"
                :value="cat"
              ></el-option>
            </el-select>
          </div>
        </el-form-item>

        <el-form-item label="预览模板" v-if="origin.isActive">
          <div class="viewImg">
            <img :src="aPage.viewImg" alt />
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="afterClose">取消</el-button>
        <el-button size="small" type="primary" :loading="sending" :disabled="sending" @click="save">
          {{sending ?
          '正在提交...' : saveBtnText}}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import api from "api";
import categoryList from "../topic-prop-components";
import categoryListPc from "../topicPc-prop-components";
import template_data_json from "./template_data";
import newLayout from "./newLayout";
import newLayoutV8 from "./newLayoutV8";
import newLayoutHome from "./newLayoutHome";
import newLayoutTab from "./newLayoutTab.json"
import topic from "api/topic";
import { AppWebsite } from "config";

export default {
  name: "CreateTopicModal",

  props: {
    categoryType: String,
    origin: Object,
    current: Object
  },

  data() {
    return {
      crowdValue: "",
      isNewLayout: 3,
      aPage: {},
      newCurrent: {},
      department: 1,
      previewImg: "",
      visible: false,
      sending: false,
      isEdit: false,
      isAdmin: false,
      isActive: false,
      checkAll: false,
      checkedCities: [],
      isIndeterminate: true,
      selectLoading: false,
      options: [],
      data: {
        state: -1,
        category: "app",
        page_type: "index",
        dialogType: 1,
        occasion: 1,
        sceneType: 1,
        crowdType: 1,
      },
      validRef: "form", //验证ref属性值
      validate: {
        state: [{ required: true, message: "请选择状态", trigger: "change" }],
        /*					category: [
                      { required: true, message: '请选择页面类型', trigger: 'change' }
                    ],*/
        branchCode: [
          { required: true, message: "请选择区域", trigger: "change" }
        ],
        clientType: [
          { required: true, message: "选择客户端 / 类型", trigger: "change" }
        ],
        page_name: [
          { required: true, message: "请填写名称", trigger: "blur" },
          { min: 2, max: 36, message: "长度在2 - 36之间", trigger: "blur" }
        ]
        /*page_id: [
            { min: 1, max: 20, message: '长度在2 - 20之间', trigger: 'blur' },
            { validator: (rule, val, callback) => {
                  if (/\s/.test(val)
                      || !/^[A-Za-z\d_]+$/.test(val))
                      var e = new Error('只能包含英文、数字及下划线，且不能含有空格等');
                  return callback(e);
              }, trigger: 'blur' },
            { validator: this.codeValid, trigger: 'blur' }
          ]*/
      },
      template_data: [],
      departmentList: [
        {
          id: 1,
          name: "药帮忙"
        },
        {
          id: 2,
          name: "控销"
        },
        {
          id: 8,
          name: "宜块钱"
        },
        {
          id: 9,
          name: "KA"
        }
      ]
    };
  },

  watch: {
    current: {
      deep: true,
      handler(val, oldVal) {
        /* 客户端类型 */
        val.category = !val.clientType ? null : val.clientType[0];
        val.page_type = !val.clientType ? null : val.clientType[1];
        this.newCurrent = JSON.parse(JSON.stringify(val));
        this.$set(
          this.newCurrent,
          "branchCode",
          this.currentAccount &&
            this.currentAccount.defaultBranch &&
            this.currentAccount.defaultBranch.branchCode
            ? this.currentAccount.defaultBranch.branchCode
            : ""
        );
      }
    }
  },

  mounted() {
    this.template_data = template_data_json;
  },

  computed: {
    currentAccount() {
      return this.$store.getters["sys/currentAccount"];
    },

    isCopy() {
      return !!this.origin.copy.page_id;
    },

    title() {
      return this.isCopy
        ? `复制页面 - ${this.origin.copy.page_name}`
        : this.isEdit
        ? "编辑页面"
        : "创建页面";
    },

    saveBtnText() {
      return this.isCopy ? "复制" : this.isEdit ? "保存" : "创建";
    },

    categoryList() {
      if (this.origin.clientTypeNow[0] === "pc") {
        return (
          categoryListPc[
            `${this.origin.clientTypeNow[0]}_${this.origin.clientTypeNow[1]}`
          ] || []
        );
      }
      return categoryList["app_h5"] || [];
    }
  },

  methods: {
    getClientTypeNowHasTab(origin){
      return origin.clientTypeNow.indexOf('tab') !== -1;
    },
    // groupChange(val){
    //        if(val===2){
    //          this.groupState=true
    //        }
    //        else{
    //          this.groupState=false
    //        }
    // },
    changeCrowdValue(e) {
      this.$forceUpdate();
    },
    changeDialogType() {
      this.newCurrent.crowdType = 1;
      this.newCurrent.occasion = 1;
      this.newCurrent.sceneType = 1;
    },
    changeSceneType () {
      this.newCurrent.occasion = 1;
    },
    changeCrowdType() {
      (this.currentCrowd || {}).id = '';
      this.crowdValue = '';
    },
    // handleSelectCrowd(item) {
    //   this.currentCrowd = item;
    // },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8"
        }
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [{
          label: data.name,
          value: val,
        }]
      } else {
        this.selectLoading = false;
        this.options = []
      }
    },
    selectCrowd(e) {
      if (e) {
        this.currentCrowd = {
          id: Number((this.options[0].value).trim()),
          value: this.options[0].label,
        };
      } else {
        this.currentCrowd = {
          id: '',
          value: '',
        };
      }
      this.$forceUpdate();
    },
    // async querySearchCrowd(queryString, cb) {
    //   const pms = {
    //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
    //     dataType: "json",
    //     data: {},
    //     head: {
    //       "Content-Type": "application/json;charset=UTF-8"
    //     }
    //   };
    //   const res = await api.proxy.post(pms);
    //   if (res.success) {
    //     const { data } = res;
    //     cb([
    //       {
    //         id: queryString,
    //         value: data.name || ""
    //       }
    //     ]);
    //     return false;
    //   }
    // },
    logInsert(params) {
      topic.logInsert(params).then(res => {
      });
    },
    show(visible) {
      this.isActive = false;
      this.visible = visible;
      this.isAdmin = this.origin.loginUser.userName == "admin"; //管理员功能限制
      this.$nextTick(() => {
        this.isEdit = !!this.newCurrent.page_id;
        if (!this.isAdmin)
          this.$set(
            this.current,
            "branchCode",
            this.newCurrent.branchCode ||
              this.origin.loginUser.branch.branchCode
          );
        if (this.origin.clientTypeNow[1] === "diaNewLog") {
          this.$set(
            this.current,
            "dialogType",
            this.isCopy ? Number(this.origin.copy.dialogType) : this.data.dialogType,
          );
          this.$set(
            this.current,
            "sceneType",
            this.isCopy ? Number(this.origin.copy.sceneType) : this.data.sceneType,
          );
          this.$set(
            this.current,
            "occasion",
            this.isCopy ? Number(this.origin.copy.occasion) : this.data.occasion,
          );
        }
        if (this.origin.clientTypeNow[1] === "exhibitionPosition") {
          this.$set(
            this.current,
            "sceneType",
            this.isCopy ? Number(this.origin.copy.sceneType) : this.data.sceneType,
          );
        }
        this.$set(
          this.current,
          "crowdType",
          this.isCopy ? (this.origin.copy.crowdId ? 2 : 1) : this.data.crowdType,
        );
        this.crowdValue = this.isCopy ? (this.origin.copy || {}).crowdValue : '';
        
        this.$set(
          this.current,
          "state",
          this.newCurrent.state || this.data.state,
        );
        this.$set(this.current, "clientType", [
          //设置默认“客户端 / 类型”
          this.newCurrent.category || this.data.category,
          this.newCurrent.page_type || this.data.page_type
        ]);
      });
    },

    save() {
      this.sending = true;
      this.$refs[this.validRef].validate(async ok => {
        if (!ok) {
          this.$message.error("验证失败！");
          this.sending = false;
          return;
        }
        const params_obj = {};
        if (
          (this.origin.clientTypeNow &&
          (this.origin.clientTypeNow[1] === "index" || this.origin.clientTypeNow[1] === "new_home"
            || (((this.origin || {}).clientTypeNow || [])[1] === "diaNewLog")
            || (((this.origin || {}).clientTypeNow || [])[1] === "start")
            || (((this.origin || {}).clientTypeNow || [])[1] === "exhibitionPosition")
          ) &&
          (this.isNewLayout === 1 || this.isNewLayout === 3 || this.isNewLayout === 4)) &&
          this.newCurrent.crowdType === 2
        ) {
          if (!this.crowdValue) {
            this.$message.error("请选择人群！");
            this.sending = false;
            return false;
          }
          if (!this.currentCrowd || !this.currentCrowd.id) {
            this.$message.error("请选择正确的人群！");
            this.sending = false;
            return false;
          }
          params_obj.crowdId = this.currentCrowd.id;
          params_obj.crowdValue = this.currentCrowd.value;
        }
        let aPageList = [{ title: this.aPage.name, children: [] }];
        if (this.aPage && this.categoryList.length > 0) {
          let asd = [];
          for (var i = 0; i < this.categoryList.length; i++) {
            asd = asd.concat(_.get(this.categoryList[i], "children"));
          }
          if (this.aPage.comList && this.aPage.comList.length > 0) {
            this.contentComDataHander(
              asd,
              this.aPage.comList,
              aPageList[0].children
            );
          } else {
            this.allComDataHander(asd, aPageList[0].children);
          }
        }
        if (this.origin.clientTypeNow[1] === "index") {
          params_obj.isNewLayout = this.isNewLayout;
          if (this.isNewLayout === 1) {
            params_obj.layout = newLayout.layout;
          } else if (this.isNewLayout === 3) {
            if (this.origin.clientTypeNow && this.origin.clientTypeNow[0] === 'pc') {
              params_obj.layout = newLayoutV8.layout.pc;
            } else {
              params_obj.layout = newLayoutV8.layout.app;
            }
          }
        }
        if (this.origin.clientTypeNow[1] === "new_home") {
          params_obj.isNewLayout = 4;
          params_obj.layout = newLayoutHome.layout.app;
        }
        if (this.origin.clientTypeNow[1] === "tab") {
          params_obj.isNewLayout = 5;
          params_obj.layout = newLayoutTab.layout.app;
        }
        if (this.origin.clientTypeNow[1] === "diaNewLog") {
          params_obj.dialogType = this.newCurrent.dialogType;
          params_obj.sceneType = this.newCurrent.sceneType;
          params_obj.occasion = this.newCurrent.occasion;
        }
        if (this.origin.clientTypeNow[1] === "exhibitionPosition") {
          params_obj.sceneType = this.newCurrent.sceneType;
        }
        let data = Object.assign(params_obj, this.newCurrent, {
          categoryList: aPageList,
          previewImg: this.aPage.viewImg
        });
        delete data.clientType;
        data.category = this.origin.clientTypeNow[0];
        data.page_type = this.origin.clientTypeNow[1];
        data.branchCode = data.branchCode || 'XS000000';
        this.isCopy
          ? this.copy(data)
          : this.isEdit
          ? this.update(data)
          : this.create(data);

        this.sending = false;
      });
    },

    contentComDataHander(_item, _vals, _dataArr) {
      for (var i = 0; i < _vals.length; i++) {
        _item.filter(v => {
          if (v.name === _vals[i].name) {
            let _obj = { title: v.title, name: v.name };
            _dataArr.push(_obj);
          }
        });
      }
    },

    allComDataHander(_item, _dataArr) {
      _item.forEach(element => {
        let _obj = { title: element.title, name: element.name };
        _dataArr.push(_obj);
      });
    },

    afterClose() {
      this.$refs[this.validRef].resetFields();
      this.afterSave();
      // this.aPage=[];
      // this.$emit('close');
    },

    afterSave() {
      this.aPage = {};
      this.previewImg = "";
      this.$emit("save-done");
      this.show(false);
    },

    async copy(data) {
      if((data.sceneType && data.sceneType==8) || (this.newCurrent.sceneType && this.newCurrent.sceneType == 8) ){
        let pms = {
          category: "app",
          pageFrom:1,
          page_type:"exhibitionPosition",
          sceneType:8
        };
        const res = await api.topic.list(pms);
        console.log(res,'lwq');
        if (res.code === 200) {
          if(res.data.total > 0){
            this.$message.error("已存在订单页优惠券展位，展位ID为" + res.data.rows[0].page_id);
            return
          }
        } else {
          this.$message.error(result.msg);
          return
        }
      }
      this.sending = true;
      const result = await api.topic.copy(
        this.origin.copy.page_id,
        data || this.newCurrent
      );
      this.sending = false;
      if (result.code === 200) {
        this.$message.success("复制成功!");
        this.afterSave();
        this.logInsert(
          Object.assign(
            {
              type: "页面复制"
            },
            data
          )
        );
      } else {
        this.$message.error(result.msg);
      }
    },

    update(data) {
      //WS.axiosPost(`updateTopic?id=${this.current.page_id}`, data || this.current, res => {
      //    this.$message.success('页面修改成功!');
      //    this.afterSave();
      //});
    },

    async create(data) {
      if((data.sceneType && data.sceneType==8) || (this.newCurrent.sceneType && this.newCurrent.sceneType == 8) ){
        let pms = {
          category: "app",
          pageFrom:1,
          page_type:"exhibitionPosition",
          sceneType:8
        };
        const res = await api.topic.list(pms);
        console.log(res,'lwq');
        if (res.code === 200) {
          if(res.data.total > 0){
            this.$message.error("已存在订单页优惠券展位，展位ID为" + res.data.rows[0].page_id);
            return
          }
        } else {
          this.$message.error(result.msg);
          return
        }
      }
      this.sending = true;
      const result = await api.topic.add(data || this.newCurrent);
      this.sending = false;
      if (result.code === 200) {
        this.$message.success("页面创建成功!");
        this.afterSave();
        this.logInsert(
          Object.assign(
            {
              type: "页面添加"
            },
            data
          )
        );
       if(data.page_type=='start'){
        console.log(data)
        data.page_id=result.id
        this.$emit("changePriority",0,data,1)
       }
      } else {
        this.$message.error(result.msg);
      }
      // let parm = {
      //   page_name: data.page_name,
      //   // branchCode: data.branchCode,
      //   category: data.category,
      //   page_type: data.page_type
      // };
      // const list = await api.topic.query(parm);
      // if (list.code === 200) {
      //   if (list.data.length > 0) {
      //     this.$confirm("该区域已经存在该页面名称，是否还要添加！", "提示", {
      //       confirmButtonText: "确定",
      //       cancelButtonText: "取消",
      //       type: "warning"
      //     }).then(async () => {
      //       const result = await api.topic.add(data || this.newCurrent);
      //       this.sending = false;
      //       if (result.code === 200) {
      //         this.$message.success("页面创建成功!");
      //         this.afterSave();
      //         this.logInsert(
      //           Object.assign(
      //             {
      //               type: "页面添加"
      //             },
      //             data
      //           )
      //         );
      //       } else {
      //         this.$message.error(result.msg);
      //       }
      //     });
      //   } else {
      //     const result = await api.topic.add(data || this.newCurrent);
      //     this.sending = false;
      //     if (result.code === 200) {
      //       this.$message.success("页面创建成功!");
      //       this.afterSave();
      //       this.logInsert(
      //         Object.assign(
      //           {
      //             type: "页面添加"
      //           },
      //           data
      //         )
      //       );
      //     } else {
      //       this.$message.error(result.msg);
      //     }
      //   }
      // }
    },

    /**
     * 编号验证
     * @param rule
     * @param value
     * @param callback
     * @returns {Promise<*>}
     */
    codeValid(rule, val, callback) {
      let data = this.newCurrent;
      if (data.id || !data.page_id) return callback();

      if (this.timId) clearTimeout(this.timId);
      this.timId = setTimeout(async () => {
        //规定时间内，不频繁验证
        let pms = {
          page_id: data.page_id
        };
        let res = await api.topic.query(pms);
        if (res.code == 200 && res.data && res.data.length > 0)
          var e = new Error("此编号已存在");
        clearTimeout(this.timId);
        return callback(e);
      }, 1000);
    }
  }
};
</script>
<style lang="scss" rel="stylesheet/scss">
.create-topic-modal {
  .aPage.el-select {
    width: 100%;
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  .viewImg {
    width: 400px;
    max-height: 600px;
    overflow: auto;

    img {
      width: 100%;
    }
  }
}
</style>
