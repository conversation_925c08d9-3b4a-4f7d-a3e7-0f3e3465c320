<template>
    <div class="topic-menu-list">
        <div>
            <div>
                背景颜色:
                <el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>
                页面名颜色:
                <el-color-picker v-model="content.subject_title_color" size="mini"></el-color-picker>
                标语说明颜色:
                <el-color-picker v-model="content.subject_introduce_color" size="mini"></el-color-picker>
            </div>
            <el-input placeholder="请输入内容" v-model="content.subject_title">
                <template slot="prepend">页面名</template>
            </el-input>

            <el-input placeholder="请输入内容" v-model="content.subject_introduce" style="margin-top: 5px">
                <template slot="prepend">标语说明</template>
            </el-input>
        </div>
        <el-table :data="list" style="width: 100%;margin-top: 5px" height="250" v-if="list.length>0"
                  ref="multipleTable">
            <el-table-column fixed label="图片" width="80">
                <template slot-scope="scope">
                    <img :src="scope.row.imageUrl" :alt="scope.row.productName" style="width:100%;max-height:50px;">
                </template>
            </el-table-column>
            <el-table-column prop="productName" label="药名">
                <template slot-scope="scope">
                    <span v-if="scope.row.productName">{{scope.row.productName}}</span>
                    <span v-else>{{scope.row.showName}}</span>
                </template>
            </el-table-column>
            <el-table-column label="规格" width="80">
                <template slot-scope="scope">
                    {{scope.row.mediumPackageTitle}}
                </template>
            </el-table-column>
            <el-table-column prop="fob" label="价格" width="80">
                <template slot-scope="scope">
                    {{scope.row.fob}}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="80">
                <template slot-scope="scope">
                    <div class="edit-button">
                        <el-button @click="handleDelete(scope.row)" type="warning" size="mini">删除</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <!--选择商品-->
        <all-link @select="onSetLink" :tabs="tabs" :params="{
                productlink: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                importGoods: {
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                goodsGroup: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
            }"></all-link>
    </div>
</template>

<script>
    import base from "../base";
    import api from 'api'

    export default {
        extends: base,
        contentDefault: {
            list: [],
            bgColor: "#578EE1",
            subject_title: "口碑好货",
            subject_title_color: "#ffffff",
            subject_introduce: "大家都在买的常用药",
            subject_introduce_color: "",
            subject_more_url: "",
        },
        data() {
            return {
                loading: false,
                tabs: [
                    {label: '商品', value: 'productlink'},
                    {label: '导入商品', value: 'importGoods'},
                    {label: '商品组', value: 'goodsGroup'}
                ],
            }
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            },
            moreLink(data) {
                if (!data || !data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            }
        },
        computed: {
            list() {
                var list = _.get(this, 'content.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            }
        },
        methods: {
            onSetLink(link) {
                if(link.tag === "goods" || link.tag === "importGoods"){
	                this.content.list = !this.content.list.length ?
			                [...link.data] :
                            [...api.common.removeRepeat(this.content.list, link.data)];
                } else if (link.tag === "goodsGroup") {
	                this.content.list = !this.content.list.length ?
			                [...link.data._goods] :
                            [...api.common.removeRepeat(this.content.list, link.data._goods)];
                }
            },
            handleDelete(row) {
                let index = this.list.indexOf(row)
                this.list.splice(index, 1)
            }

        }
    }
</script>
<style scoped lang="scss">
    /*.topic-pic-upload{*/
    /*display:block;*/
    /*width:100% ;*/
    /*}*/
    /*.el-upload--text{width:100%}*/

</style>
