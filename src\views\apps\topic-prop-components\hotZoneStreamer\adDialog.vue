<template>
<div>
  <el-dialog class="banner-dialog" :title="isEdit ? '编辑广告' : '添加广告'" :visible.sync="visible" :before-close="closeEditContent">
    <el-form ref="form" :model="editData" label-width="120px">
      <el-form-item label="广告名称：">
        <el-input v-model="editData.entry" class="entry-name"></el-input>
      </el-form-item>
      <el-form-item label="人群范围：">
        <el-radio-group v-model="editData.crowdType" @change="changeCrowdType">
          <el-radio :label="1">全部人群</el-radio>
          <el-radio :label="2">指定人群</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="指定人群：" v-if="editData.crowdType===2">
        <el-select
          v-model="editData.crowdValue"
          :loading="selectLoading"
          filterable
          :filter-method="optionFilter"
          placeholder="请输入人群id"
          clearable
          @clear="options = []"
          @change="selectCrowd"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <!-- <el-autocomplete
          style="width: 300px"
          class="inline-input"
          v-model.trim="editData.crowdValue"
          :fetch-suggestions="querySearchCrowd"
          placeholder="请输入人群id"
          :trigger-on-focus="false"
          @select="handleSelectCrowd"
          @input="changeCrowdValue"
        ></el-autocomplete> -->
      </el-form-item>
      <el-form-item label="展示时间：">
       
        <el-radio  v-model="editData.timeType" label="1">固定时段</el-radio>  <el-date-picker
          v-model="editData.timevalue"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
         
        ></el-date-picker><br>
              <el-radio  v-model="editData.timeType" label="2">周期循环</el-radio>  <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
       
      </el-form-item>
      <el-form-item label="广告模式：">
        <el-select @change="changeMode" v-model="editData.mode" placeholder="请选择">
          <el-option
            v-for="item in modeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="广告图片：">
        <el-upload
          class="topic-image-upload"
          ref="upload"
          accept="image/jpeg,image/jpg, image/png, image/gif"
          :show-file-list="false"
          :before-upload="() => {editImgLoading = true; return true;}"
          :on-success="uploadEditContImage"
        >
          <div v-if="editData.title" class="imgBox">
            <img :src="editData.title" class="image" />
            <div class="modalImg">
              <div class="childModal" v-for="(item, index) in hotZoneInfoList" :key="index" :style="{width: `${item.hotZoneWidth}%`}"></div>
            </div>
          </div>
          
          <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
          <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
        <span>建议尺寸：1242*350px</span>
      </el-form-item>
    <hr class="hr" />

    <div v-for="(item, index) in hotZoneInfoList" :key="index">
      <div class="infoItem">{{index+1}} 热区信息：</div>
      <el-form-item label="热区宽度：">
        <el-input-number @change="changeInputNumber" v-model="hotZoneInfoList[index].hotZoneWidth" :step="1" step-strictly :min="0" :max="hotZoneInfoList[index].maxNumber"></el-input-number>
      </el-form-item>
      <el-form-item label="热区名称：">
        <el-input v-model="hotZoneInfoList[index].hotZoneName" class="entry-name"></el-input>
      </el-form-item>
      <el-form-item label="链接类型：">
        <el-select v-model="hotZoneInfoList[index].linkType" placeholder="请选择">
          <el-option
            v-for="item in linkOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <div class="topic-image-picker" v-if="hotZoneInfoList[index].linkType !== 'dynamic'">
      <el-input placeholder="链接地址" v-model="hotZoneInfoList[index].page_url" @input="changePageUrl(index)">
        <template slot="prepend">跳转链接</template>
      </el-input>
    </div>

    <div v-if="item.linkType === 'dynamic'">
      <div class="topic-image-picker">
        <el-input style="width:200px" placeholder="输入跳转id" v-model="hotZoneInfoList[index].dynamicId">
          <template slot="prepend">跳转id</template>
        </el-input>
          <el-button type="primary" @click="putDynamicLink(hotZoneInfoList[index])">生成链接</el-button>
      </div>
      <el-input placeholder="链接地址" v-model="hotZoneInfoList[index].page_url">
        <template slot="prepend">跳转链接</template>
      </el-input>
    </div>

    <div v-if="item.linkType==='topic'">
      <page-link @select="onSetLink($event, item)" :params="{branchCode: branchCode}"></page-link>
    </div>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="closeEditContent">取 消</el-button>
      <el-button size="small" type="primary" @click="add_confirmEdit">确定</el-button>
    </div>

  </el-dialog>
  <loopcirculateTime ref="loopcirculateTime" @loopcirculateTimeBack="loopcirculateTimeBack"></loopcirculateTime>
</div>
</template>
<script>
  import { AppWebsite, getUrlParam } from "config";
  import loopcirculateTime from '../../components/loopcirculateTime.vue';
  import api from 'api';
  export default {
    name: 'adDialog',
    props:[ "visible", "branchCode", "adItemData", "editIndex", "pageType" ],
    components:{
      loopcirculateTime
    },
    data() {
      return {
        editImgLoading: false,
        maxNumber: 100, 
        editData: {
          linkType: "topic",
          title: "",
          entry: "",
          operation: "",
          timevalue: "",
          createTimevalue:"",
          mode: "1",
          crowdType: 1,
          crowdValue: '',
          crowdId: '',
          timeType:'1',
        circulateTime:{}
        },
        selectLoading: false,
        options: [],
        activeIndex: 0,
        modeList: [
          {
            value: "1",
            label: "1热区"
          }, {
            value: "2",
            label: "2热区"
          }, {
            value: "3",
            label: "3热区"
          }, {
            value: "4",
            label: "4热区"
          }
        ],
        hotZoneInfoList: [
          { hotZoneWidth: '100', hotZoneName: '', linkType: 'topic', dynamicId: '', page_url: '', maxNumber: 100 }
        ],
        linkOptions: [
          {
            value: "topic",
            label: "专题页链接"
          }, {
            value: "stores",
            label: "店铺页链接"
          }, {
            value: "dynamic",
            label: "动态商品链接"
          }
        ],
        pickerOptions: {
          shortcuts: [
            {
              text: "未来一周",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来三个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来六个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一年",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                picker.$emit("pick", [start, end]);
              }
            }
          ]
        },
      };
    },
    mounted () { 
      if(this.isEdit) {
        this.editData = this.adItemData;
        this.hotZoneInfoList = this.adItemData.hotZoneInfoList || [];
      }
    },
    created() {
      this.debounce = _.debounce(this.changeLink, 1000);
    },
    computed: {
      isEdit() {
        return Object.keys(this.adItemData).length > 0 ? true : false;
      }
    },
    methods: {
      // changeCrowdValue(e) {
      //   if (!e) {
      //     this.editData.crowdId = '';
      //   }
      //   this.$forceUpdate();
      // },
       //打开时间循环
    toloopcirculateTime(){
      this.$refs.loopcirculateTime.showVisible=true
    },
    //循环时间回调
    loopcirculateTimeBack(data){
      this.editData.circulateTime=data
console.log(this.editData)
    },
      changeCrowdType() {
        this.editData.crowdId = '';
        this.editData.crowdValue = '';
      },
      async optionFilter(val) {
        this.selectLoading = true;
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          this.selectLoading = false;
          this.options = [{
            label: data.name,
            value: val,
          }]
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCrowd(e) {
        if (e) {
          this.editData.crowdId = Number(this.options[0].value.trim());
          this.editData.crowdValue = this.options[0].label;
        } else {
          this.editData.crowdId = '';
          this.editData.crowdValue = '';
        }
        this.$forceUpdate();
      },
      // async querySearchCrowd(queryString, cb) {
      //   const pms = {
      //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
      //     dataType: "json",
      //     data: {},
      //     head: {
      //       "Content-Type": "application/json;charset=UTF-8"
      //     }
      //   };
      //   const res = await api.proxy.post(pms);
      //   if (res.success) {
      //     const { data } = res;
      //     cb([{
      //       id: queryString,
      //       value: data.name || ""
      //     }]);
      //     return false;
      //   }
      // },
      // handleSelectCrowd(item) {
      //   this.editData.crowdId = item.id;
      // },
      putDynamicLink(item) {
        if (!item.dynamicId) {
          this.$message({
            message: '请输入跳转id再点击生成链接',
            type: 'warning'
          });
          return false;
        }
        item.page_url = `ybmpage://homeSteadyChannel?strategyId=${item.dynamicId}&title=${this.editData.entry}`
      },
      
      onSetLink(link, item) {
        item.page_url = link.meta.page_url;
      },
      
      async uploadEditContImage(res, file) {
        this.editImgLoading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: "warning"
          });
          return;
        }
        this.editData.title = res.data.url;
      },
      
      closeEditContent() {
        this.$parent.closeEditContent();
      },

      // 设置每个输入框的maxNumber
      changeInputNumber(e) {
        this.hotZoneInfoList.map((item, index) => {
          let ortherItemArr = this.hotZoneInfoList.filter((item2) => { return item != item2 });
          let ortherWidthArr = ortherItemArr.map((item3) => { return item3.hotZoneWidth });
          let sum = ortherWidthArr.reduce((prev, cur) => {
            return Number(prev || 0) + Number(cur || 0);
          });;
          item.maxNumber = 100 - sum;
        })
      },
      
      //确定添加广告
      add_confirmEdit() {
        if (!this.editData.crowdType || (this.editData.crowdType === 2 && !this.editData.crowdId)) {
          this.$message.warning("请选择正确的人群");
          return false;
        }
        if (!this.editData.entry) {
          this.$message.warning("请填写广告名称");
          return false;
        }
        
        if (!this.editData.timevalue&&this.editData.timeType!=2) {
          this.$message.warning("请选择有效时间");
          return false;
        }
        if (this.editData.timeType==2&&(!this.editData.circulateTime||Object.keys(this.editData.circulateTime).length==0||!this.editData.circulateTime.circulateList||this.editData.circulateTime.circulateList.length==0)) {
          this.$message.warning("请添加【周期循环】 时间段。");
          return false;
        }
        if (!this.editData.title) {
          this.$message.warning("请上传图片");
          return false;
        }
        // let linkErrMsg = '';
        // this.hotZoneInfoList.forEach(async(item) => {
        //   if (item.linkType === 'topic' && item.page_url) {
        //     if (!new RegExp("^ybmpage://commonh5activity.*$").test(item.page_url)) {
        //       linkErrMsg = '跳转链接格式不正确，请检查';
        //     } else {
        //       let linkPageUrl = getUrlParam(item.page_url, 'url')
        //       const result = await api.topic.checkPageUrl({ url: linkPageUrl });
        //       if (((result || {}).data || {}).status != 200) {
        //         console.log(result);
        //         linkErrMsg = '跳转链接不存在，请检查';
        //       }
        //     }
        //   }
        // })
        // console.log(await linkErrMsg);

        // if (await linkErrMsg) {
        //   this.$message.error(linkErrMsg);
        //   return false;
        // }
        
        this.closeEditContent();
        this.psData = {
          title: this.editData.title,
          entry: `${this.editData.entry}`,
          mode: this.editData.mode,
          timevalue: this.editData.timevalue,
          linkType: this.editData.linkType,
          createTimevalue: new Date().getTime(),
          hotZoneInfoList: this.hotZoneInfoList,
          crowdId: this.editData.crowdId,
          crowdType: this.editData.crowdType,
          crowdValue: this.editData.crowdValue,
          timeType:this.editData.timeType,
        circulateTime:this.editData.circulateTime
        };
        if (this.isEdit) {
          this.$emit('saveDialog','edit', this.psData, this.editIndex )
        } else {
          this.$emit("saveDialog", 'add', this.psData);
        }
      },

      changeMode(e) {
        this.hotZoneInfoList = [];
        for(let i = 0; i < e; i++) {
          let obj = {
            hotZoneWidth: e == 3 && i == 2 ? 34 : parseInt(100 / e),
            hotZoneName: '',
            linkType: 'topic',
            dynamicId: '',
            page_url: '',
            maxNumber: 100,
          };
          this.hotZoneInfoList.push(obj)
        }
      },

      changePageUrl(index) {
        this.activeIndex = index;
      },
      async changeLink() {
        this.hotZoneInfoList[this.activeIndex].page_url= this.hotZoneInfoList[this.activeIndex].page_url.trim()

        if (this.hotZoneInfoList[this.activeIndex].linkType === 'topic' && this.hotZoneInfoList[this.activeIndex].page_url) {
          if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.hotZoneInfoList[this.activeIndex].page_url)) {
            this.$message.error('跳转链接格式不正确');
            this.hotZoneInfoList[this.activeIndex].page_url = '';
          } else if (this.hotZoneInfoList[this.activeIndex].linkType === 'topic') {
            let linkPageUrl = getUrlParam(this.hotZoneInfoList[this.activeIndex].page_url, 'url');
            const result = await api.topic.checkPageUrl({ url: linkPageUrl });
            if (((result || {}).data || {}).status != 200) {
              this.$message.error('跳转链接不存在');
              this.hotZoneInfoList[this.activeIndex].page_url = '';
            }
          }
        }
      }
    },
    //监听input输入值变化
    watch:{
      'hotZoneInfoList': {
				deep: true,
				handler(val) {
					if (val && val[this.activeIndex].page_url) {
						this.debounce();
					}
				}
			}
    }
  };
 
</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .topic-image-upload {
    .imgBox {
      position: relative;
    }
    .image {
      display: block;
      width: 100%;
    }
    .modalImg {
      display: flex;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      .childModal {
        border: 1px solid #13c2c2;
        height: 100%;
      }
    }
    .uploader-icon {
      width: 100px;
      height: 100px;
      line-height: 100px;
      border: 1px solid $border-base;
      font-size: 30px;
    }
  }
  .hr {
    height: 1px;
    background: #E8E6E6;
  }
  .infoItem {
    margin-top: 10px;
  }
  .topic-image-picker {
    padding-bottom: 10px;
  }
</style>
