<template>
  <!-- 添加内容弹层 -->
  <el-dialog class="banner-dialog" width="70%" title="关联店铺" :visible.sync="visible" :before-close="closeEditContent">
    <div style="margin: 10px 0">
      <span style="fontSize: 15px">店铺组名称：（不在用户侧展示）</span>
      <el-input style="width: 300px" size="small" v-model="editData.groupShopsName" />
    </div>
    <div style="margin: 10px 0">
      <span style="fontSize: 15px">人群范围：</span>
      <el-radio-group v-model="editData.crowdType" @change="changeCrowdType">
        <el-radio :label="1">全部人群</el-radio>
        <el-radio :label="2">指定人群</el-radio>
      </el-radio-group>
    </div>
    <div v-if="editData.crowdType===2">
      <span style="fontSize: 15px">指定人群：</span>
      <el-select
        v-model="editData.crowdValue"
        :loading="selectLoading"
        filterable
        :filter-method="optionFilter"
        placeholder="请输入人群id"
        clearable
        @clear="options = []"
        @change="selectCrowd"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <!-- <el-autocomplete
        style="width: 300px"
        class="inline-input"
        size="small"
        v-model.trim="editData.crowdValue"
        :fetch-suggestions="querySearchCrowd"
        placeholder="请输入人群id"
        :trigger-on-focus="false"
        @select="handleSelectCrowd"
        @input="changeCrowdValue"
      ></el-autocomplete> -->
    </div>
    <div slot="footer" class="dialog-footer">
      <!-- el-table放到footer里的原因：Dialog 的内容是懒渲染的，但footer是实时的。放外面的话拿不到DOM无法操作拖拽 -->
      <el-table :data="editData.shopsList" size="mini" :row-key="(row) => row.shopCode" style="margin: 0 0 20px">
        <el-table-column label="序号" width="50" type="index" />
        <el-table-column label="店铺logo">
          <template slot-scope="scope">
            <img
              v-if="scope.row.appLogoUrl"
              :src="scope.row.appLogoUrl"
              alt="图"
              class="title-image"
            />
            <i v-else class="el-icon-circle-plus-outline no-img"></i>
          </template>
        </el-table-column>
        <el-table-column label="店铺名称">
          <template slot-scope="scope">
            <el-input v-model="scope.row.customShopName" size="mini" @input="changeInput" />
          </template>
        </el-table-column>
        <el-table-column label="展示商品" v-if="!hideDisplayGoods">
          <template slot-scope="scope">
            <div ref="closepopover">
              <el-popover placement="top" trigger="click">
                <div class="priorityCheck">
                  <el-input size="mini" @input="popoverValue = !bindCsuOrProductGroup ? popoverValue.replace(/[^\d]/g, '') : popoverValue" v-model="popoverValue" />
                  <el-button type="primary" icon="el-icon-check" size="mini" @click="submitCsuId(scope.$index,scope.row)"></el-button>
                  <el-button type="info" icon="el-icon-close" size="mini" @click="hidePopover()"></el-button>
                </div>
                <el-button type="text" size="mini" slot="reference" @click="showCsuId(scope.row.csuId || scope.row.productGroupId)">{{ scope.row.csuId || scope.row.productGroupId || '自动指定' }}</el-button>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="引导语">
          <template slot-scope="scope">
            <el-input v-model="scope.row.customMessage" size="mini" />
          </template>
        </el-table-column> -->
        <el-table-column label="店铺状态">
          <template slot-scope="scope">
            <b v-if="scope.row.status === 2" style="color: #67C23A">已上线</b>
            <b v-if="scope.row.status === 1" style="color: red">待上线</b>
            <b v-if="scope.row.status === 3" style="color: red">已下线</b>
            <b v-if="scope.row.status === 4" style="color: red">已关闭</b>
          </template>
        </el-table-column>
        <el-table-column label="展示状态">
          <template slot-scope="scope">
            <span v-html="format_text(scope.row.timevalue)"></span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="有效时间" width="450">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.timevalue"
              @input="changeInput"
              type="datetimerange"
              :picker-options="pickerOptions"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="left"
            ></el-date-picker>
            <el-button
              type="danger"
              @click="del_store(scope.row.shopCode)"
              icon="el-icon-delete"
              circle
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
      <allStore
        @emitStore="handle_store"
        :stores="editData.shopsList"
        :params="{
          branchCode: topic.branchCode,
          page_type: topic.page_type,
          content: content,
          componentName: 'carefullySelectedShops'
        }"
      />
      <div style="marginTop: 20px">
        <el-button size="small" @click="closeEditContent">取消</el-button>
        <el-button size="small" type="primary" @click="confirmDialog">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
  import api from "api";
  import Sortable from 'sortablejs';
  import { AppWebsite } from "config";

  export default {
    name: 'shopDialog',
    props:[ "shopItemData", "topic", "content", "visible", "bindCsuOrProductGroup", "hideDisplayGoods" ],
    data() {
      return {
        popoverValue: '',
        sortable: null,
        selectLoading: false,
        options: [],
        editData: {
          groupShopsName: '',
          crowdType: 1,
          crowdValue: '',
          crowdId: '',
          shopsList: [],
        },
        pickerOptions: {
          shortcuts: [
            {
              text: "未来一周",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来三个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来六个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一年",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                picker.$emit("pick", [start, end]);
              }
            }
          ]
        },
      };
    },
    mounted () { 
      this.rowDrop();
      if(this.isEdit) {
        this.editData = this.shopItemData;
      }
    },
    computed: {
      isEdit() {
        return Object.keys(this.shopItemData).length > 0 ? true : false;
      }
    },
    methods: {
      // changeCrowdValue(e) {
      //   if (!e) {
      //     this.editData.crowdId = '';
      //   }
      //   this.$forceUpdate();
      // },
      rowDrop() {
        const _this = this;
        const tbody = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[1];
        Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
          ghostClass: "sortable-ghost",
          onEnd: evt => {
            const currRow = ((_this.editData || {}).shopsList || []).splice(evt.oldIndex, 1)[0];
            ((_this.editData || {}).shopsList || []).splice(evt.newIndex, 0, currRow);
          }
        });
      },
      async optionFilter(val) {
        this.selectLoading = true;
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          this.selectLoading = false;
          this.options = [{
            label: data.name,
            value: val,
          }]
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCrowd(e) {
        if (e) {
          this.editData.crowdId = Number((this.options[0].value).trim());
          this.editData.crowdValue = this.options[0].label;
        } else {
          this.editData.crowdId = '';
          this.editData.crowdValue = '';
        }
        this.$forceUpdate();
      },
      // async querySearchCrowd(queryString, cb) {
      //   const pms = {
      //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
      //     dataType: "json",
      //     data: {},
      //     head: {
      //       "Content-Type": "application/json;charset=UTF-8"
      //     }
      //   };
      //   const res = await api.proxy.post(pms);
      //   if (res.success) {
      //     const { data } = res;
      //     cb([{
      //       id: queryString,
      //       value: data.name || ""
      //     }]);
      //     return false;
      //   }
      // },
      // handleSelectCrowd(item) {
      //   this.editData.crowdId = item.id;
      // },
      format_text(data) {
        if (!data) {
          return "<b style='color: red'>请设置时间</b>";
        }
        if (!data.length) {
          return "<b style='color: red'>请设置时间</b>";
        }
        const _date = new Date().getTime();
        const start = new Date(data[0]).getTime();
        const end = new Date(data[1]).getTime();
        if (_date <= end && _date >= start) {
          return "<b style='color: #67C23A'>展示中</b>";
        } else if (_date < start) {
          return `<b style='color: #000000'>即将在${new Date(
            start
          ).toLocaleDateString()}展示</b>`;
        } else {
          return "<b style='color: #000000'>已过期</b>";
        }
      },
      changeInput() {
        this.$forceUpdate();
      },
      changeCrowdType() {
        this.editData.crowdId = '';
        this.editData.crowdValue = '';
      },
      showCsuId(value) {
        this.popoverValue = value || '';
      },
      hidePopover() {
        document.body.click();
      },
      async bindCsuOrProductGroupFn(row) {
        if (this.popoverValue) {
          const params = {
            type: 2,
            exhibitionId: this.popoverValue,
            csuIds: [],
          }
          const result = await api.topic.checkBindCsuOrProductGroup(params);
          if (((result.data || {}).data || {}).checkResult) {
            this.editData.shopsList.forEach((val, index) => {
              if (val.shopCode === row.shopCode) {
                this.$set(row, 'productGroupId', this.popoverValue)
              }
            });
            this.hidePopover();
          } else {
            this.$message.error((result.data || {}).msg);
          }
        } else {
          this.editData.shopsList.forEach((val, index) => {
            if (val.shopCode === row.shopCode) {
              this.$set(row, 'productGroupId', '')
            }
          });
          this.hidePopover();
        }
      },
      async submitCsuId(index, row) {
        if (this.bindCsuOrProductGroup) {
          this.bindCsuOrProductGroupFn(row);
          return false;
        }
        if(this.popoverValue) {
          let res = await api.topic.checkBindShopCsu({ "shopCode": row.shopCode, "csuId": this.popoverValue || '' });
          console.log('res', res);
          if (((res.data || {}).data || {}).checkResult) {
            this.editData.shopsList.forEach((val, index) => {
              if (val.shopCode === row.shopCode) {
                this.$set(row, 'csuId', this.popoverValue)
              }
            });
            this.hidePopover();
          }
          else { this.$message.error((res.data || {}).msg); }
        } else {
          this.editData.shopsList.forEach((val, index) => {
            if (val.shopCode === row.shopCode) {
              this.$set(row, 'csuId', '')
            }
          });
          this.hidePopover();
        }
      },
      handle_store(data) {
        if (data.type === "add") {
          this.editData.shopsList.push(Object.assign(data.data, { timevalue: null, customShopName: data.data.showName }));
        } else {
          let cur_index = 0;
          this.editData.shopsList.forEach((val, index) => {
            if (val.shopCode === data.data.shopCode) {
              cur_index = index;
            }
          });
          this.editData.shopsList.splice(cur_index, 1);
        }
      },
      del_store(shopCode) {
        const text = `是否取消该关联`;
        this.$confirm(text, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "info"
        }).then(() => {
          let cur_index = 0;
          this.editData.shopsList.forEach((val, index) => {
            if (val.shopCode === shopCode) {
              cur_index = index;
            }
          });
          this.editData.shopsList.splice(cur_index, 1);
          this.$message({
            type: "success",
            message: "成功!"
          });
        })
        .catch(() => {});
      },
      confirmDialog() {
        if (this.editData.crowdType === 2 && !this.editData.crowdId) {
          this.$message.error('请选择正确的人群');
          return;
        }
        let hasEmpty = false;
        this.editData.shopsList.forEach((item) => {
          if(!item.customShopName || !item.timevalue) {
            this.$message.error('请检查店铺名称/有效时间是否填写完整');
            hasEmpty = true;
          }
        })
        if(!hasEmpty) {
          this.closeEditContent();
          this.$emit('confirmEditContent', this.editData)
        }
      },
      closeEditContent() {
        this.$parent.closeEditContent();
      },
    }
  };
 
</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .banner-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }
    .image {
      width: 64px;
      height: 64px;
    }
  }
  .title-image {
    width: 64px;
    height: 64px;
  }
  .no-img {
    font-size: 35px;
    display: block;
    color: #caccd0;
  }
  .priorityCheck {
    display: flex;
    justify-content: space-around;
    .el-input {
      margin-right: 10px;
    }
  }
</style>