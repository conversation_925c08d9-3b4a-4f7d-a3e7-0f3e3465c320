<template>
  <div class="all-link">
    <el-tabs v-model="selectedConfigTab" type="border-card" style="box-shadow:none">
      <el-tab-pane
        v-for="item in tabsUsed"
        :key="item.value"
        :label="item.label"
        :name="item.value"
      >
        <keep-alive>
          <component
            v-if="selectedConfigTab === item.value"
            :is_close="is_close"
            :is="item.value"
            @select="onSelect"
            :params="item.params || {}"
            :from="params.from || 'app'"
            :page_type="params.page_type || ''"
          ></component>
        </keep-alive>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import page from "./modules/page";
import goods from "./modules/productlink";
import goodsGroup from "./modules/goodsGroup";
import goodsGroupWithLimit from "./modules/goodsGroupWithLimit";
import importGoods from "./modules/importGoods";

export default {
  props: {
    defaultSelected: String,
    /**
     * @param page {@link Object}：活动页参数；
     * @param goods {@link Object}：商品参数；
     * @param goodsGroup {@link Object}：商品组参数；
     * @param importGoods {@link Object}：导入商品参数；
     * 具体的内部参数，查阅各对应的（选项卡）子组件注释。
     */
    params: {
      type: Object,
      default() {
        return {};
      }
    },
    tabs: {
      type: Array,
      default() {
        return [
          {
            label: "活动页",
            value: "page",
            params: this.params.page
          },
          {
            label: "商品组",
            value: "goodsGroup",
            params: this.params.goodsGroup
          },
          {
            label: "商品组",
            value: "goodsGroupWithLimit",
            params: this.params.goodsGroupWithLimit
          },
          {
            label: "商品",
            value: "productlink",
            params: this.params.goods || this.params.productlink
          },
          {
            label: "导入商品",
            value: "importGoods",
            params: this.params.importGoods
          }
        ];
      }
    }
  },
  data() {
    return {
      selectedConfigTab: null,
      is_close: false
    };
  },
  computed: {
    tabsUsed() {
      for (let i = 0, len = this.tabs.length; i < len; i++) {
        let tab = this.tabs[i];
        tab.params = _.defaultsDeep(
          tab.params || {},
          this.params[tab.value] || {},
          {
            branchCode: this.params.branchCode || ""
          }
        ); //为自定义标签页，添加"params"参数
      }
      return this.tabs;
    }
  },
  methods: {
    changeTab(new_val) {
      this.selectedConfigTab = new_val;
    },
    onSelect(item) {
      this.$emit("select", item);
    },
    close_select() {
      this.is_close = !this.is_close;
    }
  },
  mounted() {
    this.selectedConfigTab = _.get(this.tabsUsed, "0.value");
  },
  components: {
    page,
    goodsGroup,
    goodsGroupWithLimit,
    importGoods,
    productlink: goods
  }
};
</script>
<style lang="scss" scoped rel="stylesheet/scss">
.el-tabs {
  margin-top: 0;
  padding-top: 0;
}
</style>
