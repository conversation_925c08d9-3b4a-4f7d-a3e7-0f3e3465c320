<template>
  <div class="breadcrumb-wrap" v-show="(pathList.length && currentAccount.parkId == 0) || (pathList.length && currentAccount.parkId > 0 && $route.name !== 'parkInfo')">
    <div class="title">{{title}}</div>
    <div class="breadcrumb-content" v-if="pathList.length > 1">
      <i class="icon-back iconfont icon-circle-left" @click="back"></i>
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: path.path }" :key="index" v-for="(path,index) in pathList">
         {{path.subTitle | subTitle($route.name)}}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'Breadcrumb',
    data() {
      return {
        title: ''
      }
    },
    watch: {
      pathList() {
        if (this.pathList.length) {
          this.title = this.pathList[ this.pathList.length - 1 ].title;
        }
      }
    },
    filters:{
      subTitle(title, name){
        //if(name == 'parkService'){
        // if(title == '园区管理'){
        //   return '服务管理'
        // }
        //  if(title == '园区详情'){
        //    return '服务详情'
        //  }
        //}
        return title;
      }
    },
    computed: {
      currentAccount() {
        return this.$store.getters[ 'sys/currentAccount' ]
      },
      pathList() {
        const pathList = this.$store.getters[ 'breadcrumb/pathList' ];
        if (!pathList.length && localStorage.getItem('current_path')) {
          this.$store.dispatch('breadcrumb/setPathList', JSON.parse(localStorage.getItem('current_path')));
        }
        return pathList;
      }
    },
    methods: {
      back(){
        const lastPath = this.pathList[this.pathList.length - 1];
        this.$store.dispatch('breadcrumb/removePath', {
          action: lastPath.action,
        });
        if(lastPath.action == 'parkInfo'){
          this.$router.replace({name: 'parkList'})
        }else{
          this.$router.go(-1)
        }
      },
    },
  }
</script>

<style lang="scss" rel="stylesheet/scss">


  .breadcrumb-wrap {
    border-bottom: 1px solid $extra-black;
    padding-left: 20px;
    padding-right: 20px;
    .title {
      padding-top: 20px;
      padding-bottom: 15px;
      font-size: 20px;
    }
    .el-breadcrumb {
      font-size: 13px;
      .el-breadcrumb__item {
        &:first-child {
          .el-breadcrumb__inner {
            color: colors(red);
          }
          .el-breadcrumb__separator {
            font-weight: bold;
          }
        }
        .el-breadcrumb__inner, .el-breadcrumb__inner a {
          font-weight: 400;
        }
      }
    }
    .breadcrumb-content {
      display: flex;
      align-items: center;
      padding-bottom: 15px;
      .icon-back {
        position: relative;
        top: -2px;
        margin-right: 8px;
        cursor: pointer;
        font-size: 18px;
      }
    }
  }
</style>
