<template>
  <div class="topic-menu-list">
    <!--模块时间设置-->
    <el-row :gutter="20">
      <div class="title">生效时间设置</div>
      <el-col :span="24">
        <div class="block">
          <el-date-picker
            v-model="content.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="left"
          ></el-date-picker>
        </div>
      </el-col>
    </el-row>

    <!--模块颜色设置-->
    <el-row :gutter="20">
      <div class="title">标签和店铺颜色</div>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">标签背景色</span>
          <el-color-picker v-model="content.tag_bgColor" size="mini"></el-color-picker>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">标签字体色</span>
          <el-color-picker v-model="content.tag_fontColor" size="mini"></el-color-picker>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">店铺名称颜色</span>
          <el-color-picker v-model="content.shopName_fontColor" size="mini"></el-color-picker>
        </div>
      </el-col>
    </el-row>

    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">期望关联的密钥</div>
      <el-col :span="24">
        <div class="block">
          <el-input placeholder="输入期望关联的密钥" v-model="content.pass_word">
            <template slot="prepend">期望关联的密钥</template>
          </el-input>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">展示个数设置</div>
      <el-col :span="24">
        <el-radio v-model="content.goods_num" label="3">一页3个</el-radio>
        <el-radio v-model="content.goods_num" label="6">一页6个</el-radio>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">最大展示页数</div>
      <el-col :span="24">
        <el-select v-model="content.pageCount" placeholder="请选择">
          <el-option
            v-for="item in pageCount_option"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>

    <!--添加快捷入口-->
    <el-row :gutter="20">
      <div class="title">关联店铺</div>
      <el-col :span="8">
        <div class="block">
          <el-button class="btn-block" type="primary" @click="add_editContent = true">关联店铺</el-button>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="
              () => {
                loading = true;
                return true;
              }
            "
            :on-success="onUploadImg"
          >
            <el-button class="btn-block" type="primary" :loading="loading">替换背景图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <el-button class="btn-block" type="primary" @click="content.bgRes = ''">还原背景图</el-button>
        </div>
      </el-col>
    </el-row>
    <br />

    <!--添加快捷入口-->
    <el-row :gutter="20">
      <div class="title">预览关联店铺</div>
      <el-col :span="24">
        <el-table :data="content.list" size="mini" :row-key="getRowKeys">
          <el-table-column label="店铺编码">
            <template slot-scope="scope">
              <span>{{ scope.row.shopCode }}</span>
            </template>
          </el-table-column>

          <el-table-column label="名称">
            <template slot-scope="scope">
              <span>{{ scope.row.showName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="店铺状态">
            <template slot-scope="scope">
              <b v-if="scope.row.status === 2" style="color: #67C23A">已上线</b>
              <b v-if="scope.row.status === 1" style="color: red">待上线</b>
              <b v-if="scope.row.status === 3" style="color: red">已下线</b>
              <b v-if="scope.row.status === 4" style="color: red">已关闭</b>
            </template>
          </el-table-column>

          <el-table-column label="店铺logo" width="80">
            <template slot-scope="scope">
              <img
                v-if="scope.row.appLogoUrl"
                :src="scope.row.appLogoUrl"
                alt="图"
                class="title-image"
              />
              <i v-else class="el-icon-circle-plus-outline no-img"></i>
            </template>
          </el-table-column>

          <el-table-column label="店铺标签">
            <template slot-scope="scope">
              <span>{{ scope.row.shopTags ? scope.row.shopTags : "" }}</span>
            </template>
          </el-table-column>

          <el-table-column label="链接">
            <template slot-scope="scope">
              <span>{{ scope.row.appLink }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <br />
    <!-- 添加内容弹层 -->
    <el-dialog class="banner-dialog" width="70%" title="关联店铺" :visible.sync="add_editContent">
      <allStore
        @emitStore="handle_store"
        :stores="content.list"
        :params="{
          branchCode: topic.branchCode,
          page_type: topic.page_type === 'h5' ? `control-${topic.page_type}` : topic.page_type,
          content: content
        }"
      ></allStore>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="closeEditContent">关闭</el-button>
      </div>
    </el-dialog>

    <br />
    <el-row :gutter="20">
      <div v-if="content.goods_group.length">
        <div class="title" style="margin-bottom: 0">预览已选商品组</div>
        <el-col :span="24">
          <el-table :data="content.goods_group" height="120" ref="multipleTable">
            <el-table-column prop="name" label="组名"></el-table-column>
            <el-table-column prop="code" label="编号"></el-table-column>
            <el-table-column prop="branchCode" label="区域号"></el-table-column>
          </el-table>
        </el-col>
      </div>
    </el-row>

    <!--选择商品-->
    <all-link
      @select="onSetLink"
      :tabs="tabs"
      :params="{
        goodsGroup: {
          seledShow: false,
          minSel: 1,
          search: {
            state: 1,
            branchCode: topic.branchCode
          }
        }
      }"
    ></all-link>
  </div>
</template>

<script>
import base from "views/apps/topic-prop-components/base.vue";
import api from "api";
export default {
  extends: base,
  contentDefault: {
    pageCount: "8",
    goods_num: "3",
    list: [],
    goods_group: [],
    bgRes: "",
    pass_word: "",
    timevalue: [],
    tag_bgColor: "#00B377",
    tag_fontColor: "#ffffff",
    shopName_fontColor: "#ffffff",
    shopType: 1
  },
  created() {
    this.content.pageCount = this.content.pageCount
      ? this.content.pageCount
      : "8";
    // if (this.content.shopType !== 2) {
    //   this.pull_stores();
    //   return false;
    // }
    // this.pull_shops();
  },
  computed: {
    list() {
      let list = _.get(this, "content.list");
      if (list) {
        this.$nextTick(function() {
          this.setSort();
        });
        return list;
      } else {
        return [];
      }
    }
  },
  data() {
    return {
      add_editContent: false,
      loading: false,
      addDialog: false,
      tabs: [{ label: "商品组", value: "goodsGroup" }],
      pickerOptions: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      pageCount_option: [
        {
          value: "4",
          label: "4页"
        },
        {
          value: "5",
          label: "5页"
        },
        {
          value: "6",
          label: "6页"
        },
        {
          value: "7",
          label: "7页"
        },
        {
          value: "8",
          label: "8页"
        }
      ]
    };
  },
  methods: {
    async pull_shops() {
      //请求数据的参数
      const params = this.list.map(item => {
        return item.shopCode;
      });
      const obj = {
        branchCode: this.topic.branchCode,
        shopCodes: params
      };
      const result = await api.stores.pullShops(obj);
      if (result.data && result.data.status === "success") {
        this.$nextTick(() => {
          this.content.list = result.data.data.shopInfos;
        });
      } else {
        // this.$message.error("业务接口错误");
      }
    },
    async pull_stores() {
      //请求数据的参数
      const params = this.list.map(item => {
        return item.shopCode;
      });
      const result = await api.stores.pullStores(params);
      if (result.data && result.data.status === "success") {
        this.$nextTick(() => {
          this.content.list = result.data.data.shopInfos;
        });
      } else {
        // this.$message.error("业务接口错误");
      }
    },
    onSetLink(link) {
      let obj = {};
      obj.name = link.data.name;
      obj.branchCode = link.data.branchCode;
      obj.code = link.data.code;
      this.content.goods_group.splice(0, 1, obj);
    },
    handle_store(data) {
      if (data.type === "add") {
        this.$set(this.content.list, 0, data.data);
      } else {
        this.content.list = [];
      }
    },
    closeEditContent() {
      this.add_editContent = false;
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.bgRes = res.data.url;
    }
  }
};
</script>

<style scoped lang="scss">
.title-image {
  width: 64px;
  height: 64px;
}
.topic-image-upload {
  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 200px;
    height: 200px;
    line-height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    font-size: 50px;
  }
}

.topic-image-upload .el-upload {
  width: 100%;
}

.el-row {
  text-align: center;

  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
</style>
