<template>
  <div class="discount_recomment_alert">
    <el-dialog
      class="banner-dialog"
      :title="`${isEdit ? '编辑' : '超值推荐楼层新建'}`"
      :before-close="addDialogCancel"
      :visible.sync="addDialog"
    >
      <el-form
        label-position="right"
        ref="addRuleForm"
        :model="addForm"
        :rules="addRules"
        size="small"
         :disabled="isInfo"
        label-width="100px"
        label-suffix="："
      >
        <el-form-item label="活动名称" prop="activityName">
          <el-input
            v-model="addForm.activityName"
            maxlength="20"
            size="mini"
            placeholder="请输入热词组名称，20个字符以内"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="人群范围" prop="crowdType">
          <el-radio-group v-model="addForm.crowdType" @change="changeCrowdType">
            <el-radio :label="1">该页面已选中人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="addForm.crowdType === 2"
          prop="crowdValue"
          label="指定人群"
        >
          <el-select
            v-model.trim="addForm.crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="展示时间"
          :prop="addForm.timeType == 1 ? 'validityTime' : 'circulateTime'"
        >
          <el-radio v-model="addForm.timeType" :label="1">固定时段</el-radio>
          <el-date-picker
            v-if="addForm.timeType == 1"
            v-model="addForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :picker-options="{
              disabledDate: (time) => {
                const times =
                  new Date(new Date().toLocaleDateString()).getTime() +
                  1095 * 8.64e7 -
                  1;
                return (
                  time.getTime() < Date.now() - 8.64e7 || time.getTime() > times
                ); // 如果没有后面的-8.64e7就是不可以选择今天的
              },
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker
          ><br />
          <el-radio v-model="addForm.timeType" :label="2">周期循环</el-radio>
          <el-button
            style="margintop: 10px"
            @click="toloopcirculateTime"
            type="primary"
            size="mini"
            >配置</el-button
          >
          <br>
          <div v-for="(item,index) in addForm.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[addForm.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{addForm.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
        </el-form-item>
        <el-form-item prop="bgImg" label="背景图">
        <div class="add-color-back">
            <el-upload
              class="upload-demo"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :show-file-list="false"
              :before-upload="
                () => {
                  loading = true;
                  return true;
                }
              "
              :on-success="onUploadImg"
            >
              <el-button size="small" type="primary" 
                >上传背景图</el-button>
              <img v-if="addForm.bgImg" :src="addForm.bgImg" alt="" style="width: 100px;height: 100px;" />
            </el-upload>
            <el-button @click="imgOnclick">清除背景图</el-button>
       
        </div>
       
  </el-form-item>
        <el-form-item label="楼层图标" prop="floorIcon">
          <div class="add-color-back">
            <el-upload
              class="upload-demo"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :before-upload="
                () => {
                  loading = true;
                  return true;
                }
              "
              :on-success="(e) => UploadTopSearchBg(e, 'floorIcon')"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <img v-if="addForm.floorIcon" :src="addForm.floorIcon" alt="" style="width: 100px;height: 100px;" />
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addForm.floorIcon = ''"
              >清空重置</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="主标题" prop="mainTitleText">
          <el-radio-group v-model="addForm.mainTitleType">
            <el-radio :label="1">纯色</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group>
          <el-input v-model="addForm.mainTitleText" v-if="addForm.mainTitleType == 1"></el-input>
          <div class="add-color-item" v-if="addForm.mainTitleType == 1">
            <span class="demonstration">点击设置纯色</span>
            <div>
              <el-color-picker
                v-model="addForm.mainTitleColor"
                size="mini"
              ></el-color-picker>
            </div>
            <!-- <span class="demonstration">激活颜色</span>
            <div>
              <el-color-picker
                v-model="addForm.mainTitleCheckColor"
                size="mini"
              ></el-color-picker>
            </div> -->
            <div class="block" style="width: 200px">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider
                  v-model="addForm.mainTitleColorTransparency"
                  :format-tooltip="formatTooltip"
                ></el-slider>
              </div>
            </div>
             <!-- <div class="block" style="width: 200px">
              <span class="demonstration">激活透明度：</span>
              <div>
                <el-slider
                  v-model="addForm.mainTitleCheckColorTransparency"
                  :format-tooltip="formatTooltip"
                ></el-slider>
              </div>
            </div> -->
          </div>
          
          <div class="add-color-back" v-else>
            <el-upload
              class="upload-demo"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :before-upload="
                () => {
                  loading = true;
                  return true;
                }
              "
              :on-success="(e) => UploadBackTopSearchBg(e, 'mainTitle')"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <img
              style="width: 100px;height: 100px;"
                v-if="addForm.mainTitleType == 2 && addForm.mainTitle"
                :src="addForm.mainTitle"
                alt=""
              />
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addForm.mainTitle = ''"
              >清空重置</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="副标题" prop="subTitleText">
          <el-radio-group v-model="addForm.subTitleType">
            <el-radio :label="1">纯色</el-radio>
            <el-radio :label="2">图片</el-radio>
          </el-radio-group>
          <el-input v-model="addForm.subTitleText" v-if="addForm.subTitleType == 1"></el-input>
          <div class="add-color-item" v-if="addForm.subTitleType == 1">
            <span class="demonstration">点击设置纯色</span>
            <div>
              <el-color-picker
                v-model="addForm.subTitleColor"
                size="mini"
              ></el-color-picker>
            </div>
            <!-- <span class="demonstration">激活颜色</span>
            <div>
              <el-color-picker
                v-model="addForm.subTitleCheckColor"
                size="mini"
              ></el-color-picker>
            </div> -->
            <div class="block" style="width: 200px">
              <span class="demonstration">透明度设置：</span>
              <div>
                <el-slider
                  v-model="addForm.subTitleColorTransparency"
                  :format-tooltip="formatTooltip"
                ></el-slider>
              </div>
            </div>
            <!-- <div class="block" style="width: 200px">
              <span class="demonstration">激活透明度：</span>
              <div>
                <el-slider
                  v-model="addForm.subTitleCheckolorTransparency"
                  :format-tooltip="formatTooltip"
                ></el-slider>
              </div>
            </div> -->
          </div>
          <div class="add-color-back" v-else>
            <el-upload
              class="upload-demo"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :before-upload="
                () => {
                  loading = true;
                  return true;
                }
              "
              :on-success="(e) => UploadBackTopSearchBg(e, 'subTitle')"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <img
              style="width: 100px;height: 100px;"
                v-if="addForm.subTitleType == 2 && addForm.subTitle"
                :src="addForm.subTitle"
                alt=""
              />
              <!-- <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div> -->
            </el-upload>
            <el-button size="small" @click="addForm.subTitle = ''"
              >清空重置</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="选品方式" prop="selectProductType">
          <el-select
            size="small"
            @change="changeProductsType"
            v-model="addForm.selectProductType"
            placeholder="请选择"
          >
            <!-- <el-option label="指定商品" value="appointProduct" /> -->
            <el-option label="指定商品组" value="appointProductGroup" />
            <el-option label="系统自动" value="systemAuto" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="商品组ID"
          v-if="addForm.selectProductType === 'appointProductGroup'"
          prop="selectProductGroupId"
        >
          <el-input
            style="width: 200px"
            size="small"
            placeholder="请输入内容"
            v-model="addForm.selectProductGroupId"
          />
        </el-form-item>
        <el-form-item label="转跳链接" prop="hrefUrl">
          <el-input v-model="addForm.hrefUrl" size="mini" clearable></el-input>
          <el-button
            type="primary"
            size="small"
            @click="isShowHrefDialog = true"
            >more</el-button
          >
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addDialogConfirm" v-if="!isInfo"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <loopcirculateTime
      ref="loopcirculateTime"
      @loopcirculateTimeBack="loopcirculateTimeBack"
    ></loopcirculateTime>
    <el-dialog
      title="跳转链接配置"
      :visible.sync="isShowHrefDialog"
      width="30%"
      append-to-body
    >
      <page-link
        @select="onSetLink"
        :params="{ branchCode: topic.branchCode }"
      ></page-link>
      <span slot="footer" class="dialog-footer">
        <el-button @click="hrefCancel">取 消</el-button>
        <el-button type="primary" @click="hrefConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import loopcirculateTime from "../../../../components/loopcirculateTime.vue";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
export default {
  components: { loopcirculateTime },
  props: { topic: Object ,isInfo:Boolean},
  data() {
    return {
      addRules: {
        activityName: [
          { required: true, message: "请填写活动名称", trigger: "blur" },
          { min: 1, max: 20, message: "长度在1 - 20之间", trigger: "blur" },
        ],
        crowdType: [
          { required: true, message: "请选择指定人群", trigger: "change" },
        ],
        crowdValue: [
          { required: true, message: "请填写人群名称", trigger: "blur" },
        ],
        floorIcon: [
          { required: true, message: "请选择楼层图标", trigger: "change" },
        ],
        // mainTitleText: [
        //   { required: true, message: "请选择主标题", trigger: "change" },
        // ],
        // subTitleText: [
        //   { required: true, message: "请选择副标题", trigger: "change" },
        // ],
        hrefUrl: [
          { required: true, message: "请填写转跳链接", trigger: "blur" },
        ],
        bgImg:[
          {required:true,message:'请选择背景图片',trigger:'change'}
        ],
        // validityTime: [
        //   { required: true, message: "展示时间不能为空", trigger: "change" },
        // ],
        // circulateTime: [
        //   { required: true, message: "展示时间不能为空", trigger: "change" },
        // ],
        selectProductType: [
          { required: true, message: "请选择选品方式", trigger: "change" },
        ],
        selectProductGroupId: [
          { required: true, message: "请输入商品组id", trigger: "blur" },
        ],
      },
      isEdit: false,
      addForm: {
        crowdType: 1, //人群switch
        activityId: "", //活动id
        activityName: "", //活动名称
        crowdValue: "", // 人群
        floorIcon: "",
        mainTitleType: 1,
        mainTitle: "",
        mainTitleColor: "#eee",
        mainTitleText:"",
        mainTitleCheckColor: "#eee",
        mainTitleColorTransparency: 100,
        mainTitleCheckColorTransparency: 100,
        subTitleType: 1,
        subTitle: "",
        subTitleText:"",
        subTitleColor: "#eee",
        subTitleCheckColor: "#eee",
        subTitleColorTransparency: 100,
        subTitleCheckolorTransparency: 100, 
        timeType: 1,
        validityTime: "", //有效期
        circulateTime: "",
        status: 1,
        selectProductGroupId: "",
        selectProductType: "",
        hrefUrl: "",
        bgImg:''
      },
      addDialog: false,
      options: [],
      addFormSelectLink: "",
      isShowHrefDialog: false,
      selectLoading: false,
    };
  },
  methods: {
    imgOnclick() {
      this.addForm.bgImg = null;
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.$set(this.addForm, "bgImg", res.data.url);
      this.$refs["addRuleForm"].clearValidate();
    },
    onSelect(val) {
      if (val) {
        this.addForm.bgRes = val;
      } else {
        this.addForm.bgRes = "#ffffff";
      }
    },
    UploadBackTopSearchBg(e,k){
      this.addForm[k]=e.data.url
    },
    open(row, isEdit) {
      this.isEdit = isEdit;
      if (this.isEdit) {
        let keys = Object.keys(row);
        for (let index = 0; index < keys.length; index++) {
          const key = keys[index];
          this.addForm[key] = row[key];
        }
      }

      this.addDialog = true;
      this.$nextTick(()=>{
        this.$refs['addRuleForm'].clearValidate()
      })
    },
    
    hrefCancel() {
      this.addFormSelectLink = "";
      this.isShowHrefDialog = false;
    },
    
    addDialogCancel() {
      this.resetAddForm();
      this.$refs['addRuleForm'].clearValidate()
      this.addDialog = false;
    },

    async addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        if (this.addForm.timeType==2&&(!this.addForm.circulateTime||Object.keys(this.addForm.circulateTime).length==0||!this.addForm.circulateTime.circulateList||this.addForm.circulateTime.circulateList.length==0)) {
          this.$message.warning("请添加[周期循环] 时间段。");
          return false;
        }
        if(this.addForm.timeType==1&&(!this.addForm.validityTime||this.addForm.validityTime.length==0)){
          this.$message.warning("请添加时段");
          return false;
        }
        if(this.addForm.mainTitleType==2&&!this.addForm.mainTitle){
          this.$message.warning("请添加主标题图片");
          return false;
        }
        if(this.addForm.mainTitleType==1&&!this.addForm.mainTitleText){
          this.$message.warning("请添加主标题");
          return false;
        }
        if(this.addForm.subTitleType==1&&!this.addForm.subTitleText){
          this.$message.warning("请添加副标题");
          return false;
        }
        if(this.addForm.subTitleType==2&&!this.addForm.subTitle){
          this.$message.warning("请添加副标题图片");
          return false;
        }
         if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.addForm.hrefUrl)) {
          this.$message.error("跳转链接格式不正确，请检查");
          return false;
        }
        let linkErrMsg = '';
         
            let linkPageUrl = getUrlParam(this.addForm.hrefUrl, 'url')
             const loading = this.$loading({
                lock: true,
                text: '校验中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
            const result = await api.topic.checkPageUrl({ url: linkPageUrl });
             loading.close()
            if (((result || {}).data || {}).status != 200) {
              linkErrMsg = '跳转链接不存在，请检查';
            }
        
        if (linkErrMsg) {
          this.$message.error(linkErrMsg);
          return false;
        }

        this.$emit("done", this.addForm);
        // this.addDialogCancel();
      });
    },
    resetAddForm() {
      this.addForm = {
        validityTime: "", //有效期
        crowdType: 1, //人群switch
        activityId: "", //活动id
        activityName: "", //活动名称
        crowdValue: "", // 人群
        floorIcon: "",
        mainTitleType: 1,
        mainTitle: "",
        mainTitleColor: "#eee",
        mainTitleCheckColor: "#eee",
        mainTitleColorTransparency: 100,
        mainTitleCheckColorTransparency: 100,
        subTitleType: 1,
        subTitle: "",
        subTitleColor: "#eee",
        subTitleCheckColor: "#eee",
        subTitleColorTransparency: 100,
        subTitleCheckolorTransparency: 100, 
        timeType: 1,
        circulateTime: "",
        status: 1,
        selectProductGroupId: "",
        selectProductType: "",
        hrefUrl: "",
        bgImg:'',
        mainTitleText:"",
        subTitleText: "",
      };
    },
    changeCrowdType() {
      this.addForm.crowdId = "";
      this.addForm.crowdValue = "";
    },
    selectCrowd(e) {
      if (e) {
        this.addForm.crowdId = Number(this.options[0].value.trim());
        this.addForm.crowdValue = this.options[0].label;
      } else {
        this.addForm.crowdId = "";
        this.addForm.crowdValue = "";
      }
      this.$forceUpdate();
    },
    changeProductsType() {
      this.addForm.selectProductGroupId = "";
      // this.productGroupId = '';
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    //打开时间循环
    toloopcirculateTime() {
      this.$refs.loopcirculateTime.circulateTime=this.addForm.circulateTime
      this.$refs.loopcirculateTime.editInit()
      this.$refs.loopcirculateTime.showVisible = true;
    },
    //循环时间回调
    loopcirculateTimeBack(data) {
      this.$set(this.addForm, "circulateTime", data);
    },
    // 设置轮播链接
    onSetLink(link) {
      this.addFormSelectLink = link.meta.page_url;
    },
    hrefConfirm() {
      this.addForm.hrefUrl = this.addFormSelectLink;
      this.$refs["addRuleForm"].clearValidate("hrefUrl");
      this.isShowHrefDialog = false;
    },

    // 上传banner对应的头部区域背景图片
    async UploadTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.addForm, type, res.data.url);
      this.$refs["addRuleForm"].clearValidate();
    },
    formatTooltip(val) {
      return val / 100;
    },
  },
};
</script>

<style scoped>
</style>