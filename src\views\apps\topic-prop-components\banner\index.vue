<template>
  <div class="recommend-list-wrap">
    <!-- <div class="block" style="margin: 10px 0">
      <el-row :gutter="20">
        <div class="title">模块有效时间设置</div>
        <el-col :span="24"
          ><el-date-picker
          style="margin-left:30px"
            v-model="content.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions2"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          >
          </el-date-picker
        ></el-col>
      </el-row>
    </div> -->
    <el-row :gutter="20"  style="margin-bottom: 10px;" class="brand-time">
      <div class="title">标题设置</div>
      <el-col :span="24">
        <el-input
          size="small"
          v-model="content.title"
          placeholder="请输入标题"
          @input="()=>{
            if(content.title.length>10){
              this.$message.error('标题最大允许输入10个汉字')
              content.title=content.title.substring(0, 10)
            }
          }"
        />
      </el-col>
    </el-row>
    <div class="bg-img">
      <el-container style="height: auto; border: 1px solid #eee">
        <el-header
          height="50px"
          style="
            background-color: rgb(19, 194, 194);
            text-align: center;
            padding-top: 15px;
            color: #fff;
          "
        >
          <span class="demonstration">背景图</span>
          <span style="margin-left: 50px"></span>
          <a @click="imgOnclick" style="cursor: pointer;color:#fff">清除背景图</a>
        </el-header>
        <el-main>
          <!--<el-upload-->
          <!--class="topic-image-upload"-->
          <!--ref="upload"-->
          <!--accept="image/jpeg,image/jpg,image/png,image/gif"-->
          <!--:show-file-list="false"-->
          <!--:before-upload="() => {loading = true; return true;}"-->
          <!--:on-success="onUploadImg">-->
          <!--<img v-if="content.image" :src="content.image" class="image">-->
          <!--<i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>-->
          <!--</el-upload>-->
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg,image/png,image/gif"
            :show-file-list="false"
            :before-upload="
              () => {
                loading = true;
                return true;
              }
            "
            :on-success="onUploadImg"
          >
            <el-button class="btn-block" type="primary" :loading="loading"
              >上传背景图</el-button
            >
            <div slot="tip" class="el-upload__tip">
              支持类型：png/jpg/jpeg/gif
            </div>
          </el-upload>
        </el-main>
      </el-container>
      <el-form
        class="data-form"
        size="mini"
        label-width="60px"
        label-suffix=" : "
        style="display: inline-block; margin-bottom: 5px; margin-top: 5px"
      >
        <label class="demonstration">背景色:</label>
        <el-color-picker
          v-model="content.color"
          size="mini"
          @active-change="onSelect"
        ></el-color-picker>
        <span class="demonstration">轮播点的颜色:</span>
        <el-color-picker
          v-model="content.rotationpointcolor"
          size="mini"
        ></el-color-picker>
      </el-form>
    </div>
    <el-button @click="toAdd" class="btn-block mb-10" type="primary"
      >上传banner图片</el-button
    >
    <el-table
      :data="list"
      :row-key="getRowKeys"
      size="mini"
      style="width: 100%"
    >
      <el-table-column label="图片">
        <template slot-scope="scope">
          <div class="container">
            <div class="img"><img :src="scope.row.image" /></div>
            <div class="button-list">
              <div class="demonstration">
                开始结束时间:{{ scope.row.timevalue }}
              </div>
              <el-button
                size="mini"
                @click="toEdit(scope.row, scope.$index)"
                type="primary"
                >编辑
              </el-button>
              <el-button size="mini" @click="toRemove(scope.row)" type="danger"
                >删除</el-button
              >
            </div>
          </div>
          <div class="link-desc">{{ scope.row.link | link }}</div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addDialog">
      <el-upload
        class="topic-image-upload"
        ref="upload"
        accept="image/jpeg,image/jpg,image/png,image/gif"
        :show-file-list="false"
        :before-upload="
          () => {
            loading = true;
            return true;
          }
        "
        :on-success="onUploadImage"
      >
        <img v-if="dataForm.image" :src="dataForm.image" class="image" />
        <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>
      <div class="block" style="margin-top: 5px">
        <span class="demonstration">开始结束时间:</span>
        <el-date-picker
          v-model="dataForm.timevalue"
          type="datetimerange"
          :picker-options="pickerOptions2"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        >
        </el-date-picker>
      </div>

      <div class="topic-image-picker">
        <!-- 跳转链接<span>({{dataForm.link | link}})</span>-->
        <el-input
          placeholder="链接地址"
          v-model.trim="dataForm.link.meta.page_url"
        >
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>
      <page-link
        @select="onSetLink"
        :params="{ branchCode: topic.branchCode }"
      ></page-link>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeAddDialog">取 消</el-button>
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import base from "../base";
import api from "api";
import { getUrlParam } from "config";

export default {
  extends: base,
  contentDefault: {
    list: [],
    bgRes: "",
    color: "#ffffff",
    rotationpointcolor: "#ffffff",
    image: "",
  },
  data() {
    return {
      loading: false,
      addDialog: false,
      dataForm: {
        image: "",
        link: {
          meta: {
            page_url: "",
          },
        },
        timevalue: "",
        title:""
      },
      pickerOptions2: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      value4: [new Date(2000, 10, 10, 10, 10), new Date(2000, 10, 11, 10, 10)],
      value5: "",
    };
  },
  computed: {
    list() {
      var list = _.get(this, "content.list");
      if (list) {
        if (list.length > 0 && list[0].link.meta) {
          this.$nextTick(function () {
            this.setSort();
          });
        }
        return list;
      } else {
        return [];
      }
    },
  },
  filters: {
    link(data) {
      if (!data.type) {
        return "";
      }
      return data.meta.page_url;
      // return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '') + (data.desc ? ',' : '') + data.desc;
    },
  },
  methods: {
    closeAddDialog() {
      this.addDialog = false;
    },
    toRemove(data) {
      let _self = this;
      return function () {
        _self.list.splice(_self.list.indexOf(data), 1);
        _self.$message({
          type: "success",
          message: "删除成功!",
        });
      }.confirm(_self)();
    },
    toEdit(data, index) {
      this.currentData = data;
      this.currentIndex = index;
      // this.dataForm = Object.assign({}, data);
      this.dataForm = JSON.parse(JSON.stringify(data));
      this.isEdit = true;
      this.addDialog = true;
    },
    toAdd() {
      this.isEdit = false;
      this.dataForm = {
        image: "",
        link: {
          meta: {
            page_url: "",
          },
        },
      };
      this.addDialog = true;
    },
    onSetLink(link) {
      console.log("???", link);
      this.dataForm.link = link;
    },
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.dataForm.image = res.data.url;
    },
    async confirm() {
      if (!this.dataForm.image) {
        this.$message.warning("请上传图片");
        return false;
      }
      let linkErrMsg = "";
      if (((this.dataForm.link || {}).meta || {}).page_url) {
        if (
          !new RegExp("^ybmpage://commonh5activity.*$").test(
            ((this.dataForm.link || {}).meta || {}).page_url
          )
        ) {
          linkErrMsg = "跳转链接格式不正确，请检查";
        } else {
          let linkPageUrl = getUrlParam(
            ((this.dataForm.link || {}).meta || {}).page_url,
            "url"
          );
          const result = await api.topic.checkPageUrl({ url: linkPageUrl });
          if (((result || {}).data || {}).status != 200) {
            linkErrMsg = "跳转链接不存在，请检查";
          }
        }
      }
      if (linkErrMsg) {
        this.$message.error(linkErrMsg);
        return false;
      }
      this.closeAddDialog();
      if (this.isEdit) {
        this.currentData = Object.assign(this.currentData, this.dataForm);
        this.list.splice(this.currentIndex, 1, this.currentData);
      } else {
        this.list.push(Object.assign({}, this.dataForm));
      }
    },
    onSelect(val) {
      this.content.bgRes = this.toColor16(val);
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.image = res.data.url;
      this.content.bgRes = res.data.url;
    },
    imgOnclick() {
      this.content.bgRes = "";
      (this.content.color = "#ffffff"), (this.content.image = "");
    },
    // setlink(e){
    //     ((this.dataForm.link || {}).meta || {}).page_url = e.target.value
    // },
    toColor16(str) {
      if (/^(rgb|RGB)/.test(str)) {
        var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
        var strHex = "#";
        for (var i = 0; i < aColor.length; i++) {
          var hex = Number(aColor[i]).toString(16);
          if (hex === "0") {
            hex += hex;
          }
          strHex += hex;
        }

        if (strHex.length !== 7) {
          strHex = str;
        }
        return strHex.toUpperCase();
      } else {
        return str;
      }
    },
  },
};
</script>

<style lang="scss" scoped rel="stylesheet/scss">
.recommend-list-wrap {
  .container {
    display: flex;
    align-items: center;
    .img {
      width: 65%;
      img {
        display: block;
        width: 100%;
      }
    }
    .button-list {
      margin-left: 10px;
    }
  }
  .link-desc {
  }
  .topic-image-upload {
    .image {
      display: block;
      width: 100%;
    }
    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }
  .topic-image-picker {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}
.el-row {
  text-align: left;
  margin: 0;
  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
</style>
<style lang="scss" rel="stylesheet/scss">
.topic-banner {
  .banner-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }
  }
}
</style>
