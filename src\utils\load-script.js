/**
 * 异步加载脚本
 * @param  {String}   url      脚本地址
 */
export default function loadScript (url) {
  return new Promise((resolve, reject) => {
    let script = document.createElement('script')
    script.type = 'text/javascript'

    if (script.readyState) { // IE
      script.onreadystatechange = () => {
        if (script.readyState === 'loaded' || script.readyState === 'complete') {
          script.onreadystatechange = null
          resolve()
        }
      }
    } else { // 其他浏览器
      script.onload = () => {
        resolve()
      }
    }
    script.src = url
    document.head.appendChild(script)
  })
}
