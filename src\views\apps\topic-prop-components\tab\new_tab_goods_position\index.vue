
<template>
  <div class="topic-search">
    <!--广告信息配置-->
    <el-row :gutter="20">
      <div class="title">广告信息配置</div>
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="活动id:">
            <el-input
              placeholder="请输入内容"
              v-model="queryParamsTop.activityId"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="活动名称:">
            <el-input
              placeholder="请输入内容"
              v-model="queryParamsTop.activityName"
            >
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="人群id:">
            <el-select
              style="margin-left: 10px"
              v-model.trim="queryParamsTop.crowdValue"
              :loading="topSelectLoading"
              filterable
              :filter-method="topOptionFilter"
              placeholder="请输入人群id"
              clearable
              @clear="topOptions = []"
              @change="topSelectCrowd"
            >
              <el-option
                v-for="item in topOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态:">
            <el-select
              v-model="queryParamsTop.status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="item in status"
                :label="item.name"
                :key="item.name + item.id"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="展示时间:">
            <el-date-picker
              v-model="queryParamsTop.validityTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>

    <div class="three-button">
      <el-button type="primary" @click="searchList" size="mini">查询</el-button>
      <el-button type="primary" @click="resetList" size="mini">重置</el-button>
      <el-button
        type="primary"
        @click="openFeedGoodsAdvertisementAlert"
        size="mini"
        >新建</el-button
      >
    </div>

    <el-table
      :data="topDataList"
      size="mini"
      class="tableBox"
      style="margin-top: 10px"
      ref="tableBoxtop"
      :row-key="(row) => row.activityId"
    >
      <el-table-column
        label="id"
        width="100"
        prop="activityId"
      ></el-table-column>
      <el-table-column label="活动名称" prop="activityName"></el-table-column>
      <el-table-column label="位置" prop="position">
        <template slot-scope="scope">
          {{ {1:"左侧",2:"右侧"}[scope.row.positionType]}}{{ scope.row.position }}
        </template>
      </el-table-column>
      <el-table-column label="人群" show-overflow-tooltip width="150">
        <template slot-scope="scope">
          <p v-if="scope.row.crowdType == 2">
            {{ scope.row.crowdId + "/" + scope.row.crowdValue || "该页面已选人群" }}
          </p>
          <p v-else>该页面已选中人群</p>
        </template>
      </el-table-column>
      <el-table-column label="展示时间" width="300">
        <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
            </div>
            <div v-else> 
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
          <!-- {{ scope.row.validityTime[0] }}-{{ scope.row.validityTime[1] }} -->
   
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <div>
            {{
              ["未开始", "上线", "已结束", "下线"][scope.row.status - 1] || "-"
            }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="170px">
        <template slot-scope="scope">
         <div>
          <el-button
            size="mini"
            @click="toEditAdvertisement(scope.row, scope.$index)"  v-if="scope.row.status == 1 ||scope.row.status == 3 || scope.row.status == 4"
            >编辑
          </el-button>
          <el-button
            size="mini"
            @click="toRemove(scope.row, 'top')" v-if="scope.row.status == 4"
            type="danger"
            >删除</el-button
          >
         </div>
         <div style="margin-top: 10px;">
            <el-button size="mini" @click="online(scope, 'top')" type="danger" v-if="scope.row.status == 4"
            >上线</el-button
          >
          <el-button size="mini" @click="outline(scope, 'top')" type="danger" v-if="scope.row.status == 2"
            >下线</el-button
          >
          <el-button size="mini" v-if="scope.row.status == 2" @click="toEditAdvertisement(scope.row, scope.$index,true)">详情</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <feedGoodsAdvertisementAlert
      style="margin-top: 5px"
      ref="feedGoodsAdvertisementAlert"
      :topic="topic"
      :isInfo="isInfo"
      @done="feedGoodsAdvertisementAlertCallBack"
    ></feedGoodsAdvertisementAlert>

    <!--广告信息配置结束-->
    <!-- 商品信息配置 -->
    <el-row :gutter="20">
      <div class="title">商品信息配置</div>
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="活动id:">
            <el-input
              placeholder="请输入内容"
              v-model="queryParamsBottom.activityId"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="活动名称:">
            <el-input
              placeholder="请输入内容"
              v-model="queryParamsBottom.activityName"
            >
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="人群id:">
            <el-select
              style="margin-left: 10px"
              v-model.trim="queryParamsBottom.crowdValue"
              :loading="bottomSelectLoading"
              filterable
              :filter-method="bottomOptionFilter"
              placeholder="请输入人群id"
              clearable
              @clear="bottomOptions = []"
              @change="bottomSelectCrowd"
            >
              <el-option
                v-for="item in bottomOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态:">
            <el-select
              v-model="queryParamsBottom.status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="item in goodInfoStatus"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="展示时间:">
            <el-date-picker
              v-model="queryParamsBottom.validityTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <div class="three-button">
      <el-button type="primary" @click="goodsInfoSearchList" size="mini"
        >查询</el-button
      >
      <el-button type="primary" @click="goodsInfoResetList" size="mini"
        >重置</el-button
      >
      <el-button type="primary" @click="openfeedGoodsInfoAlert" size="mini"
        >新建</el-button
      >
    </div>
    <el-table
      :data="goodInfoList"
      size="mini"
      class="tableBox"
      style="margin: 0 0 20px"
      ref="tableBox"
    >
      <el-table-column
        label="id"
        width="100"
        prop="activityId"
      ></el-table-column>
      <el-table-column label="活动名称" prop="activityName"></el-table-column>
      <el-table-column label="人群" show-overflow-tooltip width="150">
        <template slot-scope="scope">
          <p v-if="scope.row.crowdType == 2">
            {{ scope.row.crowdId + "/" + scope.row.crowdValue || "该页面已选人群" }}
          </p>
          <p v-else>该页面已选中人群</p>
        </template>
      </el-table-column>
      <el-table-column label="展示时间" width="300">
        <template slot-scope="scope">
            <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
              <div> 周期循环</div>
              <template v-if="scope.row.circulateTime">
                <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
              每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
              </div>
              </template>
            </div>
            <div v-else> 
              {{scope.row.validityTime[0]}}-{{scope.row.validityTime[1]}}
            </div>
          </template>
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <div>
            {{
              ["未开始", "上线", "已结束", "下线"][scope.row.status - 1] || "-"
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="150px">
        <template slot-scope="scope">
          <div style="display: flex;">
            <el-button
            size="mini"
            @click="toEditGoodsInfo(scope.row, scope.$index)" v-if="scope.row.status == 1 ||scope.row.status == 3 || scope.row.status == 4"
            >编辑
          </el-button>
          <el-button
            size="mini"
            @click="toRemove(scope.row, 'bottom')" v-if="scope.row.status == 4"
            type="danger"
            >删除</el-button
          >
          </div>
         <div style="margin-top: 10px;display: flex;">
          <el-button size="mini" @click="online(scope, 'bottom')" type="danger"  v-if="scope.row.status == 4"
            >上线</el-button
          >
          <el-button size="mini" @click="outline(scope, 'bottom')" type="danger"  v-if="scope.row.status == 2"
            >下线</el-button
          >
          <el-button size="mini" v-if="scope.row.status == 2" @click="toEditGoodsInfo(scope.row, scope.$index,true)" >详情</el-button>
         </div>
        </template>
      </el-table-column>
    </el-table>
    <feedGoodsInfoAlert
      ref="feedGoodsInfoAlert"
      :isInfo="isInfo"
      @done="feedGoodsInfoAlertDone"
    ></feedGoodsInfoAlert>
    <!-- 商品信息配置结束 -->
  </div>
</template>
<script>
import feedGoodsAdvertisementAlert from "./components/feed_goods_advertisement_alert.vue";
import feedGoodsInfoAlert from "./components/feed_goods_info_alert.vue";
import base from "../../base";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import Sortable from "sortablejs";
let sortableObject = {};
export default {
  name: "feedGoods",
  extends: base,
  components: { swiperPoint, feedGoodsAdvertisementAlert, feedGoodsInfoAlert },
  contentDefault: {
    list: [],
    goodInfoList: [],
  },
  props: {
    core: Object,
  },
  data() {
    return {
      isInfo:false,
      isEditAdvertisement: false,
      isEditGoodInfo: false,
      isShowHrefDialog: false,
      currentIndexAdvertisement: undefined,
      currentIndexGoodsInfo: undefined,
      status: [
        { id: "", name: "全部" },
        { id: 1, name: "未开始" },
        { id: 2, name: "上线" },
        { id: 3, name: "已结束" },
        { id: 4, name: "下线" },
      ],
      goodInfoStatus: [
        { id: "", name: "全部" },
        { id: 1, name: "未开始" },
        { id: 2, name: "上线" },
        { id: 3, name: "已结束" },
        { id: 4, name: "下线" },
      ],
      status: [
        { id: "", name: "全部" },
        { id: 1, name: "未开始" },
        { id: 2, name: "上线" },
        { id: 3, name: "已结束" },
        { id: 4, name: "下线" },
      ],
      addDialog: false,
      addFormSelectLink: "",
      queryParamsTop: {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        crowdId: "", // 人群id
        crowdValue: "", // 人群
        status: "", //状态
      },

      topSelectLoading: false,
      topOptions: [],

      queryParamsBottom: {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        crowdId: "", // 人群id
        crowdValue: "", // 人群
        status: "", //状态
      },

      bottomSelectLoading: false,
      bottomOptions: [],

      topDataList: [],
      goodInfoList: [],
      carouselList: {
        bannerLocation: "",
        crowdValue: "",
        status: "",
      },
      // 时间不能大于当前时间
      disabledDate: (time) => {
        return time.getTime() > Date.now();
      },
    };
  },
  filters: {
    link(data) {
      return data.meta.page_url;
    },

    jumpText(val) {
      if (!val) {
        return "app内部跳转";
      } else {
        if (val === "inLink") {
          return "app内部跳转";
        }
        return "跳转至外部";
      }
    },
  },
  mounted() {
    this.initData();
    // this.rowDrop();
    // this.changeTab("notInvalid");
    this.initTopDataStatus();
    this.initGoodsInfoDataStatus();
    // this.getDict();
  },
  computed: {
    /**
     *   获取列的状态名称
     */
    getStatusName() {
      return function (timevalue, type) {
        let item = {};
        if (!timevalue) {
          item = {
            id: 4,
            name: "未设置时间",
          };
        } else {
          const _date = new Date().getTime();
          const start = new Date(timevalue[0]).getTime();
          const end = new Date(timevalue[1]).getTime();
          if (_date <= end && _date >= start) {
            item = {
              id: 1,
              name: "生效中",
            };
          } else if (_date > end) {
            item = {
              id: 3,
              name: "已失效",
            };
          } else if (_date < start) {
            item = {
              id: 2,
              name: "待生效",
            };
          }
        }
        if (type == "id") {
          return item.id;
        } else {
          return item.name;
        }
      };
    },
  },
  methods: {
    async getDict() {
      let status = await api.goods.status();
      if (status.code == 200) this.$nextTick(() => (this.status = status.data));
      else this.$message.error(status.msg);
    },

    openFeedGoodsAdvertisementAlert() {
      this.isEditAdvertisement = false;
      this.isInfo=false
      this.$refs.feedGoodsAdvertisementAlert.open();
    },
    feedGoodsAdvertisementAlertCallBack(val) {
      let arr = [...this.content.list];
     //校验重复
     if(this.isEditAdvertisement){
        arr.splice(arr.findIndex(item => item.activityId == val.activityId),1)
      }
     let classFlag = false;
     let id;
     
        if (val.timeType == 1) {
          
          const start_form = new Date(val.validityTime[0]).getTime();          
          const end_form = new Date(val.validityTime[1]).getTime();          
          arr.forEach(item => {
            if(item.timeType==2){
              return
            }
            const start = new Date(item.validityTime[0]).getTime();
            const end = new Date(item.validityTime[1]).getTime();
            if (start_form <= start && end_form >= end) {
              if(item.positionType==val.positionType){
                if(item.position==val.position){
                  if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                           }
                      }
            } else if ((start_form >= start && start_form <= end) || (end_form >= start && end_form <= end)) {
              if(item.positionType==val.positionType){
                if(item.position==val.position){
                  if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                           }
                      }
            }
          })
        } else if (val.timeType == 2) {
          // 1:周 2:月 3:日
          arr.forEach(item => {
            if(item.timeType==1){
              return
            }
            if(Array.isArray(val.circulateTime.circulateList)){
              // let _date =  new Date().toLocaleTimeString('en-US', {hour12: false});  
              if (item.timeType == 2) {
                let _date = val.circulateTime.circulateList[0].selectTimeData;     
                if(val.circulateTime.circulateType==3){
                  item.circulateTime.circulateList.forEach(element => {
                    if ((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1])) {
                      // classFlag = true;
                      if(item.positionType==val.positionType){
                        if(item.position==val.position){
                          if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                           }
                      }
                    }
                  });
                }
                if(item.circulateTime.circulateType==1 || item.circulateTime.circulateType==2){
                  item.circulateTime.circulateList.forEach(element => {
                    if (val.circulateTime.circulateList[0].weekOrday==element.weekOrday&&((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1]))) {
                      if(item.positionType==val.positionType){
                        if(item.position==val.position){
                          if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                           }
                      }
                    }
                  });
                }
              }
            }
          })
        }
     
      if (classFlag) {
        this.$message.error("同时段、同人群、位置已有该活动id"+id);
        return;
      }
      if (this.isEditAdvertisement) {
       // this.content.list[this.content.list.findIndex(item=>item.activityId==val.activityId)]=val
       this.$set(this.content.list,this.content.list.findIndex(item=>item.activityId==val.activityId),val)
        //this.$set(this.content.list, this.currentIndexAdvertisement, val);
      } else {
        let id = 0;
        id = Math.floor(Math.random() * 90000) + 10000;
        this.$set(val, "activityId", id);
        if (this.content.list.findIndex((item) => item.activityId == id) > -1||!val.activityId) {
          this.$message("id错误，请重新添加！");
          return;
        }
        arr.splice(0, 0, val);
        this.$set(this.content, "list", arr);
      }
      this.$message.success(
        `${this.isEditAdvertisement ? "编辑" : "添加"}成功！`
      );
      this.isEditAdvertisement = false;
      this.resetList();
      this.$refs.feedGoodsAdvertisementAlert.addDialogCancel()
    },

    openfeedGoodsInfoAlert() {
      this.isEditGoodInfo=false
      this.isInfo=false
      this.$refs.feedGoodsInfoAlert.open();
    },
 // 校验绑定商品
 async checkBindCsuOrProductGroup(addForm) {
  return true
      let canSave = true;
      (addForm.selectProducts || []).forEach((item) => {
        if (isNaN(item) || item < 0) {
          canSave = false;
        }
      });
      if (!canSave) {
        this.$message.error("指定商品ID只能输入数字");
        return;
      }
      const params = {
        type: addForm.selectProductType === "appointProduct" ? 1 : 2,
        exhibitionId: addForm.selectProductGroupId,
        // csuIds: this.addForm.selectProducts.filter(i => i).map(Number),
      };
      const result = await api.topic.checkBindCsuOrProductGroup(params);
      if ((result.data.data || {}).checkResult) {
        this.$message.success("绑定成功");
        return true
      } else {
        if (addForm.selectProductType === "appointProduct") {
          this.$message.error(
            `以下商品id绑定失败：${(
              (result.data.data || {}).failureCsuIds || []
            ).join()}`
          );
          return false
        } else {
          this.$message.error(result.data.msg);
          return false
        }
      }
    },
   async feedGoodsInfoAlertDone(val) {

      // if (val.selectProductType === "systemAuto") {
        
      // } else {
      //   if(!(await this.checkBindCsuOrProductGroup(val))){
      //     return
      //   }
      // }
      let arr = [...this.content.goodInfoList];
       //校验重复
       if(this.isEditGoodInfo){
        arr.splice(arr.findIndex(item => item.activityId == val.activityId),1)
      }
       let classFlag = false;
       let id;
    
        if (val.timeType == 1) {
          
          const start_form = new Date(val.validityTime[0]).getTime();          
          const end_form = new Date(val.validityTime[1]).getTime();          
          arr.forEach(item => {
            if(item.timeType==2){
              return
            }
            const start = new Date(item.validityTime[0]).getTime();
            const end = new Date(item.validityTime[1]).getTime();
            if (start_form <= start && end_form >= end) {
              if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
            } else if ((start_form >= start && start_form <= end) || (end_form >= start && end_form <= end)) {
              if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
            }
          })
        } else if (val.timeType == 2) {
          // 1:周 2:月 3:日
          arr.forEach(item => {
            if(item.timeType==1){
              return
            }
            if(Array.isArray(val.circulateTime.circulateList)){
              // let _date =  new Date().toLocaleTimeString('en-US', {hour12: false});  
              if (item.timeType == 2) {
                let _date = val.circulateTime.circulateList[0].selectTimeData;     
                if(val.circulateTime.circulateType==3){
                  item.circulateTime.circulateList.forEach(element => {
                    if ((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1])) {
                      if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                    }
                  });
                }
                if(item.circulateTime.circulateType==1 || item.circulateTime.circulateType==2){
                  item.circulateTime.circulateList.forEach(element => {
                    if (val.circulateTime.circulateList[0].weekOrday==element.weekOrday&&((_date[0] <= element.selectTimeData[1] && _date[0] >= element.selectTimeData[0]) || (_date[1] <= element.selectTimeData[1] && _date[1] >= element.selectTimeData[0])||( _date[0] <= element.selectTimeData[0] && _date[1] >= element.selectTimeData[1]))) {
                      if((item.crowdType==1&&val.crowdType==1)||(item.crowdType==2&&val.crowdType==2&&item.crowdId==val.crowdId)){
                          classFlag = true;
                          id=item.activityId
                        }
                    }
                  });
                }
              }
            }
          })
        }
      
      if (classFlag) {
        this.$message.error("同时段、同人群已有该活动id"+id);
        return;
      }
      if (this.isEditGoodInfo) {
        this.$set(this.content.goodInfoList,this.content.goodInfoList.findIndex(item=>item.activityId==val.activityId),val)
        //this.content.goodInfoList[this.content.goodInfoList.findIndex(item=>item.activityId==val.activityId)]=val
        //this.$set(this.content.goodInfoList, this.currentIndexGoodsInfo, val);
      } else {
        let id = 0;
        id = Math.floor(Math.random() * 90000) + 10000;
        this.$set(val, "activityId", id);
        if (this.content.goodInfoList.findIndex((item) => item.activityId == id) > -1||!val.activityId) {
          this.$message("id错误，请重新添加！");
          return;
        }
        arr.splice(0, 0, val);
        this.$set(this.content, "goodInfoList", arr);
      }
      this.$message.success(`${this.isEditGoodInfo ? "编辑" : "添加"}成功！`);
      this.isEditGoodInfo = false;
      this.$refs.feedGoodsInfoAlert.addDialogCancel()
      this.goodsInfoResetList();
      
    },

    //链接去掉空格
    urlChange() {
      this.dataForm.link.meta.page_url.trim();
    },

    initData() {
      this.topDataList = this.content.list;
      this.goodInfoList = this.content.goodInfoList;
    },

    rowDrop() {
      const _this = this;
      const tbody = document.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      sortableObject = Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        ghostClass: "sortable-ghost",
        onEnd: (evt) => {
          const currRow = (_this.topDataList || []).splice(evt.oldIndex, 1)[0];
          (_this.topDataList || []).splice(evt.newIndex, 0, currRow);
          const currRowData = (_this.content.list || []).splice(
            evt.oldIndex,
            1
          )[0];
          (_this.content.list || []).splice(evt.newIndex, 0, currRowData);
        },
      });
    },

    // 按照规则排序--排序规则优先级：人群 > 帧位 > 生效时间（生效中>待生效>未设置）
    sortByRule(data) {
      const samePeople = this.content.list.filter((item, index) => {
        return Number(item.crowdId) === Number(this.dataForm.crowdId);
      });
      // 相同人群的逻辑
      if (samePeople.length) {
        const sameLocation = samePeople.filter((item, index) => {
          return (
            Number(item.bannerLocation) === Number(this.dataForm.bannerLocation)
          );
        });
        // 相同人群下，相同帧位的逻辑
        if (sameLocation.length) {
          const sameStatus = sameLocation.filter((item, index) => {
            return (
              this.getStatusName(this.dataForm.timevalue) ===
              this.getStatusName(item.timevalue)
            );
          });
          let tempIndex = undefined;
          if (sameStatus.length) {
            this.content.list.forEach((item, index) => {
              if (item.id === sameStatus[sameStatus.length - 1].id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，相同状态，插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === "生效中") {
            this.content.list.forEach((item, index) => {
              if (sameLocation[0].id === item.id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，生效中插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === "待生效") {
            this.content.list.map((item, index) => {
              if (this.getStatusName(item.timevalue) === "生效中") {
                tempIndex = index + 1;
              }
            });
            if (!tempIndex) {
              // 说明没有生效中，找未设置的
              this.content.list.map((item, index) => {
                if (this.getStatusName(item.timevalue) === "未设置时间") {
                  tempIndex = index;
                }
              });
            }
            if (!tempIndex) {
              // 说明没有生效中未设置的，插到帧位最后面
              this.content.list.map((item, index) => {
                if (sameLocation[sameLocation.length - 1].id === item.id) {
                  tempIndex = index + 1;
                }
              });
            }
            // 相同人群，相同帧位，待生效插到生效中后面或未设置时间的前面或同帧位最后面
            this.content.list.splice(tempIndex, 0, data);
          } else {
            this.content.list.map((item, index) => {
              if (sameLocation[sameLocation.length - 1].id === item.id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，未设置插到后面
            this.content.list.splice(tempIndex + 1, 0, data);
          }
        } else {
          // 相同人群下，不同帧位，比较帧位大小
          let tempIndex = undefined;
          // 找到第一个大于新增帧位的项，有则插入到前面，没有则插入到同人群下最后一位
          let maxItem = samePeople.find((item, index) => {
            return item.bannerLocation > this.dataForm.bannerLocation;
          });
          if (maxItem) {
            this.content.list.map((item, index) => {
              if (item.id === maxItem.id) {
                tempIndex = index;
              }
            });
          } else {
            this.content.list.map((item, index) => {
              if (item.id === samePeople[samePeople.length - 1].id) {
                tempIndex = index + 1;
              }
            });
          }
          // 新增帧位小插到前面；新增帧位大插到后面
          this.content.list.splice(tempIndex, 0, data);
        }
      } else {
        // 新人群，直接添加
        let tempIndex = undefined;
        tempIndex = this.content.list.filter((item) => {
          return this.getStatusName(item.timevalue) !== "已失效";
        }).length;
        this.content.list.splice(tempIndex, 0, data);
      }
      this.$nextTick(() => {
        this.searchList();
      });
    },
    changeTab(type) {
      this.activeTab = type;
      this.carouselList.status = "";
      this.searchList();
    },
    //生成唯一id
    genID(length) {
      return Number(
        Math.random().toString().substr(3, length) + Date.now()
      ).toString(36);
    },

    toEditAdvertisement(data, index,isInfo) {
            if(isInfo){
        this.isInfo=true
      }else{
         this.isInfo=false
      }
      this.isEditAdvertisement = true;
      this.currentIndexAdvertisement = index;
      this.$refs.feedGoodsAdvertisementAlert.open(data, true);
    },

    toEditGoodsInfo(data, index,isInfo) {
            if(isInfo){
        this.isInfo=true
      }else{
         this.isInfo=false
      }
      this.isEditGoodInfo=true
      this.currentIndexGoodsInfo = index;
      this.$refs.feedGoodsInfoAlert.open(data, true);
    },

    toRemove(data, type) {
      let _self = this;
      return function () {
        if (type === "top") {
          _self.content.list.splice(
            _self.content.list.findIndex(
              (item) => item.activityId == data.activityId
            ),
            1
          );
          // _self.dataList.splice(_self.dataList.findIndex((item) => item.activityId == data.activityId), 1)
          _self.topDataList = _self.content.list;
          _self.initTopDataStatus();
          _self.$message({
            type: "success",
            message: "删除成功!",
          });
        } else {
          _self.content.goodInfoList.splice(
            _self.content.goodInfoList.findIndex(
              (item) => item.activityId == data.activityId
            ),
            1
          );
          _self.goodInfoList = _self.content.goodInfoList;
          _self.initGoodsInfoDataStatus();
          _self.$message({
            type: "success",
            message: "删除成功!",
          });
        }
      }.confirm(_self)();
    },
    online(scope, type) {
      this.$confirm("确定要执行上线操作吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        if (type === "top") {
         // this.content.list[scope.$index].status = 2;
          this.content.list.find(item=>item.activityId==scope.row.activityId).status=2
          this.$message.success("操作成功！");
          this.topDataList = this.content.list;
        } else {
          this.content.goodInfoList.find(item=>item.activityId==scope.row.activityId).status=2
          //this.content.goodInfoList[scope.$index].status = 2;
          this.$message.success("操作成功！");
          this.goodInfoList = this.content.goodInfoList;
        }
      });
    },

    outline(scope, type) {
      this.$confirm("确定要执行下线操作吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        if (type === "top") {
          // this.content.list[scope.$index].status = 4;
          this.content.list.find(item=>item.activityId==scope.row.activityId).status=4
          this.$message.success("操作成功！");
          this.topDataList = this.content.list;
        } else {
          this.content.goodInfoList.find(item=>item.activityId==scope.row.activityId).status=4
          this.content.goodInfoList[scope.$index].status = 4;
          this.$message.success("操作成功！");
          this.goodInfoList = this.content.goodInfoList;
        }
      });
    },

    async topOptionFilter(val) {
      this.topSelectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.topSelectLoading = false;
        this.topOptions = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.topSelectLoading = false;
        this.topOptions = [];
      }
    },

    topSelectCrowd(e) {
      if (e) {
        this.queryParamsTop.crowdId = Number(this.topOptions[0].value.trim());
        this.queryParamsTop.crowdValue = this.topOptions[0].label;
      } else {
        this.queryParamsTop.crowdId = "";
        this.queryParamsTop.crowdValue = "";
      }
      this.$forceUpdate();
    },

    async bottomOptionFilter(val) {
      this.bottomSelectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.bottomSelectLoading = false;
        this.bottomOptions = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.bottomSelectLoading = false;
        this.bottomOptions = [];
      }
    },

    bottomSelectCrowd(e) {
      if (e) {
        this.queryParamsBottom.crowdId = Number(
          this.bottomOptions[0].value.trim()
        );
        this.queryParamsBottom.crowdValue = this.bottomOptions[0].label;
      } else {
        this.queryParamsBottom.crowdId = "";
        this.queryParamsBottom.crowdValue = "";
      }
      this.$forceUpdate();
    },
    getValidityTime(row) {
      return row.validityTime
        ? row.validityTime[0] + "-" + row.validityTime[1]
        : "-";
    },
    //查询
    searchList() {
      //只有查询全部的时候允许拖拽
      this.topDataList = this.content.list;
      if (this.queryParamsTop.validityTime.length) {
        this.topDataList = this.topDataList.filter((item, index) => {
          return (
            new Date(this.queryParamsTop.validityTime[0]) * 1 >=
              new Date(item.validityTime[0]) * 1 &&
            new Date(this.queryParamsTop.validityTime[1]) * 1 <=
              new Date(item.validityTime[1]) * 1
          );
        });
      }
      if (this.queryParamsTop.activityId) {
        this.topDataList = this.topDataList.filter((item, index) => {
          return this.queryParamsTop.activityId === item.activityId.toString();
        });
      }
      if (this.queryParamsTop.activityName) {
        this.topDataList = this.topDataList.filter((item, index) => {
          return new RegExp(this.queryParamsTop.activityName).test(item.activityName)
         // return this.queryParamsTop.activityName === item.activityName;
        });
      }
      if (this.queryParamsTop.crowdValue) {
        this.topDataList = this.topDataList.filter((item, index) => {
          return this.queryParamsTop.crowdValue === item.crowdValue;
        });
      }
      if (this.queryParamsTop.status) {
        this.topDataList = this.topDataList.filter((item, index) => {
          return this.queryParamsTop.status === item.status;
        });
      }
    },

    // 商品信息配置部分
    goodsInfoSearchList() {
      //只有查询全部的时候允许拖拽
      this.goodInfoList = this.content.goodInfoList;
      if (Array.isArray(this.queryParamsBottom.validityTime)&&this.queryParamsBottom.validityTime.length) {
        this.goodInfoList = this.goodInfoList.filter((item, index) => {
          return (
            new Date(this.queryParamsBottom.validityTime[0]) * 1 >=
              new Date(item.validityTime[0]) * 1 &&
            new Date(this.queryParamsBottom.validityTime[1]) * 1 <=
              new Date(item.validityTime[1]) * 1
          );
        });
      }
      if (this.queryParamsBottom.activityId) {
        this.goodInfoList = this.goodInfoList.filter((item, index) => {
          return (
            this.queryParamsBottom.activityId === item.activityId.toString()
          );
        });
      }
      if (this.queryParamsBottom.activityName) {
        this.goodInfoList = this.goodInfoList.filter((item, index) => {
          return new RegExp(this.queryParamsBottom.activityName).test(item.activityName)
         // return this.queryParamsBottom.activityName === item.activityName;
        });
      }
      if (this.queryParamsBottom.crowdValue) {
        this.goodInfoList = this.goodInfoList.filter((item, index) => {
          return this.queryParamsBottom.crowdValue === item.crowdValue;
        });
      }
      if (this.queryParamsBottom.status) {
        this.goodInfoList = this.goodInfoList.filter((item, index) => {
          return this.queryParamsBottom.status === item.status;
        });
      }
    },
    resetList() {
      this.topDataList = this.content.list;
      this.resetListQueryParams();
      this.initTopDataStatus();
    },
    goodsInfoResetList() {
      this.goodInfoList = this.content.goodInfoList;
      this.resetGoodsInfoQueryParams();
      this.initGoodsInfoDataStatus();
    },
    resetListQueryParams() {
      this.queryParamsTop = {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        crowdId: "", // 人群id
        crowdValue: "", // 人群
        status: "", //状态
      };
    },
    resetGoodsInfoQueryParams() {
      this.queryParamsBottom = {
        activityId: "",
        activityName: "",
        validityTime: "", //有效期
        crowdId: "", // 人群id
        crowdValue: "", // 人群
        status: "", //状态
      };
    },
    initTopDataStatus() {
      this.topDataList = this.setStatusInitList(this.topDataList);
    },
    initGoodsInfoDataStatus() {
      this.goodInfoList = this.setStatusInitList(this.goodInfoList);
    },
    setStatusInitList(data) {
      if (!data) {
        return;
      }
      data.forEach((item, index) => {
        // // item.sort = index + 1;
        // if(item.status==4){
        //   return
        // }
        this.$set(item, "sort", index + 1);
        // // 1:"月 ",2:"周 ",3:"日 "
        // if (item.status != 4) {
          if (item.timeType == 2) {
            debugger
            // if (Array.isArray(item.circulateTime.circulateList)) {
            //   let _date = new Date().toLocaleTimeString("en-US", {
            //     hour12: false,
            //   });
            //   let dateTime = new Date().getTime();
            //   if (item.circulateTime.circulateType == 3) {
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       }
            //       else if (_date >= element.selectTimeData[1]) {
            //         item.status = 3; //已结束
            //       }
            //       else if(_date<element.selectTimeData[0]){
            //          item.status = 1; //未开始
            //       }
            //     });
            //   }
            //   if (item.circulateTime.circulateType == 1) {
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         new Date().getDate() == element.weekOrday &&
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       } else if (
            //         dateTime > new Date(element.selectTimeData[1]).getTime()
            //       ) {
            //         item.status = 3; //已结束
            //       } else if(_date<element.selectTimeData[0]) {
            //          item.status = 1; //未开始
            //       }
            //     });
            //   }
            //   if (item.circulateTime.circulateType == 2) {
            //     const dayOfWeek = [
            //       "周日",
            //       "周一",
            //       "周二",
            //       "周三",
            //       "周四",
            //       "周五",
            //       "周六",
            //     ][new Date().getDay()];
            //     item.circulateTime.circulateList.forEach((element) => {
            //       if (
            //         dayOfWeek == element.weekOrday &&
            //         _date <= element.selectTimeData[1] &&
            //         _date >= element.selectTimeData[0]&&item.status!=4
            //       ) {
            //         item.status = 2; //上线
            //       } else if (
            //         dateTime > new Date(element.selectTimeData[1]).getTime()
            //       ) {
            //         item.status = 3; //已结束
            //       } else if(_date<element.selectTimeData[0]) {
            //          item.status = 1; //未开始
            //       }
            //     });
            //   }
            // }
            if(item.status!=4){
              item.status=2
            }
          } else {
            if (new Date() * 1 < new Date(item.validityTime[0]) * 1) {
               item.status = 1; // 未开始
            } else if (
              new Date() * 1 > new Date(item.validityTime[0]) * 1 &&
              new Date() * 1 < new Date(item.validityTime[1]) * 1&&item.status!=4
            ) {
              item.status = 2; //上线
            } else if (new Date() * 1 > new Date(item.validityTime[1]) * 1) {
              item.status = 3;
            } else {
              item.status = 4;
            }
          }
        // }
      });
      return data;
    },
  },
};
</script>
<style lang="scss" scoped>
.title {
  text-align: left;
  line-height: 30px;
  background-color: #f2f2f2;
  margin: 10px 0;
  padding-left: 10px;
}
.three-button {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
.tableBox {
  width: 100%;
}
.dialog-activity-sort {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.goods_info_crowd_value {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>