<template>
    <div>
        <el-form>
            <el-form-item label="间距">
                <el-input style="width: 80px;" v-model="content.top" placeholder="上"></el-input>
                <el-input style="width: 80px;" v-model="content.right" placeholder="右"></el-input>
                <el-input style="width: 80px;" v-model="content.bottom" placeholder="下"></el-input>
                <el-input style="width: 80px;" v-model="content.left" placeholder="左"></el-input>
            </el-form-item>
            <el-form-item label="行高">
                <el-input style="width: 80px;" v-model="content.lineHeight" placeholder="文本行高"></el-input>
            </el-form-item>
        </el-form>
        <!--文章-->
        <tinymce ref="editor" :height="400" v-model="content.article" />
    </div>
</template>

<script>
    import base from '../base'
    import Tinymce from 'views/apps/components/tinymce/index'
    export default {
        extends: base,
        name: 'Article',
        contentDefault: {
            article: ''
        },
        data() {
            return {
            }
        },
        components:{
            Tinymce
        },
        methods: {

        }
    }
</script>
