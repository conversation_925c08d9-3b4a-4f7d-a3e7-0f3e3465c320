<template>
  <div class="topic-editor-list" v-if="core.isNewLayout !== 4&&core.isNewLayout !== 5">
    <div class="category" v-if="core.isNewLayout!==1 && core.isNewLayout!==3 && core.isNewLayout !== 4&&core.isNewLayout !== 5">
      <div class="category-block" v-for="cat in categoryList" :key="cat.title">
        <div class="category-block-title" v-if="cat.title">{{cat.title}}</div>
        <ul>
          <li class="grid" v-for="item in cat.children" :key="item.name"
              @mousedown="e => onDrag(e, item)">
            <span class="grid-title">{{item.title}}</span>
            <span class="grid-name">{{item.name}}</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="sorry" v-else>新布局不允许改动模块,仅可以配置模块里的内容。</div>
    <div class="grid prey"
         v-show="prey.el"
         :style="{
                transform : `translate(${prey.mx-prey.ox-1}px, ${prey.my-prey.oy-1}px)`
            }">
      <template v-if="prey.el">
        <span class="grid-title">{{prey.el.title}}</span>
        <span class="grid-name">{{prey.el.name}}</span>
      </template>
    </div>
  </div>
</template>

<script>
  import categoryList from '../../topic-prop-components'
  import template_data_json from "../template_data.json";

  export default {
    props: {
      core: {
        type: Object,
        default: {},
      }
    },
    data() {
      return {
        prey: {
          el: null,
          // 偏移坐标
          ox: 0,
          oy: 0,
          // 位移坐标
          mx: 0,
          my: 0
        }
      }
    },
    computed: {
      categoryList() {
        console.log(this.core, "pagetoy")
        if (this.core.page_type == 'h5' || this.core.page_type == 'KActivity') {
          // let core_categoryList = this.core.categoryList[0]
          let core_categoryList = {};
          // 每次取最新的template_data_json中的数据
          template_data_json.forEach((i) => {
            if (i.name === this.core.categoryList[0].title) {
              core_categoryList = {
                title: i.name,
                children: i.comList || []
              };
            }
          });
          // 兜底没匹配到的情况
          if (Object.keys(core_categoryList).length === 0) {
            core_categoryList = this.core.categoryList[0];
          }
          let newCategoryList = [{title: core_categoryList.title, children: []}]
          let categoryList_h5 = categoryList[this.core.category + '_' + this.core.page_type]
          if (core_categoryList && core_categoryList.children.length > 0) {
            for (var i = 0; i < core_categoryList.children.length; i++) {
              HandleCategoryList(core_categoryList.children[i], i, categoryList_h5, newCategoryList[0].children)
            }
          }
          return newCategoryList || categoryList[this.core.category + '_' + this.core.page_type]
        } else if (this.core.page_type == "diaNewLog") {
          // dialogType==1代表弹框类型是自定义，2代表弹框类型是优惠券；自定义类型时需隐藏“popUpCoupon”组件
          if (this.core.dialogType == 1) {
            let newChildren = categoryList.app_diaNewLog[0].children.filter(item => { return item.name !== "popUpCoupon" });
            let core_categoryList = this.core.categoryList[0]
            let newCategoryList = [{title: core_categoryList.title, children: newChildren}]
            return newCategoryList || categoryList[this.core.category + '_' + this.core.page_type]
          }
        } else if (this.core.page_type == "exhibitionPosition") {
          // sceneType==6代表展位应用场景是支付成功页；只显示hotZoneStreamer组件
          if (this.core.sceneType == 6) {
            let newChildren = categoryList.app_exhibitionPosition[0].children.filter(item => { return item.name == "hotZoneStreamer" });
            let core_categoryList = this.core.categoryList[0]
            let newCategoryList = [{title: core_categoryList.title, children: newChildren}]
            return newCategoryList || categoryList[this.core.category + '_' + this.core.page_type]
          } else if (this.core.sceneType == 7) { // sceneType==7代表展位应用场景是大搜启动页；此时需只展示“mainGoods”组件
            let newChildren = categoryList.app_exhibitionPosition[0].children.filter(item => { return item.name == "mainGoods" });
            let core_categoryList = this.core.categoryList[0]
            let newCategoryList = [{title: core_categoryList.title, children: newChildren}]
            return newCategoryList || categoryList[this.core.category + '_' + this.core.page_type]
          } else if (this.core.sceneType == 8) { // sceneType==7代表展位应用场景是大搜启动页；此时需只展示“mainGoods”组件
            let newChildren = categoryList.app_exhibitionPosition[0].children.filter(item => { return item.name == "orderCoupons"});
            let core_categoryList = this.core.categoryList[0]
            let newCategoryList = [{title: core_categoryList.title, children: newChildren}]
            return newCategoryList || categoryList[this.core.category + '_' + this.core.page_type]
          }else if(this.core.sceneType == 9) { // 订单页轮播
            let newChildren = categoryList.app_exhibitionPosition[0].children.filter(item => { return item.name == "single_control_newBanner"});
            let core_categoryList = this.core.categoryList[0]
            let newCategoryList = [{title: core_categoryList.title, children: newChildren}]
            return newCategoryList || categoryList[this.core.category + '_' + this.core.page_type]
          } else { // 其余情况展示除“mainGoods”之外的组件
            let newChildren = categoryList.app_exhibitionPosition[0].children.filter(item => { return item.name !== "mainGoods" && item.name != "orderCoupons" });
            let core_categoryList = this.core.categoryList[0]
            let newCategoryList = [{title: core_categoryList.title, children: newChildren}]
            return newCategoryList || categoryList[this.core.category + '_' + this.core.page_type]
          }
        }
        function HandleCategoryList(_item, _ind, _core, _arr) {
          for (var i = 0; i < _core.length; i++) {
            _core[i].children.find((val, ind) => {
              if (val.name === _item.name) {
                _arr.push(val)
              }
            })
          }
        }
        return categoryList[this.core.category + '_' + this.core.page_type] || [];
      }
    },
    methods: {
      getComponentByName(name) {
        return _.find(this.list, {name})
      },
      onDrag(e, item) {
        this.prey = {
          el: item,
          ox: e.offsetX,
          oy: e.offsetY,
          mx: e.clientX,
          my: e.clientY
        }
        window.addEventListener('mousemove', this.onMove)
        window.addEventListener('mouseup', this.onDrop)
        document.body.style.cssText = '-webkit-touch-callout:none;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;'
        this.$emit('drag', item)
      },
      onMove(e) {
        this.prey.mx = e.clientX;
        this.prey.my = e.clientY;
      },
      onDrop(e) {
        document.body.style.cssText = '';
        window.removeEventListener('mousemove', this.onMove)
        window.removeEventListener('mouseup', this.onDrop)
        this.prey.el = null;
      }
    }
  }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


  .topic-editor-list {
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 800px;

    .category {
      .category-block {
        margin-bottom: 10px;

        .category-block-title {
          overflow: hidden;
          padding: 0 5px;
          height: 30px;
          font-size: 14px;
          line-height: 30px;
          background: $color-primary;
          color: #fff;
        }

        ul {
          display: flex;
          flex-flow: wrap; /* justify-content:center; */
          justify-content: space-between;
          padding: 5px;
        }
      }
    }

    .sorry{
      text-align: left;
      color: red;
      line-height: 20px;
      font-size: 14px;
    }
    .grid {
      width: 100px;
      height: 40px;
      padding: 5px;
      margin: 5px;
      border: $border-base;
      text-align: center;
      white-space: nowrap;
      background: #fff;
      cursor: pointer;

      .grid-title {
        display: block;
        overflow: hidden;
        height: 24px;
        font-size: 14px;
        line-height: 24px;
        color: #333;
        pointer-events: none;
      }

      .grid-name {
        display: block;
        overflow: hidden;
        height: 16px;
        font-size: 12px;
        line-height: 16px;
        color: #aaa;
        pointer-events: none;
      }
    }

    .prey {
      position: fixed;
      top: 0;
      left: 0;
      margin: 0;
      pointer-events: none;
      background: rgba(0, 0, 0, 0.5);

      .grid-title {
        color: #fff;
      }

      .grid-name {
        color: #ccc;
      }
    }
  }

  .topic-editor-list .el-tabs--border-card > .el-tabs__content {
    padding: 0;
  }
</style>
