<template>
  <div class="page-link">
    <el-input
      size="mini"
      class="mb-10"
      v-model="key"
      @keyup.enter.native="getList()"
      placeholder="关键字"
      clearable
    >
      <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
    </el-input>

    <el-table
      size="mini"
      :data="list"
      highlight-current-row
      @current-change="onSelect"
      style="margin-bottom:5px"
      v-loading="loading"
    >
      <el-table-column label="名称" width="100">
        <template slot-scope="scope">
          <p>{{scope.row.page_name}}</p>
        </template>
      </el-table-column>
      <el-table-column label="路径" prop="item_name">
        <template slot-scope="scope">{{scope.row.page_url}}</template>
      </el-table-column>
    </el-table>

    <el-pagination
      small
      layout="pager"
      :current-page="pagination.current"
      :page-size="pagination.size"
      :total="pagination.total"
      @current-change="getList"
    ></el-pagination>
  </div>
</template>

<script>
import api from "api";

export default {
  props: {
    params: {
      type: Object,
      default: 'app',
    },
    from: String,
  },
  data() {
    return {
      id: "",
      key: "",
      sku_key: "",
      list: [],
      pagination: {
        size: 5,
        current: 1,
        total: 0
      },
      loading: false,
      manualId: ""
    };
  },
  methods: {
    async getList(page = 1) {
      this.pagination.current = page;
      this.pagination.size = 5;
      this.loading = true;
      // const result = await this.$http.get('http://search.yunbaiplus.com/product/search?v=1&appKey=100001', {
      //     params: {
      //         pageNum: this.pagination.current,
      //         pageSize: this.pagination.size,
      //         key: this.key || '',
      //     }
      // })
      // if(result.result.result == 1){
      //     this.list = result.data.esProducts;
      //     this.pagination.total = result.data.totalCount;
      // }
      // this.loading = false;
      // console.log('232323', this.params)
      let params = {
        pageFrom: this.pagination.current,
        pageSize: this.pagination.size,
        state: 1,
        branchCode: this.params.branchCode,
        category: this.from || 'app'
      };
      const searchParam = {
        page_name: this.key || ""
      };
      let pms = Object.assign(params, searchParam);
      const result = await api.activity.list(pms);
      this.loading = false;
      if (result.code == 200) {
        this.$nextTick(() => {
          this.list = result.data.rows;
          this.pagination.total = result.data.total;
        });
      } else {
        this.$message.error(result.msg);
      }
    },
    onSelect(row) {
      if (!row) return;
      this.$emit("select", {
        type: "item",
        label: "活动页",
        id: row.productSid,
        desc: row.productName,
        meta: {
          id: row.id,
          page_url: row.page_url,
          page_name: row.page_name
        }
      });
    }
  },
  mounted() {
    this.getList();
  },
  filters: {
    tradeType(value) {
      return (
        {
          1: "国内贸易",
          2: "跨境贸易"
        }[value] || ""
      );
    }
  }
};
</script>
<style lang="scss" scoped rel="stylesheet/scss">
.page-link {
  border: 1px solid #0cdcdc;
  padding: 3px;
}
</style>