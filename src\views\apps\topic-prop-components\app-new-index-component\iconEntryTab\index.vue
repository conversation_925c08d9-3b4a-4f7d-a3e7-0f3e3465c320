<template>
  <div class="fast-entry">
    <!--模块背景设置-->
    <!-- <div class="tip" v-if="content.new_layout">请至少生效6个icon，防止icon互斥后，导致icon缺失！</div> -->
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="12">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
          <img v-if="content.bgImg" :src="content.bgImg" alt="">
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <div>
            <el-button @click="imgOnclick">清除背景图</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <span class="demonstration">背景色</span>
          <el-color-picker v-model="content.bgRes" size="mini" @change="onSelect"></el-color-picker>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">配置内容</div>
      <el-col :span="8">
        <div class="block" style="margin-bottom: 10px;">
          <el-button type="primary" @click="openConfigDialog">添加内容</el-button>
        </div>
      </el-col>
      <el-table :data="content.configData" size="mini" :row-key="getRowKeys" style="width: 100%">
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column prop="title" label="内容名称">
          <template slot-scope="scope">
            <span>{{scope.row.entry}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="人群名称">
          <template slot-scope="scope">
            <span>{{scope.row.crowdValue || '全部人群'}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="300">
          <template slot-scope="scope">
            <el-button
            type="primary"
            icon="el-icon-caret-top"
            :disabled="scope.$index === 0 ? true : false"
            @click="handle_sortconfigData(scope.row, 'up')"
            circle
            sizi="mini"
          />
            <el-button
            type="primary"
            icon="el-icon-caret-bottom"
            :disabled="scope.$index === (content.configData.length -1 ) ? true : false"
            @click="handle_sortconfigData(scope.row, 'down')"
            circle
            sizi="mini"
          />
            <el-button
              size="mini"
              type="primary"
              @click="configEdit(scope)"
            >编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="configDel(scope)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    
    <!-- 背景图上传弹层 -->
    <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addPic" width="30%">
      <el-upload
        class="topic-image-upload uploader-btn-state"
        ref="upload"
        accept="image/jpeg,image/jpg, image/png, image/gif"
        :show-file-list="false"
        :before-upload="() => {upImgLoading = true; return true;}"
        :on-success="onUploadImage"
      >
        <img v-if="dataForm.image" :src="dataForm.image" class="image" />
        <i v-else v-loading="upImgLoading" class="el-icon-plus uploader-icon"></i>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeAddPic">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmImg">确定</el-button>
      </div>
    </el-dialog>
    <!-- 添加内容弹层 -->
    <el-dialog class="banner-dialog" :title="isContEdit ? '编辑入口': '添加入口'" v-if="add_editContent" :visible.sync="add_editContent">
      <el-form ref="form" :model="editData" label-width="120px">
        <!-- <el-form-item label="入口位置：" v-if="!isBottom">
          <el-select
            v-model="editData.entryLocation"
            placeholder="请选择"
          >
            <el-option
              v-for="item in location_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="人群范围：" v-if="!isBottom">
          <el-radio-group v-model="editData.crowdType" @change="changeCrowdType">
            <el-radio :label="1">全部人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="editData.crowdType===2"
          label="指定人群："
        >
          <el-select
            v-model="editData.crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="菜单ICON：">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="() => {editImgLoading = true; return true;}"
            :on-success="uploadEditContImage"
          >
            <img v-if="editData.title" :src="editData.title" class="image" />
            <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="展示时间：" v-if="!isBottom">
         
          <el-radio  v-model="editData.timeType" label="1">固定时段</el-radio>  <el-date-picker
            v-model="editData.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
           
          ></el-date-picker><br>
              <el-radio  v-model="editData.timeType" label="2">周期循环</el-radio>  <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
       
        </el-form-item>
        <el-form-item label="入口名称：">
          <el-input v-model="editData.entry" class="entry-name"></el-input>
        </el-form-item>
        <el-form-item label="文字颜色：">
          <el-color-picker v-model="editData.frontColor" size="mini" @change="onSelect('editing')"></el-color-picker>
        </el-form-item>
        <el-form-item label="链接类型：">
          <el-select v-model="editData.linkType" placeholder="请选择">
            <el-option
              v-for="item in linkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="topic-image-picker" v-if="editData.linkType !== 'dynamic'">
        <el-input placeholder="链接地址" v-model.trim="editData.link.meta.page_url" @input="urlChange">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>
      <div v-if="editData.linkType === 'dynamic'">
        <div class="topic-image-picker">
          <el-input style="width:200px" placeholder="输入跳转id" v-model="editData.link.meta.dynamicId">
            <template slot="prepend">跳转id</template>
          </el-input>
          <el-button type="primary" @click="putDynamicLink(editData)">生成链接</el-button>
        </div>
        <el-input placeholder="链接地址" v-model.trim="editData.link.meta.page_url"  @input="urlChange">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>
      <div v-if="editData.linkType==='topic'">
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditContent">取 消</el-button>
        <el-button size="small" type="primary" @click="add_confirmEdit">确定</el-button>
      </div>
    </el-dialog>
    <!-- 添加配置内容弹层 -->
    <el-dialog class="banner-dialog" :title="isFormContEdit ? '编辑配置内容': '添加配置内容'" :visible.sync="add_editConfigContent">
      <el-form ref="form" :rules="validate" :model="formData" label-width="120px">
        <el-form-item label="内容名称：" prop="entry">
          <el-input v-model="formData.entry" maxlength="50" class="entry-name"></el-input>
        </el-form-item>
        <el-form-item
          label="布局模式："
        >
          <el-select
            v-model="formData.layoutType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in layout_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="人群范围：">
          <el-radio-group v-model="formData.crowdType" @change="changeFormDataCrowdType">
            <el-radio :label="1">全部人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="formData.crowdType===2"
          label="指定人群："
        >
          <el-select
            v-model="formData.crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectFormDataCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-autocomplete
            style="width: 100%"
            class="inline-input"
            v-model.trim="formData.crowdValue"
            :fetch-suggestions="querySearchCrowd"
            placeholder="请输入人群id"
            :trigger-on-focus="false"
            @select="handleSelectCrowd"
            @input="changeCrowdValue"
          ></el-autocomplete> -->
        </el-form-item>
        <el-form-item label="入口位置选择：">
          <el-select
            v-model="formData.selectLocation"
            placeholder="请选择"
          >
            <el-option
              v-for="item in location_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="添加快捷入口：">
          <div class="fast-entry-btn">
            <div class="block">
              <el-button class="btn-block" type="primary" @click="toAdd(1)" :disabled="formData.selectLocation == '兜底'">添加快捷入口</el-button>
            </div>
            <div class="block" style="margin-left: 10px">
              <el-button class="btn-block" type="primary" @click="toAdd(2)" :disabled="formData.list.filter((item) => item.entryLocation == '兜底').length > 0 || formData.selectLocation !== '兜底'">添加兜底快捷入口</el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="文字颜色：">
          <div style>
            <el-row :gutter="20">
              <div class="title">轮播进度设置</div>
              <el-col :span="8">
                <div class="block">
                  <span class="demonstration">轮播点类型</span>
                  <div>
                    <el-select v-model="formData.pro_obj.pro_type" placeholder="请选择">
                      <el-option
                        v-for="item in pro_options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="block">
                  <span class="demonstration">排列方式</span>
                  <div>
                    <el-select v-model="formData.pro_obj.pro_align_type" placeholder="请选择">
                      <el-option
                        v-for="item in pro_align_options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="block">
                  <span class="demonstration">轮播时间</span>
                  <div>
                    <el-select v-model="formData.pro_obj.pro_auto" placeholder="请选择">
                      <el-option
                        v-for="item in pro_auto_options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="block opacityDiv" style="width: 200px;">
                  <span class="demonstration">默认透明度</span>
                  <div>
                    <el-slider v-model="formData.pro_obj.default_opacity" :format-tooltip="formatTooltip"></el-slider>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="block opacityDiv">
                  <span class="demonstration">激活透明度</span>
                  <div>
                    <el-slider v-model="formData.pro_obj.active_opacity" :format-tooltip="formatTooltip"></el-slider>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="block">
                  <span class="demonstration">默认颜色</span>
                  <div>
                    <el-color-picker v-model="formData.pro_obj.default_color" size="mini"></el-color-picker>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="block">
                  <span class="demonstration">激活颜色</span>
                  <div>
                    <el-color-picker v-model="formData.pro_obj.active_color" size="mini"></el-color-picker>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-form-item>
      </el-form>
      <el-table :data="formData['allList']" size="mini" style="width: 100%">
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column prop="title" label="位置">
          <template slot-scope="scope">
            <span>{{scope.row.entryLocation}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题">
          <template slot-scope="scope">
            <img v-if="scope.row.title" :src="scope.row.title" alt="图" class="title-image" />
            <i v-else class="el-icon-circle-plus-outline no-img"></i>
          </template>
        </el-table-column>
        <el-table-column prop="entry" label="有效时间">
          <template slot-scope="scope" v-if="scope.row.entryLocation != '兜底'">
            <span style='color: #67C23A' v-if="scope.row.timeType&&scope.row.timeType==2">展示中</span>
            <span v-else v-html="format_text(scope.row.timevalue)"></span>
          </template>
        </el-table-column>
        <el-table-column prop="entry" label="入口名称">
          <template slot-scope="scope">
            <span v-html="scope.row.entry" class="entry-name" :style="{'color':scope.row.frontColor}"></span>
          </template>
        </el-table-column>
        <el-table-column prop="entry" label="指定人群">
          <template slot-scope="scope">
            <span v-html="scope.row.crowdValue || '全部人群'" class="entry-name"></span>
          </template>
        </el-table-column>
        <el-table-column prop="entry" label="链接" :show-overflow-tooltip="true" width="120px">
          <template slot-scope="scope">
            <span v-html="scope.row.link.meta.page_url"></span>
            <!-- <el-input
              type="text"
              size="mini"
              v-model="scope.row.link.meta.page_url"
            >{{scope.row.link.meta.page_url}}</el-input> -->
          </template>
        </el-table-column>
        <el-table-column label="链接类型">
          <template slot-scope="scope">
            <span>{{scope.row.linkType==='stores'?'店铺链接':'专题页链接'}}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              @click="toEdit(scope.row, scope.$index)"
              type="primary"
            >编辑</el-button>
            <el-button
              type="primary"
              icon="el-icon-caret-top"
              :disabled="scope.$index === 0 ? true : false"
              @click="handle_sort(scope.row, 'up')"
              circle
              sizi="mini"
            />
            <el-button
              type="primary"
              icon="el-icon-caret-bottom"
              :disabled="scope.$index === (formData.allList.length -1 ) ? true : false"
              @click="handle_sort(scope.row, 'down')"
              circle
              sizi="mini"
            />

          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditConfigContent">取 消</el-button>
        <el-button size="small" type="primary" @click="add_confirmEditConfig">确定</el-button>
      </div>
    </el-dialog>
    <loopcirculateTime ref="loopcirculateTime" @loopcirculateTimeBack="loopcirculateTimeBack"></loopcirculateTime>
  </div>
</template>

<script>
import base from "../../base";
import event_bus from "../../../../../utils/eventbus";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import * as http from 'utils/http'
import loopcirculateTime from '../../../components/loopcirculateTime.vue';
export default {
  extends: base,
  // content: {
  //   bgRes: "",
  //   formData: {},
  //   list: [
  //     {
  //       editData: {},
  //       editlist: []
  //     }
  //   ]
  // },
  components:{
    loopcirculateTime
  },
  contentDefault: {
    list: [
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/3faadb73-cd5a-4567-9176-bdb75a14e64c.png",
        entry: "高毛专区",
        entryLocation: '1-1',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 1,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/highmarginnew?ybm_title=高毛专区"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/48a38774-bbec-40f4-9963-16067c8aa1da.png",
        entry: "新品上架",
        entryLocation: '1-2',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 2,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/newpro?ybm_title=新品上架"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/06c30e6a-6c0b-4c0b-9720-07ec191638b7.png",
        entry: "诊所专区",
        entryLocation: '1-3',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 3,
            page_url: "ybmpage://clinicactivity?umkey=zszq"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/4b6e4ea1-4bef-4fa3-901b-34d4d64aa645.png",
        entry: "领券中心",
        entryLocation: '1-4',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 4,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/voucherscenter?ybm_title=领券中心"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/2d40238a-1fba-4c7d-bffe-8a0c293786ea.png",
        entry: "品牌推荐",
        crowdType: 1,
        entryLocation: '1-5',
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 5,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/recommendedbrands?ybm_title=品牌推荐"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/ba0a5c10-e432-4ade-85e5-09d7b68c2f4f.png",
        entry: "滋补保健",
        entryLocation: '2-1',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 6,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/temprowpage/ZS201804271137561589/666/a340e1/b1504c55-1fda-49fe-9914-9d6dbf59503c?ybm_title=滋补保健"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/ca05a3eb-140d-4973-a77e-f94c5ff97153.png",
        entry: "中药饮片",
        entryLocation: '2-2',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 7,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/zyzone?ybm_title=中药专区"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/59bc40af-44f4-481c-b7bb-cc6babbe680f.png",
        entry: "呼吸系统",
        entryLocation: '2-3',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 8,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/temprowpage/ZS201810251703073516/666/fccc24/0dff4617-a4fe-43ba-a94e-ca01b290354b?ybm_title=呼吸系统"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/51adfb1e-463d-4879-ab56-43afa8ff88da.png",
        entry: "消化系统",
        entryLocation: '2-4',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 9,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/temprowpage/ZS201804271136163420/666/521fbb/d4913abc-7ec7-4938-aea0-bcc56f1c8568?ybm_title=消化系统"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/6ce70319-8037-4508-8cdd-428c69328fec.png",
        entry: "风湿骨痛",
        entryLocation: '2-5',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 10,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/temprowpage/ZS201812131653281244/666/ff3853/9a1f2571-499c-4823-8c51-4e2a1bc66951?ybm_title=风湿骨痛"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/8a3a4090-17e7-4a8a-952b-3e9c57a8554f.png",
        entry: "皮肤用药",
        entryLocation: '2-5',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 11,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/temprowpage/ZS201804271138286118/666/fe9031/c01b6bdd-3ee7-4602-89db-348a1071d192?ybm_title=皮肤用药"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/a62cf956-6b23-4f96-aed1-83261802c8bc.png",
        entry: "战略合作",
        entryLocation: '2-5',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 12,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/protocoloem?ybm_title=战略合作"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/ed535f1c-75ea-4ab5-bd84-863e31dc3408.png",
        entry: "慢病用药",
        entryLocation: '2-5',
        crowdType: 1,
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 13,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/temprowpage/ZS201804271137301862/666/1233a9/d821dbdb-1b01-45d1-bb35-3d8a13eeed9d?ybm_title=慢病用药"
          }
        }
      }
    ],
    bgRes: "",
    color: "#ffffff",
    image: "",
    layoutType: 3,
    // list_num: 2,
    pro_obj: {
      pro_type: "longBar",
      pro_auto: 0,
      pro_align_type: "center",
      default_color: "#ffffff",
      default_opacity: 30,
      active_color: "#555555",
      active_opacity: 100,
      component_name: "fastEntry" //区分模块的标识
    }
  },
  data() {
    return {
      configIndex: null,
      add_editConfigContent: false,
      tableList: [],
      editConfigData: {
        title: "", // 内容名称
        crowdType: 1,
        entryLocation: "",
        crowdValue: "",
        crowdId: "",
        selectLocation: "", //入口位置
      },
      validate: {
			  entry: [
          { required: true, message: '请输入内容名称，不得超过50个中文汉字', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在1 - 50之间', trigger: 'blur' }
        ],
      },
      linkOptions: [
        {
          value: "topic",
          label: "专题页链接"
        },
        {
          value: "stores",
          label: "店铺页链接"
        },
        {
          value: "dynamic",
          label: "动态商品链接"
        },
        {
          value: "redEnvelope",
          label: "绑定微信链接"
        }
      ],
      layoutType: 3,
      loading: false,
      upImgLoading: false,
      editImgLoading: false,
      addPic: false,
      isBottom: false,
      isContEdit: false,
      isFormContEdit: false,
      add_editContent: false,
      editCont: "",
      canSave: true,
      selectLoading: false,
      options: [],
      dataForm: {
        image: ""
      },
      selectLocation: '',
      formData: {
        entry: "", //内容名称
        layoutType: 3, //布局模式
        crowdType: 1, //人群范围
        crowdValue: "", //指定人群
        selectLocation: "", //入口位置选择
        pro_obj: {
          pro_type: "longBar",
          pro_auto: 0,
          pro_align_type: "center",
          default_color: "#ffffff",
          default_opacity: 30,
          active_color: "#555555",
          active_opacity: 100,
          component_name: "fastEntry" //区分模块的标识
        },
        list: [],
        allList: []
      },
      editData: {
        linkType: "topic",
        title: "",
        entry: "",
        frontColor: "#000000",
        operation: "",
        timevalue: [],
        link: {
          meta: {
            page_url: ""
          }
        },
        crowdType: 1,
        entryLocation: "",
        crowdValue: "",
        crowdId: "",
        timeType:'1',
        circulateTime:{}
      },
      select_option: [
        {
          value: 1,
          label: "一个横排"
        },
        {
          value: 2,
          label: "两个横排"
        }
      ],
      pro_options: [
        {
          value: "longBar",
          label: "矩形"
        },
        {
          value: "bar",
          label: "正方形"
        },
        {
          value: "circle",
          label: "圆形"
        }
      ],
      pro_align_options: [
        {
          value: "center",
          label: "居中"
        },
        {
          value: "left",
          label: "左对齐"
        },
        {
          value: "right",
          label: "右对齐"
        }
      ],
      pro_auto_options: [
        {
          value: 0,
          label: "不轮播"
        },
        {
          value: 3000,
          label: "3秒间隔"
        },
        {
          value: 4000,
          label: "4秒间隔"
        },
        {
          value: 5000,
          label: "5秒间隔"
        }
      ],
      layout_options: [{
        value: 1,
        label: '1排1页',
      }, {
        value: 2,
        label: '1排2页',
      }, {
        value: 3,
        label: '2排1页',
      }],
      location_options:[{
        value: '兜底',
        label: '兜底',
      }, {
        value: '1-1',
        label: '位置1-1',
      }, {
        value: '1-2',
        label: '位置1-2',
      }, {
        value: '1-3',
        label: '位置1-3',
      }, {
        value: '1-4',
        label: '位置1-4',
      }, {
        value: '1-5',
        label: '位置1-5',
      }, {
        value: '2-1',
        label: '位置2-1',
      }, {
        value: '2-2',
        label: '位置2-2',
      }, {
        value: '2-3',
        label: '位置2-3',
      }, {
        value: '2-4',
        label: '位置2-4',
      }, {
        value: '2-5',
        label: '位置2-5',
      }],
      pickerOptions: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      }
    };
  },
  watch: {
  //   "editData.timeType"(newdata,ordData){
     
  //    if(newdata==2){
  //      this.editData.timevalue=""
  //    }
  //    if(newdata==1){
  //      this.editData.circulateTime={}
  //    }
  //  },
    "formData.selectLocation": {
      deep: true,
      handler() {
        // 更新当前入口位置下列表
        this.renderListTemplate();
      }
    }
  },
  computed: {
    // list() {
    //   this.$set(this.content, 'enable_list_num', true)
    //   var list = _.get(this, "content.configData[" + this.configIndex + "].list");
    //   console.log("这是list", this.configIndex, list)
    //   if (list) {
    //     // this.$nextTick(function() {
    //     //   this.setSort();
    //     // });
    //     // this.formData.list = [];
    //     // list.map((item, index) => {
    //     //   if(!item.entryLocation) {
    //     //     item.entryLocation = index < 5 ? `1-${index+1}` : index < 10 ? `2-${index-4}` : '2-5';
    //     //     item.crowdType = 1;
    //     //   }
    //     //   if(item.entryLocation == this.formData.selectLocation) {
    //     //     this.formData.list.push(item);
    //     //   } 
    //     // })
    //     var arr = list.filter((item) => {
    //       if(!item.entryLocation) {
    //         item.entryLocation = index < 5 ? `1-${index+1}` : index < 10 ? `2-${index-4}` : '2-5';
    //         item.crowdType = 1;
    //       }
    //       if(item.entryLocation == this.formData.selectLocation) {
    //         return item;
    //       } 
    //     })
    //     this.formData.list = arr;
    //   } else {
    //     this.formData.list = [];
    //   }
    // }
  },
  filters: {
    link(data) {
      if (!data.type) {
        return "";
      }
      return data.meta.page_url;
    }
  },
  mounted() {
    let keys = Object.keys(this.content);
    if (keys.indexOf("configData") < 0) {
      this.content.configData = [
        {
          crowdId: "",
          crowdType: 1,
          crowdValue: "",
          entry: "老数据",
          layoutType: this.content.layoutType || "",
          list: this.content.list,
          pro_obj: this.content.pro_obj || "",
        }
      ]
    }
    // this.content.formData = {
    //   entry: "", //内容名称
    //   layoutType: "", //布局模式
    //   crowdType: "", //人群范围
    //   crowdValue: "", //指定人群
    //   selectLocation: "", //入口位置选择
    //   pro_obj: {
    //     pro_type: "longBar",
    //     pro_auto: 0,
    //     pro_align_type: "center",
    //     default_color: "#ffffff",
    //     default_opacity: 30,
    //     active_color: "#555555",
    //     active_opacity: 100,
    //     component_name: "fastEntry" //区分模块的标识
    //   },
    //   list: []
    // };
  },
  methods: {
    
    //打开时间循环
    toloopcirculateTime(){
      this.$refs.loopcirculateTime.showVisible=true
    },
    //循环时间回调
    loopcirculateTimeBack(data){
      this.editData.circulateTime=data
    },
    // 配置内容编辑
    configEdit(row) {
      this.configIndex = row.$index;
      this.formData = {...row.row};
      this.isFormContEdit = true;
      this.add_editConfigContent = true;
    },
    configDel(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        this.content.configData.splice(row.$index, 1);
      });
    },
    renderListTemplate() {
      if (this.formData.list) {
        var arr = this.formData.list.filter((item) => {
          if(!item.entryLocation) {
            item.entryLocation = index < 5 ? `1-${index+1}` : index < 10 ? `2-${index-4}` : '2-5';
            item.crowdType = 1;
          }
          if(item.entryLocation == this.formData.selectLocation) {
            return item;
          } 
        })
        this.formData.allList = arr;
      } else {
        this.formData.allList = [];
      }
    },
    openConfigDialog() {
      this.configIndex = null;
      this.formData = {
        entry: "", //内容名称
        layoutType: 3, //布局模式
        crowdType: 1, //人群范围
        crowdValue: "", //指定人群
        selectLocation: "", //入口位置选择
        pro_obj: {
          pro_type: "longBar",
          pro_auto: 0,
          pro_align_type: "center",
          default_color: "#ffffff",
          default_opacity: 30,
          active_color: "#555555",
          active_opacity: 100,
          component_name: "fastEntry" //区分模块的标识
        },
        list: [],
        allList: []
      };
      this.isFormContEdit = false;
      this.add_editConfigContent = true;
    },
    // changeCrowdValue(e) {
    //   if (!e) {
    //     this.editData.crowdId = '';
    //   }
    //   this.$forceUpdate();
    // },
    //删除空格
    urlChange(){
      this.editData.link.meta.page_url=this.editData.link.meta.page_url.trim()
    },
    changeCrowdType() {
      this.editData.crowdId = '';
      this.editData.crowdValue = '';
    },
    changeFormDataCrowdType() {
      this.formData.crowdId = '';
      this.formData.crowdValue = '';
    },
    handle_sort(item,type){
      const index = this.formData.list.indexOf(item);
      if(type === 'up') {
        this.formData.list.splice(index,1);
        this.formData.list.splice(index-1,0,item)
      } else {
        this.formData.list.splice(index,1);
        this.formData.list.splice(index+1,0,item)
      }
      this.renderListTemplate();
    },
    handle_sortconfigData(item,type){
      const index = this.content.configData.indexOf(item);
      if(type === 'up') {
        this.content.configData.splice(index,1);
        this.content.configData.splice(index-1,0,item)
      } else {
        this.content.configData.splice(index,1);
        this.content.configData.splice(index+1,0,item)
      }
     
    },
    putDynamicLink(item) {
      if (!item.link.meta.dynamicId) {
        this.$message({
          message: '请输入跳转id再点击生成链接',
          type: 'warning'
        });
        return false;
      }
      item.link.meta.page_url = `ybmpage://homeSteadyChannel?strategyId=${item.link.meta.dynamicId}&title=${item.entry}`
    },
    change_default_height(val) {
      if (val === 2) {
        event_bus.$emit("change_default_height", "192pt");
      }
    },
    format_text(data) {
      if (!data) {
        return "<b style='color: red'>请设置时间</b>";
      }
      const _date = new Date().getTime();
      const start = new Date(data[0]).getTime();
      const end = new Date(data[1]).getTime();
      if (_date <= end && _date >= start) {
        return "<b style='color: #67C23A'>展示中</b>";
      } else if (_date < start) {
        return `<b style='color: #000000'>即将在${new Date(
          start
        ).toLocaleDateString()}展示</b>`;
      } else {
        return "<b style='color: #000000'>已过期</b>";
      }
    },
    formatTooltip(val) {
      return val / 100;
    },
    closeAddPic() {
      this.addPic = false;
    },
    onSetLink(link) {
      this.editData.link = link;
    },
    toEdit(data, index) {
      if (data.entryLocation == '兜底') {
        this.isBottom = true;
      } else {
        this.isBottom = false;
      }
      // this.currentCrowd = {
      //   crowdId: data.crowdId,
      // }
      // this.nowIndex = index;
      this.isContEdit = true;
      this.add_editContent = true;
      this.nowIndex = this.formData.list.indexOf(data);
      // this.nowIndex = this.content.list.indexOf(data);
      // this.currentData = Object.assign({}, data);
      // this.editData = Object.assign({}, data);
      if(!data.timeType){
        data.timeType="1"
      }
      this.editData = JSON.parse(JSON.stringify(data))
      if(this.editData.timeType==2&&this.editData.circulateTime){
        this.$refs.loopcirculateTime.circulateTime=this.editData.circulateTime
        this.$refs.loopcirculateTime.editInit()

      }
     
    },
    toAdd(type) {
      if (!this.formData.selectLocation) {
        this.$message.warning('请选择入口位置');
        return;
      }
      if (type == 2) {
        this.isBottom = true;
      } else {
        this.isBottom = false;
      }
      this.add_editContent = true;
      this.isContEdit = false;
      //新建时清空
      this.editData = {
        linkType: "topic",
        title: "",
        timevalue: "",
        entry: "",
        frontColor: "#000000",
        operation: "",
        link: {
          meta: {
            page_url: ""
          }
        },
        crowdType: 1,
        entryLocation: "",
        crowdValue: "",
        crowdId: "",
        timeType:'1',
        circulateTime:{}
      };
      this.$refs.loopcirculateTime.circulateTime={}
      (this.currentCrowd || {}).crowdId = '';
    },
    async onUploadImage(res, file) {
      this.upImgLoading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.dataForm.image = res.data.url;
    },
    async uploadEditContImage(res, file) {
      this.editImgLoading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.editData.title = res.data.url;
    },
    confirmImg() {
      if (!this.dataForm.image) {
        this.$message.warning("请上传图片");
        return false;
      }
      this.closeAddPic();
      if (this.isEdit) {
        this.currentData = {};
        this.content.list.splice(this.currentIndex, 1, this.currentData);
      } else {
        this.content.list.push(Object.assign({}, this.dataForm));
      }
    },
    closeEditContent() {
      // this.editContent = false;
      this.add_editContent = false;
    },
    closeEditConfigContent() {
      this.add_editConfigContent = false;
    },
    add_confirmEditConfig() {
      if (!this.formData.entry) {
        this.$message.warning("请输入内容名称");
        return false;
      }
      if (!this.formData.crowdType) {
        this.$message.warning("请选择人群范围");
        return false;
      }
      if (this.formData.crowdType === 2 && !this.formData.crowdId) {
        this.$message.warning("请选择正确的人群");
        return false;
      }
      if (!this.formData.selectLocation) {
        this.$message.warning("请选择入口位置");
        return false;
      }
      this.closeEditConfigContent();
      let obj = {...this.formData}
      if (this.isFormContEdit) {
        this.content.configData.splice(this.configIndex, 1, obj);
      } else {
        this.content.configData.push(obj);
      }
    },
    async add_confirmEdit() {
      if (this.isBottom) {
        this.editData.entryLocation = '兜底';
      }
      // if (!this.editData.entryLocation) {
      //   this.$message.warning("请选择入口位置");
      //   return false;
      // }
      if (!this.isBottom && !this.editData.crowdType) {
        this.$message.warning("请选择人群范围");
        return false;
      }
      if (!this.isBottom && this.editData.crowdType === 2 && !this.editData.crowdId) {
        this.$message.warning("请选择正确的人群");
        return false;
      }
      // this.dataForm.crowdId = this.currentCrowd.id;
      if (!this.editData.title) {
        this.$message.warning("请上传图片");
        return false;
      }
      if (!this.editData.timevalue&&this.editData.timeType!=2) {
          this.$message.warning("请选择有效时间");
          return false;
        }
        if (this.editData.timeType==2&&(!this.editData.circulateTime||Object.keys(this.editData.circulateTime).length==0||!this.editData.circulateTime.circulateList||this.editData.circulateTime.circulateList.length==0)) {
          this.$message.warning("请添加【周期循环】 时间段。");
          return false;
        }
      if (!this.editData.entry) {
        this.$message.warning("请填写文字");
        return false;
      }
      if (!this.editData.link) {
        this.$message.warning("请填写链接");
        return false;
      }
      if (this.editData.linkType === 'topic' && !new RegExp("^ybmpage://commonh5activity.*$").test(((this.editData.link || {}).meta || {}).page_url)) {
        this.$message.error("跳转链接格式不正确，请检查");
        return false;
      }
      let linkErrMsg = '';
      if (this.editData.linkType === 'topic') {
        let linkPageUrl = getUrlParam(((this.editData.link || {}).meta || {}).page_url, 'url')
        const result = await api.topic.checkPageUrl({ url: linkPageUrl });
        if (((result || {}).data || {}).status != 200) {
          linkErrMsg = '跳转链接不存在，请检查';
        }
      }
      if (linkErrMsg) {
        this.$message.error(linkErrMsg);
        return false;
      }
      this.closeEditContent();
      this.psData = {
        title: this.editData.title,
        entry: `${this.editData.entry}`,
        frontColor: this.editData.frontColor,
        link: this.editData.link,
        timevalue: this.editData.timevalue,
        linkType: this.editData.linkType,
        crowdId: this.editData.crowdId,
        crowdType: this.editData.crowdType,
        crowdValue: this.editData.crowdValue,
        // entryLocation: this.editData.entryLocation,
        entryLocation: this.formData.selectLocation,
        timeType:this.editData.timeType,
        circulateTime:this.editData.circulateTime
      };
      if (this.isContEdit) {
        this.formData.list.splice(this.nowIndex, 1, this.psData);
      } else {
        // this.content.list.push(this.psData);
        // 此逻辑为了新增时相同词片在一起，排序不出错
        let lastIndex = '';
        this.formData.list.map((val, index) => {
          if(val.entryLocation == this.formData.selectLocation) {
            lastIndex = index;
          }
        });
        this.formData.list.splice(lastIndex+1, 0, this.psData);
      }
      this.formData.list = this.formData.list;
      this.renderListTemplate();
    },
    onSelect(val, isEditEntry) {
      if (isEditEntry == "editing") {
        if (val) {
          this.editData.frontColor = val;
        } else {
          this.editData.frontColor = "#000000";
        }
      } else {
        if (val) {
          this.content.bgRes = val;
        } else {
          this.content.bgRes = "#ffffff";
        }
      }
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.$set(this.content, 'bgImg', res.data.url);
      // this.content.bgImg = res.data.url;
    },
    imgOnclick() {
      this.content.bgImg = null;
      // this.$set(this.content, 'bgImg', null);
    },
    toColor16(str) {
      if (/^(rgb|RGB)/.test(str)) {
        var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
        var strHex = "#";
        for (var i = 0; i < aColor.length; i++) {
          var hex = Number(aColor[i]).toString(16);
          if (hex === "0") {
            hex += hex;
          }
          strHex += hex;
        }

        if (strHex.length !== 7) {
          strHex = str;
        }
        return strHex.toUpperCase();
      } else {
        return str;
      }
    },
    async optionFilter(val) {
        this.selectLoading = true;
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          this.selectLoading = false;
          this.options = [{
            label: data.name,
            value: val,
          }]
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCrowd(e) {
        if (e) {
          this.editData.crowdId = Number(this.options[0].value.trim());
          this.editData.crowdValue = this.options[0].label;
        } else {
          this.editData.crowdId = '';
          this.editData.crowdValue = '';
        }
        this.$forceUpdate();
      },
      selectFormDataCrowd(e) {
        if (e) {
          this.formData.crowdId = Number(this.options[0].value.trim());
          this.formData.crowdValue = this.options[0].label;
        } else {
          this.formData.crowdId = '';
          this.formData.crowdValue = '';
        }
        this.$forceUpdate();
      },
    // async querySearchCrowd(queryString, cb) {
    //   const pms = {
    //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
    //     dataType: "json",
    //     data: {},
    //     head: {
    //       "Content-Type": "application/json;charset=UTF-8"
    //     }
    //   };
    //   const res = await api.proxy.post(pms);
    //   if (res.success) {
    //     const { data } = res;
    //     cb([{
    //       id: queryString,
    //       value: data.name || ""
    //     }]);
    //     return false;
    //   }
    // },
    // handleSelectCrowd(item) {
    //   this.editData.crowdId = item.id;
    // },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss">
.tip {
  line-height: 40px;
  font-size: 16px;
  color: red;
}
.fast-entry {
  .fast-entry-btn {
    display: flex;
  }
  .container {
    display: flex;
    align-items: center;

    .img {
      width: 65%;

      img {
        display: block;
        width: 100%;
      }
    }

    .button-list {
      margin-left: 10px;
    }
  }

  .content-setting {
    color: #fff;
    background-color: #13c2c2;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .title-image {
    width: 64px;
    height: 64px;
  }

  .topic-image-upload {
    .image {
      display: block;
      width: 100%;
    }

    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }

  .entry-name {
    width: 70%;
  }

  // single-upload
  .uploader-btn-state {
    text-align: center;
  }

  .topic-image-picker {
    padding: 10px 0;
  }

  .el-table {
    .cell {
      text-align: center;
      padding: 0;
    }

    th .cell {
      color: #606266;
    }
  }

  .banner-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }

    .image {
      width: 64px;
      height: 64px;
    }
  }

  .no-img {
    font-size: 35px;
    display: block;
    color: #caccd0;
  }
}

.el-loading-spinner {
  top: auto !important;
  margin-top: auto !important;
}

.el-row {
  text-align: center;

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
.block {
  width: 100%;
  // display: flex;
  align-items: center;
  img {
    width: 60px;
    height: 30px;
  }
}
.opacityDiv {
  display: flex;
  flex-direction: column;
  >div {
    flex: 1;
    width: 100%;
  }
}
</style>
