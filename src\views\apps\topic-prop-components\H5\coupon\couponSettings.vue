<template>
  <el-dialog class="banner-dialog" :title="isEdit ? '编辑优惠券' : '添加优惠券'" :visible="true" :before-close="closeDialog">
    <p v-if="orient == 2">一排一个比例模板图：（710px）</p>
    <el-upload
      v-if="orient == 2"
      class="topic-image-upload"
      ref="upload"
      accept="image/jpeg,image/jpg, image/png, image/gif"
      :before-upload="() => {defImgloading = true; return true;}"
      :show-file-list="false"
      :on-success="onUploadDefultImage"
    >
      <img v-if="editData.defaultImg" :src="editData.defaultImg" class="image" />
      <i v-loading="defImgloading" v-else class="el-icon-plus uploader-icon"></i>
      <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
    </el-upload>
    <p v-if="orient == 2">一排二个比例模板图：（345px）</p>
    <el-upload
      class="topic-image-upload"
      ref="upload"
      accept="image/jpeg,image/jpg, image/png, image/gif"
      :before-upload="() => {loading = true; return true;}"
      :show-file-list="false"
      :on-success="onUploadImage"
    >
      <img v-if="editData.image" :src="editData.image" class="image" />
      <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
      <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
    </el-upload>
    <p v-if="orient == 2" style="color: #F59C29; marginBottom: 10px">*兜底需要，如果替换默认模板样式，需分别上传以上2种比例的模板图片</p>
    <ul class="file-list">
      <li v-for="(item,index) in fileList" :key="index">
        <img :src="item.url" alt width="100" height="50" @click="fileListChange(item)" />
      </li>
    </ul>
    <div class="blank_15"></div>
    <el-row :gutter="10" style="marginBottom: 5px;">
      <el-col :span="121" class="attr-right">关联优惠券ID：</el-col>
      <el-col :span="12">
        <el-select
          v-model="editData.couponTitle"
          :loading="selectLoading"
          filterable
          :filter-method="optionFilter"
          placeholder="请输入优惠券id"
          clearable
          @clear="options = []"
          @change="selectCoupon"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <!-- <el-autocomplete
          size="small"
          v-model.trim="editData.couponTitle"
          :fetch-suggestions="querySearchCoupon"
          placeholder="请输入优惠券id"
          :trigger-on-focus="false"
          @select="handleSelectCoupon"
        ></el-autocomplete> -->
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-row :gutter="10">
          <el-col :span="12" class="attr-right">价格:</el-col>
          <el-col :span="12">
            <el-input disabled size="small" v-model="editData.fob" placeholder="价格"></el-input>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="8">
        <el-row :gutter="10">
          <el-col :span="12" class="attr-right">优惠信息</el-col>
          <el-col :span="12">
            <el-input disabled size="small" v-model="editData.info" placeholder="优惠信息"></el-input>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="8">
        <el-row :gutter="10">
          <el-col :span="12" class="attr-right">优惠券背景颜色</el-col>
          <el-col :span="12">
            <el-color-picker size="small" v-model="editData.couponBgColor"></el-color-picker>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-row :gutter="10">
          <el-col :span="12" class="attr-right">价格颜色</el-col>
          <el-col :span="12">
            <el-color-picker size="small" v-model="editData.fobColor"></el-color-picker>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="8">
        <el-row :gutter="10">
          <el-col :span="12" class="attr-right">优惠券信息颜色</el-col>
          <el-col :span="12">
            <el-color-picker size="small" v-model="editData.infoColor"></el-color-picker>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="8">
        <el-row :gutter="10">
          <el-col :span="12" class="attr-right">优惠券类型颜色</el-col>
          <el-col :span="12">
            <el-color-picker size="small" v-model="editData.typeColor"></el-color-picker>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <el-row :gutter="10">
          <el-col :span="12" class="attr-right">立即领取颜色</el-col>
          <el-col :span="12">
            <el-color-picker size="small" v-model="editData.centerColor"></el-color-picker>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="closeDialog">取 消</el-button>
      <el-button size="small" type="primary" @click="confirmAdd">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
  import { AppWebsite } from "config";
  import api from 'api';
  export default {
    name: 'couponSettings',
    props:["dataForm", "editIndex", "fileList", "isEdit", "orient"],
    data() {
      return {
        loading: false,
        defImgloading: false,
        editData: {},
        defultImgList: [],
        options: [],
        selectLoading: false,
        couponObj: {},
      };
    },
    mounted () {
      this.editData = JSON.parse(JSON.stringify(this.dataForm));
    },
    methods: {
      closeDialog() {
        this.$parent.closeAddDialog();
      },
      async onUploadImage(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: "warning"
          });
          return;
        }
        this.editData.image = res.data.url;
      },
      async onUploadDefultImage(res, file) {
        this.defImgloading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: "warning"
          });
          return;
        }
        this.editData.defaultImg = res.data.url;
      },
      fileListChange(row) {
        this.editData.image = row.url;
        if (this.orient == 2) {
          this.editData.defaultImg = row.url;
        }
      },
      async optionFilter(val) {
        this.selectLoading = true;
        const res = await api.stores.searchCoupon([val]);
        const { data } = res;
        if (data && data.success) {
          this.selectLoading = false;
          if (!Object.keys(data.data).length) {
            return false;
          }
          const { list } = data.data;
          if (list && list.length) {
            this.couponObj = list[0];
            this.options = [{
              label: list[0].voucherTitle || list[0].templateName,
              value: val,
            }]
          } else {
            this.options = []
          }
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCoupon(e) {
        if (e) {
          this.editData = Object.assign(this.editData, this.couponObj);
          this.editData.fob = this.couponObj.voucherState === 1 ? this.couponObj.discount : this.couponObj.moneyInVoucher;
          this.editData.info = `${this.couponObj.voucherUsageWay === 1 ? '每满' : '满'}${this.couponObj.minMoneyToEnable}可用`;
          this.editData.couponTitle = this.couponObj.voucherTitle || this.couponObj.templateName;
          this.editData.templateId = this.couponObj.templateId;
          this.$set(this.editData, 'customerGroupName', this.couponObj.customerGroupName);
        } else {
          this.editData.couponTitle = '';
          this.editData.templateId = '';
          this.editData.fob = "56"; //优惠券价格
          this.editData.info = "满65000元可用"; //优惠信息
          this.editData.customerGroupName = '';
        }
      },
      // async querySearchCoupon(queryString, cb) {
      //   const res = await api.stores.searchCoupon([queryString]);
      //   const { data } = res;
      //   if (data && data.success) {
      //     if (!Object.keys(data.data).length) {
      //       return false;
      //     }
      //     const { list } = data.data;
      //     if (list && list.length) {
      //       const curr = list[0];
      //       curr.id = curr.queryString || curr.templateId;;
      //       curr.value = curr.voucherTitle;
      //       cb([curr]);
      //     }
      //   }
      // },
      // handleSelectCoupon(curr) {
      //   this.editData = Object.assign(this.editData, curr);
      //   this.editData.fob = curr.voucherState === 1 ? curr.discount : curr.moneyInVoucher;
      //   this.editData.info = `${curr.voucherUsageWay === 1 ? '每满' : '满'}${curr.minMoneyToEnable}可用`;
      //   this.editData.couponTitle = curr.voucherTitle;
      //   this.editData.templateId = curr.templateId;
      //   this.$set(this.editData, 'customerGroupName', curr.customerGroupName);
      // },
      
      //确定添加广告
      confirmAdd() {
        if (!this.editData.image || (this.orient === 2 && !(this.editData.defaultImg && this.editData.image))) {
          this.$message.error('请完成图片模板上传');
          return;
        }
        this.psData = {
          image: this.editData.image,
          defaultImg: this.editData.defaultImg,
          templateId: this.editData.templateId,
          couponTitle: this.editData.couponTitle,
          customerGroupName: this.editData.customerGroupName,
          fob: this.editData.fob,
          info: this.editData.info,
          couponBgColor: this.editData.couponBgColor,
          fobColor: this.editData.fobColor,
          infoColor: this.editData.infoColor,
          typeColor: this.editData.typeColor,
          centerColor: this.editData.centerColor,
          };
        if (this.isEdit) {
          this.$emit('saveDialog','edit', this.psData, this.editIndex )
        } else {
          this.$emit("saveDialog", 'add', this.psData);
        }
      },

    }
  };
 
</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .topic-image-upload {
    margin: 20px 0;
    .image {
      display: block;
      width: 100%;
    }
    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }
</style>
