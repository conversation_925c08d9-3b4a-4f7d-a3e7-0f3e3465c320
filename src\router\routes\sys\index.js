const Sys = r => require.ensure([], () => r(require('views/sys')), 'sys')

/*
* 账号管理
* */
const  Account = r => require.ensure([], () => r(require('views/sys/account')), 'sys')
const  Role = r => require.ensure([], () => r(require('views/sys/role')), 'sys')
const  Org = r => require.ensure([], () => r(require('views/sys/org/index')), 'sys')
const  addUser = r => require.ensure([], () => r(require('views/sys/org/user-add')), 'sys')
const  UserInfo = r => require.ensure([], () => r(require('views/sys/org/user-info')), 'sys')

export default [
  { path: '/sys',
    component: Sys,
    children: [
      { path: 'account', name: 'account', component: Account },
      { path: 'role', name: 'role', component: Role },
      { path: 'org', name: 'org', component: Org },
      { path: 'org/addUser', name: 'addUser', component: addUser },
      { path: 'org/user/:id', name: 'sysUserInfo', component: UserInfo },
      { path: 'org/user/:id/edit', name: 'editUser', component: addUser },
    ]
  },
]
