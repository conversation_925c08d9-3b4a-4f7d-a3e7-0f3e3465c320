<template>
  <div class="bottom-fast-entry">
    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">背景色</span>
          <el-color-picker v-model="content.bgRes" size="mini" @change="onSelect"></el-color-picker>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <div>
            <el-button @click="imgOnclick">清除背景图</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg, image/jpg, image/png, image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
    </el-row>

    <!--添加快捷入口-->
    <el-row :gutter="20">
      <div class="title">添加快捷入口</div>
      <!-- <el-col :span="12">
        <div class="block">
          <el-button class="btn-block" type="primary" @click="add_editContent=true">添加快捷入口</el-button>
        </div>
      </el-col>-->
      <el-col :span="12" v-if="!content.new_layout">
        <div class="block">
          <span>快捷入口个数</span>
          <el-select v-model="content.list_num" placeholder="选择快捷入口个数">
            <el-option
              v-for="item in select_option"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </el-col>
    </el-row>

    <el-table :data="list" size="mini" :row-key="getRowKeys" style="width: 100%;marginTop: 10px;">
      <el-table-column type="index" width="50"></el-table-column>
      <el-table-column prop="title" label="标题">
        <template slot-scope="scope">
          <img v-if="scope.row.title" :src="scope.row.title" alt="图" class="title-image" />
          <i v-else class="el-icon-circle-plus-outline no-img"></i>
        </template>
      </el-table-column>

      <el-table-column prop="entry" label="有效时间">
        <template slot-scope="scope">
          <span v-html="format_text(scope.row.timevalue)"></span>
        </template>
      </el-table-column>

      <el-table-column prop="entry" label="入口名称">
        <template slot-scope="scope">
          <span v-html="scope.row.entry" class="entry-name" :style="{'color':scope.row.frontColor}"></span>
        </template>
      </el-table-column>

      <el-table-column prop="entry" label="链接" :show-overflow-tooltip="true" width="120px">
        <template slot-scope="scope">
          <span>{{ scope.row.link.meta.page_url }}</span>
          <!-- <el-input
            type="text"
            size="mini"
            v-model="scope.row.link.meta.page_url"
          >{{scope.row.link.meta.page_url}}</el-input> -->
        </template>
      </el-table-column>

      <el-table-column label="链接类型">
        <template slot-scope="scope">
          <span>{{scope.row.linkType==='stores'?'店铺链接':'专题页链接'}}</span>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.operation =='1'"
            size="mini"
            @click="toEdit(scope.row, scope.$index)"
            type="primary"
          >编辑</el-button>
          <el-button size="mini" v-else @click="toEdit(scope.row, scope.$index)" type="primary">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 背景图上传弹层 -->
    <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addPic" width="30%">
      <el-upload
        class="topic-image-upload uploader-btn-state"
        ref="upload"
        accept="image/jpeg, image/jpg, image/png, image/gif"
        :show-file-list="false"
        :before-upload="() => {upImgLoading = true; return true;}"
        :on-success="onUploadImage"
      >
        <img v-if="dataForm.image" :src="dataForm.image" class="image" />
        <i v-else v-loading="upImgLoading" class="el-icon-plus uploader-icon"></i>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeAddPic">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmImg">确定</el-button>
      </div>
    </el-dialog>
    <!-- 编辑内容弹层 -->
    <el-dialog class="banner-dialog" title="编辑入口" :visible.sync="editContent">
      <el-form ref="form" :model="editData" label-width="120px">
        <el-form-item label="菜单ICON：">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg, image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="() => {editImgLoading = true; return true;}"
            :on-success="uploadEditContImage"
          >
            <img v-if="editData.title" :src="editData.title" class="image" />
            <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </el-form-item>

        <el-form-item label="有效时间：">
          <el-date-picker
            v-model="editData.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="入口名称：">
          <el-input v-model="editData.entry" class="entry-name"></el-input>
        </el-form-item>

        <el-form-item label="文字颜色：">
          <el-color-picker
            v-model="editData.frontColor"
            size="mini"
            @active-change="onSelect('editing')"
          ></el-color-picker>
        </el-form-item>

        <el-form-item label="链接类型：">
          <el-select v-model="editData.linkType" placeholder="请选择">
            <el-option
              v-for="item in linkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div class="topic-image-picker" v-if="editData.linkType !== 'dynamic'">
        <el-input placeholder="链接地址" v-model.trim="editData.link.meta.page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="editData.linkType === 'dynamic'">
        <div class="topic-image-picker">
          <el-input style="width:200px" placeholder="输入跳转id" v-model="editData.link.meta.dynamicId">
            <template slot="prepend">跳转id</template>
          </el-input>
          <el-button type="primary" @click="putDynamicLink(editData)">生成链接</el-button>
        </div>
        <el-input placeholder="链接地址" v-model.trim="editData.link.meta.page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="editData.linkType==='topic'">
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditContent">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmEdit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 添加内容弹层 -->
    <el-dialog class="banner-dialog" title="添加入口" :visible.sync="add_editContent">
      <el-form ref="form" :model="editData" label-width="120px">
        <el-form-item label="菜单ICON：">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg, image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="() => {editImgLoading = true; return true;}"
            :on-success="uploadEditContImage"
          >
            <img v-if="editData.title" :src="editData.title" class="image" />
            <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="有效时间：">
          <el-date-picker
            v-model="editData.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="入口名称：">
          <el-input v-model="editData.entry" class="entry-name"></el-input>
        </el-form-item>
        <el-form-item label="文字颜色：">
          <el-color-picker v-model="editData.frontColor" size="mini" @change="onSelect('editing')"></el-color-picker>
        </el-form-item>

        <el-form-item label="链接类型：">
          <el-select v-model="editData.linkType" placeholder="请选择">
            <el-option
              v-for="item in linkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div class="topic-image-picker" v-if="editData.linkType !== 'dynamic'">
        <el-input placeholder="链接地址" v-model="editData.link.meta.page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="editData.linkType === 'dynamic'">
        <div class="topic-image-picker">
          <el-input style="width:200px" placeholder="输入跳转id" v-model="editData.link.meta.dynamicId">
            <template slot="prepend">跳转id</template>
          </el-input>
          <el-button type="primary" @click="putDynamicLink(editData)">生成链接</el-button>
        </div>
        <el-input placeholder="链接地址" v-model="editData.link.meta.page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="editData.linkType==='topic'">
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditContent">取 消</el-button>
        <el-button size="small" type="primary" @click="add_confirmEdit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import base from "../base";
import event_bus from "../../../../utils/eventbus";
import api from "api";
import { getUrlParam } from "config";

export default {
  extends: base,
  contentDefault: {
    list: [
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/3faadb73-cd5a-4567-9176-bdb75a14e64c.png",
        entry: "高毛专区",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 1,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/highmarginnew?ybm_title=高毛专区"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/48a38774-bbec-40f4-9963-16067c8aa1da.png",
        entry: "新品上架",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 2,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/newpro?ybm_title=新品上架"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/06c30e6a-6c0b-4c0b-9720-07ec191638b7.png",
        entry: "诊所专区",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 3,
            page_url: "ybmpage://clinicactivity?umkey=zszq"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/4b6e4ea1-4bef-4fa3-901b-34d4d64aa645.png",
        entry: "领券中心",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 4,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/voucherscenter?ybm_title=领券中心"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/2d40238a-1fba-4c7d-bffe-8a0c293786ea.png",
        entry: "品牌推荐",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 5,
            page_url:
              "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/static/xyyvue/dist/#/recommendedbrands?ybm_title=品牌推荐"
          }
        }
      }
    ],
    bgRes: "",
    color: "#ffffff",
    image: "",
    list_num: 5
  },
  data() {
    return {
      linkOptions: [
        {
          value: "topic",
          label: "专题页链接"
        },
        {
          value: "stores",
          label: "店铺页链接"
        },
        {
          value: "dynamic",
          label: "动态商品链接"
        }
      ],
      loading: false,
      upImgLoading: false,
      editImgLoading: false,
      addPic: false,
      editContent: false,
      add_editContent: false,
      editCont: "",
      dataForm: {
        image: ""
      },
      editData: {
        linkType: "topic",
        title: "",
        entry: "",
        frontColor: "#000000",
        operation: "",
        timevalue: "",
        link: {
          meta: {
            page_url: ""
          }
        }
      },
      select_option: [
        {
          value: 2,
          label: "一行2个"
        },
        {
          value: 3,
          label: "一行3个"
        },
        {
          value: 4,
          label: "一行4个"
        },
        {
          value: 5,
          label: "一行5个"
        }
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      }
    };
  },
  computed: {
    list() {
      var list = _.get(this, "content.list");
      if (list) {
        this.$nextTick(function() {
          this.setSort();
        });
        return list;
      } else {
        return [];
      }
    }
  },
  filters: {
    link(data) {
      if (!data.type) {
        return "";
      }
      return data.meta.page_url;
    }
  },
  methods: {
    putDynamicLink(item) {
      if (!item.link.meta.dynamicId) {
        this.$message({
          message: "请输入跳转id再点击生成链接",
          type: "warning"
        });
        return false;
      }
      item.link.meta.page_url = `ybmpage://homeSteadyChannel?strategyId=${item.link.meta.dynamicId}&title=${item.entry}`;
    },
    format_text(data) {
      if (!data) {
        return "<b style='color: red'>请设置时间</b>";
      }
      const _date = new Date().getTime();
      const start = new Date(data[0]).getTime();
      const end = new Date(data[1]).getTime();
      if (_date <= end && _date >= start) {
        return "<b style='color: #67C23A'>展示中</b>";
      } else if (_date < start) {
        return `<b style='color: #000000'>即将在${new Date(
          start
        ).toLocaleDateString()}展示</b>`;
      } else {
        return "<b style='color: #000000'>已过期</b>";
      }
    },
    formatTooltip(val) {
      return val / 100;
    },
    closeAddPic() {
      this.addPic = false;
    },
    onSetLink(link) {
      this.editData.link = link;
    },
    toEdit(data, index, status) {
      if (status == "1") {
        //新建时清空
        this.editData.title = "";
        this.editData.timevalue = "";
        this.editData.entry = "";
        this.editData.frontColor = "#000";
        this.editData.link = {
          meta: {
            page_url: ""
          }
        };
      } else {
        this.editData = JSON.parse(JSON.stringify(data));
        // this.editData.entry = data.entry;
        // this.editData.timevalue = data.timevalue;
        // this.editData.title = data.title;
        // this.editData.frontColor = data.frontColor;
        // this.editData.link = data.link;
        // this.editData.linkType = data.linkType ? data.linkType : "topic";
      }

      this.editContent = true;
      this.nowData = data;
      this.nowIndex = index;
      this.isContEdit = true;
      this.addContPic = true;
    },
    toAdd() {
      this.isEdit = false;
      this.dataForm = {
        image: ""
      };
      this.addPic = true;
    },
    async onUploadImage(res, file) {
      this.upImgLoading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.dataForm.image = res.data.url;
    },
    async uploadEditContImage(res, file) {
      this.editImgLoading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.editData.title = res.data.url;
    },
    confirmImg() {
      if (!this.dataForm.image) {
        this.$message.warning("请上传图片");
        return false;
      }
      this.closeAddPic();
      if (this.isEdit) {
        this.currentData = {};
        this.list.splice(this.currentIndex, 1, this.currentData);
      } else {
        this.list.push(Object.assign({}, this.dataForm));
      }
    },
    closeEditContent() {
      this.editContent = false;
      this.add_editContent = false;
    },
    async confirmEdit() {
      if (!this.editData.title) {
        this.$message.warning("请上传图片");
        return false;
      }

      if (!this.editData.entry) {
        this.$message.warning("请填写文字");
        return false;
      }

      if (!this.editData.link) {
        this.$message.warning("请填写链接");
        return false;
      }
      let linkErrMsg = '';
      if (this.editData.linkType === 'topic' && this.editData.link.meta.page_url) {
        if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.editData.link.meta.page_url)) {
          linkErrMsg = '跳转链接格式不正确，请检查';
        } else {
          let linkPageUrl = getUrlParam(this.editData.link.meta.page_url, 'url');
          const result = await api.topic.checkPageUrl({ url: linkPageUrl });
          if (((result || {}).data || {}).status != 200) {
            linkErrMsg = '跳转链接不存在，请检查';
          }
        }
      }
      if (linkErrMsg) {
        this.$message.error(linkErrMsg);
        return false;
      }
      this.closeEditContent();
      if (this.isContEdit) {
        // entry: document.querySelector('.ql-editor').innerText,//只取文本
        this.psData = {
          title: this.editData.title,
          timevalue: this.editData.timevalue,
          entry: `${this.editData.entry}`,
          frontColor: this.editData.frontColor,
          link: this.editData.link,
          linkType: this.editData.linkType
        };
        this.list.splice(this.nowIndex, 1, this.psData);
      } else {
        this.list.push(Object.assign([], this.list));
      }
    },
    async add_confirmEdit() {
      if (!this.editData.title) {
        this.$message.warning("请上传图片");
        return false;
      }

      if (!this.editData.entry) {
        this.$message.warning("请填写文字");
        return false;
      }

      if (!this.editData.link) {
        this.$message.warning("请填写链接");
        return false;
      }
      let linkErrMsg = '';
      if (this.editData.linkType === 'topic' && this.editData.link.meta.page_url) {
        if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.editData.link.meta.page_url)) {
          linkErrMsg = '跳转链接格式不正确，请检查';
        } else {
          let linkPageUrl = getUrlParam(this.editData.link.meta.page_url, 'url');
          const result = await api.topic.checkPageUrl({ url: linkPageUrl });
          if (((result || {}).data || {}).status != 200) {
            linkErrMsg = '跳转链接不存在，请检查';
          }
        }
      }
      if (linkErrMsg) {
        this.$message.error(linkErrMsg);
        return false;
      }
      
      this.closeEditContent();
      this.psData = {
        title: this.editData.title,
        entry: `${this.editData.entry}`,
        frontColor: this.editData.frontColor,
        link: this.editData.link,
        timevalue: this.editData.timevalue,
        linkType: this.editData.linkType
      };
      this.list.push(this.psData);
    },
    onSelect(val, isEditEntry) {
      if (isEditEntry == "editing") {
        if (val) {
          this.editData.frontColor = val;
        } else {
          this.editData.frontColor = "#000000";
        }
      } else {
        if (val) {
          this.content.bgRes = val;
        } else {
          this.content.bgRes = "#ffffff";
        }
      }
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.bgRes = res.data.url;
    },
    imgOnclick() {
      this.content.bgRes = "#ffffff";
    },
    toColor16(str) {
      if (/^(rgb|RGB)/.test(str)) {
        var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
        var strHex = "#";
        for (var i = 0; i < aColor.length; i++) {
          var hex = Number(aColor[i]).toString(16);
          if (hex === "0") {
            hex += hex;
          }
          strHex += hex;
        }

        if (strHex.length !== 7) {
          strHex = str;
        }
        return strHex.toUpperCase();
      } else {
        return str;
      }
    }
  },
  watch: {
    add_editContent(new_val) {
      this.editData = {
        linkType: "topic",
        title: "",
        timevalue: "",
        entry: "",
        frontColor: "#000000",
        operation: "",
        link: {
          meta: {
            page_url: ""
          }
        }
      };
    }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss">
.tip {
  line-height: 40px;
  font-size: 16px;
  color: red;
}
.bottom-fast-entry {
  .container {
    display: flex;
    align-items: center;

    .img {
      width: 65%;

      img {
        display: block;
        width: 100%;
      }
    }

    .button-list {
      margin-left: 10px;
    }
  }

  .content-setting {
    color: #fff;
    background-color: #13c2c2;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .title-image {
    width: 64px;
    height: 64px;
  }

  .topic-image-upload {
    .image {
      display: block;
      width: 100%;
    }

    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }

  .entry-name {
    width: 70%;
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  // single-upload
  .uploader-btn-state {
    text-align: center;
  }

  .topic-image-picker {
    padding: 10px 0;
  }

  .el-table {
    .cell {
      text-align: center;
      padding: 0;
    }

    th .cell {
      color: #606266;
    }
  }

  .banner-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }

    .image {
      width: 64px;
      height: 64px;
    }
  }

  .no-img {
    font-size: 35px;
    display: block;
    color: #caccd0;
  }
}

.el-loading-spinner {
  top: auto !important;
  margin-top: auto !important;
}

.el-row {
  text-align: center;

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
</style>
