<template>
    <div class="upload-icon">
        <el-upload
                class="avatar-uploader"
                :on-success="uploadImage"
                :show-file-list="false"
        >
            <img v-if="tImage" :src="tImage" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
    </div>
</template>

<script>
    export default {
        name: "uploadImage",
        props: ['index', 'image'],
        data() {
            return {
                loading: false,
                tImage: this.image || '',
            }
        },
        watch:{
            image(new_val){
                if(new_val){
                    this.tImage=new_val
                }
            }
        },
        methods: {
            uploadImage(res, file) {
                this.tImage = res.data.url;
                this.$emit('listenImage', {index: this.index, image: this.tImage})
            }
        }
    }
</script>

<style scoped lang="scss">
    .upload-icon {
        @include flexbox($rowRank:space-around);
    }

    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 50px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        border: 1px dotted #ccc;
    }

    .avatar {
        width: 50px;
        height: 50px;
        display: block;
    }
</style>
