<template>
  <div class="main-content">
    <el-row class="mb-10">
      <el-col :span="24">
        <el-row class="search-wrap" type="flex" :gutter="12">
          <el-popover placement="top" trigger="hover">
            <el-button
              @click="cancel()"
              :loading="loading"
              type="info"
              icon="el-icon-refresh"
              size="mini"
              plain
            >清空</el-button>
            <el-button
              @click="changeSize()"
              slot="reference"
              :loading="loading"
              type="primary"
              icon="el-icon-search"
              size="mini"
              plain
            >查询</el-button>
          </el-popover>
          <el-select v-model.number="searchParam.state" placeholder="状态" size="mini" clearable>
            <el-option v-for="(item, i) in state" :value="Number(i)" :key="i" :label="item"></el-option>
          </el-select>
          <el-select
            v-model="searchParam.branchCode"
            v-if="true"
            placeholder="相关域"
            @change="handleChange()"
            size="mini"
            default-first-option
            filterable
            clearable
          >
            <el-option
              v-for="(item, i) in branchs"
              :key="i"
              :value="item.branchCode"
              :label="item.branchName"
            ></el-option>
          </el-select>
          <el-input v-model="searchParam.name" placeholder="商品组名" size="mini" clearable></el-input>
          <el-input v-model="searchParam.code" placeholder="商品组ID" size="mini" clearable></el-input>
          <el-col :span="12">
            <el-button-group>
              <!--                            <el-button type="primary" size="mini" icon="el-icon-plus" @click="showModal()">添加</el-button>-->
              <el-popover>
                <el-button-group>
                  <el-button @click="del({state: -1})" type="danger" size="mini" round plain>禁用状态</el-button>
                  <el-button
                    @click="createTimeDelShow = !createTimeDelShow"
                    type="danger"
                    size="mini"
                    round
                    plain
                  >时间段</el-button>
                </el-button-group>
                <!--                                <el-button slot="reference" type="danger" size="mini" v-show="isAdmin" icon="el-icon-delete">删除</el-button>-->
              </el-popover>
              <el-button
                type="primary"
                :disabled="isClick1"
                size="mini"
                @click="updateMongGoodGroups()"
              >同步数据</el-button>
              <el-button
                type="primary"
                :disabled="isClick2"
                size="mini"
                @click="updateRedisGoodGroups()"
              >更新缓存</el-button>
            </el-button-group>
            <!-- 时间段删除弹窗 -->
            <el-dialog :visible.sync="createTimeDelShow" title="选择删除时间段" width="30%">
              <el-form label-position="right" size="mini" label-width="100px" label-suffix="：">
                <el-form-item label="大于等于">
                  <el-date-picker
                    class="input-class"
                    v-model="createTimeDel._$gte"
                    size="mini"
                    type="datetime"
                    placeholder=">=起始段"
                    default-time="00:00:00"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item label="小于等于">
                  <el-tooltip effect="light">
                    <div slot="content">
                      结束时间
                      <b>不能小于</b>起始时间
                    </div>
                    <el-date-picker
                      class="input-class"
                      v-model="createTimeDel._$lte"
                      size="mini"
                      type="datetime"
                      placeholder="<=结束段"
                      default-time="23:59:59"
                    ></el-date-picker>
                  </el-tooltip>
                </el-form-item>
              </el-form>
              <span slot="footer" class="dialog-footer">
                <el-button @click="createTimeDelShow = false" size="mini">取 消</el-button>
                <el-button
                  @click="crtTimeDel({create_time: createTimeDel});"
                  type="primary"
                  size="mini"
                >确 定</el-button>
              </span>
            </el-dialog>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <!-- 列表 -->
    <el-table
      :data="dataList"
      v-loading="loading"
      :row-class-name="tabRowCla"
      class="custom-table"
      size="mini"
      border
    >
      <div slot="empty" class="empty-wrap">
        <i class="iconfont icon-tishi"></i>
        <span>未获取到商品组</span>
      </div>
      <el-table-column type="index" width="50%" align="right"></el-table-column>
      <el-table-column
        prop="code"
        :show-overflow-tooltip="true"
        label="商品组ID"
        width="300"
        align="left"
      ></el-table-column>
      <el-table-column
        prop="name"
        :show-overflow-tooltip="true"
        label="商品组名"
        width="280%"
        align="center"
      ></el-table-column>
      <el-table-column prop="goods.length" label="商品数" width="80%" align="right">
        <template slot-scope="scope">
          <el-tooltip content="点击查看" placement="left" :open-delay="600">
            <el-badge
              @click.native="goodsShow(scope.row)"
              :value="scope.row.goods.length"
              class="item goods-num"
              type="success"
            ></el-badge>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="branchCode" label="相关域" width="200" align="center">
        <template
          slot-scope="scope"
        >{{ $options.filters.getBranchName(scope.row.branchCode, branchs) }}</template>
      </el-table-column>
      <el-table-column label="店铺名称"  align="center">
        <template slot-scope="scope">{{ scope.row.shopName}}</template>
      </el-table-column>
      <el-table-column prop="state" label="状态" width="80%" align="center">
        <template slot-scope="scope">
          <span
            v-bind:class="{active: (scope.row.state === 1)}"
          >{{ $options.filters.stateTip(scope.row.state, state) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="create_name" label="创建人" width="150%" align="center"></el-table-column> -->
      <el-table-column prop="create_time" label="创建时间" width="250">
        <template slot-scope="scope">{{ scope.row.create_time | dateFmt }}</template>
      </el-table-column>
      <!-- <el-table-column prop="update_name" label="更新人" width="150%" align="center"></el-table-column> -->
      <!-- <el-table-column prop="update_time" label="更新时间" width="150%">
        <template slot-scope="scope">{{ scope.row.update_time | dateFmt }}</template>
      </el-table-column> -->
      <!-- <el-table-column
        prop="remark"
        :show-overflow-tooltip="true"
        label="备注"
        min-width="80"
        align="left"
      ></el-table-column> -->
      <!--            <el-table-column-->
      <!--                    fixed="right"-->
      <!--                    label="操作"-->
      <!--                    width="300%"-->
      <!--                    align="center">-->
      <!--                <template slot-scope="scope">-->
      <!--                    <el-popover :disabled="!isAdmin" placement="left" trigger="hover" :open-delay="800" popper-class="myHover">-->
      <!--                        <el-button @click="del({id: scope.row.id, name: scope.row.name})" v-if="isAdmin" type="danger" size="mini" plain>删除</el-button>-->
      <!--                        <div slot="reference">-->
      <!--                            <el-button @click="showModal(scope.row)" type="primary" :plain="true" size="mini">编辑</el-button>-->
      <!--                            <el-button @click="copy(scope.row)" type="primary" :plain="true" size="mini" v-if="branchs.length > 1">复制</el-button>-->
      <!--                            <el-button @click="updateState(scope.row)" size="mini" plain>启用 / 禁用</el-button>-->
      <!--                        </div>-->
      <!--                    </el-popover>-->
      <!--                </template>-->
      <!--            </el-table-column>-->
    </el-table>
    <el-pagination
      background
      v-show="totalSize > 10"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      @size-change="changeSize"
      @current-change="changePage"
      layout="total, sizes, slot, jumper, prev, pager, next"
      :total="totalSize"
    ></el-pagination>

    <el-dialog
      @closed="goodsTabClose"
      :visible.sync="goodsTab.show"
      :title="`“${goodsTab.group.name}”商品信息`"
      center
    >
      <el-table
        :data="goodsTab.data"
        max-height="300"
        class="custom-table"
        size="mini"
        highlight-current-row
        border
      >
        <el-table-column type="index" width="35%" align="right"></el-table-column>
        <el-table-column
          prop="code"
          :show-overflow-tooltip="true"
          label="条码"
          width="120%"
          align="right"
        ></el-table-column>
        <el-table-column prop="showName" :show-overflow-tooltip="true" label="商品名称" align="center"></el-table-column>
        <el-table-column label="图片" width="50%" align="center">
          <template slot-scope="scope">
            <el-popover placement="right-end" trigger="focus">
              <img :src="scope.row.imageUrl" class="avatar" alt="图片" />
              <img slot="reference" :src="scope.row.imageUrl" class="avatar goods-img-min" />
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          prop="mediumPackageTitle"
          :show-overflow-tooltip="true"
          label="包装"
          width="120%"
          align="left"
        ></el-table-column>
        <el-table-column prop="availableQty" label="库存数" align="right" width="60%"></el-table-column>
        <el-table-column prop="isPromotion" label="促销" align="center" width="80%">
          <template slot-scope="scope">{{ $options.filters.isPromotion(scope.row.isPromotion)}}</template>
        </el-table-column>
        <el-table-column prop="isFragileGoods" label="易碎品" align="center" width="80%">
          <template
            slot-scope="scope"
          >{{ $options.filters.isFragileGoods(scope.row.isFragileGoods)}}</template>
        </el-table-column>
        <el-table-column prop="productType" label="秒杀" align="center" width="80%">
          <template slot-scope="scope">{{ $options.filters.isProductType(scope.row.productType)}}</template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center" width="60%">
          <template slot-scope="scope">{{ $options.filters.stateTip(scope.row.status, gosStatus) }}</template>
        </el-table-column>
        <el-table-column prop="channelCodes" label="渠道" align="center" >
          <template
            slot-scope="scope"
          >{{ $options.filters.channelCodesFilter(scope.row.channelCodes) }}</template>
        </el-table-column>
      </el-table>
      <el-pagination
        small
        :total="goodsTab.page.total"
        :page-size="goodsTab.page.pageSize"
        :current-page="goodsTab.page.pageNum"
        layout="prev, pager, next, jumper"
        @current-change="goodsPage"
      ></el-pagination>
    </el-dialog>
    <edit-modal
      ref="goodsGroupModal"
      :goodsGroupData="goodsGroupData"
      @close="closeModal"
      @save-done="loadData"
    ></edit-modal>
    <copy-modal
      ref="goodsGroupCopyModal"
      :goodsGroupData="goodsGroupData"
      @close="closeModal"
      @save-done="loadData"
    ></copy-modal>
  </div>
</template>
<script>
import api from "api";
import editModal from "./edit";
import copyModal from "./copy";
import Page4array from "../../../../utils/page4array";
import { fetch, getDate } from "../../../../utils/time-format";
import bus from "utils/eventbus";

export default {
  name: "goodsGroup",
  data() {
    return {
      currentPage: 1,
      pageFrom: 1,
      pageSize: 10,
      totalSize: 10,
      loading: false,
      isAdmin: false,
      dataList: [],
      goodsGroupData: {
        data: {}
      },
      searchParam: {
        branchCode: '',
        code: '',
        name: '',
      },
      createTimeDelShow: false,
      createTimeDel: {},
      goodsTab: {
        show: false,
        data: null,
        group: {},
        page: {}
      },
      state: null,
      branchs: null,
      gosStatus: null,
      loginUser: null,
      isClick1: false,
      isClick2: false
    };
  },
  components: {
    editModal,
    copyModal
  },
  created() {
    bus.$on("change_branch", this.changeBranch);
  },
  filters: {
    //渠道
    channelCodesFilter(val) {
      if (Array.isArray(val)) {
        if (val.length == 0) {
          return "无";
        }
        if (val.indexOf("1") != -1 && val.indexOf("2") != -1) {
          return "b2b,壹块钱";
        } else if (val.indexOf("1") != -1) {
          return "b2b";
        } else if (val.indexOf("2") != -1) {
          return "壹块钱";
        }
        return "未知";
      }
      return "无";
    },
    dateFmt(date) {
      return date ? getDate(date) : "";
    },
    stateTip(status, dict) {
      return !dict ? "未知" : dict[status];
    },
    isPromotion(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isFragileGoods(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isProductType(val) {
      if (val == 2) {
        return "是";
      } else {
        return "否";
      }
    },
    getBranchName(code, branchs) {
      let branchName = "";
      if (!code || !branchs || !branchs.length) return branchName;
      for (let i = 0, len = branchs.length; i < len; i++) {
        let branch = branchs[i];
        if (branch.branchCode == code) {
          branchName = branch.branchName;
          break;
        }
      }
      return branchName;
    }
  },
  watch: {
    createTimeDel: {
      deep: true,
      handler(val, oldVal) {
        if (val._$gte && !(!val._$gte ^ !val._$lte)) {
          //两时间段都有值
          if (val._$lte.getTime() < val._$gte.getTime()) val._$lte = val._$gte;
        }
      }
    }
  },
  mounted() {
    this.dict().then(data => {
      this.loadData();
      this.$store.dispatch("breadcrumb/clearPath");
      this.$store.dispatch("breadcrumb/addPath", {
        title: "商品组管理",
        subTitle: "商品组管理",
        action: "goodsGroup"
      });
    });
    bus.$emit("menu_type", "other-商品组管理");
  },
  methods: {
    changeBranch(item) {
      this.searchParam.branchCode = item.branchCode;
      this.pageFrom = 1;
      this.pageSize = 10;
      this.loadData();
    },
    async loadData() {
      this.loading = true;
      let pms = {
        pageFrom: this.pageFrom,
        pageSize: this.pageSize
      };
      let branchCode = this.searchParam.branchCode;
      if (!branchCode) {
        let user = await api.user.current();
        if (user.data.branch[0] === "XS000000") {
          this.searchParam.branchCode = "XS420000";
        } else {
          this.searchParam.branchCode = user.data.branch[0];
        }
      }

      pms = _.assign(pms, this.searchParam);
      const res = await api.goodsGroup.select(pms);
      // let shopNameArr = [];
      // if (res.data) {
      //   let namePms = {
      //     exhibitionIds: res.data.rows.map(item => {
      //       return item.mysql_id;
      //     })
      //   };

      //   const shopName = await api.stores.pullStoresName(namePms);
      //   if (shopName.data.success) {
      //     shopNameArr = shopName.data.data.shopNames;
      //   }
      // }
      this.loading = false;
      if (res.code == 200) {
        let data = res.data;
        this.$nextTick(() => {
          this.totalSize = data.total;
          // data.rows.forEach((item, index) => {
          //   item.shopName = shopNameArr[index];
          // });
          this.dataList = data.rows;
        });
      } else {
        this.$notify({
          message: res.msg,
          type: "error",
          dangerouslyUseHTMLString: true, //允许html
          offset: 100, //偏移
          duration: 60000
        });
      }
    },
    changePage(pageNo) {
      this.pageFrom = pageNo;
      this.loadData();
    },
    changeSize(pageSize) {
      this.currentPage = 1;
      this.pageSize = pageSize || this.pageSize;
      this.pageFrom = 1;
      this.loadData();
    },
    cancel() {
      this.searchParam = {
        branchCode: !this.isAdmin ? this.searchParam.branchCode : undefined
      };
      this.changeSize();
    },
    dict() {
      let user = new Promise(res => res(api.user.current())).then(user => {
        if (user.code != 200) {
          this.$message.error(user.msg);
        } else {
          this.$nextTick(
            () => (this.goodsGroupData.loginUser = this.loginUser = user.data)
          );
          this.isAdmin = user.data.userName == "admin";
          /*if (!this.isAdmin)
                this.searchParam.branchCode = user.data.branch.branchCode;*/
        }
        return user;
      });
      let state = new Promise(res => res(api.goodsGroup.state())).then(
        state => {
          if (state.code == 200)
            this.$nextTick(
              () => (this.goodsGroupData.state = this.state = state.data)
            );
          else this.$message.error(state.msg);
          return state;
        }
      );
      let gosStatus = new Promise(res => res(api.goods.status())).then(
        gosStatus => {
          if (gosStatus.code == 200)
            this.$nextTick(() => (this.gosStatus = gosStatus.data));
          else this.$message.error(gosStatus.msg);
          return gosStatus;
        }
      );
      let bho = new Promise(res => res(api.dict.branchHasOpen())).then(bho => {
        if (bho.code == 200)
          this.$nextTick(
            () => (this.goodsGroupData.branchs = this.branchs = bho.data)
          );
        else this.$message.error(bho.msg);
        return bho;
      });
      return Promise.all(
        [user, state, gosStatus, bho].map(item => item.catch(e => e))
      ); //并发请求
    },
    updateState(row) {
      if (!row.id || !row.code || !row.state) {
        this.$message.warning("参数错误！");
        return;
      }
      let state = -Number(row.state);
      this.$confirm(
        `是否<b class="el-tag el-tag--warning el-tag--mini">${this.state[state]}</b>“${row.name}”此商品组？`,
        "提示",
        {
          dangerouslyUseHTMLString: true, //允许html
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "info"
        }
      )
        .then(async () => {
          let res = await api.goodsGroup.update({
            branchCode: row.branchCode,
            code: row.code,
            state: state
          });
          if (res.code != 200) {
            this.$message.error(res.msg);
          } else {
            row.state = state;
            this.$message.success(`“${row.name}”${this.state[state]}成功。`);
          }
        })
        .catch(() => {});
    },
    crtTimeDel(row) {
      if (!row.create_time._$gte && !row.create_time._$lte) {
        this.$message.error("请至少选择一个起始、结束时间段！");
        return;
      }
      this.del(row);
      this.createTimeDel = {};
      this.createTimeDelShow = false;
    },
    del(row = {}) {
      if (row.id || row.code) {
        //单个删除
        var hint = `确定删除“${row.name}”此商品组？`;
      } else if (row.state == -1) {
        //禁用状态删除
        var hint = `确定删除所有“${this.state[row.state]}”状态的商品组？`;
      } else if (
        row.create_time &&
        (row.create_time._$gte || row.create_time._$lte)
      ) {
        //时间段删除
        let lgeHint = !row.create_time._$gte
          ? ""
          : `[ >= ${getDate(row.create_time._$gte)}]`;
        let lteHint = !row.create_time._$lte
          ? ""
          : `[ <= ${getDate(row.create_time._$lte)}]`;
        var hint = `确定删除以下创建时间范围的商品组？<p>${lgeHint}</p><p>${lteHint}</p>`;
      } else {
        this.$message.warning("参数错误！");
        return;
      }
      if (!this.isAdmin)
        //非管理员，过滤区域
        row.branchCode = this.loginUser.branch.branchCode;
      this.$confirm(hint, "提示", {
        dangerouslyUseHTMLString: true, //允许html
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          row.branchCode = this.searchParam.branchCode;
          let res = await api.goodsGroup.del(row);
          if (res.code != 200) {
            this.$message.error(res.msg);
          } else {
            this.$message.success("删除成功。");
            this.loadData();
          }
        })
        .catch(() => {});
    },
    async reqGoods(goodsIds) {
      if (!goodsIds || !goodsIds.length) return null;
      let pms = {
        skuIdList: goodsIds
        // limit: this.goodsTab.page.size,
        // offset: this.goodsTab.page.current
      };
      let r = await api.goods.selectGoods(pms);
      if (r.code != 200) {
        this.$notify.error({
          message: r.msg,
          dangerouslyUseHTMLString: true, //允许html
          offset: 100, //偏移
          duration: 5000
        });
        return null;
      }
      return r.data.list || [];
    },
    /**
     * 添加已获取的商品信息，并缓存到当前商品组对象
     */
    addPageGoodsObjs(goodsIds) {
      let grp = this.goodsTab.group;
      let page = this.goodsTab.page;
      if (!grp._goods) {
        grp._goods = new Array(page.total);
      } else {
        let gos = grp._goods.slice(page.start, page.end + 1);
        if (gos.filter(item => !!item).length)
          var r = new Promise(res => res(gos));
      }
      if (!r)
        var r = this.reqGoods(goodsIds).then(data => {
          grp._goods.splice(page.start, page.pageSize, ...(data || []));
          return data;
        });
      return r;
    },
    goodsPage(pn) {
      let page = this.goodsTab.page.to(pn, this.addPageGoodsObjs);
      page.data.then(data => (this.goodsTab.data = data));
    },
    goodsShow(row) {
      this.loading = true;
      this.goodsTab.group = row;
      this.goodsTab.page = Page4array.Init(row.goods);
      this.goodsPage();
      this.goodsTab.show = true;
    },
    goodsTabClose() {
      this.goodsTab.group = this.goodsTab.page = {};
      this.goodsTab.data = null;
      this.loading = false;
    },
    tabRowCla({ row, i }) {
      if (row.state == -1) return "bgc-warn";
      return "";
    },
    showModal(data, ref = "goodsGroupModal") {
      this.goodsGroupData.data = _.cloneDeep(data || {});
      this.$refs[ref].show(true);
    },
    copy(data, ref = "goodsGroupCopyModal") {
      this.goodsGroupData.data = _.cloneDeep(data || {});
      let newBranchs = [];
      for (let i = 0; i < this.goodsGroupData.branchs.length; i++) {
        if (
          this.goodsGroupData.branchs[i].branchCode !=
          this.searchParam.branchCode
        ) {
          newBranchs.push(this.goodsGroupData.branchs[i]);
        }
      }
      this.goodsGroupData.tempBranchs = newBranchs;
      this.goodsGroupData.data.branchCode = newBranchs[0].branchCode;
      this.$refs[ref].show(true);
    },
    async updateMongGoodGroups() {
      this.isClick1 = true;
      //不可再点击
      let result = await api.goodsGroup.updateMongGoodGroups();
      let msg = "同步成功！";
      if (result.code == 200 && result.msg == 1) {
        //恢复点击，刷新页面
        this.loadData();
        msg = "同步失败！";
      }
      if (result.code == 200 && result.msg == 2) {
        //恢复点击，刷新页面
        this.loadData();
        msg = "正在同步，请稍等！";
      }
      this.isClick1 = false;
      this.$alert(msg, "提醒", {
        confirmButtonText: "确定"
      });
    },
    async updateRedisGoodGroups() {
      this.isClick2 = true;
      //不可再点击
      let result = await api.goodsGroup.updateRedisGoodGroups();
      let msg = "更新失败！";
      if (result.code == 200 && result.msg == 0) {
        //恢复点击，刷新页面
        this.loadData();
        msg = "更新成功！";
      }
      if (result.code == 200 && result.msg == 2) {
        //恢复点击，刷新页面
        this.loadData();
        msg = "正在更新，请稍等！";
      }
      this.isClick2 = false;
      this.$alert(msg, "提醒", {
        confirmButtonText: "确定",
        callback: action => {
          this.loadData();
        }
      });
    },
    closeModal() {
      this.goodsGroupData.data = {};
      this.goodsGroupData.tempBranchs = [];
    },
    handleChange() {
      this.$forceUpdate();
    }
  },
  destroyed() {
    bus.$off("change_branch", this.changeBranch);
  }
};
</script>
<style lang="scss" rel="stylesheet/scss">
.main-content {
}

.el-table .bgc-warn {
  background: oldlace;
}

.el-badge.goods-num {
  cursor: pointer;
}

.goods-img-min {
  max-width: 100%;
  max-height: 30px;
}

.active {
  color: red;
}

.myHover {
  min-width: 50px;
}
</style>
