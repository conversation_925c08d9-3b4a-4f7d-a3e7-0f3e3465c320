var resource = [
    { "category": "app", "page_type": "h5", "state": -1, "page_name": "头图+商品流", "branchCode": "XS420000", "categoryList": [{ "name": "ActivePageModule", "title": "活动页模块", "children": [{ "title": "横幅广告", "name": "streamer", "component": { "name": "streamer", "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "image": "", "bgRes": "", "color": "#000000", "link": { "page_url": "", "page_name": "" }, "timevalue": "" }, "computed": {}, "methods": {}, "staticRenderFns": [null], "_compiled": true, "_scopeId": "data-v-a6154a60", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/streamer/index.vue", "beforeDestroy": [null] } }] }, { "name": "FullReductionSingle", "title": "单品满减", "children": [{ "title": "自定义banner", "name": "single_product_init", "component": { "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "list": [], "bg_color": "#FC4340", "bg_img": "" }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-70cceb67", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/single_product/product_init.vue", "beforeDestroy": [null] } }, { "title": "商品列表", "name": "single_product_list", "component": { "name": "hotRecommend", "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "list": [], "goods_group": [], "type": 0, "list_body_color": "#f1f1f1" }, "filters": {}, "computed": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-3c81892e", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/single_product/goods_list.vue", "beforeDestroy": [null] } }] }], "previewImg": "" },
    { "category": "app", "page_type": "h5", "state": -1, "page_name": "头图+优惠券+商品流", "branchCode": "XS420000", "categoryList": [{ "name": "ActivePageModule", "title": "活动页模块", "children": [{ "title": "横幅广告", "name": "streamer", "component": { "name": "streamer", "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "image": "", "bgRes": "", "color": "#000000", "link": { "page_url": "", "page_name": "" }, "timevalue": "" }, "computed": {}, "methods": {}, "staticRenderFns": [null], "_compiled": true, "_scopeId": "data-v-a6154a60", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/streamer/index.vue", "beforeDestroy": [null] } }, { "title": "优惠券", "name": "coupon", "component": { "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "list": [{ "image": "", "link": { "meta": { "id": 1, "page_url": "" } }, "operation": "1" }], "bgRes": "", "color": "#000000", "rotationpointcolor": "#000000", "image": "", "activeKey": 1, "form": { "module": 1, "orient": 1, "fobFont": "AVGARDD" } }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-c515b4ee", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/coupon/index.vue", "beforeDestroy": [null] } }] }, { "name": "FullReductionSingle", "title": "单品满减", "children": [{ "title": "自定义banner", "name": "single_product_init", "component": { "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "list": [], "bg_color": "#FC4340", "bg_img": "" }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-70cceb67", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/single_product/product_init.vue", "beforeDestroy": [null] } }, { "title": "商品列表", "name": "single_product_list", "component": { "name": "hotRecommend", "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "list": [], "goods_group": [], "type": 0, "list_body_color": "#f1f1f1" }, "filters": {}, "computed": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-3c81892e", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/single_product/goods_list.vue", "beforeDestroy": [null] } }] }], "previewImg": "" },
    { "category": "app", "page_type": "h5", "state": -1, "page_name": "头图+套餐+商品流", "branchCode": "XS420000", "categoryList": [{ "name": "ActivePageModule", "title": "活动页模块", "children": [{ "title": "横幅广告", "name": "streamer", "component": { "name": "streamer", "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "image": "", "bgRes": "", "color": "#000000", "link": { "page_url": "", "page_name": "" }, "timevalue": "" }, "computed": {}, "methods": {}, "staticRenderFns": [null], "_compiled": true, "_scopeId": "data-v-a6154a60", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/streamer/index.vue", "beforeDestroy": [null] } }] }, { "name": "FullReductionSingle", "title": "单品满减", "children": [{ "title": "自定义banner", "name": "single_product_init", "component": { "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "list": [], "bg_color": "#FC4340", "bg_img": "" }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-70cceb67", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/single_product/product_init.vue", "beforeDestroy": [null] } }] }, { "name": "rankingList", "title": "排行榜", "children": [{ "title": "优+套餐", "name": "excellentGroup", "component": { "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "name": "excellentGroup", "contentDefault": { "code": 0, "isVolid": false, "styleNum": 1, "isTitle": false, "bgImage": "", "flag": "" }, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-15587b0a", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/excellent-group/index.vue", "beforeDestroy": [null] } }] }], "previewImg": "" },
    { "category": "app", "page_type": "h5", "state": -1, "page_name": "头图+优惠券+坑位+商品流", "branchCode": "XS420000", "categoryList": [{ "name": "ActivePageModule", "title": "活动页模块", "children": [{ "title": "横幅广告", "name": "streamer", "component": { "name": "streamer", "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "image": "", "bgRes": "", "color": "#000000", "link": { "page_url": "", "page_name": "" }, "timevalue": "" }, "computed": {}, "methods": {}, "staticRenderFns": [null], "_compiled": true, "_scopeId": "data-v-a6154a60", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/streamer/index.vue", "beforeDestroy": [null] } }, { "title": "优惠券", "name": "coupon", "component": { "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "list": [{ "image": "", "link": { "meta": { "id": 1, "page_url": "" } }, "operation": "1" }], "bgRes": "", "color": "#000000", "rotationpointcolor": "#000000", "image": "", "activeKey": 1, "form": { "module": 1, "orient": 1, "fobFont": "AVGARDD" } }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-c515b4ee", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/coupon/index.vue", "beforeDestroy": [null] } }, { "title": "横排3图", "name": "levelList_three", "component": { "mixins": [{ "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-f4c564ac", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/ad-style/index.vue", "beforeDestroy": [null] }], "contentDefault": { "list": [{ "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }, { "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }, { "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }], "bgRes": "", "color": "#000000", "rotationpointcolor": "#000000", "image": "" }, "__file": "src/views/apps/topic-prop-components/ad-style/three.vue", "beforeCreate": [null], "beforeDestroy": [null] } }] }, { "name": "FullReductionSingle", "title": "单品满减", "children": [{ "title": "自定义banner", "name": "single_product_init", "component": { "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "list": [], "bg_color": "#FC4340", "bg_img": "" }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-70cceb67", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/single_product/product_init.vue", "beforeDestroy": [null] } }] }], "previewImg": "" },
    { "category": "app", "page_type": "h5", "state": -1, "page_name": "头图+坑位+tab+商品流复制", "branchCode": "XS420000", "categoryList": [{ "name": "ActivePageModule", "title": "活动页模块", "children": [{ "title": "横幅广告", "name": "streamer", "component": { "name": "streamer", "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "image": "", "bgRes": "", "color": "#000000", "link": { "page_url": "", "page_name": "" }, "timevalue": "" }, "computed": {}, "methods": {}, "staticRenderFns": [null], "_compiled": true, "_scopeId": "data-v-a6154a60", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/streamer/index.vue", "beforeDestroy": [null] } }, { "title": "轮播图", "name": "banner", "component": { "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "list": [], "bgRes": "", "color": "#000000", "rotationpointcolor": "#000000", "image": "" }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-632185e2", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/banner/index.vue", "beforeDestroy": [null] } }, { "title": "横排1图", "name": "levelList_one", "component": { "mixins": [{ "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-f4c564ac", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/ad-style/index.vue", "beforeDestroy": [null] }], "contentDefault": { "list": [{ "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }], "bgRes": "", "color": "#000000", "rotationpointcolor": "#000000", "image": "" }, "__file": "src/views/apps/topic-prop-components/ad-style/one.vue", "beforeCreate": [null], "beforeDestroy": [null] } }, { "title": "横排2图", "name": "levelList_two", "component": { "mixins": [{ "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-f4c564ac", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/ad-style/index.vue", "beforeDestroy": [null] }], "contentDefault": { "list": [{ "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }, { "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }], "bgRes": "", "color": "#000000", "rotationpointcolor": "#000000", "image": "" }, "__file": "src/views/apps/topic-prop-components/ad-style/two.vue", "beforeCreate": [null], "beforeDestroy": [null] } }, { "title": "横排3图", "name": "levelList_three", "component": { "mixins": [{ "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-f4c564ac", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/ad-style/index.vue", "beforeDestroy": [null] }], "contentDefault": { "list": [{ "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }, { "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }, { "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }], "bgRes": "", "color": "#000000", "rotationpointcolor": "#000000", "image": "" }, "__file": "src/views/apps/topic-prop-components/ad-style/three.vue", "beforeCreate": [null], "beforeDestroy": [null] } }, { "title": "横排1+2图", "name": "levelList_one_plus_two", "component": { "mixins": [{ "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-f4c564ac", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/ad-style/index.vue", "beforeDestroy": [null] }], "contentDefault": { "list": [{ "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }, { "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }, { "image": "", "link": { "meta": { "page_url": "" } }, "operation": "1" }], "bgRes": "", "color": "#000000", "rotationpointcolor": "#000000", "image": "" }, "__file": "src/views/apps/topic-prop-components/ad-style/one_plus_two.vue", "beforeCreate": [null], "beforeDestroy": [null] } }] }, { "name": "FullReductionSingle", "title": "单品满减", "children": [{ "title": "自定义banner", "name": "single_product_init", "component": { "extends": { "contentDefault": {}, "props": {}, "watch": { "value": { "deep": true }, "content": { "deep": true } }, "staticRenderFns": [], "_compiled": true, "__file": "src/views/apps/topic-prop-components/base.vue", "beforeCreate": [null], "beforeDestroy": [null] }, "contentDefault": { "list": [], "bg_color": "#FC4340", "bg_img": "" }, "computed": {}, "filters": {}, "methods": {}, "staticRenderFns": [], "_compiled": true, "_scopeId": "data-v-70cceb67", "beforeCreate": [null, null], "__file": "src/views/apps/topic-prop-components/single_product/product_init.vue", "beforeDestroy": [null] } }] }], "previewImg": "" }
];
var result = [];
for (let index = 0; index < resource.length; index++) {
    var element = resource[index];
    var name = element.page_name;
    var list = element.categoryList;
    var result_item = {
        name: name,
        comList: [],
        viewImg: ""
    }

    for (let j = 0; j < list.length; j++) {
        var ele = list[j];
        var children = ele.children;
        for (let k = 0; k < children.length; k++) {
            var k_item = children[k];
            var ele_name = k_item.name;
            var ele_title = k_item.title;
            var comList_item = {
                name: ele_name,
                title: ele_title
            }
            result_item['comList'].push(comList_item);
        }
    }

    result.push(result_item);
}

console.log(JSON.stringify(result));
//export default result;