<template>
  <div class="images-box">

    <el-row :gutter="20">
      <div class="title">吸顶设置</div>
      <el-col :span="24">
        <div class="block">
          <el-radio-group v-model="content.is_header">
            <el-radio :label="false">不吸顶</el-radio>
            <el-radio :label="true">吸顶</el-radio>
          </el-radio-group>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="brand-time">
      <div class="title">模块有效时间设置</div>
      <el-col :span="24">
        <el-date-picker
          v-model="content.timevalue"
          @change="change_time"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right">
        </el-date-picker>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="brand-time">
      <div class="title">标题设置</div>
      <el-col :span="24">
        <el-input
          size="small"
          v-model="content.title"
          placeholder="请输入标题"
          @input="()=>{
            if(content.title.length>10){
              this.$message.error('标题最大允许输入10个汉字')
              content.title=content.title.substring(0, 10)
            }
          }"
        />
      </el-col>
    </el-row>

    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块图片上传</div>
      <el-col :span="24">
        <div class="block">

          <div>
            <p style="color: red">点击热区预览图</p>
            <img width="100%" :src="`/static/images/${content.main_type}.jpg`" alt="">
          </div>
          <div>
            <img width="100%" :src="content.bgRes" alt="">
          </div>

        </div>
      </el-col>
      <el-col :span="24">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadImg">
              <el-button class="btn-block" type="primary" :loading="loading"> 上传模板图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
    </el-row>

    <!--上传图片设置和编辑-->
    <el-row :gutter="20">
      <div class="title">上传图片设置和编辑</div>
      <el-col :span="24">

        <el-table :data="list" size="mini" style="width: 100%">
          <el-table-column
            type="index"
            width="50">
          </el-table-column>
          <el-table-column label="热区信息（热区宽度累加起来应该等于100）,1+n系列不支持配置宽度">
            <template slot-scope="scope">
              <div>
                人群范围：
                {{scope.row.crowdValue || '全部人群'}}
              </div>
              <div>
                热区宽度：
                <span style="color: red">
                 {{scope.row.link.meta.page_width}}
                </span>
              </div>
              <div>
                是否分享：
                {{scope.row.link.meta.is_share|is_no}}
              </div>
              <div>
                埋点名称（用户可见名称）：
                {{!scope.row.link.meta.is_share?scope.row.link.meta.page_name:""}}
              </div>
              <div>
                链接：
                {{!scope.row.link.meta.is_share?scope.row.link.meta.page_url :""}}
              </div>
              <div>
                <el-button size="mini"
                           @click="toEdit(scope.row, scope.$index)"
                           type="primary">编辑
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <!--上传图片弹框-->
    <el-dialog class="banner-dialog" title="设置信息" :visible.sync="addDialog">
      <div class="level">
        人群范围：
        <el-radio-group v-model="dataForm.crowdType" @change="changeCrowdType">
          <el-radio :label="1">全部人群</el-radio>
          <el-radio :label="2">指定人群</el-radio>
        </el-radio-group>
      </div>
      <div class="level" v-if="dataForm.crowdType===2">
        指定人群：
        <el-select
          v-model="dataForm.crowdValue"
          :loading="selectLoading"
          filterable
          :filter-method="optionFilter"
          placeholder="请输入人群id"
          clearable
          @clear="options = []"
          @change="selectCrowd"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>
      
      
      <div class="level" v-if="dataForm.link.meta.page_width">
        热区宽度（百分比）
        <el-input-number v-model="dataForm.link.meta.page_width" :max="100" label="描述文字"></el-input-number>
      </div>

      <div class="level">
        <el-radio v-model="is_share" :label="false">无分享功能</el-radio>
        <el-radio v-model="is_share" :label="true">此图具有分享功能</el-radio>
      </div>

      <div class="level" v-if="!is_share">
        <div class="topic-image-picker">
          <span>此字段用于：埋点统计的名称，默认赋予（用户可见名称） 可修改！</span>
          <el-input placeholder="页面名称" v-model="dataForm.link.meta.page_name">
            <template slot="prepend">用户可见名称</template>
          </el-input>
        </div>

        <div class="topic-image-picker">
          <span>链接类型：</span>
          <el-select v-model="dataForm.link.meta.linkType" placeholder="请选择" @change="changeLinkType">
            <el-option
              v-for="item in linkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>

        <div class="topic-image-picker" v-if="dataForm.link.meta.linkType !== 'dynamic'">
          <el-input placeholder="链接地址" v-model.trim="dataForm.link.meta.page_url">
            <template slot="prepend">跳转链接</template>
          </el-input>
        </div>
        <div v-if="dataForm.link.meta.linkType === 'dynamic'">
          <div class="topic-image-picker">
            <el-input style="width: 300px; margin: 10px 0" placeholder="输入跳转id" v-model="dataForm.link.meta.dynamicId">
              <template slot="prepend">跳转id</template>
            </el-input>
            <el-button type="primary" @click="putDynamicLink(dataForm)">生成链接</el-button>
          </div>
          <el-input placeholder="链接地址" v-model.trim="dataForm.link.meta.page_url">
            <template slot="prepend">跳转链接</template>
          </el-input>
        </div>
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode,
                  page_type:topic.page_type}"></page-link>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeAddDialog">取 消</el-button>
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import base from '../../base'
  import api from "api";
  import { AppWebsite, getUrlParam } from "config";

  export default {
    extends: base,
    data() {
      return {
        currentIndex:0,
        is_share: false,
        loading: false,
        addDialog: false,
        selectLoading: false,
        dataForm: {
          link: {
            meta: {
              page_url: '',
              page_name: "",
              is_share: false,
              page_width: "",
              linkType: 'topic',
              dynamicId: '',
            }
          },
          crowdId: '',
          crowdValue: '',
          crowdType: 1,
          title:""
        },
        options: [],
        linkOptions: [{
          value: "topic",
          label: "专题页链接"
        }, {
          value: "stores",
          label: "店铺页链接"
        }, {
          value: "dynamic",
          label: "动态商品链接"
        }],
      }
    },
    computed: {
      list() {
        let list = _.get(this, 'content.list');
        if (list.length) {
          return list
        } else {
          return [];
        }
      },
    },
    filters: {
      is_no(val) {
        if (val) return "是";
        return "否"
      }
    },
    methods: {
      changeCrowdType() {
        this.dataForm.crowdId = '';
        this.dataForm.crowdValue = '';
      },
      async optionFilter(val) {
        this.selectLoading = true;
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          this.selectLoading = false;
          this.options = [{
            label: data.name,
            value: val,
          }]
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCrowd(e) {
        if (e) {
          this.dataForm.crowdId = Number(this.options[0].value.trim());
          this.dataForm.crowdValue = this.options[0].label;
        } else {
          this.dataForm.crowdId = '';
          this.dataForm.crowdValue = '';
        }
        this.$forceUpdate();
      },
      changeLinkType() {
        this.$forceUpdate();
      },
      putDynamicLink(item) {
        if (!item.link.meta.dynamicId) {
          this.$message({
            message: "请输入跳转id再点击生成链接",
            type: "warning"
          });
          return false;
        }
        item.link.meta.page_url = `ybmpage://homeSteadyChannel?strategyId=${item.link.meta.dynamicId}&title=${item.link.meta.page_name}`;
      },
      change_time() {
        for (let item of this.content.list) {
          this.$set(item, "time", this.content.timevalue)
        }
        this.content.list = this.content.list.map(item => {
          return item
        });
      },
      closeAddDialog() {
        this.addDialog = false;
      },
      toEdit(data, index) {
        this.currentIndex = index;
        this.dataForm = JSON.parse(JSON.stringify(data));
        this.dataForm.link.meta.linkType = this.dataForm.link.meta.linkType || 'topic',
        this.is_share = this.dataForm.link.meta.is_share ? this.dataForm.link.meta.is_share : false;
        this.addDialog = true;
      },
      onSetLink(link) {
        this.dataForm.link.meta.page_url = link.meta.page_url;
        this.dataForm.link.meta.page_name = link.meta.page_name;
      },
      async confirm() {
        let linkErrMsg = '';
        if (this.dataForm.link.meta.page_url) {
          if (!this.dataForm.link.meta.page_name) {
            this.$message({
              message: '埋点名称不能为空',
              type: 'warning',
              time: 3000
            });
            return;
          }
          if (this.dataForm.link.meta.linkType === 'topic') {
            if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.dataForm.link.meta.page_url)) {
            linkErrMsg = '跳转链接格式不正确，请检查';
            } else {
              let linkPageUrl = getUrlParam(this.dataForm.link.meta.page_url, 'url');
              const result = await api.topic.checkPageUrl({ url: linkPageUrl });
              if (((result || {}).data || {}).status != 200) {
                linkErrMsg = '跳转链接不存在，请检查';
              }
            }
          }
        }
        if (this.dataForm.link.meta.page_name) {
          if (!this.dataForm.link.meta.page_url) {
            this.$message({
              message: '链接不能为空',
              type: 'warning',
              time: 3000
            });
            return;
          }
        }
        if (linkErrMsg) {
          this.$message.error(linkErrMsg);
          return false;
        }
        this.content.list.splice(this.currentIndex, 1, this.dataForm);
        this.closeAddDialog();
      },
      async onUploadImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          });
          return;
        }
        this.content.bgRes = res.data.url;
      },
    },
    watch: {
      is_share(new_val) {
        this.dataForm.link.meta.is_share = new_val
      }
    }
  }
</script>

<style lang="scss" scoped rel="stylesheet/scss">
  .level {
    margin-bottom: 10px;
  }

  .images-box {
    .container {
      display: flex;
      align-items: center;

      .img {
        width: 78%;

        img {
          display: block;
          max-width: 300px;
          height: 100px;
        }
      }

      .button-list {
        margin-left: 10px;
      }
    }

    .content-setting {
      color: #fff;
      background-color: #13c2c2;
      padding: 10px;
      text-align: center;
      font-size: 16px;
      margin-bottom: 10px;
    }

    .el-icon-circle-plus-outline {
      font-size: 35px;
      color: #c7bdbd;
    }

    .topic-image-upload {
      .image {
        display: block;
        width: 100%;
      }

      .uploader-icon {
        width: 200px;
        height: 200px;
        line-height: 200px;
        border: 1px solid $border-base;
        font-size: 50px;
      }
    }

    .topic-image-picker {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .el-row {
    text-align: center;

    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
  }
</style>
<style lang="scss" rel="stylesheet/scss">
  .topic-banner {
    .banner-dialog {
      .el-dialog__body {
        padding-top: 10px;
      }
    }
  }
</style>
