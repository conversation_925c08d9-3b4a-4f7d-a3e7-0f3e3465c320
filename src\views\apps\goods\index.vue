<template>
  <div class="main-content">
    <el-row class="mb-10">
      <el-col :span="24">
        <el-row class="search-wrap" type="flex" :gutter="12">
          <el-popover placement="top" trigger="hover">
            <el-button
              @click="cancel()"
              :loading="loading"
              type="info"
              icon="el-icon-refresh"
              size="mini"
              plain
            >清空</el-button>
            <el-button
              @click="changeSize()"
              :loading="loading"
              slot="reference"
              type="primary"
              icon="el-icon-search"
              size="mini"
              plain
            >查询</el-button>
          </el-popover>
          <el-select v-model="searchParam.status" placeholder="状态" size="mini" clearable>
            <el-option v-for="(item, i) in status" :value="i" :key="i" :label="item"></el-option>
          </el-select>
          <el-select
            v-if="true"
            v-model="searchParam.branchCode"
            placeholder="区域"
            @change="handleChange()"
            size="mini"
            default-first-option
            filterable
            clearable
          >
            <el-option
              v-for="(item, i) in branchs"
              :key="i"
              :value="item.branchCode"
              :label="item.branchName"
            ></el-option>
          </el-select>
          <el-input v-model="searchParam.showName" placeholder="搜索商品名称" size="mini" clearable></el-input>
          <el-input v-model="searchParam.barcode" placeholder="编码" size="mini" clearable></el-input>
        </el-row>
      </el-col>
    </el-row>
    <el-table :data="dataList" v-loading="loading" class="custom-table" size="mini" border>
      <div slot="empty" class="empty-wrap">
        <i class="iconfont icon-tishi"></i>
        <span>未获取到商品</span>
      </div>
      <el-table-column type="index" width="45%" align="right"></el-table-column>
      <el-table-column type="expand" width="25%">
        <template slot-scope="scope">
          <el-form label-position="left" inline class="demo-table-expand">
            <el-form-item label="实物图：">
              <el-card>
                <img
                  :src="$options.filters.imgUrl(cdnWebsite, 'imgUrl', scope.row.imageUrl)"
                  class="image goods-img"
                  alt="实物图"
                />
              </el-card>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column prop="id" label="ID" width="80%" align="right"></el-table-column>
      <el-table-column prop="code" label="条码" width="120%" align="left"></el-table-column>
      <el-table-column prop="barcode" label="编码" width="100%" align="left"></el-table-column>
      <el-table-column prop="showName" label="商品名称" width="200%" align="center"></el-table-column>
      <el-table-column prop="imageUrl" label="图片" width="90%" align="center">
        <template slot-scope="scope">
          <el-popover ref="imgMin" placement="right-end" trigger="focus">
            <img
              :src="$options.filters.imgUrl(cdnWebsite, 'imgUrlMin', scope.row.imageUrl)"
              class="avatar"
              alt="图片"
            />
          </el-popover>
          <img
            v-popover:imgMin
            :src="$options.filters.imgUrl(cdnWebsite, 'imgUrlMin', scope.row.imageUrl)"
            class="avatar goods-img-min"
            alt="图片"
          />
        </template>
      </el-table-column>
      <el-table-column prop="mediumPackageTitle" label="包装" width="100" align="left"></el-table-column>
      <el-table-column prop="mediumPackageNum" label="数量" width="50" align="right"></el-table-column>
      <el-table-column prop="productUnit" label="单位" width="50" align="center"></el-table-column>
      <el-table-column prop="spec" label="规格" width="150%" align="center"></el-table-column>
      <el-table-column prop="fob" label="原 / 售价(￥)" width="95" align="right">
        <template slot-scope="scope">
          <el-tag size="mini" type="success">{{ scope.row.fob }}</el-tag>
          <hr />
          <el-tag size="mini">{{ scope.row.retailPrice }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="grossMargin" label="毛利" width="50" align="right"></el-table-column>
      <el-table-column prop="availableQty" label="库存数" width="60" align="right"></el-table-column>
      <el-table-column prop="status" label="状态" width="60%" align="center">
        <template slot-scope="scope">{{ $options.filters.statusTip(scope.row.status, status) }}</template>
      </el-table-column>
      <el-table-column prop="isPromotion" label="促销" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isPromotion(scope.row.isPromotion)}}</template>
      </el-table-column>
      <el-table-column prop="isFragileGoods" label="易碎品" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isFragileGoods(scope.row.isFragileGoods)}}</template>
      </el-table-column>
      <el-table-column prop="productType" label="秒杀" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isProductType(scope.row.productType)}}</template>
      </el-table-column>
      <el-table-column prop="isControl" label="控销" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isControl(scope.row.isControl)}}</template>
      </el-table-column>
      <el-table-column prop="isPromotion" label="促销" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isPromotion(scope.row.isPromotion)}}</template>
      </el-table-column>
      <el-table-column prop="isLimited" label="限购" align="center" width="80%">
        <template slot-scope="scope">{{ $options.filters.isLimited(scope.row.isLimited)}}</template>
      </el-table-column>
      <el-table-column
        prop="manufacturer"
        :show-overflow-tooltip="true"
        label="厂商"
        width="180%"
        align="center"
      ></el-table-column>
      <el-table-column prop="branchCode" label="区域" width="90%" align="right">
        <template
          slot-scope="scope"
        >{{ $options.filters.getBranchName(scope.row.branchCode, branchs) }}</template>
      </el-table-column>
      <el-table-column prop="channelCodes" label="渠道" align="center" width="100%">
        <template
          slot-scope="scope"
        >{{ $options.filters.channelCodesFilter(scope.row.channelCodes) }}</template>
      </el-table-column>
      <el-table-column prop="createTime" :show-overflow-tooltip="true" label="创建时间" width="140%">
        <template slot-scope="scope">{{ scope.row.createTime | dateFmt }}</template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page.sync="currentPage"
      v-show="totalSize > 10"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      @size-change="changeSize"
      @current-change="changePage"
      layout="total, sizes, slot, jumper, prev, pager, next"
      :total="totalSize"
    ></el-pagination>
  </div>
</template>
<script>
import api from "api";
// import {AppWebsite} from 'config';
import { fetch, getDate } from "../../../utils/time-format";
import bus from "utils/eventbus";

export default {
  name: "Goods",
  data() {
    return {
      currentPage: 1,
      pageFrom: 1,
      pageSize: 10,
      totalSize: 10,
      loading: false,
      isAdmin: false,
      dataList: [],
      searchParam: {},
      status: null,
      branchs: null,
      cdnWebsite: null //cdn域名
    };
  },
  filters: {
    //渠道
    channelCodesFilter(val) {
      if (Array.isArray(val)) {
        if (val.length == 0) {
          return "无";
        }
        if (val.indexOf("1") != -1 && val.indexOf("2") != -1) {
          return "b2b,壹块钱";
        } else if (val.indexOf("1") != -1) {
          return "b2b";
        } else if (val.indexOf("2") != -1) {
          return "壹块钱";
        }
        return "未知";
      }
      return "无";
    },
    imgUrl(website, midst, url) {
      return website + api.goods[midst] + url;
    },
    statusTip(status, dict) {
      return !dict ? "未知" : dict[status];
    },
    isPromotion(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isFragileGoods(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isProductType(val) {
      if (val == 2) {
        return "是";
      } else {
        return "否";
      }
    },
    isLimited(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isPromotion(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isControl(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    dateFmt(date) {
      return date ? getDate(date) : "";
    },
    getBranchName(code, branchs) {
      let branchName = "";
      if (!code || !branchs || !branchs.length) return branchName;
      for (let i = 0, len = branchs.length; i < len; i++) {
        let branch = branchs[i];
        if (branch.branchCode == code) {
          branchName = branch.branchName;
          break;
        }
      }
      return branchName;
    }
  },
  async mounted() {
    await this.dict(); //字典接口
    this.loadData();
    this.$store.dispatch("breadcrumb/clearPath");
    this.$store.dispatch("breadcrumb/addPath", {
      title: "商品管理",
      subTitle: "商品管理",
      action: "goods"
    });
    bus.$emit("menu_type", "other-商品管理");
  },
  methods: {
    async loadData() {
      this.loading = true;
      let branchCode = this.searchParam.branchCode;
      let user = await api.user.current();
      if (!branchCode) {
        if (user.data.branch[0] === "XS000000") {
          this.searchParam.branchCode = "XS420000";
        } else {
          this.searchParam.branchCode = user.data.branch[0];
        }
      }
      let pms = {
        offset: this.pageFrom,
        limit: this.pageSize
      };
      let res = await api.goods.select(Object.assign(pms, this.searchParam));
      this.loading = false;
      if (res.code == 200) {
        let page = res.data;
        this.$nextTick(() => {
          this.dataList = page.list;
          this.totalSize = page.total;
        });
      } else {
        this.$notify({
          message: res.msg,
          type: "error",
          dangerouslyUseHTMLString: true, //允许html
          offset: 100, //偏移
          duration: 60000
        });
      }
    },
    changePage(pageNo) {
      this.pageFrom = pageNo;
      this.loadData();
    },
    changeSize(pageSize) {
      this.currentPage = 1;
      this.pageSize = pageSize || this.pageSize;
      this.pageFrom = 1;
      this.loadData();
    },
    cancel() {
      this.searchParam = {
        branchCode: !this.isAdmin ? this.searchParam.branchCode : undefined
      };
      this.changeSize();
    },
    async dict() {
      let user = await api.user.current();
      if (user.code != 200) {
        this.$message.error(user.msg);
      } else {
        this.$nextTick(() => (this.loginUser = user.data));
        this.isAdmin = user.data.userName == "admin";
        /*if (!this.isAdmin)
						this.searchParam.branchCode = user.data.branch.branchCode;*/
      }
      let cw = await api.dict.cdnWebsite();
      if (cw.code == 200) this.$nextTick(() => (this.cdnWebsite = cw.data));
      else this.$message.error(cw.msg);

      let bho = await api.dict.branchHasOpen();
      if (bho.code == 200) this.$nextTick(() => (this.branchs = bho.data));
      else this.$message.error(bho.msg);

      let status = await api.goods.status();
      if (status.code == 200) this.$nextTick(() => (this.status = status.data));
      else this.$message.error(status.msg);
    },
    handleChange() {
      this.$forceUpdate();
    }
  }
};
</script>
<style lang="scss" rel="stylesheet/scss">
.main-content {
}
.goods-img-min {
  max-width: 100%;
  max-height: 50px;
}
</style>
