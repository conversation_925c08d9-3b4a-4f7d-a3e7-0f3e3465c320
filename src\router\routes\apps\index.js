const Apps = r => require.ensure([], () => r(require('views/apps')), 'apps')

const Topic = r => require.ensure([], () => r(require('views/apps/topic/index')), 'apps')
const TopicEditor = r => require.ensure([], () => r(require('views/apps/topic/editor/index')), 'apps')
const TopicPcEditor = r => require.ensure([], () => r(require('views/apps/topic/editor-pc/index')), 'apps')
const TopicStartEditor = r => require.ensure([], () => r(require('views/apps/topic/editor/index_start')), 'apps')

const ActivityPage = r => require.ensure([], () => r(require('views/apps/activitypage/index')), 'apps')
const Goods = r => require.ensure([], () => r(require('views/apps/goods/index')), 'apps')
const GoodsGroup = r => require.ensure([], () => r(require('views/apps/goods/group/index')), 'apps')

const Log = r => require.ensure([], () => r(require('views/apps/log/index')), 'apps')

export default [
    {
        path: '/apps',
        component: Apps,
        children: [
            {
                name: 'topic?category=app',
                path: 'topic?category=app',
                component: Topic
            }, {
                name: 'topic?category=pc',
                path: 'topic?category=pc',
                component: Topic
            }, {
		        name: 'topic',
		        path: 'topic',
		        component: Topic
	        }, {
		        name: 'topicEditor',
		        path: 'topic/:id',
		        component: TopicEditor
            }, {
		        name: 'topicPcEditor',
		        path: 'topicPc/:id',
		        component: TopicPcEditor
            }, {
                name: 'topicStartEditor',
                path: 'topic/start/:id',
                component: TopicStartEditor
	        }, {
		        name: 'activitypage',
		        path: 'activitypage',
		        component: ActivityPage
	        }, {
		        name: 'goods',
		        path: 'goods',
		        component: Goods
	        }, {
		        name: 'goodsGroup',
		        path: 'goodsGroup',
		        component: GoodsGroup
	        }, {
		        name: 'log',
		        path: 'log',
		        component: Log
	        }
        ]
    }
]
