import * as http from 'utils/http'

export default {
    list(params) {
        return http.get('user', params, {
	        contentType: 'application/json; charset=UTF-8'
        });
    },
	select(pms) {
        return http.post('user/select', pms, {
	        contentType: 'application/json; charset=UTF-8'
        });
    },
    query(params) {
        return http.get('user/query', params);
    },
    get(id) {
        return http.get(`user/${id}`);
    },
    add(params) {
        return http.post('user/add', params);
    },
    update(id, params) {
        return http.post(`user/${id}/update`, params);
    },
    current() {
        return http.get('user/current');
    },
	validate(pms) {
		return http.post('user/validate', pms);
	},
    remove(id, params) {
        return http.post(`user/${id}/remove`, params);
    },
    editDefaultBranch(id, params){
        return http.post(`/user/${id}/editDefaultBranch`, params);
    }
}
