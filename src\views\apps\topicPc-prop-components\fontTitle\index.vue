<template>
  <div class="topic-image">
    <el-row :gutter="20">
      <div class="title">模块有效时间设置</div>
      <div class="block">
         <el-col :span="24">
          <el-date-picker
            v-model="content.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions0"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-col>
      </div>
    </el-row>

    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">背景颜色：</span>
          <div>
            <el-color-picker v-model="content.bgColor" @change="change_bgColor" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
    </el-row>

    <!--文字颜色设置-->
    <el-row :gutter="20">
      <div class="title">文字颜色设置</div>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">主标题文字颜色：</span>
          <div>
            <el-color-picker v-model="content.textColor" @change="change_textColor" size="mini"></el-color-picker>
          </div>
          <span class="demonstration pl">副标题文字颜色：</span>
          <div>
            <el-color-picker v-model="content.subTextColor" @change="change_subTextColor" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
    </el-row>

    <!--活动文案设置-->
    <el-row :gutter="20">
      <div class="title">标题设置</div>
      <div class="block noflex">
        <el-col :span="24">
          <el-input placeholder="请输入内容" v-model="content.title">
            <template slot="prepend">主标题:</template>
          </el-input>
        </el-col>
        <el-col :span="24">
          <el-input placeholder="请输入内容" v-model="content.subTitle">
            <template slot="prepend">副标题:</template>
          </el-input>
        </el-col>
      </div>
    </el-row>

    <!--活动链接设置-->
    <el-row :gutter="20">
      <div class="title">标题链接设置</div>
      <!-- <el-col :span="24">
        <el-input placeholder="请输入内容" v-model="content.url"></el-input>
      </el-col> -->
      <div class="block">
        <el-radio-group v-model="linkType">
          <el-radio :label="'link'">链接</el-radio>
          <el-radio :label="'point'">锚点</el-radio>
          <!-- <el-radio :label="'topic'">店铺</el-radio> -->
          <el-radio :label="'stores'">商详</el-radio>
        </el-radio-group>
      </div>

      <div class="topic-image-picker">
        <el-input placeholder="链接地址" v-model.trim="content.url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <!-- <el-col :span="24">
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
      </el-col> -->

      <div v-if="linkType==='link'">
        <all-link @select="onSetLink" :tabs="[{label: '活动页', value: 'page'}]" :params="{branchCode: topic.branchCode, from: 'pc'}"></all-link>
      </div>

      <div v-if="linkType==='point'" class="block">
        <el-radio-group v-model="content.url"  @change="handleChangeFloor">
          <el-radio-button v-for="count in coreLength" :key="count" :label="`#floor${count - 1}`">楼层{{count}}</el-radio-button>
        </el-radio-group>
      </div>

      <!-- <div v-if="linkType==='topic'">
        <control-page @select="onSetLink" :params="{branchCode: topic.branchCode}"></control-page>
      </div> -->
    </el-row>

    <!--背景的-->
    <el-dialog class="banner-dialog" title="添加图片" :visible.sync="banner_bg_dialog">
      <el-upload
        class="topic-image-upload"
        ref="upload"
        accept="image/jpeg,image/jpg, image/png, image/gif"
        :show-file-list="false"
        :before-upload="() => {loading = true; return true;}"
        :on-success="upload_bg_img"
      >
        <img v-if="content.bgRes.length>10" :src="content.bgRes" class="image" />
        <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="banner_bg_dialog=false">取 消</el-button>
        <el-button size="small" type="primary" @click="banner_bg_dialog=false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import base from "../base";
import api from "api";

export default {
  name: "more_active",
  extends: base,
  contentDefault: {
    timevalue: "",
    textColor: "#000000",
    subTextColor: "#000000",
    bgColor: "#000000",
    bgRes: "",
    title: "",
    subTitle: "更多",
    url: "",
    activeKey: 1
  },
  props: {
    coreLength: Number,
    pageTimeValue: Array
  },
  data() {
    return {
      pickerOptions0: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      banner_bg_dialog: false,
      loading: false,
      linkType: "link",
    };
  },
  computed: {},
  created() {
    this.debounce = _.debounce(this.changeLink, 1000);
  },
  mounted () {
    if (this.pageTimeValue) {
      this.content.timevalue = this.content.timevalue || this.pageTimeValue;
    }
  },
  watch: {
    "pageTimeValue"(new_val) {
      if (new_val) {
        this.content.timevalue = new_val;
      }
    },
    'content.url': {
      handler(val, oldVal) {
        if (val && this.linkType === 'link') {
          this.debounce();
        }
      }
    }
  },
  methods: {
    change_subTextColor(val) {
      if (!val) {
        this.content.subTextColor = "#000000";
      }
    },
    change_bgColor(val) {
      if (!val) {
        this.content.bgColor = "#000000";
      }
    },
    change_textColor(val) {
      console.log(val);
      if (!val) {
        this.content.textColor = "#000000";
      }
    },
    handleChangeFloor(value) {
      this.content.url = value;
    },
    // 设置轮播链接
    onSetLink(link) {
      this.content.url = link.meta.page_url;
    },
    clear_self_bg_img() {
      this.content.bg_img = "";
    },
    async upload_bg_img(res) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.$set(this.content, "bgRes", res.data.url);
    },
    async changeLink() {
      if (this.content.url) {
        const result = await api.topic.checkPageUrl({ url: this.content.url });
        if (((result || {}).data || {}).status != 200) {
          this.$message.error('跳转链接不存在');
          this.content.url = '';
        }
      }
    }
  }
};
</script>

<style lang="scss">
.container-table {
  margin: 10px auto;
  padding-bottom: 10px;
  .img {
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.topic-image-upload {
  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 200px;
    height: 200px;
    line-height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    font-size: 50px;
  }
}

.topic-image-upload .el-upload {
  width: 100%;
}
.el-row {
  text-align: center;
  .el-col-8 {
    width: auto;
  }
  .title {
    text-align: left;
    line-height: 35px;
    background-color: #f2f2f2;
    padding-left: 15px;
  }
  .block {
    display: flex;
    padding: 20px 0;
    padding-left: 15px;
    padding-right: 15px;
    .el-col-24 {
      float: none;
    }
    .pl {
      margin-left: 40px;
    }
  }
  .noflex {
    display: block;
    .el-col-24:last-child {
      margin-top: 15px;
    }
  }
}

.url-container {
  margin: 10px 0;
  padding-left: 10px;
  color: #13c2c2;
  text-align: left;
  line-height: 30px;
  background-color: #f2f2f2;
}
</style>

