<template>
  <div class="recommend-list-wrap">
    <!-- <div style="margin: 10px 0">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-radio-group v-model="content.activeKey">
            <el-radio
              :label="index"
              v-for="(item,index) in recomlist_menu"
              :key="index"
              :disabled="index>1"
            >{{item}}</el-radio>
          </el-radio-group>
        </el-col>
      </el-row>
    </div>-->

    <el-row :gutter="20">
      <div class="title">模块有效时间设置</div>
      <div class="block">
        <el-col :span="24">
          <el-date-picker
            v-model="content.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions2"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-col>
      </div>
    </el-row>

    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="6">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadGoodsBgImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
          <el-button class="add-button" @click="imgOnclick">清除背景图</el-button>
        </div>
      </el-col>
    </el-row>

    <!--模块外背景设置-->
    <el-row :gutter="20">
      <div class="title">按钮颜色设置</div>
      <el-col :span="6">
        <div class="block">
          <span>默认文字颜色：</span>
          <div>
            <el-color-picker v-model="content.fontColor" size="mini"></el-color-picker>
          </div>
          <span class="pl">激活文字颜色：</span>
          <div>
            <el-color-picker v-model="content.fontColorActive" size="mini"></el-color-picker>
          </div>
          <span class="pl">默认背景颜色：</span>
          <div>
            <el-color-picker v-model="content.goodsBgRes" size="mini"></el-color-picker>
          </div>
          <span class="pl">激活背景颜色：</span>
          <div>
            <el-color-picker v-model="content.goodsBgResActive" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
      <!-- <el-col :span="6">
        <div class="block">
          <span class="title">轮播点颜色</span>
          <div>
            <el-color-picker
              v-model="content.swiperDotRes"
              size="mini"
              @active-change="onSelect('swiperDot')"
            ></el-color-picker>
          </div>
        </div>
      </el-col>-->

      <!-- <el-col :span="6">
        <div class="block">
          <span class="title">背景色</span>
          <div>
            <el-color-picker
              v-model="content.goodsBgRes"
              size="mini"
              @active-change="onSelect('goodsBgRes')"
            ></el-color-picker>
          </div>
        </div>
      </el-col>-->
    </el-row>

    <el-row :gutter="20">
      <div class="title">头图设置</div>
      <el-col :span="24">
        <div class="block">
          <el-upload
            class="topic-image-upload title-bg-image"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="() => {titleImgloading = true; return true;}"
            :on-success="onUploadTitleImg"
          >
            <el-button class="btn-block" type="primary" :loading="titleImgloading">上传头图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </div>
      </el-col>
    </el-row>

    <el-table :data="[list]" size="mini" style="width: 100%" class="image-height-wrap">
      <el-table-column label="标题图片">
        <template slot-scope="scope">
          <img v-if="content.titleimage" :src="content.titleimage" class="table-images" />
          <i v-else class="el-icon-circle-plus-outline no-img"></i>
        </template>
      </el-table-column>
      <el-table-column prop="operation" label="操作" align="center" width="100%">
        <template slot-scope="scope">
          <el-button @click="toEdit(scope.row, scope.$index)" type="primary" size="mini">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑内容弹层 -->
    <el-dialog
      class="rec-list-dialog"
      title="横向商品展示编辑"
      :visible.sync="editContent"
      :close-on-click-modal="false"
    >
      <el-form
        :model="editData"
        :rules="validate"
        :ref="validRef"
        size="small"
        label-width="100px"
        label-suffix="："
      >
        <el-form-item label="标题">
          <el-input v-model="editData.recTitle" class="entry-content" placeholder="请输入标题"></el-input>
          <label class="sec-column">标题颜色：</label>
          <el-color-picker
            v-model="editData.rectitleColor"
            @active-change="onSelect('rectitleColor')"
          ></el-color-picker>
        </el-form-item>
        <el-form-item label="副标题">
          <el-input v-model="editData.recSubTitle" class="entry-content" placeholder="请输入副标题"></el-input>
          <label class="sec-column">副标题颜色：</label>
          <el-color-picker
            v-model="editData.recSubTitleColor"
            @active-change="onSelect('recSubTitleColor')"
          ></el-color-picker>
        </el-form-item>
        <el-form-item label="跳转链接">
          <div v-if="editData.link" class="text item">
            <el-input placeholder="链接地址" v-model.trim="editData.link.meta.page_url">
              <template slot="prepend">{{editData.link.meta.page_name}}</template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="每页数量">
          <el-select v-model="editData.count" placeholder="每页显示商品数量">
            <el-option value="3" label="3个"></el-option>
            <el-option value="4" label="4个"></el-option>
            <el-option value="5" label="5个"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="最大页数">
          <el-select v-model="editData.pageCount" placeholder="每页显示商品数量">
            <el-option value="4" label="4页"></el-option>
            <el-option value="5" label="5页"></el-option>
            <el-option value="6" label="6页"></el-option>
            <el-option value="7" label="7页"></el-option>
            <el-option value="8" label="8页"></el-option>
          </el-select>
        </el-form-item>-->
        <el-form-item label="商品" prop="goods">
          <el-collapse>
            <el-collapse-item name="goodsGroup">
              <template slot="title">
                <el-breadcrumb separator="/">
                  <el-breadcrumb-item>
                    <el-tooltip content="商品组名称：" placement="top">
                      <span>{{editData.goodsGroup.name}}</span>
                    </el-tooltip>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    <el-tooltip content="已选商品数：" placement="top">
                      <el-badge :value="(editData.goodsGroup.goods || []).length" type="success"></el-badge>
                    </el-tooltip>
                  </el-breadcrumb-item>
                  <el-breadcrumb-item>
                    <el-tooltip content="商品组状态：" placement="top">
                      <span>{{ $options.filters.stateTip(editData.goodsGroup.state, grpState) }}</span>
                    </el-tooltip>
                  </el-breadcrumb-item>
                </el-breadcrumb>
              </template>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>
      </el-form>

      <el-collapse v-model="collShow">
        <el-collapse-item name="collapse">
          <template slot="title">
            <i class="header-icon el-icon-sort"></i>
            <b class="el-message-box__content">选项卡</b>
          </template>
          <all-link
            @select="onSetLink"
            :tabs="[
              {
				        label: '商品组',
				        value: 'goodsGroup',
				        params:
                  {
                    seledShow: false,
                    minSel: 1,
                    search: {
                      state: 1,
                      branchCode: topic.branchCode
                    }
                  }
			        },
              {
                label: '活动页',
                value: 'page',
                params: {
                  branchCode: topic.branchCode
                }
              }
            ]"
            :params="{
              from: 'pc'
            }"
          ></all-link>
        </el-collapse-item>
      </el-collapse>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditContent">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmEdit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from "api";
import base from "../base";

export default {
  extends: base,
  contentDefault: {
    recTitle: "",
    recSubTitle: "",
    rectitleColor: "#FFFFFF",
    recSubTitleColor: "#FFFFFF",
    timevalue: "",
    titleimage: "",
    goodsimage: "",
    goodsBgRes: "",
    goodsBgResActive: "#ffffff",
    fontColor: "#333333",
    fontColorActive: "#333333",
    // outside_bgColor: '#F7F7F8',
    swiperDotRes: "",
    count: "5",
    pageCount: "8",
    link: {
      meta: {
        page_url: ""
      }
    },
    goodslist: [],
    goodsGroup: {
      goods: []
    },
    activeKey: 1
  },
  props: {
    pageTimeValue: Array
  },
  data() {
    return {
      loading: false,
      titleImgloading: false,
      editImgLoading: false,
      editContent: false,
      editLinking: false,
      showGosTab: false,
      collShow: "collapse",
      grpState: null,
      gosStatus: null,
      editData: {
        recTitle: "",
        recSubTitle: "",
        rectitleColor: "#000",
        recSubTitleColor: "#000",
        count: "5",
        pageCount: "8",
        link: {
          meta: {
            page_url: ""
          }
        },
        goodslist: [],
        goodsGroup: {
          goods: []
        }
      },
      validRef: "form", //验证ref属性值
      validate: {
        recTitle: [
          { required: true, message: "请填写标题", trigger: "blur" },
          { min: 2, max: 36, message: "长度在2 - 36之间", trigger: "blur" }
        ],
        recSubTitle: [
          { required: true, message: "请填写副标题", trigger: "blur" },
          { min: 2, max: 36, message: "长度在2 - 36之间", trigger: "blur" }
        ],
        goods: [{ required: true, validator: this.goodsValid, trigger: "blur" }]
      },
      pickerOptions2: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      menu: localStorage.getItem("menu"),
      recomlist_menu: ["首页", "活动专区", "爆款推荐", "常购清单"],
      goodsGroupEditData: {
        start: 0,
        end: 0
      }
    };
  },
  computed: {
    contentBackgroud() {
      let url = _.get(this, "content.background.goodsimage");
      return !url ? "" : `url(${url})`;
    },
    contentTitleimage() {
      let url = _.get(this, "content.background.titleimage");
      return !url ? "" : `url(${url})`;
    },
    list() {
      let ct = _.get(this, "content");
      return ct || this.editData;
    },
    goodslist() {
      let goodslist = _.get(this, "content.goodslist");
      return goodslist || [];
    }
  },
  filters: {
    //渠道
    channelCodesFilter(val) {
      if (Array.isArray(val)) {
        if (val.length == 0) {
          return "无";
        }
        if (val.indexOf("1") != -1 && val.indexOf("2") != -1) {
          return "b2b,壹块钱";
        } else if (val.indexOf("1") != -1) {
          return "b2b";
        } else if (val.indexOf("2") != -1) {
          return "壹块钱";
        }
        return "未知";
      }
      return "无";
    },
    link(data) {
      return !data.type ? "" : data.meta.page_url;
    },
    /*            products(data) {
                      return !data.type ? '' : data.showName;
                  },*/
    stateTip(state, dict) {
      return !dict ? "未知" : dict[state];
    },
    isPromotion(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isFragileGoods(val) {
      if (val == 1) {
        return "是";
      } else {
        return "否";
      }
    },
    isProductType(val) {
      if (val == 2) {
        return "是";
      } else {
        return "否";
      }
    }
  },
  methods: {
    onSelect(val, flag) {
      if (flag === "swiperDot") {
        //轮播图点颜色
        this.content.swiperDotRes = val;
      } else if (flag === "goodsBgRes") {
        //商品区背景色
        this.content.goodsBgRes = val;
      } else if (flag === "rectitleColor") {
        //标题颜色
        this.content.rectitleColor = val;
      } else if (flag === "recSubTitleColor") {
        //副标题颜色
        this.content.recSubTitleColor = val;
      } else if (flag === "outside_bgColor") {
        this.content.outside_bgColor = val;
      }
    },
    toEdit(data, index) {
      if (status == "1") {
        //新建时清空
        // this.editData.titleimage = '';
        this.editData.operation = "1";
        this.editData.link = {
          meta: {
            page_url: ""
          }
        };
        this.editData.recTitle = "";
        this.editData.recSubTitle = "";
        this.editData.rectitleColor = "#000";
        this.editData.recSubTitleColor = "#000";
        this.editData.count = "5";
        this.editData.pageCount = "8";
      } else {
        this.editData = JSON.parse(JSON.stringify(data));
        // this.editData.titleimage = data.titleimage;
        // this.editData.operation = "2";
        // this.editData.link = data.link;
        // this.editData.recTitle = data.recTitle;
        // this.editData.recSubTitle = data.recSubTitle;
        // this.editData.rectitleColor = data.rectitleColor;
        // this.editData.recSubTitleColor = data.recSubTitleColor;
        // //this.editData.goodslist = data.goodslist;
        // this.editData.goodsGroup = data.goodsGroup || this.editData.goodsGroup;
        // this.editData.count = data.count;
        // this.editData.pageCount = data.pageCount;
      }
      this.goodsGroupEditData.start = data.goodsGroup.start;
      this.goodsGroupEditData.end = data.goodsGroup.end;
      this.editContent = true;
      this.nowData = data;
      // this.nowIndex = index;
      this.isContEdit = true;
      this.addContPic = true;
    },
    imgOnclick() {
      this.content.goodsimage = "";
      // this.content.goodsBgRes = "";
    },
    closeEditContent() {
      this.editContent = false;
    },
    /*            cutStr(str) {
                      if(str && str.length > 6) {
                          return str.substring(0, 6) + '...';
                      } else {
                          return str;
                      }
                  },*/
    onSetLink(obj) {
      if (obj.tag) {
        // console.log('232323', obj.tag)
        this[obj.tag + "Tag"](obj); //调用各组件的对应函数
        // this.goodsGroupEditData.start = obj.goodsGroupEditData.start;
        // this.goodsGroupEditData.end = obj.goodsGroupEditData.end;
      } else {
        //选择的是more跳转的链接
        this.editData.link = obj;
        // this.content.link = obj;
      }
    },
    goodsTag(obj) {
      this.editData.goodsGroup.goods = obj.ids;
      this.editData.goodslist = this.goodsFilter(obj.data);
    },
    importGoodsTag(obj) {
      this.goodsTag(obj);
    },
    goodsGroupTag(obj) {
      this.editData.goodsGroup = obj.data;
      this.editData.goodslist = this.goodsFilter(obj.data._goods);
    },
    goodsFilter(goods) {
      if (goods)
        for (let i = 0, len = goods.length; i < len; i++)
          goods[i] = _.pick(goods[i], [
            "id",
            "code",
            "showName",
            "link",
            "imageUrl",
            "mediumPackageNum",
            "productUnit",
            "spec",
            "retailPrice",
            "availableQty",
            "status",
            "branchCode",
            "retailPrice",
            "isPromotion",
            "isFragileGoods",
            "channelCodes"
          ]);
      return goods;
    },
    confirmEdit() {
      this.loading = !this.loading;
      this.$refs[this.validRef].validate(async ok => {
        if (!ok) {
          this.loading = !this.loading;
          this.$message.error("验证失败！");
          return;
        }
        if (!this.editData.goodsGroup.goods) {
          this.loading = !this.loading;
          this.$message.warning("请选择商品");
          return;
        }
        if (
          Object.keys(this.editData.link).length == 0 ||
          this.editData.link.length == 0
        ) {
          this.loading = !this.loading;
          this.$message.warning("请选择链接");
          return;
        }
        let linkErrMsg = '';
        if (this.editData.link.meta.page_url) {
          const result = await api.topic.checkPageUrl({ url: this.editData.link.meta.page_url });
          if (((result || {}).data || {}).status != 200) {
            linkErrMsg = '跳转链接不存在，请检查';
          }
        }
        if (linkErrMsg) {
          this.$message.error(linkErrMsg);
          return false;
        }
        this.closeEditContent();
        if (this.isContEdit) {
          let tempGoodsGroup = _.pick(this.editData.goodsGroup, [
            "_id",
            "id",
            "goods",
            "code",
            "name",
            "branchCode",
            "state"
          ]);
          tempGoodsGroup.start = parseInt(this.goodsGroupEditData.start);
          tempGoodsGroup.end = parseInt(this.goodsGroupEditData.end);
          this.psData = {
            // titleimage: this.editData.titleimage,
            // operation: this.editData.operation,
            link: this.editData.link,
            recTitle: this.editData.recTitle,
            recSubTitle: this.editData.recSubTitle,
            rectitleColor: this.editData.rectitleColor,
            recSubTitleColor: this.editData.recSubTitleColor,
            //goodslist: this.editData.goodslist,
            goodsGroup: tempGoodsGroup,
            count: this.editData.count,
            pageCount: this.editData.pageCount
          };
          // this.list.splice(this.nowIndex, 1, this.psData);
          this.content = Object.assign(this.content, this.psData);
        } else {
          // this.list.push(Object.assign([], this.list));
          this.content = Object.assign(this.content, this.editData);
        }
        this.loading = !this.loading;
      });
    },
    async onUploadGoodsBgImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message.warning(`[${res.code}]${res.msg}`);
        return;
      }
      this.content.goodsimage = res.data.url;
      //this.content.goodsBgRes = res.data.url;
    },
    async onUploadTitleImg(res, file) {
      this.titleImgloading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.titleimage = res.data.url;
    },
    async dict() {
      let state = await api.goodsGroup.state();
      if (state.code == 200) this.$nextTick(() => (this.grpState = state.data));
      else this.$message.error(state.msg);

      let gosStatus = await api.goods.status();
      if (gosStatus.code == 200)
        this.$nextTick(() => (this.gosStatus = gosStatus.data));
      else this.$message.error(gosStatus.msg);
    },
    /**
     * 商品验证
     * @param rule
     * @param value
     * @param callback
     * @returns {*}
     */
    goodsValid(rule, value, callback) {
      if (!this.editData.goodsGroup.code) var e = new Error("请选择商品");
      return callback(e);
    }
  },
  mounted() {
    if (this.pageTimeValue) {
      this.content.timevalue = this.content.timevalue || this.pageTimeValue;
    }
    this.content.pageCount = this.content.pageCount
      ? this.content.pageCount
      : "8";
    this.dict();
  },
  watch: {
    "pageTimeValue"(new_val) {
      if (new_val) {
        this.content.timevalue = new_val;
      }
    }
  }
};
</script>

<style lang="scss" rel="stylesheet/scss">
.recommend-list-wrap {
  margin-bottom: 15px;

  .set-title-goods-container {
    display: flex;
    align-items: center;
    margin: 10px 0;

    .img {
      width: 72%;

      img {
        display: block;
        max-width: 100%;
      }
    }

    .button-list {
      margin-left: 10px;
    }
  }

  .move-image {
    position: absolute;
    left: 20px;
    top: 90px;
    width: 50%;
    height: 100px;
    z-index: 100;

    img {
      max-height: 100px;
    }
  }

  // upload
  .topic-image-upload {
    .image {
      display: block;
      width: 100%;
    }

    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }

  .table-images {
    max-width: 100%;
  }

  // content title
  .content-setting {
    color: #fff;
    background-color: #13c2c2;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }

  // bg swiperdot color and bgimg
  .data-wrap {
    margin-bottom: 5px;
  }

  .demonstration,
  .clear-a {
    vertical-align: 9px;
    color: #fff;
  }

  .clear-a {
    cursor: pointer;
  }

  .el-icon-circle-plus-outline {
    font-size: 35px;
    color: #c7bdbd;
    padding-top: 28px;
  }

  // large yellow btn
  .title-bg-image {
    // margin-bottom: 10px;
    position: relative;
    flex: 1;

    .el-upload--text {
      display: block;
      width: 100%;
      margin: 0 auto;
    }
  }

  .el-button--warning {
    width: 100%;
  }

  .topic-image-picker {
    padding: 10px 0;
  }

  // init set wrap
  .set-title-goods {
    width: 80%;
    margin: 0 auto 10px;
  }

  .image-height-wrap .el-table__body td {
    height: 100px;
  }

  .image-height-wrap th {
    .cell {
      font-weight: normal;
      color: #333333;
    }
  }

  // pop layer
  .rec-list-dialog {
    .el-form-item {
      margin-bottom: 15px;
    }

    .el-color-picker {
      vertical-align: -8px;
    }

    .el-form-item__label {
      text-align: left;
      padding-left: 20px;
      color: #606266;
    }

    .entry-content {
      width: 50%;
      margin-right: 35px;
    }
  }

  .goods-img-min {
    max-width: 100%;
    max-height: 30px;
  }
}

.el-row {
  text-align: center;
  // margin-bottom: 10px;
  .el-col-6 {
    width: auto;
  }
  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 35px;
    background-color: #f2f2f2;
    padding-left: 15px;
  }
  .block {
    display: flex;
    padding: 20px 0;
    padding-left: 15px;
    padding-right: 15px;
    .el-col-24 {
      float: none;
    }
    .pl {
      margin-left: 40px;
    }
    .btn-block:last-child {
      margin-left: 40px;
    }
    .add-button {
      margin-left: 40px;
    }
  }
}
</style>
