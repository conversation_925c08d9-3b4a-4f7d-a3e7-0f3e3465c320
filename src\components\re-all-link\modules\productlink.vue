<template>
    <div class="product-link">
        <el-input size="small" style="margin-bottom:5px" v-model="key" placeholder="关键字">
            <el-button slot="append" icon="el-icon-search" @click="getList()"></el-button>
        </el-input>
        <el-table size="mini" ref="multipleTable" :data="list"  highlight-current-row @selection-change="onSelect" style="margin-bottom:5px" v-loading="loading">
            <el-table-column type="selection" width="35"></el-table-column>
            <el-table-column label="商品名称" width="230">
                <template slot-scope="scope"><p>{{scope.row.showName}}</p>
                </template>
            </el-table-column>
            <el-table-column label="库存数" >
                <template slot-scope="scope">
                    {{scope.row.availableQty}}
                </template>
            </el-table-column>
            <el-table-column label="图片" width="100">
                <template slot-scope="scope"><img style="display:block;width:100%;" :src="scope.row.imageUrl">
                </template>
            </el-table-column>
        </el-table>

        <el-pagination
                small
                layout="pager"
                :current-page="pagination.current"
                :page-size="pagination.size"
                :total="pagination.total"
                @current-change="getList">
        </el-pagination>
        
        <div slot="footer" class="dialog-footer">
            <el-button size="small" @click="closeAddGoods">取 消</el-button>
            <el-button size="small" type="primary" @click="confirmGoods">确定</el-button>
        </div>
    </div>
</template>

<script>
    import api from 'api';
    import {AppWebsite} from 'config';

    export default {
        data() {
            return {
                id: '',
                key: '',
                sku_key: '',
                list: [],
                pagination: {
                    size: 5,
                    current: 1,
                    total: 0
                },
                loading: false,
                manualId: '',
                multipleSelection: [],
                selectData: []
            }
        },
        methods: {
            async getList(page = 1) {
                this.pagination.current = page;
                this.pagination.size = 5;
                this.loading = true;
                const params = {
                    offset: this.pagination.current,
                    limit: this.pagination.size,
                }
                const searchParam ={
                    showName: this.key || '',
                }
                let pms = Object.assign(params, searchParam);
                const result = await api.goods.selectGoods(pms);

                this.loading = false;
                if (result.code == 200) {
                    let page = result.data;
                    this.$nextTick(() => {
                        this.list = page.list;
                        this.pagination.total = page.total;
                        /* 设置选中 */
                        if (!this.list || !this.list.length
                            || !this.selectData || !this.selectData.length)
                        	return;
                        this.list.map((v, i) => {
                            this.selectData.map((sv, j) => {
                                if (v.id == sv.id) {
	                                return setTimeout(() => {
                                        this.$refs.multipleTable.toggleRowSelection(v); //设置选中
                                    }, 100);
                                }
                            });
                        });
                    });
                } else {
                    this.$message.error(result.msg);
                }
            },
            closeAddGoods() {
				//取消
                this.selectData = [];
                this.$refs.multipleTable.clearSelection();
			},
			confirmGoods(val) {
                //添加
                this.$emit('select', this.selectData)
			},
            onSelect(objs) {
	            let gos = Object.assign([], this.list);
	            for (let i = 0; i < objs.length; i++) {
	            	let obj = objs[i];
		            let id = obj.id;
	            	for (let j = 0; j < gos.length; j++) {
	            		let go = gos[j];
			            if (!go)
	            			continue;
	            		if (id == go.id) {
                            gos[j] = undefined;     //清空相同元素
				            break;
                        }
                    }
		            if (this.selIdxOf(id) == -1)     //已选中列表中不存在此id，则添加
			            this.selectData.push(obj);
                }
                /* 将剩余不同元素（未选中），从this.selectData剔除 */
                for (let i = 0; i < gos.length; i++) {
	            	let go = gos[i];
	            	if (!go)
	            		continue;
	                let idx = this.selIdxOf(go.id);
	                if (idx >= 0)
			            this.selectData.splice(idx, 1);
                }
            },
	        /**
             * 查询所在已选ID列表的索引位置。
             * 不在列表中，则返回"-1"
	         * @param id {@link Number}：查询元素；
	         * @returns {number}
	         */
	        selIdxOf(id) {
                let sels = this.selectData;
                if (!id || !sels || !sels.length)
                	return -1;
                for (let i = 0; i < sels.length; i++) {
                	let sel = sels[i];
                	if (id == sel.id)
                		return i;
                }
                return -1;
	        }
        },
        mounted() {
            this.getList();
        },
        filters: {
            tradeType(value) {
                return {
                    1: '国内贸易',
                    2: '跨境贸易',
                }[ value ] || ''
            }
        }
    }
</script>
<style lang="scss" scoped rel="stylesheet/scss">
    .product-link {
        border:1px solid #0cdcdc;
        padding: 3px;
    }
</style>