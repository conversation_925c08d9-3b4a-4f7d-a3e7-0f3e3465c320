<template>
    <div>
        <!--选择所需组件并排序-->
        <p class="blank_20">选择组件:</p>
        <div class="select-components">
            <div v-for="(item,index) in current.componentsList" :key="index">
                <el-input v-model.lazy="item.num" @change="changeData(item)" size="mini"></el-input>
                <span>个{{item.label}}</span>
            </div>
        </div>
        <p class="blank_20">拖动排序组件</p>
        <ul class="sort-components">
            <li v-for="item in current.selectComponentList"   :key="item.id" class="tab_container">
                <el-tag
                        closable
                        :disable-transitions="false"
                        @close="handle_tabs_close(item)">
                    {{item.label}}
                </el-tag>
            </li>
            <!--<li v-for="item in current.selectComponentList" :key="item.id">{{item.label}}</li>-->
        </ul>
        <!--选项卡后面的组件数据需要填在pageData里-->
        <p class="blank_20"></p>
        <p class="blank_20">编辑当前组件:</p>
        <el-radio-group v-model="current.currentComponent" @change="selectCurrentComponent">
            <el-radio :label="item.id" v-for="(item,index) in current.selectComponentList" :key="index">{{item.label}}
            </el-radio>
        </el-radio-group>
        <p class="blank_20"></p>
        <p class="edit-header" v-if="current.currentComponent.length>0">{{textArr(current.currentComponent)}}</p>
        <div v-for="item in current.selectComponentList"
             :key="item.id"
             v-show="current.currentComponent===item.id"
             v-if="current.selectComponentList.length>0">
            <components :is="item.name"
                        :pageData="current.pubData[item.id]"
                        :label="item.id"
                        :componentsList='current.componentsList'
                        :branchCode="branchCode"
                        @listenData="getData"
                        @listenCurrentRow="getEditRow"
            ></components>
        </div>
    </div>
</template>

<script>
    import Sortable from 'sortablejs'
    import {common} from 'api'
    import allComponents from './components'

    export default {
        props: ['content', 'flag', 'branchCode'],
        name: "edit-component",
        data() {
            return {
                maxNum: 3,
                current: {
                    componentsList: [],
                    currentComponent: '',
                    selectComponentList: [],//选中的组件列表
                    pubData: {},
                },
                sortable: null,
            }
        },
        mounted() {
            if (!common.isEmptyObject(this.content)) {
                this.initData()
            } else {
                this.initComponent()//初始化components
            }
            //排序
            let el;
            if (this.flag === 'pub') {
                el = document.querySelectorAll('.sort-components')[0]
            } else {
                this.pageInit()
                el = document.querySelectorAll('.sort-components')[1]
            }
            this.setSort(el)

        },
        components: allComponents.components,
        methods: {
            handle_tabs_close(tab) {
                for(let i=0;i<this.current.selectComponentList.length;i++){
                    if(tab===this.current.selectComponentList[i]){
                        this.current.selectComponentList.splice(i,1)
                        // this.current.componentsList
                    }
                }
                for(let i=0;i<this.current.componentsList.length;i++){
                    if(tab.name===this.current.componentsList[i].name){
                        let num=this.current.componentsList[i].num-1
                        this.$set(this.current.componentsList[i],"num",num)
                    }
                }
                const commitData = {
                    flag: this.flag,
                    data: this.current
                };
                this.$store.dispatch('storeData', commitData)

            },
            initComponent() {
                //获取组件列表
                let componentsList = _.cloneDeep(allComponents.componentsList)
                if (this.flag === 'pub') {
                    this.current.componentsList = componentsList
                } else {
                    let components = componentsList.filter(item => {
                        return item.name !== 'tabSwitch'
                    })
                    this.current.componentsList = components
                }
            },
            pageInit() {
                this.current = {
                    componentsList: [],
                    currentComponent: '',
                    selectComponentList: [],//选中的组件列表
                    pubData: {}
                }
                this.initComponent()
                //填充数据
                const currentSwitch = this.$store.state.page.currentSwitch;
                const pageData = this.$store.state.page.pageData

                if (pageData.length > 0) {
                    const index = common.getRepeatResult('name', currentSwitch, pageData)
                    if (index >= 0) {
                        const currentRow = this.$store.state.page.currentRow;
                        if (pageData[index].data[currentRow[0]]&&pageData[index].data[currentRow[0]][currentRow[1]]) {
                            this.current = _.cloneDeep(pageData[index].data[currentRow[0]][currentRow[1]])

                        }
                    }
                }
            },
            initData() {
                //填入数据
                this.current = Object.assign({}, this.content)
            },
            selectCurrentComponent(val) {
                const commitData = {
                    flag: this.flag,
                    data: this.current
                }
                this.$store.dispatch('storeData', commitData)
                if (this.flag === 'pub') {
                    this.$store.dispatch('setEditStatus', false)
                }
            },
            changeData(item) {
                if (item.name === 'tabSwitch' || item.name === 'topList' || item.name === 'highSort') {
                    if (Number(item.num) > 1) {
                        item.num = 1;
                        this.$message.warning(`最多选择1个${item.label}`)
                    }
                }
                // 解除组件最大个数的限制
                // else {
                //     if (Number(item.num) > this.maxNum) {
                //         item.num = this.maxNum
                //         this.$message.warning(`最多显示${this.maxNum}个组件`)
                //     }
                // }
                if (Number(item.num) === 0) {
                    this.$confirm('设置0，该组件的据数会被删除，是否继续', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(_ => {
                        this.setData(item)
                        console.log('改变组件个数')
                        const commitData = {
                            flag: this.flag,
                            data: this.current
                        };
                        this.$store.dispatch('storeData', commitData)
                        return true
                    }).catch(_ => {
                        this.$message({
                            type: 'info',
                            message: '已取消'
                        });
                        let self_num=0;
                        for(let i=0;i<this.current.selectComponentList.length;i++){
                            if(item.name===this.current.selectComponentList[i].name){
                                self_num++
                            }
                        }
                        for(let i=0;i<this.current.componentsList.length;i++){
                            if(item.name===this.current.componentsList[i].name){
                                this.$set(this.current.componentsList[i],"num",self_num)
                            }
                        }
                        return false
                    })
                }
                else {
                    let self_len=parseInt(item.num);
                    for(let i=0;i<this.current.selectComponentList.length;i++){
                        if(item.name===this.current.selectComponentList[i].name){
                            let arr=this.current.selectComponentList[i].id.split(this.current.selectComponentList[i].name);
                            if(self_len<=parseInt(arr[1])){
                                this.current.selectComponentList.splice(i,1)
                                i=i-1;
                            }
                        }
                    }
                }
                if(parseInt(item.num)){
                    this.setData(item)
                    console.log('改变组件个数')
                    const commitData = {
                        flag: this.flag,
                        data: this.current
                    };
                    this.$store.dispatch('storeData', commitData)
                }
            },
            setData(item) {
                let arr = []
                this.current.componentsList.forEach((item, index) => {
                    if (item.num > 0) {
                        for (let i = 0; i < item.num; i++) {
                            //初始化组件数据
                            if (!this.current.pubData[item.name + i]) {
                                this.current.pubData[item.name + i] = this.current.pubData[item.name + i] = Object.assign({}, allComponents[item.name])
                            }
                            //列出需要排序的所有组件
                            arr.push({
                                name: item.name,
                                label: item.label + (i + 1),
                                id: item.name + i
                            })
                        }
                    }

                })
                if (this.current.selectComponentList.length === 0) {
                    this.current.selectComponentList = arr
                } else {
                    // 去除组件重复选择
                    this.current.selectComponentList = common.removeRepeat(this.current.selectComponentList, arr)
                    //去除num为0的组件
                    if (Number(item.num) === 0) {
                        let zeroArr = []
                        let reArr = this.current.selectComponentList.filter(sItem => {
                            if (sItem.name === item.name) {
                                zeroArr.push(sItem)
                            }
                            return (sItem.name !== item.name)
                        })
                        this.current.selectComponentList = reArr
                        //去除pubData里对应的数据
                        const index = this.getIndex(this.current.currentComponent)
                        if (this.current.currentComponent.slice(0, index) === item.name) {
                            this.current.currentComponent = ''
                        }
                        zeroArr.forEach(sItem => {
                            delete this.current.pubData[sItem.id]
                        })
                    }
                }
            },
            getData(data) {
                console.log(this.flag + '-' + data.key + '-组件传来的数据')
                this.current.pubData[data.key] = data.data
                const commitData = {
                    flag: this.flag,
                    data: this.current
                }
                this.$store.dispatch('storeData', commitData)
            },
            getIndex(val) {
                //定位数值所在位置
                let re = /[0-9]+.?[0-9]*/;
                if (re.test(val)) {
                    return val.search(re)
                }
            },
            textArr: function (value) {
                //转换成文本
                if (!value) return '';
                let arr = this.current.selectComponentList.filter(item => {
                    return value === item.id
                });
                if(arr.length){
                    return arr[0].label
                }

            },
            getEditRow(data) {
                console.log('选项卡坐标')
                if (!data.key || data.key === 'tab') {
                    this.$store.dispatch('setEditStatus', false)
                    return
                }
                this.$store.dispatch('setEditStatus', false)
                this.$store.dispatch('subCurrentSwitch', data.key)
                this.$store.dispatch('subCurrentRow', data.data.slice())
                this.$nextTick(_ => {
                    this.$store.dispatch('setEditStatus', true)
                })
            },
            setSort(el) {
                this.sortable = Sortable.create(el, {
                    ghostClass: 'sortable-ghost',
                    setData: function (dataTransfer) {
                        dataTransfer.setData('Text', '')
                    },
                    onEnd: evt => {
                        const targetRow = this.current.selectComponentList.splice(evt.oldIndex, 1)[0];
                        this.current.selectComponentList.splice(evt.newIndex, 0, targetRow)
                        console.log('拖拽change')
                        const commitData = {
                            flag: this.flag,
                            data: this.current
                        }
                        this.$store.dispatch('storeData', commitData)
                    }
                });
            }
        }

    }
</script>


<style scoped lang="scss">

    .edit-header {
        line-height: 40px;
        background: #fbbc05;
        font-size: 16px;
        padding-left: 10px;
        margin-bottom: 10px;
    }

    .select-components {
        @include flexbox($rowRank: flex-start, $wrap: wrap, $columnRank: flex-start);

        div {
            @include flexbox($rowRank: flex-start);
            margin-bottom: 3px;

            .el-input {
                width: 45px;
            }

            span {
                display: inline-block;
                width: 85px;
            }
        }
    }

    .sort-components {
        @include flexbox($rowRank: flex-start, $wrap: wrap, $columnRank: flex-start);

        li {
            /*width: 80px;*/
            /*padding: 5px;*/
            /*border: 1px solid #ccc;*/
            margin: 5px;
            /*text-align: center;*/
        }
    }
</style>
