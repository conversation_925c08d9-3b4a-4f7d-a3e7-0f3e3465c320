import Vue from 'vue'
import Router from 'vue-router'
import intercept from './intercept'

import auth from './routes/auth'
import apps from './routes/apps'
import sys from './routes/sys'

Vue.use(Router)

const menuList = JSON.parse(localStorage.getItem('menuList'));
const backFrom = localStorage.getItem('backFrom');

let url = (menuList && menuList.length > 0) ? menuList[backFrom === 'pc' ? 1 : 0].actionUrl : '/auth/login';

let routes = [
    { path: '/', redirect: url},
    ...auth,
    ...apps,
    ...sys
]
const router = new Router({
  routes: routes
})

intercept(router, Vue)

export default router
