<template>
    <div class="topic-menu-list">
        <el-form class="data-form" size="mini" label-width="120px" label-suffix=" : ">
            <el-form-item label="查看全部链接">
                {{content.link | moreLink}}
                <el-button @click="toOpenTopicDialog" type="primary">添加链接</el-button>
            </el-form-item>
        </el-form>
        <el-button @click="toAdd" class="btn-block mb-10" type="primary">添加页面</el-button>
        <el-table
                :data="list"
                size="mini"
                style="width: 100%">
            <el-table-column
                    label="图片">
                <template slot-scope="scope">
                    <div class="container">
                        <div class="img"><img :src="scope.row.image"></div>
                        <div class="button-list">
                            <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑
                            </el-button>
                            <el-button size="mini" @click="toRemove(scope.row)" type="danger">删除</el-button>
                        </div>
                    </div>
                    <div class="link-desc">{{scope.row.link | link}}</div>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog class="banner-dialog" title="添加品牌" :visible.sync="addDialog">
            <el-form class="data-form" size="mini" label-width="90px" label-suffix=" : ">
                <el-form-item label="品牌Logo">
                    <el-upload
                            class="topic-image-upload"
                            ref="upload"
                            accept="image/jpeg,image/jpg,image/png,image/gif"
                            :show-file-list="false"
                            :before-upload="() => {loading = true; return true;}"
                            :on-success="onUploadImage">
                        <img v-if="dataForm.image" :src="dataForm.image" class="image">
                        <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div class="topic-image-picker">
                菜单链接填选项<span>({{dataForm.link | link}})</span>
            </div>
            <gen-link @select="onSetLink" :tabs="tabs"></gen-link>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeAddDialog">取 消</el-button>
                <el-button size="small" type="primary" @click="confirm">确定</el-button>
            </div>
        </el-dialog>
        <el-dialog class="banner-dialog" title="选择链接" :visible.sync="addTopicDialog">
            <gen-link @select="onSetTopicLink" :tabs="tabs"></gen-link>
        </el-dialog>
    </div>
</template>

<script>
    import base from '../base'

    export default {
        extends: base,
        contentDefault: {
            list: [],
            link: {},
        },
        data() {
            return {
                loading: false,
                addDialog: false,
                addTopicDialog: false,
                tabs: [
                    { label: '页面', value: 'topic' },
                ],
                dataForm: {
                    image: '',
                    link: {},
                },
            }
        },
        computed: {
            list() {
                const list = _.get(this, 'content.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
            link: {
                get: function () {
                    const link = _.get(this, 'content.link')
                    if (link) {
                        return link
                    } else {
                        return {};
                    }
                },
                set: function (link) {
                    this.content.link = link;
                }
            }
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            },
            moreLink(data) {
                if (!data || !data.type) {
                    return '';
                }
                return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
            }
        },
        methods: {
            toOpenTopicDialog() {
                this.addTopicDialog = true;
            },
            closeAddDialog() {
                this.addDialog = false;
            },
            toRemove(data) {
                this.list.splice(this.list.indexOf(data), 1)
            },
            toEdit(data, index) {
                this.currentData = data;
                this.currentIndex = index;
                this.dataForm = Object.assign({}, data);
                this.isEdit = true;
                this.addDialog = true;
            },
            toAdd() {
                this.isEdit = false;
                this.dataForm = {
                    image: '',
                    link: {},
                };
                this.addDialog = true;
            },
            onSetTopicLink(link) {
                this.addTopicDialog = false;
                //this.link = link;
                this.$set(this.content, 'link', link)
            },
            onSetLink(link) {
                this.dataForm.link = link
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.dataForm.image = res.data.url
            },
            confirm() {
                if (!this.dataForm.image) {
                    this.$message.warning('请上传图片');
                    return false;
                }
                this.closeAddDialog();
                if (this.isEdit) {
                    this.currentData = Object.assign(this.currentData, this.dataForm);
                    this.list.splice(this.currentIndex, 1, this.currentData);
                } else {
                    this.list.push(Object.assign({}, this.dataForm));
                }
            },
        },
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .topic-menu-list {
        .container {
            display: flex;
            align-items: center;
            .img {
                padding: 10px;
                width: 35%;
                margin-right: 50px;
                img {
                    display: block;
                    width: 100%;
                }
            }
            .button-list {
                margin-left: 10px;
            }
        }
        .link-desc {
        }
        .topic-image-upload {
            .image {
                display: block;
                width: 100%;
            }
            .uploader-icon {
                width: 200px;
                height: 200px;
                line-height: 200px;
                border: 1px solid $border-base;
                font-size: 50px;
            }
        }
        .topic-image-picker {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }

</style>
<style lang="scss" rel="stylesheet/scss">
    .topic-banner {
        .banner-dialog {
            .el-dialog__body {
                padding-top: 10px;
            }
        }
    }

    .el-loading-spinner {
        top: auto !important;
        margin-top: auto !important;
    }
</style>
