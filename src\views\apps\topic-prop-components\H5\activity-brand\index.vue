<template>
	<div>
		<tab-switch :page-data="content.switch"
		            label="switch"
		            @listenData="getData"
		            @listenCurrentRow="getEditRow"
		            :componentsList="componentsList"></tab-switch>
		<promotion :page-data="content.promotion"
		           label="promotion"
		           :branchCode="topic.branchCode"
		           @listenData="getData"></promotion>
		<el-dialog title="编辑当前页" :visible.sync="dialogVisible" width="40%">
			<p class="blank_20">选择组件:</p>
			<el-checkbox-group v-model="selectComponent" @change="setComponent">
				<el-checkbox :label="item.name" v-for="(item,index) in componentsList" :key="index">{{item.label}}
				</el-checkbox>
			</el-checkbox-group>
			<p class="blank_20">编辑组件:</p>
			<el-radio-group v-model="currentComponent">
				<el-radio :label="item.name" v-for="(item,index) in selectComponentList" :key="index">{{item.label}}
				</el-radio>
			</el-radio-group>
			<p class="blank_20"></p>
			<header class="edit-header">{{textArr(currentComponent)}}</header>
			<collection v-if="isShow.collection"
			            :pageData="content.collection"
			            label="collection"
			            :branchCode="topic.branchCode"
			            :clearNum="clearNum"
			            @listenData="getData" ></collection>
		</el-dialog>

	</div>
</template>

<script>
	import tabSwitch from '../../../components/public/tab-switch'
	import promotion from '../../../components/common/promotion'
	import collection from '../../../components/public/brand-collection'
	import base from "../../base";
	import activityPage from '../../../mixins/activityPage'
	import {common} from 'api'

	export default {
		name: "activityBrand",
		extends: base,
		mixins: [activityPage],
		contentDefault: {
			switch: {
				color: '#2A2A2A',
				hoverColor: '#333',
				lineColor: '#00DC82',
				bgColor: '#EBEBEB',
				isBtn: false,
				list: [
					{
						"name": "正在抢购",
						"content": [
							{
								"title": "shop",
							}
						]
					},
					{
						"name": "活动预告",
						"content": [
							{
								"title": "prepare",
							}
						]
					}
				]
			},
			currentRow: [0, 0],
			list: [
				[{collection: {list:[], title:''},componentsName:["collection"]}],
				[{collection: {list:[], title:''},componentsName:["collection"]}]
			],
			promotion: {
				list: [],
				count: 3
			},
			collection: {
				list: [],
				title: ''
			}
		},
		data() {
			return {
				isShow: {
					promotion: false,
					collection: false
				},
				clearNum: 1,
				componentsList: [
					{name: 'collection', label: '品牌集合'}
				]
			}
		},
		mounted(){
			this.setConfig();
		},
		components: {
			tabSwitch, collection, promotion
		},
		methods: {
			getData(data) {
				// 返回组件的数据
				if (this.content.list.length === 0) {
					return
				}
				data=_.cloneDeep(data);
				if (data.key === 'collection') {
					if (!this.content.list[this.content.currentRow[0]]) {
						this.$set(this.content.list, this.content.currentRow[0], [])
					}
					if (!this.content.list[this.content.currentRow[0]][this.content.currentRow[1]]) {
						this.$set(this.content.list[this.content.currentRow[0]], this.content.currentRow[1], {})
					}

					this.$set(this.content.list[this.content.currentRow[0]][this.content.currentRow[1]], data.key, data.data)
					return
				}
				this.content[data.key] = data.data;
			},
			getEditRow(data) {
				// data为选中的当前行,
				if (!this.content) return;
				if (data.key === 'tab') {
					this.content.currentRow = data.data;
					return
				}
				this.content.currentRow = data.data;
				this.setConfig();
				this.showComponents()
			},
			setConfig(){
				//填入数据
				this.clearData();
				if(this.content.list.length>0&&this.content.list[this.content.currentRow[0]]){
					if(this.content.list[this.content.currentRow[0]][this.content.currentRow[1]]){
						this.content.collection=_.cloneDeep(this.content.list[this.content.currentRow[0]][this.content.currentRow[1]]['collection']);
					}
				}
			},
			clearData(){
				//清空数据
				this.clearNum++;
				this.content.collection.title="";
				this.content.collection.list.length>0?
					this.content.collection.list.splice(0,this.content.collection.list.length)
					:null;
				this.selectComponent.splice(0,this.selectComponent.length)
			},
		}
	}
</script>

<style scoped>

</style>
