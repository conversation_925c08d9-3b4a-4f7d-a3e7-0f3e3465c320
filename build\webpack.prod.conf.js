var path = require('path')
var utils = require('./utils')
var webpack = require('webpack')
var config = require('../config')
var merge = require('webpack-merge')
var baseWebpackConfig = require('./webpack.base.conf')
var CopyWebpackPlugin = require('copy-webpack-plugin')
var HtmlWebpackPlugin = require('html-webpack-plugin')
var ExtractTextPlugin = require('extract-text-webpack-plugin')
var OptimizeCSSPlugin = require('optimize-css-assets-webpack-plugin')
var env = process.env.BUILD_ENV;
var webpackConfig = merge(baseWebpackConfig, {
  module: {
    rules: utils.styleLoaders({
      sourceMap: config[ env ].productionSourceMap,
      extract: true
    })
  },
  devtool: config[ env ].productionSourceMap ? '#source-map' : false,
  output: {
    path: config[ env ].assetsRoot,
    filename: utils.assetsPath('js/[name].[chunkhash].js'),
    chunkFilename: utils.assetsPath('js/[id].[chunkhash].js')
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': process.env.NODE_ENV
    }),
    new webpack.LoaderOptionsPlugin({
      minimize: true,
      debug: false
    }),
    new webpack.optimize.UglifyJsPlugin({
      beautify: false,
      comments: false,
      compress: {
        warnings: false,
        drop_console: true,
        drop_debugger: true,
        collapse_vars: true,
        reduce_vars: true,
      }
    }),
    new ExtractTextPlugin(utils.assetsPath('css/[name].[contenthash].css')),
    // Compress extracted CSS. We are using this plugin so that possible
    // duplicated CSS from different components can be deduped.
    new OptimizeCSSPlugin({
      cssProcessorOptions: {
        safe: true
      }
    }),
    //new webpack.optimize.CommonsChunkPlugin({
    //  name: 'vendors',
    //  filename: 'vendors.[hash].js'
    //}),
    new webpack.DefinePlugin({
      'process.env': {
        NODE_ENV: '"prod"'
      }
    }),
    // copy custom static assets
    new CopyWebpackPlugin([
      {
        from: path.resolve(__dirname, '../static'),
        to: config[ env ].assetsSubDirectory,
        ignore: [ '.*' ]
      }
    ]),
    new HtmlWebpackPlugin({
      filename:  'index.html',
      template: './src/template/index.html',
      inject: true,              // js插入位置
      chunksSortMode: 'dependency'
    }),
    function () {
      this.plugin("done", function (stats) {
        if (stats.compilation.errors && stats.compilation.errors.length) {
          console.log(stats.compilation.errors);
        }
      });
    },
  ]
})

module.exports = webpackConfig
