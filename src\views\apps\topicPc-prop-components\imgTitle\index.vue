<template>
  <div class="topic-image">
    <!-- <div style="margin: 10px 0">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-radio-group v-model="content.activeKey">
            <el-radio
              :label="index"
              v-for="(item,index) in menu"
              :key="index"
              :disabled="index>1"
            >{{item}}</el-radio>
          </el-radio-group>
        </el-col>
      </el-row>
    </div>-->
    <!--模块有效时间设置-->
    <el-row :gutter="20">
      <div class="title">模块有效时间设置</div>
      <div class="block">
        <el-col :span="24">
          <el-date-picker
            v-model="content.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions0"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-col>
      </div>
    </el-row>

    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="8">
        <div class="block">
          <div style="width: 400px">
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :show-file-list="false"
              :on-success="onUploadImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
          <el-button class="btn-block" @click="content.bgRes = ''">清除背景图</el-button>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">文字颜色设置</div>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">主标题文字颜色：</span>
          <div>
            <el-color-picker v-model="content.main_title_color" size="mini"></el-color-picker>
          </div>
          <span class="demonstration pl">副标题文字颜色：</span>
          <div>
            <el-color-picker v-model="content.sub_title_color" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">标题设置</div>
      <div class="block noflex">
        <el-col :span="24">
          <el-input placeholder="请输入内容" v-model="content.main_title">
            <template slot="prepend">主标题:</template>
          </el-input>
        </el-col>
        <el-col :span="24">
          <el-input placeholder="请输入内容" v-model="content.sub_title">
            <template slot="prepend">副标题:</template>
          </el-input>
        </el-col>
      </div>
    </el-row>

    <!--活动链接设置-->
    <el-row :gutter="20">
      <div class="title">标题链接设置</div>
      <!-- <el-col :span="24">
        <el-input placeholder="请输入内容" v-model="content.url"></el-input>
      </el-col> -->
      <div class="block">
        <el-radio-group v-model="linkType">
          <el-radio :label="'link'">链接</el-radio>
          <el-radio :label="'point'">锚点</el-radio>
          <!-- <el-radio :label="'topic'">店铺</el-radio> -->
          <el-radio :label="'stores'">商详</el-radio>
        </el-radio-group>
      </div>

      <div class="topic-image-picker">
        <el-input placeholder="链接地址" v-model.trim="content.url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="linkType==='link'">
        <all-link @select="onSetLink" :tabs="[{label: '活动页', value: 'page'}]" :params="{branchCode: topic.branchCode, from: 'pc'}"></all-link>
      </div>

      <div v-if="linkType==='point'" class="block">
        <el-radio-group v-model="content.url"  @change="handleChangeFloor">
          <el-radio-button v-for="count in coreLength" :key="count" :label="`#floor${count - 1}`">楼层{{count}}</el-radio-button>
        </el-radio-group>
      </div>

      <!-- <el-col :span="24" v-if="linkType==='topic'">
        <control-page @select="onSetLink" :params="{branchCode: topic.branchCode}"></control-page>
      </el-col> -->

    </el-row>
  </div>
</template>
<script>
import base from "../base";
import api from "api";

export default {
  name: "wonder-active",
  extends: base,
  contentDefault: {
    timevalue: "",
    bgRes: "",
    main_title_color: "#000000",
    sub_title_color: "#000000",
    main_title: "",
    sub_title: "",
    activeKey: 1,
    url: ""
  },
  props: {
    coreLength: Number,
    pageTimeValue: Array
  },
  data() {
    return {
      linkType: "link",
      pickerOptions0: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      loading: false
    };
  },
  computed: {},
  created() {
    this.debounce = _.debounce(this.changeLink, 1000);
  },
  mounted () {
    if (this.pageTimeValue) {
      this.content.timevalue = this.content.timevalue || this.pageTimeValue;
    }
  },
  watch: {
    "pageTimeValue"(new_val) {
      if (new_val) {
        this.content.timevalue = new_val;
      }
    },
    'content.url': {
      handler(val, oldVal) {
        if (val && this.linkType === 'link') {
          this.debounce();
        }
      }
    }
  },
  methods: {
    handleChangeFloor(value) {
      this.content.url = value;
    },
    onSetLink(link) {
      this.content.url = link.meta.page_url;
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.bgRes = res.data.url;
    },
    async changeLink() {
      if (this.content.url) {
        const result = await api.topic.checkPageUrl({ url: this.content.url });
        if (((result || {}).data || {}).status != 200) {
          this.$message.error('跳转链接不存在');
          this.content.url = '';
        }
      }
    }
  }
};
</script>

<style lang="scss">
.el-row {
  text-align: center;
  .title {
    text-align: left;
    line-height: 35px;
    background-color: #f2f2f2;
    padding-left: 15px;
  }
  .el-col-8 {
    width: auto;
  }
  .block {
    display: flex;
    padding: 20px 0;
    padding-left: 15px;
    padding-right: 15px;
    .el-col-24 {
      float: none;
    }
    .pl {
      margin-left: 40px;
    }
    .btn-block:last-child {
      margin-left: 40px;
    }
  }
  .noflex {
    display: block;
    .el-col-24:last-child {
      margin-top: 15px;
    }
  }
}
</style>
<style lang="scss" scoped>
.headline {
  margin-bottom: 10px;
}
</style>