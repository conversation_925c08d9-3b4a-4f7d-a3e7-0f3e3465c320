<template>
    <div class="topic-image">
        <div class="block" style="margin-botom:4px">
            <el-date-picker
                    v-model="content.timevalue"
                    type="datetimerange"
                    :picker-options="pickerOptions0"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    align="right">
            </el-date-picker>
        </div>
        <div class="topic-image-tips">
            注：大图请使用具有切片功能的<b>横幅广告</b>组件，此组件只适合于不想切片的<b>小图</b>（gif动图、二维码图、需长按保存的图），上传体积不允许超过<b>2M</b>。
        </div>
        <div class="bg-img">
            <el-container style="height: auto; border: 1px solid #eee">
                <el-header height="50px"
                           style="background-color: rgb(19, 194, 194);text-align: center;padding-top: 15px">
                    <label class="demonstration">背景色:</label>
                    <el-color-picker v-model="content.color" size="mini" @active-change="onSelect"></el-color-picker>
                    <span style="margin-left: 50px"></span>
                    <a @click="imgOnclick" style="cursor: pointer">清除背景图</a>
                </el-header>
                <el-main>
                </el-main>
            </el-container>
        </div>
        <el-upload
                class="topic-image-upload"
                ref="upload"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                :show-file-list="false"
                :before-upload="() => {loading = true; return true;}"
                :on-success="onUploadImg">
            <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
        <br/>
        <el-upload
                class="topic-image-upload"
                ref="upload"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                :max-size="1"
                :show-file-list="false"
                :on-success="onUploadImage">
            <el-button class="btn-block" type="primary" :loading="loading">上传单图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
        <div class="topic-image-info">
            <div class="name">{{linkName}}</div>
            <el-input placeholder="链接地址" v-model.trim="content.link.page_url" >
            </el-input>
            <div class="data"></div>
            <div class="del el-icon-delete" @click="onResetLink"></div>
        </div>

        <div class="topic-image-picker">跳转链接</div>
        <page-link @select="onSetLink" :params="{branchCode: branchCode}"></page-link>
    </div>
</template>
<script>
    import api from "api";
    import { getUrlParam } from "config";
    export default {
        props:{
            pageData:[Array,Object],
            branchCode:String,
            label:String,
            clearNum:{
                type:Number,
                default:1
            }
        },
        name: 'streamer',
        data() {
            return {
                pickerOptions0: {
                    shortcuts: [
                        {
                            text: "未来一周",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来一个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来三个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来六个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来一年",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                                picker.$emit("pick", [start, end]);
                            }
                        }
                    ]
                },
                loading: false,
                content:{}
            }
        },
        created(){
            this.content=_.cloneDeep(this.pageData)
            this.debounce = _.debounce(this.changeLink, 1000);
        },
        computed: {
            linkName() {
                return this.content.link.page_name || ''
            }
        },
        watch: {
            'content':{
                deep: true,
                handler(val) {
                    this.$emit('listenData',{key:this.label,data:val})
                }
            },
            'clearNum':function(){
                this.content=_.cloneDeep(this.pageData)
            },
            'content.link.page_url': {
                handler(val, oldVal) {
                if (val) {
                    this.debounce();
                }
                }
            }
        },
        methods: {
            onSetLink(link) {
                this.content.link = link.meta;
            },
            onResetLink() {
                this.content.link = {
                    page_url: '',
                    page_name:''
                }
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: "warning"
                    });
                    return;
                }
                this.content.image = res.data.url;
            },
            async onUploadImg(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.bgRes = res.data.url;
            },
            onSelect(val) {
                this.content.color = val;
                this.content.bgRes = this.toColor16(val);
            },
            imgOnclick() {
                this.content.bgRes = '';
                this.content.color = '#000000';
            },
            toColor16(str) {
                if (/^(rgb|RGB)/.test(str)) {
                    var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
                    var strHex = "#";
                    for (var i = 0; i < aColor.length; i++) {
                        var hex = Number(aColor[i]).toString(16);
                        if (hex === "0") {
                            hex += hex;
                        }
                        strHex += hex;
                    }

                    if (strHex.length !== 7) {
                        strHex = str;
                    }
                    return strHex.toUpperCase();
                } else {
                    return str;
                }
            },
            async changeLink() {
                if (this.content.link.page_url) {
                if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.content.link.page_url)) {
                    this.$message.error('跳转链接格式不正确');
                    this.content.link.page_url = '';
                } else {
                    let linkPageUrl = getUrlParam(this.content.link.page_url, 'url');
                    const result = await api.topic.checkPageUrl({ url: linkPageUrl });
                    if (((result || {}).data || {}).status != 200) {
                    this.$message.error('跳转链接不存在');
                    this.content.link.page_url = '';
                    }
                }
                }
            }
        }
    };
</script>
<style lang="scss" scoped rel="stylesheet/scss">


    .topic-image-tips {
        padding: 5px 0;
        font-size: 12px;
        color: #999;

        b {
            color: $color-danger;
        }
    }

    .topic-image-info {
        position: relative;
        overflow: hidden;
        height: 62px;
        padding-bottom: 10px;
        margin: 5px 0;
        border: $border-base;
        font-size: 12px;
        .name {
            position: relative;
            overflow: hidden;
            height: 26px;
            padding: 0 5px;
            margin-bottom: 3px;
            font-size: 14px;
            line-height: 26px;
            border-bottom: $border-base;
        }

        .data {
            position: relative;
            overflow: hidden;
            height: 16px;
            padding: 0 5px;
            line-height: 16px;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #999;
        }
        .del {
            position: absolute;
            top: 0;
            right: 0;
            padding: 7px;
            border-left: $border-base;
            background: #fff;
            cursor: pointer;
            &:hover {
                background: $color-base-silver;
                color: #fff;
            }
        }
    }

    .topic-image-picker {
        line-height: 40px;
        text-align: center;
        background: $color-base-gray;
        color: $border-color-hover;
    }
</style>
<style>
    .topic-image-upload .el-upload {
        width: 100%;
    }
</style>
