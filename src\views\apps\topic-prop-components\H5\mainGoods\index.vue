<template>
	<div class="topic-image">
    <div class="title">模块有效时间设置</div>
    <el-radio-group v-model="content.mode">
      <el-radio :label="1">
        固定时段
        <el-date-picker v-model="content.timevalue" type="datetimerange" :picker-options="pickerOptions2" range-separator="至"  start-placeholder="开始日期" end-placeholder="结束日期" align="right">
          </el-date-picker>
      </el-radio>
      <el-radio :label="2">
        周期循环
        <el-button style="marginTop: 10px" :disabled="content.mode === 1" @click="settingTime" type="primary" size="mini">配置</el-button>
      </el-radio>
    </el-radio-group>
    <el-row :gutter="24">
      <div class="title">模块属性设置</div>
      <!-- <el-col :span="6">
        <div class="block">
          <span class="demonstration">背景色</span>
          <div>
            <el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :on-success="uploadTopUrl"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传顶栏背景图</el-button>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :on-success="onUploadBgImg"
            >
              <el-button class="btn-block" type="primary" :loading="loading">上传模块背景图</el-button>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <el-button @click="clearBg">清除背景图</el-button>
      </el-col>
      <el-col :span="6" style="marginTop: 10px">
        <el-upload
          class="topic-image-upload"
          ref="upload"
          accept="image/jpeg,image/jpg,image/png,image/gif"
          :show-file-list="false"
          :on-success="uploadMainTitleImg"
        >
          <el-button class="btn-block" type="primary" :loading="loading">上传主标题</el-button>
        </el-upload>
      </el-col>
      <el-col :span="6" style="marginTop: 10px">
        <el-button @click="clearSubtitle">清除标题</el-button>
      </el-col> -->
      <el-col :span="24" class="headline">
        <el-input class="entryNameBox" placeholder="请输入内容" v-model="content.titleName" :maxlength="10">
          <template slot="prepend">标题名称</template>
        </el-input>
      </el-col>
      <el-col :span="24" class="headline">
        <el-input class="entryNameBox" placeholder="请输入内容" v-model="content.jumpName" :maxlength="4">
          <template slot="prepend">入口名称</template>
        </el-input>
      </el-col>
      <el-col :span="24" class="headline">
        <el-input placeholder="请输入内容" v-model="content.jumpUrl">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">展示个数设置</div>
      <el-col :span="24">
        <el-radio v-model="content.count" :label="6">一页6个</el-radio>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">展示页数</div>
      <el-col :span="24">
        <el-select v-model="content.pageCount" placeholder="请选择" disabled>
          <el-option label="1页" :value="1" />
        </el-select>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">商品配置</div>
      <el-col :span="24" style="marginBottom: 20px">
        选品方式：
        <el-select size="small" @change="changeProductsType" v-model="content.selectProductType" placeholder="请选择">
          <el-option label="指定商品" value="appointProduct" />
          <el-option label="指定商品组" value="appointProductGroup" />
          <el-option label="系统自动" value="systemAuto" />
        </el-select>
      </el-col>
      <el-col :span="24" v-if="content.selectProductType === 'appointProductGroup'">
        商品组ID：
        <el-input style="width: 200px" size="small" placeholder="请输入内容" v-model="productGroupId" />
      </el-col>
      <el-table
        v-if="content.selectProductType === 'appointProduct'"
        :data="productsArr"
        size="mini"
      >
        <el-table-column label="显示序号" type="index" width="100" />
        <el-table-column label="指定商品ID">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row"
              size="mini"
              placeholder="请输入指定商品ID"
              clearable
              style="width: 200px"
              @input="changeInput($event, scope.$index)"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <el-row class="btnBox" :gutter="20">
      <el-button v-if="content.selectProductType !== 'systemAuto'" type="primary" size="mini" @click="checkBindCsuOrProductGroup">确认</el-button>
    </el-row>
    <loop-by-week
      v-if="showTimeVis"
      :weekData="content.weekData"
      @cancelModal="showTimeVis = false"
      @confirmSetLoopTime="confirmSetLoopTime"
    >
    </loop-by-week>
  </div>
</template>
<script>
  import LoopByWeek from '../../../components/loopByWeek.vue';
  import base from "../../base";
  import api from "api";
  export default {
    props: {
      core: Object,
    },
    components: {
      LoopByWeek,
    },
    name: 'mainGoods',
    extends: base,
    contentDefault: {
      timevalue: [],
      mode: 1,
      weekData: [],
      titleName: '',
      pageCount: 1,
      count: 6,
      jumpName: '更多',
      bgColor: '#fff',
      bgTopUrl: '//upload.ybm100.com/ybm/app/layout/cmsimages/2022-1/d7d3f71ffe5b526702f42e25e9687ccb.png',
      list: [],
      bgRes: "",
      selectProducts: [],
      bgContentUrl: '',
      titleUrl: '//upload.ybm100.com/ybm/app/layout/cmsimages/2022-1/406c33bba81bd44af0c2d239484d6246.png',
      jumpUrl: 'ybmpage://commonh5activity?cache=0&url=https://app.ybm100.com/public/2021/6/16401.html'
    },
    data() {
      return {
        showTimeVis: false,
        loading: false,
        productsArr: [],
        productGroupId: '',
        pickerOptions2: {
          shortcuts: [
            {
              text: "未来一周",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来三个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来六个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一年",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                picker.$emit("pick", [start, end]);
              }
            }
          ]
        }
      }
    },
    computed: {},
    created() {
      this.initData();
      if (this.content.mode === undefined) {
        this.content.mode = 1;
      }
      if (this.content.weekData === undefined) {
        this.content.weekData = [];
      }
      if (!this.content.weekData.length && this.core.weekData && this.core.weekData.length) {
        this.content.weekData = this.core.weekData;
      }
    },
    methods: {
      confirmSetLoopTime(data) {
        this.$set(this.content, 'weekData', data);
        this.showTimeVis = false;
      },
      settingTime() {
        if ((this.core.timevalue || []).length === 0) {
          this.$message.error('请先设置开始结束时间');
          return;
        }
        this.showTimeVis = true;
      },
      initData() {
        console.log('content', this.content);
        // this.productsArr = _.get(this, "content.selectProducts");
        this.productsArr = JSON.parse(JSON.stringify(this.content.selectProducts || []));
        this.productGroupId = this.content.selectProductGroupId;
      },
      changeInput(val, index) {
        this.$set(this.productsArr, index, Number(val.trim()));
      },
      changeProductsType() {
        this.productsArr = Array.from({length: 6}, v => '');
        this.content.selectProductGroupId = '';
        this.productGroupId = '';
      },
      clearBg() {
        this.content.bgContentUrl = '';
        this.content.bgTopUrl = '//upload.ybm100.com/ybm/app/layout/cmsimages/2022-1/d7d3f71ffe5b526702f42e25e9687ccb.png';
        this.content.bgColor = '#FFFFFF';
      },
      clearSubtitle() {
        this.content.titleUrl = '//upload.ybm100.com/ybm/app/layout/cmsimages/2022-1/406c33bba81bd44af0c2d239484d6246.png';
      },
      async onUploadBgImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.content.bgContentUrl = res.data.url;
      },
      async uploadMainTitleImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.content.titleUrl = res.data.url;
      },
      async uploadTopUrl(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.content.bgTopUrl = res.data.url;
      },
      async checkBindCsuOrProductGroup() {
        let canSave = true;
        this.productsArr.forEach((item) => {
          if (isNaN(item) || item < 0) {
            canSave = false;
          }
        });
        if (!canSave) {
          this.$message.error('指定商品ID只能输入数字');
          return;
        }
        const params = {
          type: this.content.selectProductType === 'appointProduct' ? 1: 2,
          exhibitionId: this.productGroupId,
          csuIds: this.productsArr.filter(i => i).map(Number),
        }
        const result = await api.topic.checkBindCsuOrProductGroup(params);
        if ((result.data.data || {}).checkResult) {
          if (this.content.selectProductType === 'appointProduct' ) {
            this.$set(this.content, 'selectProducts', this.productsArr)
          } else {
            this.content.selectProductGroupId = this.productGroupId;
          }
          this.$message.success('绑定成功')
        } else {
          this.content.selectProductGroupId = '';
          if (this.content.selectProductType === 'appointProduct' ) {
            this.$message.error(`以下商品id绑定失败：${((result.data.data || {}).failureCsuIds || []).join()}`)
          } else {
            this.$message.error(result.data.msg)
          }
        }
      },
    }
  }
</script>

<style lang="scss">
  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
  .el-row {
    text-align: center;
    .entryNameBox {
      margin: 10px 0;
    }
  }
  .confirmIcon {
    color: #fff;
  }
  .productsDataBox {
    margin: 10px 0;
  }
  .btnBox {
    text-align: right;
    margin-right: 30px !important;
    margin-top: 20px;
  }
</style>