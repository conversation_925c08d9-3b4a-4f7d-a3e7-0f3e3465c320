<template>
  <div>
    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">背景颜色</span>
          <div>
            <el-color-picker
              v-model="content.banner.bgRes"
              size="mini"
            ></el-color-picker>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">页面名颜色</span>
          <div>
            <el-color-picker
              v-model="content.banner.subject_title_color"
              size="mini"
            ></el-color-picker>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <span class="demonstration">标语说明颜色</span>
          <div>
            <el-color-picker
              v-model="content.banner.subject_introduce_color"
              size="mini"
            ></el-color-picker>
          </div>
        </div>
      </el-col>

      <el-col :span="8">
        <div class="block">
          <span class="demonstration">上传背景图</span>
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :on-success="onUploadImg"
            >
              <el-button class="btn-block" type="primary">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">
                支持类型：png/jpg/jpeg/gif
              </div>
            </el-upload>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">大小title设置</div>
      <el-col :span="24">
        <div class="block">
          <span class="demonstration"></span>
          <div>
            <el-input
              placeholder="请输入内容"
              v-model="content.banner.subject_title"
            >
              <template slot="prepend">大title</template>
            </el-input>
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="block">
          <span class="demonstration"></span>
          <div>
            <el-input
              placeholder="请输入内容"
              v-model="content.banner.subject_introduce"
              style="margin-top: 5px"
            >
              <template slot="prepend">小title</template>
            </el-input>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <div class="title">截止时间</div>
      <el-col :span="24">
        <div class="block">
          <span class="demonstration"></span>
          <div>
            <el-date-picker
              v-model="content.all_limit_time"
              type="datetimerange"
              :picker-options="pickerOptions"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </div>
        </div>
      </el-col>
    </el-row>
    <br />
    <!--选择商品-->
    <all-link
      @select="onSetLink"
      :tabs="tabs"
      :params="{
        goodsGroup: {
          seledShow: false,
          minSel: 1,
          search: {
            state: 1,
            branchCode: topic.branchCode,
          },
        },
      }"
    ></all-link>
  </div>
</template>

<script>
import base from "../../base";
export default {
  extends: base,
  contentDefault: {
    banner: {
      branchCode: "",
      exhibitionId: "",
      list: [],
      bgRes: "#578EE1",
      subject_title: "值得买",
      subject_title_color: "#ffffff",
      subject_introduce: "上新限时特惠",
      subject_introduce_color: "#ffffff",
    },
    branchCode: null,
    exhibitionId: null,
    all_limit_time: null,
  },
  created() {
    this.content.all_limit_time ? this.content.all_limit_time : null;
    this.content.branchCode ? this.content.branchCode : null;
    this.content.exhibitionId ? this.content.exhibitionId : null;
  },
  data() {
    return {
      tabs: [{ label: "商品组", value: "goodsGroup" }],
      all_limit_time: null,
      pickerOptions: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      goodsGroup: [],
    };
  },
  methods: {
    async onUploadImg(res, file) {
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.banner.bgRes = res.data.url;
    },
    onSetLink(link) {
      this.content.branchCode = link.data.branchCode;
      this.content.exhibitionId = link.data.code;
      this.content.banner.list = [];
    },
  },
};
</script>

<style scoped lang="scss">
.el-row {
  text-align: center;

  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
</style>
