<template>
  <div class="main-content">
    <el-row class="mb-10">
      <el-col :span="24">
        <el-row class="search-wrap" type="flex" :gutter="10" style="align-items: center;width: 100%;">
          <el-popover placement="top" trigger="hover">
            <el-button @click="cancel()" :loading="loading" type="info" icon="el-icon-refresh" size="mini"
              plain>清空</el-button>
            <el-button @click="changeSize()" style="margin-left: 10px;" :loading="loading" slot="reference"
              type="primary" icon="el-icon-search" size="mini" plain>查询</el-button>
          </el-popover>
          <el-select v-model="searchParam.status" placeholder="请选择">
            <el-option label="全部" :value="''"></el-option>
            <el-option label="未开始" :value="1"></el-option>
            <el-option label="上线" :value="2"></el-option>
            <el-option label="已结束" :value="3"></el-option>

          </el-select>
          <!--  <el-input v-model="searchParam.status" size="mini" clearable></el-input> -->
          <span style="width: 70px;margin-left: 8px;">状态：</span>
          <el-input v-model="searchParam.customerGroupId" placeholder="人群ID" size="mini" clearable></el-input>

          <span style="width: 100px;margin-left: 8px;">人群ID：</span>
          <el-input v-model="searchParam.page_id" placeholder="页面ID" size="mini" clearable></el-input>
          <span style="width: 100px;margin-left: 8px;">弹窗ID：</span>
          <el-input v-model="searchParam.frameName" placeholder="搜索页面名称" size="mini" clearable></el-input>
          <span style="width: 120px;margin-left: 8px;">弹窗名称：</span>
          <el-col :span="12">
            <el-button type="primary" size="mini" icon="el-icon-plus" @click="showModal()">添加</el-button>
            <!-- <el-button type="danger" size="mini" @click="synchronous()">全部同步</el-button> -->
            <el-button type="" size="mini" @click="deleteMore()">删除</el-button>
            <el-button type="" size="mini" @click="copy">复制</el-button>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-table :loading="loading" :data="dataList" v-loading="loading" height="600" size="mini" border @sort-change="handlesortChange"
      style="width: 100%;margin-top: 20px;" @selection-change="handleSelectionChange">
      <div slot="empty" class="empty-wrap">
        <i class="iconfont icon-tishi"></i>
        <span>尚未添加活动页</span>
      </div>
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column v-for="item in columns" :prop="item.field" :key="item.label" :label="item.title"
        :show-overflow-tooltip="true" width="170%" align="center"  :sortable="item.field == 'sortNo'?'custom':false"  >
        <template slot-scope="scope">
          <div v-if="item.field == 'creator'">{{ scope.row.operator.create && scope.row.operator.create.userName }}
          </div>
          <div v-else-if="item.field == 'createTime'">{{ scope.row.create_time && formatTime(scope.row.create_time) }}
          </div>
          <div v-else-if="item.field == 'updateTime'">{{ scope.row.update_time && formatTime(scope.row.update_time) }}
          </div>
          <div v-else-if="item.field == 'updator'">{{ scope.row.operator.edit && scope.row.operator.edit.userName }}
          </div>
          <div v-else-if="item.field == 'state'">
          {{ checkTimeStatus(scope.row.startTime, scope.row.endTime) }}
          </div>
          <div ref="closepopover" v-else-if="item.field == 'sortNo'">
            <el-popover placement="top" trigger="click">
              <div class="priorityCheck" style="text-align: center;">
                <el-input size="mini" maxlength="11" @input="popoverValue = popoverValue.replace(/[^\d]/g, '')"
                  v-model="popoverValue" />
                <el-button style="width: 150px;height: 30px;text-align: center !important;" type="primary"
                  icon="el-icon-check" size="mini" @click="changePriority(scope.$index, scope.row)"></el-button>
                <el-button style="width: 150px;height: 30px;text-align: center;" type="info" icon="el-icon-close"
                  size="mini" @click="hidePriority()"></el-button>
              </div>
              <div  style="width: 150px !important;height: 20px;cursor: pointer;" size="mini" slot="reference"
                @click="setPriority(scope.row.priority)">{{ scope.row.priority }}</div>
            </el-popover>
          </div>
          <div v-else slot-scope="scope">{{ scope.row[item.field] }}</div>
        </template>

      </el-table-column>
      <el-table-column label="操作" width="170%" align="center" fixed="right">
        <template slot-scope="scope">
          <span style="color:rgb(110,161,211);cursor: pointer;" @click="info(scope.row)">查看</span>
          <span style="color:rgb(110,161,211);margin-left: 10px;cursor: pointer;" @click="edit(scope.row)">修改</span>


        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page.sync="searchParam.pageFrom" :page-sizes="[10, 20, 50, 100]"
      :page-size="searchParam.pageSize" @size-change="changeSize" @current-change="changePage"
      layout="total, sizes, slot, jumper, prev, pager, next" :total="totalSize"></el-pagination>
    <el-dialog :visible.sync="dialogVisible" title="创建浮窗" style="width: 1800px;" :before-close="handleClose">
      <el-form ref="form" label-position="left" :model="form" :rules="rules" label-width="200px" hide-required-asterisk
        :disabled="dialogType == 2">
        <el-form-item label="浮窗名称：" class="is-required" prop="frameName">
          <el-input v-model="form.frameName" maxlength="20" id="frameName"></el-input>
        </el-form-item>
        <el-form-item label="人群范围">
          <div style="display: flex;align-items: center;">
            <el-radio v-model="form.customerGroupIdRadio" :label="1">全部人群</el-radio>
            <div><el-radio v-model="form.customerGroupIdRadio" :label="2">指定人群id</el-radio>
              <!-- <el-input
                style="width: 140px;" v-model="form.customerGroupId" placeholder="单行输入">
              </el-input> -->
              <el-select
                  style="width: 140px;"
                  v-model.trim="form.customerGroupValue"
                  filterable
                  :filter-method="optionFilter"
                  placeholder="请输入人群id"
                  clearable
                  @clear="options = []"
                  @change="selectCrowd"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="浮窗生效时间：" class="is-required" prop="times">
          <div style="display: flex;align-items: center;">
            <el-date-picker v-model="form.startTime" type="datetime" :picker-options="option"
            style="border: none !important;"
              value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间">
            </el-date-picker>
            &nbsp;
            <span> 至</span>
            &nbsp;
            <el-date-picker v-model="form.endTime" :picker-options="option" type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间">
            </el-date-picker>
            <el-radio :label="2" style="padding: 3px 0px;margin-right:3px;margin-left: 8px;"
              @click.native.prevent="radioSelect(2)" v-model="form.timeType"
              @click="toloopcirculateTime">周期循环</el-radio>
            <el-button @click="toloopcirculateTime"
              style="background-color: rgb(19,194,194);color: white;">配置</el-button>
          </div>
        </el-form-item>
        <el-form-item label="APP浮窗图片" class="is-required" prop="appImageUrl">
          <uploadImg @listenImage="listenImage" :image="form.appImageUrl" style="width: 200px;" :key="form.appImageUrl">
          </uploadImg>
        </el-form-item>
        <el-form-item label="APP跳转链接" class="is-required" prop="appJumpUrl">
          <el-input v-model="form.appJumpUrl"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submit" v-if="dialogType != 2">
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- //设置周期循环 -->
    <loopcirculateTime ref="loopcirculateTime" @loopcirculateTimeBack="loopcirculateTimeBack"></loopcirculateTime>
  </div>

</template>

<script>
import api from "api";
import loopcirculateTime from "../components/loopcirculateTime.vue";
import uploadImg from "@/views/apps/components/upload-image.vue"
import { AppWebsite, getUrlParam } from "config";
export default {
  components: {
    uploadImg, loopcirculateTime
  },
  mounted() {
    this.loadData()
  },

  data() {
    return {
      loading: false,
      popoverValue: "",
      dialogType: 0,
      handleSelectionVal: [],//多选
      option: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 1 * 24 * 3600 * 1000
        }
      },
      searchParam: {
        pageFrom: 1,
        pageSize: 10,
        category: "app",
        page_type: "floatWindow",
        page_id: "",
        frameName: "",
        customerGroupId: "",
        status: "",
      },
      totalSize: 0,
      dialogVisible: false,
      loading: false,
      search_obj: {},
      dataList: [],
      columns: [
        {
          field: "page_id",
          title: "弹窗ID",
          align: "center",
          valign: "middle",
          sortable: true,
        },
        {
          field: "frameName",
          title: "浮窗名称",
          align: "center",
          valign: "middle",
          sortable: true,
          /* }, {
                         field: 'branchName',
                         title: '展示地区',
                         align: 'center',
                         valign: 'middle',
                         sortable: true*/
        },
        {
          field: "customerGroupId",
          title: "人群ID",
          align: "center",
          valign: "middle",
          sortable: true,
        },
        {
          field: "sortNo",
          title: "排序号",
          align: "center",
          valign: "middle",
          sortable: true,
        },
        {
          field: "startTime",
          title: "开始时间",
          align: "center",
          valign: "middle",
          formatter: function (val, row) {
            return ToolUtil.dateFormat(val, "yyyy-MM-dd HH:mm:ss");
          },
        },
        {
          field: "endTime",
          title: "结束时间",
          align: "center",
          valign: "middle",
          formatter: function (val, row) {
            return ToolUtil.dateFormat(val, "yyyy-MM-dd HH:mm:ss");
          },
        },
        {
          field: "state",
          title: "状态",
          align: "center",
          valign: "middle",
          formatter: function (val) {
            switch (val) {
              case 0:
                return "未开始";
                break;
              case 1:
                return "上线";
                break;
              case 2:
                return "已结束";
                break;
            }
          },
        },
        {
          field: "createTime",
          title: "创建时间",
          align: "center",
        },
        {
          field: "creator",
          title: "创建人",
          align: "center",
        },
        {
          field: "updateTime",
          title: "修改时间",
          align: "center",
        },
        {
          field: "updator",
          title: "修改人",
          align: "center",
          valign: "middle",
          sortable: true,
        }
      ],
      options:[],
      form: {
        page_type: "floatWindow",
        category: "app",
        frameName: "",
        customerGroupIdRadio: 1,
        customerGroupId: "",
        startTime: "",
        endTime: "",
        circulateTime: {},
        timeType: 1,
        appImageUrl: "",
        appJumpUrl: "",
        customerGroupValue:""

      },

      rules: {
        frameName: [
          { required: true, message: "请填写浮窗名称", trigger: "blur" },
        ],
        appImageUrl: [
          // { required: true, message: "请选择图片", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!this.form.appImageUrl) {
                callback(new Error("请上传图片"));
              } else {
                return callback();
              }
            }, trigger: "blur"
          },
        ],
        appJumpUrl: [
          { required: true, message: "请输入地址", trigger: "blur" },
        ],
        times:[
          { required: false, message: "请选择图片", trigger: "blur" },
        {
            validator: (rule, value, callback) => {
              if ((!this.form.startTime || !this.form.endTime)) {
                callback(new Error("请选择时间"));
              return
             } else {
                return callback();
              }
            }, trigger: "blur"
          },
        ]
      }
    };
  },
  methods: {
    selectCrowd(e) {
      if (e) {
        this.form.customerGroupId = Number(this.options[0].value.trim());
        this.form.customerGroupValue = this.options[0].label;
      } else {
        this.form.customerGroupId = "";
        this.form.customerGroupValue= "";
      }
      this.$forceUpdate();
    },
    async optionFilter(val) {

      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.options = [];
      }
    },
    closeDialog(){
      this.dialogVisible = false
      this.$refs.form.clearValidate();
    },

    setPriority(value) {
      this.popoverValue = value
    },
    //校验时间有没有超过三年
    verTime(val) {
      // 获取当前时间的时间戳
      const currentTimeStamp = new Date().getTime()

      // 假设要判断的时间戳为 timestamp，这里用一个假设的时间戳来示例
      const timestamp = new Date(val).getTime(); // 假设为2021年1月1日的时间戳

      // 计算时间差值（毫秒数）
      const timeDiff = timestamp - currentTimeStamp;

      // 将毫秒数转换为天数
      const daysDiff = timeDiff / (60 * 60 * 24 * 1000);

      // 判断天数差值是否大于1095天
      if (daysDiff > 1095) {
        return false
      } else {
        return true
      }
    },
    hidePriority() {
      document.body.click()
    },
    async changePriority(index, row,newData) {
      //若为启动页则走新排序

      this.loading = true
      let popoverValue =  newData?newData:this.popoverValue ? Number(this.popoverValue):1
        if(Number(popoverValue)<=0||Number(popoverValue)>99999){
          this.$message.error("优先级范围为1-99999")
          this.loading = false
            return
        }

      let ts = await api.topic.updatePriority([{ "id": row.page_id, "newPriority": popoverValue, ordPriority: row.priority || 0, page_type: "floatWindow" }])
      if (ts.code == 200) {

        this.hidePriority()
        this.loadData();

      }
      else { this.$message.error(ts.msg); this.loading = false }



    },
    loopcirculateTimeBack(data) {
      this.form.circulateTime = data

    },
    toloopcirculateTime() {
      if (this.form.circulateTime) {
        this.$refs.loopcirculateTime.circulateTime = this.form.circulateTime
      } else {
        this.$refs.loopcirculateTime.circulateTime = {}
      }
      this.$refs.loopcirculateTime.showVisible = true
    },

    handlesortChange(val){
        //selfSortFields priority
        this.searchParam.selfSortFields={}
        console.log(val)
        if(val.order=="descending"){
        this.searchParam.selfSortFields.priority=-1
        }else if(val.order=="ascending"){
        this.searchParam.selfSortFields.priority=1
        }else{
        delete this.searchParam.selfSortFields
        }
        this.loadData()
    },
    async copy() {
      if(this.handleSelectionVal.length<1){
        this.$message.error("请选择")
        return
      }
      let data = this.handleSelectionVal.map(element => {
        return {
          page_type: "floatWindow",
          branchCode:"XS000000",
          category: "app",
          timeType: element.timeType,
          circulateTime: element.circulateTime,
          frameName: element.frameName + 'copy',
          customerGroupIdRadio: element.customerGroupIdRadio,
          customerGroupId: element.customerGroupId,
          startTime:"",
          endTime:"",
          appImageUrl: element.appImageUrl,
          appJumpUrl: element.appJumpUrl,
          customerGroupValue:element.customerGroupValue
        }
      });
      console.log(data)
      let asyncOperations=[]
      data.forEach(element => {
        asyncOperations.push(api.topic.add(element))
      });
      Promise.all(asyncOperations)
      .then(() => {
        this.$message.success("复制成功")
        this.loadData()
      });

    },
    //删除
    deleteMore() {
      if(this.handleSelectionVal.length<1){
        this.$message.error("请选择")
        return
      }
      console.log(this.handleSelectionVal)
      this.$confirm("是否删除该浮窗?删除后将永不能恢复，后果很严重！", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        let data = this.handleSelectionVal.map(item => item.page_id)
        console.log(data)
        const result = await api.topic.removeMore(data);
        if (result.code == 200) {
          this.$message.success("删除成功");
          this.searchParam.pageFrom=1
          this.loadData();
        } else {
          this.$message.error(result.msg);
        }
      });
    },
    handleSelectionChange(val) {
      //多选
      this.handleSelectionVal = val
    },
    //上传回调
    listenImage(val) {
      this.form.appImageUrl = val.image
    },
    //提交
    async submit() {
      this.$refs['form'].validate(async ok => {
      });

      console.log(this.form)
      if (!this.form.frameName) {
        this.$message.error("填写不完整")
        return
      }
      // if(this.form.customerGroupIdRadio==2&&!this.form.customerGroupId){
      //   this.$message.error("指定人群不能为空")
      //   return
      // }
      if (this.form.timeType == 2 && (!this.form.circulateTime || Object.keys(this.form.circulateTime).length == 0 || !this.form.circulateTime.circulateList || this.form.circulateTime.circulateList.length == 0)) {
        this.$message.error("请选择周期循环时间")
        return
      }
      if ((!this.form.startTime || !this.form.endTime)) {
        this.$message.error("填写不完整")
        return
      }
      if (!this.form.appImageUrl) {
        this.$message.error("填写不完整")
        return
      }
      if ((this.form.startTime > this.form.endTime || !this.verTime(this.form.endTime))) {
        this.$message.error("开始时间不可早于当前时间，有效期请不要超过1095天")
        return
      }
    if(this.form.customerGroupIdRadio==2&&!this.form.customerGroupValue){
      this.$message.error("请选择人群")
        return
    }

      /* if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.form.appJumpUrl)) {
        this.$message.error("跳转链接格式不正确，请检查")
        return
      } */

      if (this.dialogType == 0) {
        const result = await api.topic.add(this.form);
        this.$message.success("新增成功");
        let data=JSON.parse(JSON.stringify(this.form))

        console.log(data)
        data.page_id=result.id
        this.changePriority(0,data,1)
        //this.$emit("changePriority",0,data,1)

        this.dialogVisible = false
        this.$refs.form.clearValidate();
        this.loadData();
      } else {
        const result = await api.topic.update(this.form.page_id, this.form);
        this.$message.success("修改成功");
        this.dialogVisible = false
        this.$refs.form.clearValidate();
        this.loadData();
      }

    },
    //编辑
    edit(val) {
      this.dialogType = 1
      this.form = {
        page_id: val.page_id,
        page_type: "floatWindow",
        category: "app",
        branchCode:val.branchCode,
        timeType: val.timeType,
        circulateTime: val.circulateTime,
        frameName: val.frameName,
        customerGroupIdRadio: val.customerGroupIdRadio,
        customerGroupId: val.customerGroupId,
        customerGroupValue:val.customerGroupId,
        startTime: val.startTime,
        endTime: val.endTime,
        appImageUrl: val.appImageUrl,
        appJumpUrl: val.appJumpUrl,
        create_time: val.create_time
      }
      this.optionFilter(this.form.customerGroupId)
      this.dialogVisible = true
    },
    //新增
    showModal() {
      this.dialogType = 0
      this.form = {
        page_type: "floatWindow",
        branchCode:"XS000000",
        category: "app",
        timeType: 1,
        circulateTime: {},
        frameName: "",
        customerGroupIdRadio: 1,
        customerGroupId: "",
        startTime: "",
        endTime: "",
        appImageUrl: "",
        appJumpUrl: "",
        customerGroupValue:""
      }
      this.dialogVisible = true
    },
    info(val) {
      this.edit(val)
      this.dialogType = 2
    },
    handleClose(done) {
      this.$refs.form.clearValidate();
      done()
    },
    changePage(pageNo) {
      this.searchParam.pageFrom = pageNo;
      this.loadData();
    },

    changeSize(pageSize) {

      this.searchParam.pageSize = pageSize || this.searchParam.pageSize;
      this.searchParam.pageFrom = 1;
      this.loadData();
    },
    radioSelect(val) {
      if (this.dialogType == 2) {
        return
      }
      if (val == this.form.timeType && this.form.timeType) {
        this.form.timeType = 1
      } else {

        this.form.timeType = val
      }
    },
    //加载数据
    async loadData() {
      this.loading = true;
      let data = {}
      data.pageFrom = this.searchParam.pageFrom
      data.pageSize = this.searchParam.pageSize
      data.category = this.searchParam.category
      data.page_type = this.searchParam.page_type
      data.selfSortFields = this.searchParam.selfSortFields
      if (this.searchParam.frameName) {
        data.frameName = this.searchParam.frameName
      }
      if (this.searchParam.page_id) {
        data.page_id = this.searchParam.page_id
      }
      if (this.searchParam.customerGroupId) {
        data.customerGroupId = this.searchParam.customerGroupId
      }

      switch (this.searchParam.status) {
        case 1: {
          data.startTime = { _$gte: this.formatTime(new Date()) }
          break
        }
        case 2: {
          data.startTime = { _$lte: this.formatTime(new Date()) }
          data.endTime = { _$gte: this.formatTime(new Date()) }
          break
        }
        case 3: {
          data.endTime = { _$lte: this.formatTime(new Date()) }
          break
        }


      }

      // if(this.searchParam.frameName){
      //   data.frameName=this.searchParam.frameName
      // }
      const result = await api.topic.list(data);
      console.log(result)
      if (result.code == 200) {
        this.dataList = result.data.rows
        this.totalSize = result.data.total
      }
      this.$nextTick(() => {
        this.loading = false;
      })

    },
    //状态判断
    checkTimeStatus(startTime, endTime) {
      const now = new Date(); // 获取当前时间
      startTime = new Date(startTime); // 将开始时间转换为日期对象
      endTime = new Date(endTime); // 将结束时间转换为日期对象

      if (now < startTime) {
        return "未开始";
      } else if (now >= startTime && now <= endTime) {
        return "上线";
      } else {
        return "已结束";
      }
    },
    formatTime(time) {
      // 假设 date 是一个日期类型或时间戳
      const date = new Date(time); // 或者 new Date(timestamp);

      // 获取年、月、日、小时和分钟
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      // 构造转换后的日期字符串
      const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}`;

      return formattedDate
    }
  },
};
</script>
<style>
.is-required .el-form-item__label::after {
  content: "*";
  color: #ff0000;
  margin-left: 4px;
}

.avatar {
  width: 150px !important;
  height: 150px !important;
  display: block;
}
</style>