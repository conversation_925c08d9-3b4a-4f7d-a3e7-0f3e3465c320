import Vue from 'vue'
import App from './App'
import router from 'router'
import store from 'store'
import ElementUI from 'element-ui'
import 'styles/customm.scss'
import 'styles/common.scss'
import loadStyle from 'utils/load-style'
import _ from 'lodash'
import Upload from 'config/upload';
import VueQuillEditor  from 'vue-quill-editor';
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import axios from 'axios'
import VueAxios from 'vue-axios'
import 'config/axios'
// 组件
import components from './components'
import Sortable from 'sortablejs'
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)

window._ = _;
Vue.use(ElementUI)
Vue.use(Upload);
Vue.use(VueAxios, axios)
Vue.use(VueQuillEditor);
Object.keys(components).forEach(function (key) {
  Vue.component(key, components[key])
})
//window.client = new baidubce.sdk.BosClient({
//    endpoint: 'http://bj.bcebos.com',
//    credentials: {
//        ak: '277dbc90042d4611a24ba3bc45baa8d6',
//        sk: '6396a63e75d047c39f2d038f484180a0'
//    }
//})

//全局混入拖拽排序
Vue.mixin({
  data() {
    return {
      rSortable: null
    }
  },
  methods:{
    getRowKeys(row){
      if(row.link){
        if(!row.link.meta){
          return
        }
        return row.link.meta.id
      }
      return row.id?row.id:null;
    },
    getRowKeys_fastEntry(row){
      if(row[0].link){
        if(!row[0].link.meta){
          return
        }
        return row[0].link.meta.id
      }
      return row[0].id?row[0].id:null;
    },
    setSort(){
      const el = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      this.rSortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost',
        setData: function(dataTransfer) {
          dataTransfer.setData('Text', '')
        },
        onEnd: evt => {
          const targetRow = this.content.list.splice(evt.oldIndex, 1)[0]
          this.content.list.splice(evt.newIndex, 0, targetRow);
        }
      })
    },
  }

})


//统一添加删除确定的方法
Function.prototype.confirm=(function(){
  let confirm=function(vm){
    return new Promise((resolve,reject)=>{
      vm.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resolve(true)
      }).catch(() => {
        reject(false)
      });
    })
  }
  return function(){
    let _self=this,
       vm=Array.from(arguments)[0];
    return function(){
        confirm(vm).then(()=>{
          return _self.apply(this,arguments)
        }).catch(()=>{
          vm.$message({
              type: 'info',
              message: '已取消删除'
            });
        })

    }
  }
})()

////////////////////////
new Vue({
  el: '#app',
  router,
  store,
  template: '<App/>',
  components: { App }
})
loadStyle('//at.alicdn.com/t/font_555960_ge9cg673tbj4i.css')
