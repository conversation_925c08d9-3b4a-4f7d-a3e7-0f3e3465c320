<template>
    <div class="topic-menu-list">
        <div class="block" style="margin-bottom: 4px">
            <el-date-picker v-model="content.timevalue" type="datetimerange" :picker-options="pickerOptions0"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
            </el-date-picker>
        </div>
        <div style="margin: 10px 0" v-if="this.categoryList[0].title != '综合页（主会场，广告位，商品流）'">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-radio-group v-model="content.activeKey">
                        <el-radio :label="index" v-for="(item,index) in menu" :key="index" :disabled="index>1">
                            {{item}}
                        </el-radio>
                    </el-radio-group>
                </el-col>
            </el-row>
        </div>
        <div class="bg-img">
            <el-container style="height: auto; border: 1px solid #eee">
                <el-header height="50px"
                           style="background-color: rgb(19, 194, 194);text-align: center;padding-top: 15px">
                    <label class="demonstration">背景色:</label>
                    <el-color-picker v-model="content.color" size="mini" @active-change="onSelect"></el-color-picker>
                    <span style="margin-left: 50px"></span>
                    <a @click="imgOnclick" style="cursor: pointer">清除背景图</a>
                </el-header>
                <el-main>
                    <el-upload
                            class="topic-image-upload"
                            ref="upload"
                            accept="image/jpeg,image/jpg,image/png,image/gif"
                            :show-file-list="false"
                            :before-upload="() => {loading = true; return true;}"
                            :on-success="onUploadImg">
                        <el-button class="btn-block" type="primary" :loading="loading" size="small">上传背景图</el-button>
                        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                    </el-upload>
                </el-main>
            </el-container>
        </div>
        <el-table :data="dataList" v-if="dataList.length>0" ref="multipleTable"
                  @selection-change="handleSelectionChange" style="width: 100%" height="250" size="mini" border>
            <!--<el-table-column type="selection" width="55">-->
            <!--</el-table-column>-->
            <el-table-column fixed label="图片" width="90" align="center">
                <template slot-scope="scope">
                    <el-popover
                            placement="right-end"
                            trigger="focus">
                        <img :src="scope.row.imageUrl" class="avatar" alt="图片"/>
                        <img slot="reference" :src="scope.row.imageUrl" :alt="scope.row.productName"
                             class="avatar goods-img-min"/>
                    </el-popover>
                </template>
            </el-table-column>
            <el-table-column
                    prop="commonName"
                    :show-overflow-tooltip="true"
                    label="药名"
                    align="center"
                    width="150">
            </el-table-column>
            <el-table-column
                    prop="mediumPackageTitle"
                    label="规格"
                    align="center"
                    width="90">
            </el-table-column>
            <el-table-column
                    prop="fob"
                    label="价格"
                    width="90"
                    align="center">
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="130" align="center">
                <template slot-scope="scope">
                    <div class="edit-button">
                        <el-button
                                v-if="scope.$index>0"
                                @click="handleEdit(scope.row)" type="primary" size="mini">
                            设为主推
                        </el-button>
                        <!--<el-button @click="handleCancle(scope.row, scope.$index)" type="warning" size="mini">取消推荐-->
                        <!--</el-button>-->
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <!-- 商品选择列表 -->
        <section>
            <all-link @select="onSetLink"
                      ref="allLinkGoodsIds"
                      :tabs="tabs" :params="{
                    productlink: {
                        search: {             //初始搜索条件
                            status: 1,
                            branchCode: topic.branchCode,
                            productType : 1 //排除秒杀商品
                        },
                    },
                    importGoods: {
                        minSel: 1,
                        search: {
                            status: 1,
                            needFilter : true,
                            branchCode: topic.branchCode
                        }
                    }
                }"></all-link>
        </section>
    </div>
</template>

<script>
    import base from '../base'

    export default {
        extends: base,
        contentDefault: {
            timevalue: '',
            list: [],
            bgRes: '', //背景图
            color: '#ffffff',
            page_url: '',
            image: '',
            activeKey: 1
        },
        data() {
            return {
                tabs: [
                    {label: '商品', value: 'productlink'},
                    {label: '导入商品', value: 'importGoods'}
                ],
                pickerOptions0: {
                    shortcuts: [{
                        text: "未来一周",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                            picker.$emit("pick", [start, end]);
                        }
                    },
                        {
                            text: "未来一个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来三个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来六个月",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                                picker.$emit("pick", [start, end]);
                            }
                        },
                        {
                            text: "未来一年",
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                                picker.$emit("pick", [start, end]);
                            }
                        }
                    ]
                },
                loading: false,
                maxLength: 4,
                selectionTable: [], //请选中的药品列表添加连接
            }
        },
        computed: {
            dataList: {
                get() {
                    var dataList = _.get(this, 'content.list');
                    if (dataList.length < this.maxLength) {
                        // this.$message.error(`您选择的商品包含秒杀，请重新选择${this.maxLength}个商品`)
                    }
                    return dataList || [];
                },
                set(val) {
                    this.content.list = val;
                }
            },
            goodsIds() {
                return !this.dataList ? null : this.dataList.map(item => item.id);
            }
        },
        methods: {
            onSelect(val) {
                this.content.bgRes = this.toColor16(val);
            },
            imgOnclick() {
                this.content.bgRes = '';
                this.content.color = '#ffffff',
                    this.content.image = '';
            },
            async onUploadImg(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.image = res.data.url;
                this.content.bgRes = res.data.url;
            },
            onSetLink(rs) {
                console.log(rs)
                if (rs.tag) {   //"tag"为组件特有属性
                    if(rs.data.length>4){
                        this.$message.warning("您商品选多了，只能选取前4个商品")
                    }
                    if(rs.data.length<4){
                        this.$message.warning("您商品选少了，请选择4个商品")
                    }
                    this.content.list = rs.data.slice(0,4);
                } else {
                    //选中了连接,给选中的商品添加连接
                    if (this.selectionTable.length === 0) {
                        this.$message.error('请先选择商品');
                        return;
                    }
                    this.selectUrl(rs);
                    this.$refs.multipleTable.clearSelection(); //清空选项
                }
            },
            selectUrl(link) {
                this.selectionTable.map((item, array, index) => {
                    let dataListIndex = this.dataList.indexOf(item)
                    if (dataListIndex >= 0) {
                        this.dataList[Index].page_url = link.page_url
                    }
                })
            },
            handleEdit(row) {
                let index = 0;
                this.dataList.forEach(function (item, i) {
                    if (item.id === row.id) {
                        index = i;
                    }
                });
                this.toFirst(this.dataList, index);
            },
            //置顶移动
            toFirst(fieldData, index) {
                if (index != 0) {
                    // fieldData[index] = fieldData.splice(0, 1, fieldData[index])[0]; 这种方法是与另一个元素交换了位子，
                    fieldData.unshift(fieldData.splice(index, 1)[0]);
                }
            },
            handleCancle(row, ind) {
                let _self = this;
                return function () {
                    const index = _self.dataList.indexOf(row)
                    _self.dataList.splice(index, 1)
                    _self.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                }.confirm(_self)(

                )
            },
            handleSelectionChange(val) {
                this.selectionTable = val
            },
            toColor16(str) {
                if (/^(rgb|RGB)/.test(str)) {
                    var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
                    var strHex = "#";
                    for (var i = 0; i < aColor.length; i++) {
                        var hex = Number(aColor[i]).toString(16);
                        if (hex === "0") {
                            hex += hex;
                        }
                        strHex += hex;
                    }

                    if (strHex.length !== 7) {
                        strHex = str;
                    }
                    return strHex.toUpperCase();
                } else {
                    return str;
                }
            }

        }
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .topic-image-upload {
        .image {
            display: block;
            width: 100%;
        }

        .uploader-icon {
            width: 200px;
            height: 200px;
            line-height: 200px;
            border: 1px solid $border-base;
            font-size: 50px;
        }
    }

    .topic-image-picker {
        padding-top: 10px;
        padding-bottom: 10px;
    }

    .edit-button {
        @include flexbox($direction: column, $wrap: wrap, $rowRank: center, $columnRank: flex-end);
    }

    .goods-img-min {
        max-width: 100%;
        max-height: 70px;
    }
</style>
