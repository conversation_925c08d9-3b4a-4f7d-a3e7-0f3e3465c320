import dict from './dict';
import proxy from './proxy';
import { AppWebsite } from '../config/index';

/**
 * 商品
 */
export default {
	imgUrl: 'ybm/product/',         //图片路径（因服务端图片路径不全，需要自行拼接）
	imgUrlMin: 'ybm/product/min/', //（小）图片路径
	async addImg(goods) {
		if (!goods || !goods.length)
			return goods;

		let resWeb = await dict.cdnWebsite();
		if (resWeb.code != 200)
			return goods;

		const website = resWeb.data;
		for (let i = 0; i < goods.length; i++) {
			let item = goods[i];
			if (!item.imageUrl)
				continue;
			item.imageUrl = website + this.imgUrlMin + item.imageUrl;  //修改图片url
		}
		return goods;
	},
	/**
	 * 查询列表
	 * 此方法已修改图片路径
	 * @param pm
	 * @returns {Promise<*>}
	 */
	async selectGoods(pm) {
		let res = await this.select(pm);
		if (res.code == 200)
			await this.addImg(res.data.list);
		return res;
	},
	/**
	 * 列表
	 * @param pm
	 * @returns {Promise<*>}
	 */
	async select(pm) {
		let pms = {
			// url: AppWebsite + 'app/sku/select',
			url: AppWebsite + 'app/sku/listProducts',
			dataType: 'json',
			data: pm,
			head: {
				terminalType: 1,
				// 'Content-Type': 'application/json;charset=UTF-8',
				'Content-Type': 'application/x-www-form-urlencoded',
				isadmin: true,
				referer: window.origin,
			}
		};
		let res = await proxy.post(pms);    //代理请求，外部接口
		// return (res.code != 1) ? {
		// 		code: 500,
		// 		msg: res.msg
		// 	} : {
		// 		code: 200,
		// 		data: res.result
		// 	};
		return (res.code != 1000) ? {
			code: 500,
			msg: res.msg
		} : {
			code: 200,
			data: res.data
		};
	},

	async selectSingleGoods(pm) {
		let res = await this.selectSingle(pm);
		if (res.code == 200)
			await this.addImg(res.data.list);
		return res;
	},
	/**
	 * 列表
	 * @param pm
	 * @returns {Promise<*>}
	 */
	async selectSingle(pm) {
		let pms = {
			url: AppWebsite + 'app/sku/select',
			dataType: 'json',
			data: pm,
			head: {
				terminalType: 1,
				'Content-Type': 'application/json;charset=UTF-8',
				isadmin: true,
			}
		};
		let res = await proxy.post(pms);    //代理请求，外部接口
		return (res.code != 1) ? {
			code: 500,
			msg: res.msg
		} : {
			code: 200,
			data: res.result
		};
	},
	/**
	 * 通过商品编码、区域编码，查询商品信息
	 * @param pm
	 * @returns {Promise<*>}
	 */
	async select4barcodesAndBranchCodes(pm) {
		let pms = {
			url: AppWebsite + 'app/sku/select/barcodes/branchs',
			dataType: 'json',
			data: pm,
			head: {
				terminalType: 1,
				'Content-Type': 'application/json;charset=UTF-8'
			}
		};
		let res = await proxy.post(pms);    //代理请求，外部接口
		if (res.code == 1)
			await this.addImg(res.result);
		return (res.code != 1) ? {
				code: 500,
				msg: res.msg
			} : {
				code: 200,
				data: res.result
			};
	},
	/**
	 * 商品状态
	 * @returns {Promise<undefined>}
	 */
	async status() {
		let pms = {
			url: AppWebsite + 'app/sku/status',
			dataType: 'json',
			head: {
				terminalType: 1,
				'Pragma': `public, max-age=${12 * 60 * 60}`,         //允许使用缓存（单位：秒），兼容http-1.0
				'Cache-Control': `public, max-age=${12 * 60 * 60}`  //允许使用缓存（单位：秒）
			}
		};
		let res = await proxy.post(pms);    //代理请求，外部接口
		return (!res.code) ? {
				code: 200,
				data: res
			} : {
				code: 500,
				msg: '获取商品状态失败。'
			};
	},

    /**
     * 通过商品id集合查询商品信息，过滤掉其他商品状态
     * @param pm
     * @returns {Promise<*>}
     */
    async selectSKUsByOrderId(pm) {
        let pms = {
            url: AppWebsite + 'app/sku/selectSKUsByOrderId',
            dataType: 'json',
            data: pm,
            head: {
                terminalType: 1,
                'Content-Type': 'application/json;charset=UTF-8'
            }
        };
        let res = await proxy.post(pms);    //代理请求，外部接口
        if (res.code == 1 && res.result.list && res.result.list.length > 0)
            await this.addImg(res.result.list);
        return (res.code != 1) ? {
            code: 500,
            msg: res.msg
        } : {
            code: 200,
            data: res.result.list
        };
    },
}
