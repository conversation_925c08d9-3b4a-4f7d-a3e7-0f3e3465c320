<template>
  <div class="topic-menu-list">
    <el-row :gutter="20">
      <div class="title">列表基础配置</div>
      <el-col :span="12">
        <el-radio-group v-model="content.type">
          <el-radio :label="index" v-for="(item,index) in typeList" :key="index">{{item}}
          </el-radio>
        </el-radio-group>
      </el-col>

      <el-col :span="12">
        <div class="block">
          <span class="demonstration">列表背景颜色</span>
          <div>
            <el-color-picker v-model="content.list_body_color" size="mini"></el-color-picker>
          </div>
        </div>
      </el-col>

    </el-row>


    <el-row :gutter="20">
      <div v-if="list.length>0">
        <div class="title" style="margin-bottom: 0">预览已选择的商品</div>
        <el-col :span="24">
          <el-table :data="list"
                    height="300"
                    ref="multipleTable">
            <el-table-column label="药名">
              <template slot-scope="scope">
                <span v-if="scope.row.productName">{{scope.row.productName}}</span>
                <span v-else>{{scope.row.showName}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="药品ID">

            </el-table-column>
            <el-table-column prop="availableQty" label="库存">

            </el-table-column>
          </el-table>
        </el-col>
      </div>
    </el-row>

    <el-row :gutter="20">
      <div v-if="goods_group.length">
        <div class="title" style="margin-bottom: 0">预览已选商品组</div>
        <el-col :span="24">
          <el-table :data="goods_group"
                    height="120"
                    ref="multipleTable">
            <el-table-column prop="name" label="组名">
            </el-table-column>
            <el-table-column prop="code" label="编号">
            </el-table-column>
            <el-table-column prop="branchCode" label="区域号">
            </el-table-column>
          </el-table>
        </el-col>
      </div>
    </el-row>

    <!--选择商品-->
    <all-link ref="all_link"
              @select="onSetLink"
              :tabs="tabs"
              :params="{
                goodsGroup: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
            }"></all-link>
  </div>
</template>

<script>
  import base from "views/apps/topic-prop-components/base.vue";

  export default {
    name: "commodity-flow",
    extends: base,
    contentDefault: {
      list: [],
      goods_group: [],
      type: 0,
      list_body_color: "#f1f1f1"
    },
    data() {
      return {
        loading: false,
        tabs: [
          {label: '商品组', value: 'goodsGroup'}
        ],
        typeList: ['列表模式', '大图模式'],

      }
    },
    filters: {
      link(data) {
        if (!data.type) {
          return '';
        }
        return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
      },
      moreLink(data) {
        if (!data || !data.type) {
          return '';
        }
        return '已选:' + data.label + (data.id ? ',' : '') + (data.id || '');
      }
    },
    computed: {
      list() {
        let list = _.get(this, 'content.list');
        if (list) {
          return list
        } else {
          return [];
        }
      },
      goods_group() {
        let list = _.get(this, 'content.goods_group');
        if (list) {
          return list
        } else {
          return [];
        }
      }
    },
    methods: {
      async onUploadImage(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: "warning"
          });
          return;
        }
        this.content.image = res.data.url;
      },
      onSetLink(link) {
        function handle_arr(arr = []) {
          return arr.map((item) => {
            let obj = {};
            obj.init_img_url = item.init_img_url;
            obj.imageUrl = item.imageUrl;
            obj.productName = item.productName;
            obj.showName = item.showName;
            obj.mediumPackageTitle = item.mediumPackageTitle;
            obj.fob = item.fob;
            obj.id = item.id;
            return obj
          });
        }

        if (link.tag === "goods" || link.tag === "importGoods") {
          let _self_arr = handle_arr(link.data);
          this.content.list = [..._self_arr]
          this.content.goods_group = []
        } else if (link.tag === "goodsGroup") {
          let obj = {};
          obj.name = link.data.name;
          obj.branchCode = link.data.branchCode;
          obj.code = link.data.code;
          this.content.goods_group.splice(0, 1, obj);
          this.content.list = []
        }
      },
      group_delete(row) {
        const index = this.goods_group.indexOf(row);
        this.goods_group.splice(index, 1)
      },
      handleDelete(row) {
        const index = this.list.indexOf(row);
        this.list.splice(index, 1)

      }

    }
  }
</script>
<style scoped lang="scss">

  .el-row {
    text-align: center;

    .title {
      text-align: left;
      line-height: 30px;
      /*background-color: #f2f2f2;*/
      color: #13c2c2;
      padding-left: 10px;
      margin: 10px;
    }
  }


</style>

