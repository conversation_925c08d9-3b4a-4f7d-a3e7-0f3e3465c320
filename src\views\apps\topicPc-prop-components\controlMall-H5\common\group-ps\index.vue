<template>
  <div class="images-box">

    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块图片上传</div>
      <el-col :span="24">
        <div class="block">
          <div>
            <img width="100%" :src="content.bgRes" alt="">
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="block">
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadImg">
              <el-button class="btn-block" type="primary" :loading="loading"> 上传图片</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
          </div>
        </div>
      </el-col>
    </el-row>



    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">
        关联密钥：
        <span style="color: red">
          此模块会根据关联密钥和黄金单品，店铺楼层进行显示绑定。
          可关联多个
        </span>
      </div>
      <el-col :span="24">
        <div class="block">
          <el-input placeholder="输入密钥" v-model="content.pass_word">
            <template slot="prepend">关联密钥</template>
          </el-input>
        </div>
      </el-col>
    </el-row>

  </div>
</template>

<script>
  import base from "views/apps/topic-prop-components/base.vue";
  export default {
    extends: base,
    contentDefault: {
      pass_word:"",
      bgRes: '',
    },
    data() {
      return {
        loading: false,
      }
    },
    methods: {
      async onUploadImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.content.bgRes = res.data.url;
      },
    }
  }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


  .images-box {
    .container {
      display: flex;
      align-items: center;

      .img {
        width: 78%;

        img {
          display: block;
          max-width: 300px;
          height: 100px;
        }
      }

      .button-list {
        margin-left: 10px;
      }
    }

    .content-setting {
      color: #fff;
      background-color: #13c2c2;
      padding: 10px;
      text-align: center;
      font-size: 16px;
      margin-bottom: 10px;
    }

    .el-icon-circle-plus-outline {
      font-size: 35px;
      color: #c7bdbd;
    }

    .topic-image-upload {
      .image {
        display: block;
        width: 100%;
      }

      .uploader-icon {
        width: 200px;
        height: 200px;
        line-height: 200px;
        border: 1px solid $border-base;
        font-size: 50px;
      }
    }

    .topic-image-picker {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .el-row {
    text-align: center;

    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
  }

  .level{
    margin-top: 10px;
  }
</style>
<style lang="scss" rel="stylesheet/scss">
  .topic-banner {
    .banner-dialog {
      .el-dialog__body {
        padding-top: 10px;
      }
    }
  }
</style>
