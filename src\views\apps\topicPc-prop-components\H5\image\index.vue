<template>
  <div class="topic-image">
    <div class="panel-common-header">
      模块图片上传
    </div>
    <div class="updatedImg" v-if="content.image">
      <img :src="content.image"/>
    </div>
    <el-upload
      class="topic-image-upload"
      ref="upload"
      accept="image/jpeg,image/jpg, image/png, image/gif"
      :show-file-list="false"
      :before-upload="() => {loading = true; return true;}"
      :on-success="onUploadImage"
    >
      <el-button class="btn-block" type="primary" :loading="loading">上传单图</el-button>
      <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
    </el-upload>
  </div>
</template>

<script>
import base from "../../base";
export default {
  extends: base,
  contentDefault: {
    image: "",
  },
  data() {
    return {
      loading: false,
      updatedImg: '',
    };
  },
  computed: {
    image() {
      var url = _.get(this, "content.image");
      if (url) {
        return `url(${url})`;
      } else {
        return "";
      }
    }
  },
  methods: {
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.updatedImg = res.data.url;
      this.content.image = res.data.url;
    },
  }
};
</script>

<style lang="scss" scoped rel="stylesheet/scss">
.topic-image-tips {
  padding: 5px 0;
  font-size: 12px;
  color: #999;

  b {
    color: $color-danger;
  }
}

.topic-image-info {
  position: relative;
  overflow: hidden;
  height: 62px;
  padding-bottom: 10px;
  margin: 5px 0;
  border: $border-base;
  font-size: 12px;
  .name {
    position: relative;
    overflow: hidden;
    height: 26px;
    padding: 0 5px;
    margin-bottom: 3px;
    font-size: 14px;
    line-height: 26px;
    border-bottom: $border-base;
  }
  .desc {
    position: relative;
    overflow: hidden;
    height: 16px;
    padding: 0 5px;
    line-height: 16px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .data {
    position: relative;
    overflow: hidden;
    min-height: 16px;
    padding: 0 5px;
    line-height: 16px;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #999;
  }
  .del {
    position: absolute;
    top: 0;
    right: 0;
    padding: 7px;
    border-left: $border-base;
    background: #fff;
    cursor: pointer;
    &:hover {
      background: $color-base-silver;
      color: #fff;
    }
  }
}

.topic-image-picker {
  line-height: 40px;
  text-align: center;
  background: $color-base-gray;
  color: $border-color-hover;
}

.updatedImg {
  width: 100%;
  height: 250px;
  overflow: hidden;
  // img {
  //   width: 100%;
  //   height: 100%;
  // }
}

.panel-common-header {
  height: 35px;
  line-height: 35px;
  background: #f2f2f2;
  padding: 0 0 0 15px;
}
</style>
<style>
.topic-image-upload .el-upload {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}
</style>
