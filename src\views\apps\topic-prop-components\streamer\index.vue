<template>
  <div class="topic-image">
    <div class="block" style="margin-botom:4px">
      <el-date-picker
        v-model="content.timevalue"
        type="datetimerange"
        :picker-options="pickerOptions0"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        align="right"
      ></el-date-picker>
    </div>
    <div class="topic-image-tips">
      注：大图请使用具有切片功能的
      <b>横幅广告</b>组件，此组件只适合于不想切片的
      <b>小图</b>（gif动图、二维码图、需长按保存的图），上传体积不允许超过
      <b>2M</b>。
    </div>
    <el-upload
      class="topic-image-upload"
      ref="upload"
      accept="image/jpeg,image/jpg, image/png, image/gif"
      :max-size="1"
      :show-file-list="false"
      :on-success="onUploadImg"
    >
      <el-button class="btn-block" type="primary" :loading="loading">上传单图</el-button>
      <img :src="content.image" alt class="contentImage" />
      <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
    </el-upload>
    <div class="topic-image-info">
      <div class="name">{{linkName}}</div>
      <el-input placeholder="链接地址" v-model.trim="content.link.page_url"></el-input>
      <div class="data"></div>
      <div class="del el-icon-delete" @click="onResetLink"></div>
    </div>

    <div class="topic-image-picker">跳转链接</div>
    <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
  </div>
</template>
<script>
import base from "../base";
import api from "api";
import { getUrlParam } from "config";

export default {
  name: "streamer",
  extends: base,
  contentDefault: {
    image: "",
    color: "#ffffff",
    link: {
      page_url: "",
      page_name: ""
    },
    timevalue: "",
    activeKey: 1
  },
  data() {
    return {
      pickerOptions0: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      loading: false
    };
  },
  computed: {
    linkName() {
      return this.content.link.page_name || "";
    }
  },
  created() {
    this.debounce = _.debounce(this.changeLink, 1000);
  },
  methods: {
    onSetLink(link) {
      this.content.link = link.meta;
    },
    onResetLink() {
      this.content.link = {
        page_url: "",
        page_name: ""
      };
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.image = res.data.url;
    },
    async changeLink() {
      if (this.content.link.page_url) {
        if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.content.link.page_url)) {
          this.$message.error('跳转链接格式不正确');
          this.content.link.page_url = '';
        } else {
          let linkPageUrl = getUrlParam(this.content.link.page_url, 'url');
          const result = await api.topic.checkPageUrl({ url: linkPageUrl });
          if (((result || {}).data || {}).status != 200) {
            this.$message.error('跳转链接不存在');
            this.content.link.page_url = '';
          }
        }
      }
    }
  },
  //监听input输入值变化
    watch:{
      'content.link.page_url': {
        handler(val, oldVal) {
          if (val) {
            this.debounce();
          }
        }
      }
    }
};
</script>
<style lang="scss" scoped rel="stylesheet/scss">
.topic-image-tips {
  padding: 5px 0;
  font-size: 12px;
  color: #999;

  b {
    color: $color-danger;
  }
}

.topic-image-info {
  position: relative;
  overflow: hidden;
  height: 62px;
  padding-bottom: 10px;
  margin: 5px 0;
  border: $border-base;
  font-size: 12px;

  .name {
    position: relative;
    overflow: hidden;
    height: 26px;
    padding: 0 5px;
    margin-bottom: 3px;
    font-size: 14px;
    line-height: 26px;
    border-bottom: $border-base;
  }

  .data {
    position: relative;
    overflow: hidden;
    height: 16px;
    padding: 0 5px;
    line-height: 16px;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #999;
  }

  .del {
    position: absolute;
    top: 0;
    right: 0;
    padding: 7px;
    border-left: $border-base;
    background: #fff;
    cursor: pointer;

    &:hover {
      background: $color-base-silver;
      color: #fff;
    }
  }
}

.topic-image-picker {
  line-height: 40px;
  text-align: center;
  background: $color-base-gray;
  color: $border-color-hover;
}

.contentImage {
  margin: 15px 0;
  width: 100%;
}
</style>
<style>
.topic-image-upload .el-upload {
  width: 100%;
}
</style>
