<template>
  <div>
    <!--首页选项卡-->
    <p class="blank_20"></p>
    <el-row :gutter="5">
      <el-col :span="4">
        <label>文字:</label>
        <el-color-picker
            v-model="content.color"
            size="mini"
        ></el-color-picker>
      </el-col>
      <el-col :span="6">
        <label>当前文字:</label>
        <el-color-picker
            v-model="content.hoverColor"
            size="mini"
        ></el-color-picker>
      </el-col>
      <el-col :span="6">
        <label>当前标志颜色:</label>
        <el-color-picker
            v-model="content.lineColor"
            size="mini"
        ></el-color-picker>
      </el-col>
      <el-col :span="8">
      </el-col>
    </el-row>
    <div class="blank_10"></div>
    <div v-if="menu">
      <label class="demonstration">选项卡:</label>
      <el-radio-group v-model="content.activeKey">
        <el-radio :label="index+1" v-for="(item,index) in menu" :key="index">{{item}}</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
  let menu=['活动专区','爆款推荐','常购清单']
  localStorage.setItem('menu',JSON.stringify(menu))
  import base from "../base";
  export default {
    extends: base,
    name: "homeTab",
    contentDefault: {
      color: '#676773',
      hoverColor: '#292933',
      lineColor:'#00B377',
      activeKey:1
    },
    data(){
      return {
        menu:menu
      }
    }
  }
</script>

<style scoped>

</style>
