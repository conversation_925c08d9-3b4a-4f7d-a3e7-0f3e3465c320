import {common} from 'api'
export default{
	data(){
		return {
			selectComponent: [],//选中的纯数组
			dialogVisible: false,
			currentComponent: '',
			selectComponentList: []//选中的数组对象
		}
	},
	methods:{
		textArr: function (value) {
			//转换成文本
			if (!value) return ''
			if(common.isArray(value)){
				return value.map(item=>{
					let arr=this.componentsList.filter(sitem=>{
						if(item===sitem.name){
							return sitem.label
						}
					})
					return arr[0].label
				}).join(',')
			}else if(common.isString(value)){
				let arr=this.componentsList.filter(item=>{
					return value===item.name
				})
				return arr[0].label
			}

		},
		setComponent(){
			if (!this.content.list[this.content.currentRow[0]]) {
				this.$set(this.content.list, this.content.currentRow[0], [])
			}
			if (!this.content.list[this.content.currentRow[0]][this.content.currentRow[1]]) {
				this.$set(this.content.list[this.content.currentRow[0]], this.content.currentRow[1], {})
			}
			this.content.list[this.content.currentRow[0]][this.content.currentRow[1]].componentsName=this.selectComponent
			this.showComponents()
		},
		showComponents(){
			this.dialogVisible = true
			for (let key in this.selectComponent) {
				this.isShow[this.selectComponent[key]] = true
			}
			//显示正在编辑当前组件
			this.selectComponentList = this.selectComponent.map(item => {
				let arr = this.componentsList.filter(sitem => {
					return item === sitem.name
				})
				return arr[0]
			})
			//默认选中组件的第一个显示
			this.currentComponent = this.selectComponent[0]
		}
	}
}
