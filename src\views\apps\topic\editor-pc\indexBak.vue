<template>
  <div class="topic-editor" @click.self="selectedIndex = -1" v-loading="loading">
    <div class="topic-editor-head">
      <el-form label-position="left" size="small" label-width="80px" label-suffix="：">
        <el-row :gutter="40">
          <el-col :span="9">
            <el-form-item label="模板名称">
              <el-input v-model="core.page_name" placeholder="页面名称" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="开始结束时间" label-width="110px">
              <el-date-picker
                v-model="core.timevalue"
                type="datetimerange"
                :picker-options="pickerOptions2"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-button @click="goback2start">返回列表</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="40">
          <el-col :span="9">
            <el-form-item label-width="110px" label="用户可见名称">
              <el-input v-model="core.page_title" placeholder="CMS页面" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="是否启用" label-width="110px">
              <el-switch
                v-model="value3"
                active-text="启用"
                inactive-text="停用">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="背景色">
              <el-color-picker v-model="core.background" @change="onSelect"></el-color-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="区域">
              <el-tag >{{ $options.filters.getBranchName(core.branchCode, branchs) }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-dropdown @command="confirmRollback($event, $route.params.id)"
                           v-show="core.versions && core.versions.length">
                <el-button type="primary">
                  <span>回滚</span>
                  <i class="el-icon-caret-bottom el-icon-right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="v in core.versions" :class="{active: v.active}" :command="v.mtime" :key="v.mtime">
                    <span>{{v.mtime | datetime}}</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-dialog :visible.sync="dialogVisible" width="30%">
      <img width="100%" :src="qrCode" alt="">
    </el-dialog>

    <div class="topic-editor-main" @click.self="selectedIndex = -1">
      <list-panel class="topic-editor-list"
                  ref="list"
                  :core="core"
                  @drag="onPickupComponent"
                  @click.native.self="selectedIndex = -1">
      </list-panel>

      <view-panel class="topic-editor-view"
                  ref="view"
                  :core="core"
                  :dragging="dragging"
                  :selectedIndex="selectedIndex"
                  @select="index => selectedIndex = index"
                  @drop="onDropComponent"
                  @custom="onCustomCell"
                  @click.native.self="selectedIndex = -1">
        {{core.page_title || '页面'}}
      </view-panel>
      <prop-panel class="topic-editor-prop"
                  ref="prop"
                  :key="selectedCell ? selectedCell.instance_id : 'page'"
                  :cell="selectedCell"
                  :topic="topicBase"
                  :index="selectedIndex"
                  :coreLength="core.layout && core.layout.length"
                  @remove="onRemoveCell"
                  @moveup="onMoveupCell"
                  @movedown="onMovedownCell"
                  @update-common="onUpdateCellCommon"
                  @update-content="onUpdateCellContent"
                  @click.native.self="selectedIndex = -1">
        <div class="topic-editor-prop-page" slot="page">
          <div class="topic-editor-prop-page-buttons">
            <el-button size="small" type="primary" @click="save()" :loading="saving">保存</el-button>
            <el-button size="small" @click="preview()">预览链接</el-button>
            <el-button size="small" type="danger" @click="publish()" :loading="publishing">发布</el-button>
            <el-button size="small" @click="onlineView()">正式链接</el-button>
          </div>
        </div>
      </prop-panel>
    </div>

    <!--<itemgroup-modal ref="itemgroupModal" @select-itemgroup="onSelectItemgroup"></itemgroup-modal>-->
  </div>
</template>

<script>
  import ShareIconDefault from './share-icon.png'
  import ListPanel from './list-panel'
  import ViewPanel from './view-panel'
  import PropPanel from './prop-panel'
  import api from 'api'
  import {API_SERVER} from 'config'
  import {AppWebsite} from 'config'

  export default {
    name: 'Editor',
    data() {
      return {
        qrCode : '',
        loading: true,
        dialogVisible: false,
        page: '',
        core: {},
        dragging: false,
        selectedIndex: -1,
        selectedComponent: null,
        saving: false,
        publishing: false,
        value3: true,
        branchs: null,
        coreInit: {
          page_name: '',
          page_title: '',
          background: "#fff",
          layout: [],
        },
        pickerOptions2: {
          shortcuts: [{
            text: '未来一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '未来一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '未来三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          },{
            text: '未来六个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit('pick', [start, end]);
            }
          },{
            text: '未来一年',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit('pick', [start, end]);
            }
          }]
        }
      }
    },
    computed: {
      dialogImageUrl(){
        return _.get(this.core, 'previewImg')
      },
      selectedCell() {
        return _.get(this.core, 'layout.' + this.selectedIndex);
      },
      topicBase(){
        return _.pick(this.core, ['_id', 'id', 'page_id', 'page_type', 'page_name', 'page_title', 'category', 'branchCode', 'url', 'startDate', 'endDate', 'state', 'create_time']);
      },
      shareIcon: {
        get() {
          return this.core.share_icon || ShareIconDefault;
        },
        set(value) {
          this.core.share_icon = value;
        }
      },
      isShowProducts: {
        get() {
          return this.core.rel_itemgroup ? !!this.core.rel_itemgroup.group_id : false;
        },
        set(isShow) {
          //if (isShow) {
          //    this.showModal('itemgroupModal');
          //} else {
          //    this.core.rel_itemgroup.group_id = '';
          //    this.core.rel_itemgroup.group_name = '';
          //}
        }
      }
    },
    components: {
      listPanel: ListPanel,
      viewPanel: ViewPanel,
      propPanel: PropPanel,
      //ItemgroupModal
    },
    watch: {
      async [ '$route' ]() {
        //var loading = Loading.service()
        this.core = _.merge({}, this.coreInit);
        try {
          await this.getCore();
        } catch (e) {
        }
        //loading.close()
      }
    },
    methods: {
      // seePreviewImg(){
      //     if(this.dialogImageUrl){
      //         this.dialogVisible = true;
      //     } else {
      //         this.$message.error('没有上传预览图');
      //     }
      // },
      onSelect(val) {
        if(val){
          this.core.background = this.toColor16(val);
        }else {
          this.core.background = this.toColor16("#ffffff");
        }
      },
      async onUploadImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.core.background = res.data.url;
      },
      toColor16(str) {
        if (/^(rgb|RGB)/.test(str)) {
          var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
          var strHex = "#";
          for (var i = 0; i < aColor.length; i++) {
            var hex = Number(aColor[i]).toString(16);
            if (hex === "0") {
              hex += hex;
            }
            strHex += hex;
          }

          if (strHex.length !== 7) {
            strHex = str;
          }
          return strHex.toUpperCase();
        } else {
          return str;
        }
      },
      // 布局系列
      onPickupComponent(component) {
        this.selectedComponent = component;
        this.dragging = true;
        this.selectedIndex = -1;
      },
      onDropComponent(index) {
        if (index !== null && index >= 0) {
          this.core.layout.splice(index, 0, this.$refs.prop.createCell(this.selectedComponent))
        }
        this.selectedComponent = null;
        this.dragging = false;
      },
      onRemoveCell() {
        this.core.layout.splice(this.selectedIndex, 1);
        this.selectedIndex = -1;
      },
      onMoveupCell() {
        if (this.selectedIndex <= 0) return;
        var target = this.core.layout[ this.selectedIndex ];
        var destination = this.core.layout[ this.selectedIndex - 1 ];
        this.core.layout.splice(this.selectedIndex - 1, 2, target, destination);
        this.selectedIndex--;
      },
      onMovedownCell() {
        if (this.selectedIndex === -1 && this.selectedIndex >= this.core.layout.length - 1) return;
        var target = this.core.layout[ this.selectedIndex ];
        var destination = this.core.layout[ this.selectedIndex + 1 ];
        this.core.layout.splice(this.selectedIndex, 2, destination, target);
        this.selectedIndex++;
      },
      // 格子系列
      onUpdateCellContent(content) {// 配置面板组件通知本组件更新配置数据，-> 触发deep watch core 回调 -> 传递新的core给子页面
        this.selectedCell.content = content;
        this.$refs.view.coreUpdate()
      },
      onUpdateCellCommon(common) {
        this.selectedCell.common = common;
        this.$refs.view.coreUpdate()
      },
      onCustomCell(data) {
        this.$refs.prop.custom(data)
      },
      async getCore() {
        this.loading = true;
        const result = await api.topic.get(this.$route.params.id);
        this.loading = false;
        if (result.code === 200) {
          this.core = _.merge({}, this.coreInit, result.data);
          this.value3 = this.core.state === 1 ? true : false;
        } else {
          this.$message({
            message: result.msg,
            type: 'warning'
          })
        }
      },
      async save() {
        this.saving = true;
        if(this.core.timevalue){
          if(this.core.timevalue.length > 0){
            this.core.startDate = this.core.timevalue[0];
            this.core.endDate = this.core.timevalue[1];
          }else {
            this.$alert("您还没有选择时间范围，将自动填充！", '提醒', {
              confirmButtonText: '确定',
              callback: action => {

              }
            });
            this.$set(this.core, 'timevalue', []);
            this.core.timevalue.push(this.core.startDate);
            this.core.timevalue.push(this.core.endDate);
          }
        }else {
          this.$alert("您还没有选择时间范围，将自动填充！", '提醒', {
            confirmButtonText: '确定',
            callback: action => {

            }
          });
          this.$set(this.core, 'timevalue', []);
          this.core.timevalue.push(this.core.startDate);
          this.core.timevalue.push(this.core.endDate);
        }
        this.core.state = this.value3 === true ? 1 : -1;
        if(this.core.layout.length > 0){
          this.core.layout.forEach(function (value,i) {
            if(value.name === 'floorSpacing'){
              value.styles.height = value.content.floorSpaceHeight+'pt';
            }
          })
        }
        const result = await api.topic.update(this.$route.params.id, this.core)
        this.saving = false;
        if (result.code === 200) {
          this.$message.success('保存成功')
        } else {
          this.$message.error(result.msg)
        }
      },
      async publish() {
        this.$confirm('确定发布该页面?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.publishing = true;
          const result = await api.topic.publish(this.$route.params.id);
          if (result.code === 200) {
            const result = await api.topic.get(this.$route.params.id);
            if (result.code === 200) {
              this.core = _.merge({}, this.coreInit, result.data);
            }
            this.$message.success('发布成功')
          } else {
            this.$message.error(result.msg)
          }
          this.publishing = false;
        })
      },
      preview() {
        let url = `${API_SERVER}topic/preview?id=${this.$route.params.id}&time=${new Date()}`;
        var iWidth = 380;                         //弹出窗口的宽度;
        var iHeight = 600;                        //弹出窗口的高度;
        //window.screen.height获得屏幕的高，window.screen.width获得屏幕的宽
        var iTop = (window.screen.height-30-iHeight)/2;       //获得窗口的垂直位置;
        var iLeft = (window.screen.width-10-iWidth)/2;        //获得窗口的水平位置;
        window.open(url, '_blank', 'height='+iHeight+',,innerHeight='+iHeight+',width='+iWidth+',innerWidth='+iWidth+',top='+iTop+',left='+iLeft+',menubar=no,scrollbars=auto,resizeable=no,location=no,status=no');

      },
      onlineView() {
        let suffix = _.get(this.core, 'suffix', '');
        if(suffix){
          let url = _.get(this.core, 'url', '');
          if(url){
            var k = url.split('/');
            var l = k[k.length - 1];
            url = url.replace(l,'')+suffix+l;
            this.qrCode = AppWebsite + '/cms/getQrCodeByUrl?url=' + url + '&' + new Date()
            this.dialogVisible = true;
            /*window.open(url);*/
          }
        }else {
          this.$message.success('没有发布正式连接');
        }
      },
      async confirmRollback(mtime, id) {
        try {
          await this.$confirm(`确认回滚到 ${mtime} ?`, '提示', { type: 'info' });
        } catch (e) {
          return;
        }
        const result = await api.topic.rollback(id, {
          version: mtime,
        });
        if (result.code === 200) {
          this.$message.success('回滚成功');
        } else {
          this.$message.error(result.msg);
        }
      },
      onSelectItemgroup(row) {
        this.core.rel_itemgroup.group_id = row.item_group_info.id;
        this.core.rel_itemgroup.group_name = row.item_group_info.name;
      },
      showModal(refName) {
        this.$refs[ refName ].show(true);
      },
      dict() {
        let prs = new Array(1);
        prs[0] = new Promise(res => res(api.dict.branchHasOpen())).then(bho => {
          if (bho.code == 200)
            this.$nextTick(() =>  this.branchs = bho.data);
          else
            this.$message.error(bho.msg);
          return bho;
        });
        return Promise.all(prs.map(item => item.catch(e => e)));//并发请求
      },
      goback2start(){
        this.$confirm('请确定是否已进行保存？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.$router.replace('/');
        })
      }
    },
    filters: {
      datetime(dt) {
        return dt.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, function (match, y, m, d, h, i, s) {
          return [ y, m, d ].join('-') + ' ' + [ h, i, s ].join(':');
        });
      },
      getBranchName(code, branchs) {
        let branchName = '';
        if (!code || !branchs || !branchs.length)
          return branchName;
        for (let i = 0, len = branchs.length; i < len; i++) {
          let branch = branchs[i];
          if (branch.branchCode == code) {
            branchName = branch.branchName;
            break;
          }
        }
        return branchName;
      }
    },
    mounted() {
      this.dict().then(data => {
        this.$store.dispatch('sideBar/setSideBarState', false);
        this.getCore();
      });
    },
  }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


  .topic-editor {
    min-width: 1320px + 18px;
    height: 100%;
    padding: 15px;
    .topic-editor-head {
      display: flex;
      width: 1310px;
      margin-left: auto;
      margin-right: auto;
      padding: 5px 5px 0;
      margin-bottom: 10px;
      border: 1px solid $border-color-base;
      line-height: 36px;

      .el-form-item {
        margin-bottom: 10px
      }
    }

    .topic-editor-share {
      display: flex;
      flex-flow: wrap;

      .topic-editor-share-left {
        height: 84px;
      }
      .topic-editor-share-right {
        flex: 1;
        margin-left: 10px
      }
      .topic-editor-share-top {
        width: 100%;
        margin-bottom: 10px;
        .el-input {
          float: right;
          width: 403px;
        }
      }
      .topic-editor-share-icon {
        width: 95px;
        height: 95px;
        border: 1px dashed $border-color-base;
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }

    .topic-editor-main {
      display: flex;
      margin-left: auto;
      margin-right: auto;
      width: 1320px;
      align-items: stretch;
      min-height: 600px;
    }

    .topic-editor-list {
      position: relative;
      z-index: 1;
      overflow-x: hidden;
      overflow-y: auto;
      width: 280px;
      max-height: 800px;
      margin-right: 1px;
      border: 1px solid $border-color-base;
      box-sizing: border-box;
    }

    .topic-editor-view {
      overflow-x: hidden;
      overflow-y: auto;
      width: 390px;
      max-height: 800px;
      margin: 0 5px;
      border: 1px solid $border-color-base;
      box-sizing: border-box;
    }

    .topic-editor-prop {
      overflow-x: hidden;
      overflow-y: auto;
      width: 650px;
      max-height: 800px;
      border: 1px solid $border-color-base;
      box-sizing: border-box;

      .topic-editor-prop-page-buttons {
        display: flex;
        padding: 5px 0 5px 5px;
        border-bottom: $border-base;
        .el-button {
          flex: 1;
          margin-right: 5px;
          min-width: auto;
        }
      }
    }
  }
</style>
