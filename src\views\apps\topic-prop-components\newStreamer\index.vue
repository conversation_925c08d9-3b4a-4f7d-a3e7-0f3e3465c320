<template>
  <div class="topic-image">
    <div class="topic-image-tips">
      注：大图请使用具有切片功能的<b>横幅广告</b>组件，此组件只适合于不想切片的<b>小图</b>（gif动图、二维码图、需长按保存的图），上传体积不允许超过<b>2M</b>。
    </div>
    <!--添加广告入口-->
    <div class="block">
        <el-button class="btn-block" type="primary" @click="addAdList">添加广告</el-button>
    </div>

    <el-table :data="content.list" size="mini" :row-key="getRowKeys" style="width: 100%">
      <el-table-column type="index" width="50"></el-table-column>
      <el-table-column prop="entry" label="广告名称">
        <template slot-scope="scope">
          <span v-html="scope.row.entry"></span>
          <!-- <img v-if="scope.row.title" :src="scope.row.title" alt="图" class="title-image" />
          <i v-else class="el-icon-circle-plus-outline no-img"></i> -->
        </template>
      </el-table-column>

      <el-table-column prop="timevalue" label="有效时间">
        <template slot-scope="scope" v-if="scope.row.timevalue">
          {{format_date(scope.row.timevalue[0])}}~{{format_date(scope.row.timevalue[1])}}
        </template>
      </el-table-column>

      <el-table-column prop="timevalue" label="状态">
        <template slot-scope="scope">
          <span v-html="format_text(scope.row.timevalue)"></span>
        </template>
      </el-table-column>

      <el-table-column prop="link" label="链接">
        <template slot-scope="scope">
          <el-input
            type="text"
            size="mini"
            v-model="((scope.row.link || {}).meta || {}).page_url"
          >{{((scope.row.link || {}).meta || {}).page_url}}</el-input>
        </template>
      </el-table-column>

      <el-table-column label="链接类型">
        <template slot-scope="scope">
          <span>{{scope.row.linkType==='stores'?'店铺链接':'专题页链接'}}</span>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="120px">
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="toEdit(scope.row, scope.$index)"
            type="primary"
          >编辑</el-button>
          <el-button size="mini" @click="handleCancle(scope.row, scope.$index)" type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 背景图上传弹层 -->
    <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addPic" width="30%">
      <el-upload
        class="topic-image-upload uploader-btn-state"
        ref="upload"
        accept="image/jpeg,image/jpg, image/png, image/gif"
        :show-file-list="false"
        :before-upload="() => {upImgLoading = true; return true;}"
        :on-success="onUploadImage"
      >
        <img v-if="dataForm.image" :src="dataForm.image" class="image" />
        <i v-else v-loading="upImgLoading" class="el-icon-plus uploader-icon"></i>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeAddPic">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmImg">确定</el-button>
      </div>
    </el-dialog>
    <!-- 编辑内容弹层 -->
    <el-dialog class="banner-dialog" title="编辑广告" :visible.sync="editContent">
      <el-form ref="form" :model="editData" label-width="120px">
        <el-form-item label="横幅广告：">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="() => {editImgLoading = true; return true;}"
            :on-success="uploadEditContImage"
          >
            <img v-if="editData.title" :src="editData.title" class="image" />
            <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </el-form-item>

        <el-form-item label="有效时间：">
          <el-date-picker
            v-model="editData.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="广告名称：">
          <el-input v-model="editData.entry" class="entry-name"></el-input>
        </el-form-item>

        <el-form-item label="链接类型：">
          <el-select v-model="editData.linkType" placeholder="请选择">
            <el-option
              v-for="item in linkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div class="topic-image-picker" v-if="editData.linkType !== 'dynamic'">
        <el-input placeholder="链接地址" v-model="(((editData || {}).link || {}).meta || {}).page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="editData.linkType === 'dynamic'">
        <div class="topic-image-picker">
          <el-input style="width:200px" placeholder="输入跳转id" v-model="(((editData || {}).link || {}).meta || {}).dynamicId">
            <template slot="prepend">跳转id</template>
          </el-input>
           <el-button type="primary" @click="putDynamicLink(editData)">生成链接</el-button>
        </div>
        <el-input placeholder="链接地址" v-model="(((editData || {}).link || {}).meta || {}).page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>
      
      <div v-if="editData.linkType==='topic'">
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditContent">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmEdit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 添加内容弹层 -->
    <el-dialog class="banner-dialog" title="添加广告" :visible.sync="add_editContent">
      <el-form ref="form" :model="editData" label-width="120px">
        <el-form-item label="横幅广告：">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="() => {editImgLoading = true; return true;}"
            :on-success="uploadEditContImage"
          >
            <img v-if="editData.title" :src="editData.title" class="image" />
            <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="有效时间：">
          <el-date-picker
            v-model="editData.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="广告名称：">
          <el-input v-model="editData.entry" class="entry-name"></el-input>
        </el-form-item>
        <el-form-item label="链接类型：">
          <el-select v-model="editData.linkType" placeholder="请选择">
            <el-option
              v-for="item in linkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div class="topic-image-picker" v-if="editData.linkType !== 'dynamic'">
        <el-input placeholder="链接地址" v-model="(((editData || {}).link || {}).meta || {}).page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="editData.linkType === 'dynamic'">
        <div class="topic-image-picker">
          <el-input style="width:200px" placeholder="输入跳转id" v-model="(((editData || {}).link || {}).meta || {}).dynamicId">
            <template slot="prepend">跳转id</template>
          </el-input>
           <el-button type="primary" @click="putDynamicLink(editData)">生成链接</el-button>
        </div>
        <el-input placeholder="链接地址" v-model="(((editData || {}).link || {}).meta || {}).page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="editData.linkType==='topic'">
        <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditContent">取 消</el-button>
        <el-button size="small" type="primary" @click="add_confirmEdit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import base from "../base";
  export default {
    name: 'streamer',
    extends: base,
    contentDefault: {
      list: [],
      pro_obj: {
        pro_type: "longBar",
        pro_auto: 0,
        pro_align_type: "center",
        default_color: "#ffffff",
        default_opacity: 30,
        active_color: "#555555",
        active_opacity: 100,
        component_name: "fastEntry" //区分模块的标识
      }
    },
    data() {
      return {
        linkOptions: [
          {
            value: "topic",
            label: "专题页链接"
          },
          {
            value: "stores",
            label: "店铺页链接"
          },
          {
            value: "dynamic",
            label: "动态商品链接"
          },
          {
            value: "redEnvelope",
            label: "绑定微信链接"
          }
        ],
        loading: false,
        upImgLoading: false,
        editImgLoading: false,
        addPic: false,
        editContent: false,
        add_editContent: false,
        editCont: "",
        dataForm: {
          image: ""
        },
        editData: {
          linkType: "topic",
          title: "",
          entry: "",
          operation: "",
          timevalue: "",
          link: {
            meta: {
              page_url: ""
            }
          },
          createTimevalue:""
        },
        pickerOptions: {
          shortcuts: [
            {
              text: "未来一周",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来三个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来六个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一年",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                picker.$emit("pick", [start, end]);
              }
            }
          ]
        }
      };
    },
    mounted () { 
      
    },
    computed: {
      // list() {
      //   var list = _.get(this, "content.list");
      //   if (list) {
      //     this.$nextTick(function() {
      //       this.setSort();
      //     });
      //     return list;
      //   } else {
      //     return [];
      //   }
      // }
    },
    filters: {
      link(data) {
        if (!data.type) {
          return "";
        }
        return data.meta.page_url;
      }
    },
    methods: {
      putDynamicLink(item) {
        if (!item.link.meta.dynamicId) {
          this.$message({
            message: '请输入跳转id再点击生成链接',
            type: 'warning'
          });
          return false;
        }
        item.link.meta.page_url = `ybmpage://homeSteadyChannel?strategyId=${item.link.meta.dynamicId}&title=${item.entry}`
      },
      change_default_height(val) {
        if (val === 2) {
          event_bus.$emit("change_default_height", "192pt");
        }
      },
      format_text(data) {
        if (!data) {
          return "<b style='color: red'>请设置时间</b>";
        }
        const _date = new Date().getTime();
        const start = new Date(data[0]).getTime();
        const end = new Date(data[1]).getTime();
        if (_date <= end && _date >= start) {
          return "<b style='color: #67C23A'>展示中</b>";
        } else if (_date < start) {
          return `<b style='color: #000000'>即将在${new Date(
            start
          ).toLocaleDateString()}展示</b>`;
        } else {
          return "<b style='color: #000000'>已过期</b>";
        }
      },
      addTamp(m) {
          return m < 10 ? '0' + m : m
      },
      format_date(timestamp) {
        if (timestamp === '') {
            return ''
        }
        var time = new Date(timestamp);
        var year = time.getFullYear();
        var month = time.getMonth() + 1;
        var date = time.getDate();
        var hours = time.getHours();
        var minutes = time.getMinutes();
        var seconds = time.getSeconds();
        return year + '-' + this.addTamp(month) + '-' + this.addTamp(date) + ' ' + this.addTamp(hours) + ':' +
            this.addTamp(minutes) + ':' + this.addTamp(seconds);
      },
      formatTooltip(val) {
        return val / 100;
      },
      closeAddPic() {
        this.addPic = false;
      },
      onSetLink(link) {
        this.editData.link = link;
      },
      //编辑
      toEdit(data, index, status) {
        if (status == "1") {
          //新建时清空
          this.editData.title = "";
          this.editData.timevalue = "";
          this.editData.entry = "";
          this.editData.link = {
            meta: {
              page_url: ""
            }
          };
          this.editData.createTimevalue = "";
        } else {
          this.editData.entry = data.entry;
          this.editData.timevalue = data.timevalue;
          this.editData.title = data.title;
          this.editData.link = data.link;
          this.editData.linkType = data.linkType ? data.linkType : "topic";
          this.editData.createTimevalue = data.createTimevalue;
        }

        this.editContent = true;
        this.nowData = data;
        this.nowIndex = index;
        this.isContEdit = true;
        this.addContPic = true;
      },
      //删除
      handleCancle(row,index) {
        let _self = this;
        return function () {
            const index = _self.content.list.indexOf(row)
            _self.content.list.splice(index, 1)
            _self.$message({
                type: 'success',
                message: '删除成功!'
            });
        }.confirm(_self)()
      },
      toAdd() {
        this.isEdit = false;
        this.dataForm = {
          image: ""
        };
        this.addPic = true;
      },
      async onUploadImage(res, file) {
        this.upImgLoading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: "warning"
          });
          return;
        }
        this.dataForm.image = res.data.url;
      },
      async uploadEditContImage(res, file) {
        this.editImgLoading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: "warning"
          });
          return;
        }
        this.editData.title = res.data.url;
      },
      confirmImg() {
        if (!this.dataForm.image) {
          this.$message.warning("请上传图片");
          return false;
        }
        this.closeAddPic();
        if (this.isEdit) {
          this.currentData = {};
          this.content.list.splice(this.currentIndex, 1, this.currentData);
        } else {
          this.content.list.push(Object.assign({}, this.dataForm));
        }
      },
      closeEditContent() {
        this.editContent = false;
        this.add_editContent = false;
      },
      confirmEdit() {
        if (!this.editData.title) {
          this.$message.warning("请上传图片");
          return false;
        }

        if (!this.editData.entry) {
          this.$message.warning("请填写文字");
          return false;
        }

        if (!this.editData.link) {
          this.$message.warning("请填写链接");
          return false;
        }
        this.closeEditContent();
        if (this.isContEdit) {
          // entry: document.querySelector('.ql-editor').innerText,//只取文本
          this.psData = {
            title: this.editData.title,
            timevalue: this.editData.timevalue,
            entry: `${this.editData.entry}`,
            link: this.editData.link,
            linkType: this.editData.linkType,
            createTimevalue: this.editData.createTimevalue
          };
          this.content.list.splice(this.nowIndex, 1, this.psData);
        } else {
          this.content.list.push(Object.assign([], this.content.list));
        }
        // this.content.list = this.list;
      },
      addAdList(){
        this.add_editContent=true;
        if(this.content.list.length >= 10){
          this.add_editContent=false;
          this.$message({
            message: '最多可添加10条广告',
            type: "warning"
          });
        }
      },
      //确定添加广告
      add_confirmEdit() {
        if (!this.editData.title) {
          this.$message.warning("请上传图片");
          return false;
        }

        if (!this.editData.entry) {
          this.$message.warning("请填写广告名称");
          return false;
        }

        if (!this.editData.link) {
          this.$message.warning("请填写链接");
          return false;
        }
        this.closeEditContent();
        this.psData = {
          title: this.editData.title,
          entry: `${this.editData.entry}`,
          link: this.editData.link,
          timevalue: this.editData.timevalue,
          linkType: this.editData.linkType,
          createTimevalue: new Date().getTime()
        };
        this.content.list.push(this.psData);
      }
    },
    watch: {
      add_editContent(new_val) {
        this.editData = {
          linkType: "topic",
          title: "",
          timevalue: "",
          entry: "",
          operation: "",
          link: {
            meta: {
              page_url: ""
            }
          }
        };
      }
    }
  };
</script>
<style lang="scss" scoped rel="stylesheet/scss">
  .topic-image-tips {
    padding: 5px 0;
    font-size: 12px;
    color: #999;
    b {
      color: $color-danger;
    }
  }
  .btn-block {
    display: block;
    width: 90%;
    margin:10px auto 20px;
  }
  .el-form-item {
    margin-bottom: 12px;
  }
  .topic-image-upload {
    .image {
      display: block;
      width: 100%;
    }
    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }
  .banner-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }
    .image {
      width: 64px;
      height: 64px;
    }
  }
  .topic-image-picker {
    padding: 10px 0;
  }
  .title-image {
    width: 64px;
    height: 64px;
  }
  .el-table {
    .cell {
      text-align: center;
      padding: 0;
    }

    th .cell {
      color: #606266;
    }
  }
  .el-button--mini {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
  }

.fast-entry {
  .container {
    display: flex;
    align-items: center;

    .img {
      width: 65%;

      img {
        display: block;
        width: 100%;
      }
    }

    .button-list {
      margin-left: 10px;
    }
  }

  .content-setting {
    color: #fff;
    background-color: #13c2c2;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }
  .entry-name {
    width: 70%;
  }

  // single-upload
  .uploader-btn-state {
    text-align: center;
  }
  .no-img {
    font-size: 35px;
    display: block;
    color: #caccd0;
  }
}

.el-loading-spinner {
  top: auto !important;
  margin-top: auto !important;
}

.el-row {
  text-align: center;

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}


  

  .topic-image-info {
    position: relative;
    overflow: hidden;
    height: 62px;
    padding-bottom: 10px;
    margin: 5px 0;
    border: $border-base;
    font-size: 12px;

    .name {
      position: relative;
      overflow: hidden;
      height: 26px;
      padding: 0 5px;
      margin-bottom: 3px;
      font-size: 14px;
      line-height: 26px;
      border-bottom: $border-base;
    }

    .data {
      position: relative;
      overflow: hidden;
      height: 16px;
      padding: 0 5px;
      line-height: 16px;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: #999;
    }

    .del {
      position: absolute;
      top: 0;
      right: 0;
      padding: 7px;
      border-left: $border-base;
      background: #fff;
      cursor: pointer;

      &:hover {
        background: $color-base-silver;
        color: #fff;
      }
    }
  }

</style>
