<template>
    <div class="init_topic-banner">
        <div style="margin: 10px 0">
            设置banner的背景色:
            <el-color-picker v-model="content.bg_color" size="mini" @change="clear_self_bg_img"></el-color-picker>
            <el-button @click="banner_bg_dialog=true"  type="primary">上传banner的背景图片</el-button>
            <img :src="content.bg_img" alt="" width="40" height="40">
        </div>
        <el-button @click="init_toAdd" class="btn-block mb-10" type="primary">上传图片</el-button>
        <el-table :data="list" size="mini" style="width: 100%">
            <el-table-column label="图片">
                <template slot-scope="scope">
                    <div class="init_container">
                        <div class="img"><img :src="scope.row.image"></div>
                        <div class="button-list">
                            <el-row style="margin-top: 5px" :gutter="10">
                                <el-col :span="15">
                                    <div class="grid-content bg-purple">
                                        <el-tooltip placement="top" style="width: 100px">
                                            <div slot="content" style="width: 250px">
                                                <div class="demonstration">商品名称:{{scope.row.goods_title}}</div>
                                                <div class="demonstration" v-if="scope.row.timevalue">开始时间:{{scope.row.timevalue[0]}}</div>
                                                <div class="demonstration" v-if="scope.row.timevalue">结束时间:{{scope.row.timevalue[1]}}</div>
                                                <div class="demonstration">活动页链接:
                                                    <p>
                                                        {{scope.row.link|link}}
                                                    </p>
                                                </div>
                                                <div class="demonstration">商品介绍:{{scope.row.goods_introduce}}</div>
                                                <div class="demonstration">商品说明1:{{scope.row.first_explain}}</div>
                                                <div class="demonstration">商品说明2:{{scope.row.second_explain}}</div>
                                            </div>
                                            <el-button>配置详情</el-button>
                                        </el-tooltip>
                                    </div>
                                </el-col>
                                <el-col :span="9">
                                    <div style="margin: 5px 0">
                                        <el-button size="mini" @click="init_toEdit(scope.row, scope.$index)" type="primary">编辑</el-button>
                                    </div>
                                    <div style="margin: 5px 0">
                                        <el-button size="mini" @click="init_toRemove(scope.row)" type="danger">删除</el-button>
                                    </div>

                                </el-col>
                            </el-row>

                        </div>
                    </div>
                    <!--<div class="link-desc">{{scope.row.link | link}}</div>-->
                </template>
            </el-table-column>
        </el-table>
        <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addDialog">
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="init_onUploadImage">
                <img v-if="dataForm.image" :src="dataForm.image" class="image">
                <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>

            <el-row style="margin-top: 5px" :gutter="10">
                <el-col :span="24">
                    <div class="grid-content bg-purple">
                        <el-input placeholder="商品名称" v-model="dataForm.goods_title">
                            <template slot="prepend">商品名称</template>
                        </el-input>

                    </div>
                </el-col>

            </el-row>

            <el-row style="margin-top: 15px" :gutter="10">
                <el-col :span="24">
                    <div class="grid-content bg-purple">
                        <el-input placeholder="商品介绍" v-model="dataForm.goods_introduce">
                            <template slot="prepend">商品介绍</template>
                        </el-input>

                    </div>
                </el-col>

            </el-row>


            <el-row style="margin-top: 15px" :gutter="10">
                <el-col :span="12">
                    <div class="grid-content bg-purple">
                        <el-input placeholder="底部说明1" v-model="dataForm.first_explain">
                            <template slot="prepend">底部说明1</template>
                        </el-input>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="grid-content bg-purple">
                        <el-input placeholder="底部说明2" v-model="dataForm.second_explain">
                            <template slot="prepend">底部说明2</template>
                        </el-input>
                    </div>
                </el-col>
            </el-row>

            <div class="topic-image-picker">
                <!-- 跳转链接<span>({{dataForm.link | link}})</span>-->
                <el-input placeholder="链接地址" v-model.trim="dataForm.link.meta.page_url">
                    <template slot="prepend">跳转链接</template>
                </el-input>
            </div>
            <page-link @select="init_onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeAddDialog">取 消</el-button>
                <el-button size="small" type="primary" @click="init_confirm">确定</el-button>
            </div>
        </el-dialog>
        <el-dialog class="banner-dialog" title="添加图片" :visible.sync="banner_bg_dialog">
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="upload_bg_img">
                <img v-if="content.bg_img"
                     :src="content.bg_img" class="image">
                <i v-loading="loading" v-else  class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="banner_bg_dialog=false">取 消</el-button>
                <el-button size="small" type="primary" @click="banner_bg_dialog=false">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import base from '../base'
    import api from "api";
    import { getUrlParam } from "config";
    export default {
        extends: base,
        contentDefault: {
            list: [],
            bg_color: "#FC4340",
            bg_img:"",
        },
        data() {
            return {
                loading: false,
                addDialog: false,
                banner_bg_dialog:false,
                dataForm: {
                    first_explain: "",
                    second_explain: "",
                    goods_introduce: "",
                    goods_title: "",
                    image: '',
                    link: {
                        meta: {
                            page_url: ''
                        }
                    },
                    timevalue: '',
                },
            }
        },
        computed: {
            list() {
                let list = _.get(this, 'content.list')
                if (list) {
                    if (list.length > 0 && list[0].link.meta) {
                        this.$nextTick(function () {
                            this.setSort()
                        })
                    }
                    return list
                } else {
                    return [];
                }
            },
        },
        filters: {
            link(data) {
                if (!data.type) {return '';}
                return data.meta.page_url;
            }
        },
        methods: {
            async upload_bg_img(res){
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    });
                    return;
                }
                this.$set(this.content, "bg_img", res.data.url)
                this.$set(this.content, "bg_color", "")
            },
            clear_self_bg_img(){
                this.content.bg_img=""
            },
            closeAddDialog() {
                this.addDialog = false;
            },
            init_toRemove(data) {
                const index = this.list.indexOf(data)
                this.list.splice(index, 1)
            },
            init_toEdit(data, index) {
                this.currentData = data;
                this.currentIndex = index;
                // this.dataForm = Object.assign({}, data);
                this.dataForm = JSON.parse(JSON.stringify(data));
                this.isEdit = true;
                this.addDialog = true;
            },
            init_toAdd() {
                this.isEdit = false;
                this.dataForm = {
                    image: '',
                    link: {
                        meta: {
                            page_url: ''
                        }
                    },
                };
                this.addDialog = true;
            },
            init_onSetLink(link) {
                this.dataForm.link = link
            },
            async init_onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.dataForm.image = res.data.url
            },
            async init_confirm() {
                if (!this.dataForm.image) {
                    this.$message.warning('请上传图片');
                    return false;
                }
                let linkErrMsg = '';
                if (this.dataForm.link.meta.page_url) {
                    if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.dataForm.link.meta.page_url)) {
                        linkErrMsg = '跳转链接格式不正确，请检查';
                    } else {
                        let linkPageUrl = getUrlParam(this.dataForm.link.meta.page_url, 'url');
                        const result = await api.topic.checkPageUrl({ url: linkPageUrl });
                        if (((result || {}).data || {}).status != 200) {
                        linkErrMsg = '跳转链接不存在，请检查';
                        }
                    }
                }
                if (linkErrMsg) {
                    this.$message.error(linkErrMsg);
                    return false;
                }
                this.closeAddDialog();
                if (this.isEdit) {
                    this.currentData = Object.assign(this.currentData, this.dataForm);
                    this.list.splice(this.currentIndex, 1, this.currentData);
                } else {
                    this.list.push(Object.assign({}, this.dataForm));
                }
            },
            init_setlink(e) {
                this.content.link.meta.page_url = e.target.value
            },
        },
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">
    .init_topic-banner {
        .banner-dialog {
            .el-dialog__body {
                padding-top: 10px;
            }
        }
        .init_container {
            display: flex;
            align-items: center;
            .img {
                width: 65%;
                img {
                    display: block;
                    width: 100%;
                }
            }
            .button-list {
                margin-left: 10px;
            }
        }
        .link-desc {
        }

        .topic-image-upload {
            .image {
                display: block;
                width: 100%;
            }

            .uploader-icon {
                width: 200px;
                height: 200px;
                line-height: 200px;
                border: 1px solid #8b8b8b;
                font-size: 50px;
            }
        }

        .topic-image-picker {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }

</style>
