<template>
  <header class="account-wrap" v-loading="loading">
    <el-menu :style="{minHeight: contentHeight}" :defaultActive="activeIndex" class="side-menu-list"
             :router="true" :unique-opened="true">
      <el-menu-item v-for="nav in menuList" :key="nav.actionUrl" :index="nav.actionUrl">
        <i class="iconfont" :class="nav.icon"></i>
        {{nav.menuName}}
      </el-menu-item>
    </el-menu>
  </header>
</template>

<script>
  import api from 'api'

  export default {
    name: 'Sidebar',
    data() {
      return {
        activeIndex: this.$store.getters[ 'sideBar/activeNav' ],
        contentHeight: window.innerHeight - 115 + 'px',
        loading: false,
        activeIndex: window.location.hash.substr(1),
      }
    },
    computed: {
      menuList() {
        return this.$store.getters[ 'sideBar/accountMenu' ]
      },
    },
    async mounted() {
      this.loading = true;
      const navRes = await api.menu.list();
      if (navRes.code === 200) {
        this.loading = false;
        this.$store.dispatch('sideBar/updateNavList', navRes.data);
        this.$store.dispatch('sideBar/updateAccountNavList', navRes.data);
        this.$store.dispatch('sideBar/setActiveNavId', window.localStorage.getItem('activeNav') || navRes.data[ 0 ].actionUrl);
      } else {
        this.$message.error(navRes.msg);
      }
    },
    methods: {
      selectTab(tab) {
      }
    }
  }
</script>


<style lang="scss" rel="stylesheet/scss">


  .account-wrap {
    position: relative;
    .side-menu-list {
      padding-top: 30px;
      width: 206px;
    }
    .el-menu-item {
      height: 48px;
      line-height: 48px;
      border-top: 1px solid $extra-black;
      box-sizing: border-box;
      &:last-child {
        border-bottom: 1px solid $extra-black;
      }
      &.is-active {
        position: relative;
        background-color: #f3f3f3;
        color: $black;
        transition: none;
        &:after {
          @include middle-center-y();
          right: -14px;
          content: "";
          border-width: 7px;
          border-style: dashed dashed dashed solid;
          border-color: transparent transparent transparent #f3f3f3;
        }
      }
      &:hover {
        position: relative;
        background-color: #f3f3f3;
      }
    }
  }
</style>
