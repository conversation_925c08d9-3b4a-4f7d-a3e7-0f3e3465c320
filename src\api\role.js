import * as http from 'utils/http'

export default {

    list() {
        return http.get('role')
    },
    get(id) {
        return http.get(
            `role/${id}`
        )
    },
    add(params) {
        return http.post(
            'role/add', params
        )
    },
    update(id, params) {
        return http.post(
            `role/${id}/update`, params
        )
    },
    remove(id, params) {
        return http.post(
            `role/${id}/remove`, params
        )
    },
}
