<template>
    <div class="create-activity-modal">
        <el-dialog @close="afterClose" :visible.sync="visible" :title="title">
            <el-form :model="current" :rules="validate" :ref="validRef" label-position="right" size="small" label-width="100px" label-suffix="：">
                <el-form-item label="活动页ID" prop="page_id">
                    <el-input v-model="current.page_id" :disabled=true placeholder="编号id自动生成" clearable></el-input>
                </el-form-item>
                <el-form-item label="名称" prop="page_name">
                    <el-input v-model="current.page_name" placeholder="请填写名称" clearable></el-input>
                </el-form-item>
                   <el-form-item label="链接" prop="page_url">
                    <el-input v-model="current.page_url" placeholder="请填写链接" clearable></el-input>
                </el-form-item>
                <el-form-item label="类型" prop="clientType">
                    <el-cascader
                            v-model="current.clientType"
                            :options="origin.clientType"
                            placeholder="选择客户端 / 类型"
                            separator=">"
                            filterable
                            clearable>
                    </el-cascader>
                </el-form-item>
                <el-form-item label="状态" prop="state">
	                <el-radio-group v-model.number="current.state">
		                <el-radio v-for="(v, k, i) in origin.state" :label="Number(k)">{{ v }}</el-radio>
	                </el-radio-group>
                </el-form-item>
                <!-- <el-form-item label="所在域" prop="branchCode">
                    <el-select v-model="current.branchCode" :disabled="isEdit" placeholder="选择区域" default-first-option filterable>
                        <el-option
                                v-for="(item, i) in origin.branchs"
                                :value="item.branchCode"
                                :label="item.branchName"
                        ></el-option>
                    </el-select>
                </el-form-item> -->
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="visible = false">取消</el-button>
                <el-button size="small" type="primary" :loading="sending" :disabled="sending" @click="save">{{sending ? '正在提交...' : saveBtnText}}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
    import api from 'api';

    export default {
        name: 'CreateActivityModal',
        props: {
	        origin: Object,
	        current: Object
        },
        data() {
	        return {
                visible: false,
                sending: false,
                isEdit: false,
		        isAdmin: false,
		        data: {
			        state: -1,
			        category: 'app',
			        page_type: 'h5'
		        },
		        validRef: 'form',   //验证ref属性值
		        validate: {
			        page_url: [
				        { required: true, message: '请填写链接', trigger: 'blur' },
				        { min: 16, max: 500, message: '长度在16 - 500之间', trigger: 'blur' }
			        ],
			        state: [
				        { required: true, message: '请选择', trigger: 'change' }
			        ],
			        clientType: [
				        { required: true, message: '选择客户端 / 类型', trigger: 'change' }
			        ],
			        // branchCode: [
				    //     { required: true, message: '请选择区域', trigger: 'change' }
			        // ],
			        page_name: [
				        { required: true, message: '请填写名称', trigger: 'blur' },
				        { min: 2, max: 36, message: '长度在2 - 36之间', trigger: 'blur' }
			        ],
			        page_id: [
				        { min: 2, max: 20, message: '长度在2 - 20之间', trigger: 'blur' },
				        { validator: (rule, val, callback) => {
						        if (/\s/.test(val)
							        || !/^[A-Za-z\d_]+$/.test(val))
							        var e = new Error('只能包含英文、数字及下划线，且不能含有空格等');
						        return callback(e);
					        }, trigger: 'blur' },
				        { validator: this.codeValid, trigger: 'blur' }
			        ]
		        }
            }
        },
        computed: {
            isCopy() {
                return !!this.origin.id;
            },
            title() {
                return this.isCopy ? `复制活动页 - ${this.current.page_name}` : this.isEdit ? '编辑活动页' : '创建活动页';
            },
            saveBtnText() {
                if (this.isCopy)
                    this.current.page_id = 'YBM' + this.dateFormat.replace(/[\s\:\-]/g, '') + (Date.now() + '').slice(-3);
                return this.isCopy ? '复制' : this.isEdit ? '保存' : '创建';
            },
            dateFormat() {
                var dt = new Date();
                var dateArr = [dt.getFullYear(), dt.getMonth() + 1, dt.getDate()].map(n => ((n + '').length >= 2 ? '' : '0') + n);
                var timeArr = [dt.getHours(), dt.getMinutes(), dt.getSeconds()].map(n => ((n + '').length >= 2 ? '' : '0') + n);
                return dateArr.join('-') + ' ' + timeArr.join(':');
            }
        },
	    watch: {
		    /* 客户端类型 */
		    'current.clientType': {
			    handler(val, oldVal) {
                    this.current.category = !val ? null : val[0];
                    this.current.page_type = !val ? null : val[1];
			    }
		    }
	    },
        methods: {
            show() {
                this.visible = true;
	            this.isAdmin = this.origin.loginUser.userName == 'admin'; //管理员功能限制
	            this.$nextTick(() => {
	            	this.isEdit = !!this.current.page_id;
		            // if (!this.isAdmin)
			            // this.$set(this.current, 'branchCode', (this.current.branchCode || this.origin.loginUser.branch.branchCode));
		            this.$set(this.current, 'state', (this.current.state || this.data.state));
		            this.$set(this.current, 'clientType', [     //设置默认“客户端 / 类型”
			            this.current.category || this.data.category,
			            this.current.page_type || this.data.page_type
		            ]);
	            });
            },
            save() {
	            this.sending = true;
	            this.$refs[this.validRef].validate(async (ok) => {
		            if (!ok) {
			            this.$message.error('验证失败！');
			            this.sending = !this.sending;
			            return;
		            }
		            let data = Object.assign({}, this.current);
		            delete data.clientType;
                    this.isCopy ? this.create(data) : this.isEdit ? this.update(data) : this.create(data);
		            this.sending = !this.sending;
	            });
            },
            afterClose() {
                this.$emit('close');
	            this.$refs[this.validRef].resetFields();
            },
            afterSave() {
                this.$emit('save-done');
                this.visible = false;
            },
            async update(data) {
                this.sending = true;
                const result = await api.activity.update(this.current.id, data || this.current);
                this.sending = false;
                if (result.code === 200) {
                    this.$message.success('活动页更新成功!');
                    this.afterSave();
                } else {
                    this.$message.error(result.msg);
                }
            },
            async create(data) {
                this.sending = true;
                const result = await api.activity.add(data || this.current);
                this.sending = false;
                if (result.code === 200) {
                    this.$message.success('活动页创建成功!');
                    this.afterSave();
                } else {
                    this.$message.error(result.msg);
                }
            },
	        /**
	         * 编号验证
	         * @param rule
	         * @param value
	         * @param callback
	         * @returns {Promise<*>}
	         */
	        codeValid(rule, val, callback) {
		        let data = this.current;
		        if (data.id || !data.page_id)
			        return callback();

		        if (this.timId)
			        clearTimeout(this.timId);
		        this.timId = setTimeout(async () => {   //规定时间内，不频繁验证
			        let pms = {
				        page_id: data.page_id
			        };
			        let res = await api.activity.query(pms);
			        if (res.code == 200 && res.data && res.data.length > 0)
				        var e = new Error('此编号已存在');
			        clearTimeout(this.timId);
			        return callback(e);
		        }, 1000);
	        },
        }
    };
</script>
