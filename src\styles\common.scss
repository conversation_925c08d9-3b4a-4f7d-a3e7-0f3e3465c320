@charset "UTF-8";

/** 清除内外边距 **/
body, h1, h2, h3, h4, h5, h6, hr, p,
blockquote,
dl, dt, dd, ul, ol, li,
pre,
form, fieldset, legend, button, input, textarea,
th, td,
img {
  border: medium none;
  margin: 0;
  padding: 0;
}

h1, h2, h3, h4, h5, h6 {
  font-size: 100%;
}

em {
  font-style: normal;
}

/** 重置列表元素 **/
ul, ol {
  list-style: none;
}

/** 重置超链接元素 **/
a {
  text-decoration: none;
  color: #333;
}

body {
  margin: 0;
  font-size: 13px;
  font-family: Hiragino Sans GB, Helvetica Neue, Microsoft YaHei, WenQuanYi Micro Hei, sans-serif;
  line-height: 1.5;
  background: #fff;
  color: #383838;
}

body, html {
  height: 100%;
  overflow: hidden;
}

.hide-text {
  white-space: nowrap;
  text-overflow: ellipsis;
}
.blank_10 {
  height: 10px;
}
.blank_15 {
  height: 15px;
}
.blank_20 {
  height: 20px;
}


