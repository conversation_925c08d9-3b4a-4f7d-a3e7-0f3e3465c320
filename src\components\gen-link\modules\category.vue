<template>
    <div>
        <el-tree :data="list" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
    </div>
</template>

<script>
export default {
    data() {
        return {
            list: [],
            loading: false,
            defaultProps: {
                children: 'list',
                label: 'dispName'
            }
        }
    },
    methods: {
        async getList(page=1){
            this.loading = true;
            const result = await this.$http.get('http://**************:8080/router?v=1.0&appKey=100001&method=product.getCateoryList', {
                params: {}
            })
            if(result.result.result == 1){
                this.list = result.data;
            }
            this.loading = false;
        },
        handleNodeClick(row){
            this.$emit('select', {
                type : 'category',
                label: '分类',
                id   : row.cateDispId,
                desc : row.dispName,
                meta : row
            })
        }
    },
    mounted(){
        this.getList();
    }
}
</script>
