<template>
	<div>
		<!--公共组件-->
		<edit-component :content="pub"  :branchCode="topic.branchCode" flag="pub"></edit-component>
		<!--选项卡里的组件-->
		<div v-if="$store.state.page.isEdit" ref="messageBox" class="messageBox">
			<edit-component :content="page" flag="page"  :branchCode="topic.branchCode"></edit-component>
		</div>
	</div>
</template>

<script>
	import editComponent from './edit-component'
	import base from "../base";
	import {common} from 'api'
	export default {
		name: "pubComponent",
		extends: base,
		contentDefault: {
			pubData:{},//存储公共组件数据
			pageData:[],//存储选项卡每页中的数据
			currentSwitch:'',//如tabSwitch0
			currentRow:[0,0] //如tabSwitch0下的坐标
		},
		data(){
			return {
				pub:{},
				page:{},
			}
		},
		components: {
			editComponent
		},
		created(){
			this.$store.dispatch('initData',this.content)
			if(!common.isEmptyObject(this.content.pubData)){
				this.pub=_.cloneDeep(this.content.pubData)
			}
			if(!common.isEmptyObject(this.content.pageData)){
				const index=common.getRepeatResult('name',this.content.currentSwitch,this.content.pageData)
				this.page=_.cloneDeep(this.content.pageData[index].data[this.content.currentRow[0]][this.content.currentRow[1]])
			}
			this.$store.dispatch('setEditStatus',false)
		},
		watch:{
			'$store.state.page':{
				deep:true,
				handler(val){
					this.content=_.cloneDeep(val)
				}
			}
		}
	}
</script>

