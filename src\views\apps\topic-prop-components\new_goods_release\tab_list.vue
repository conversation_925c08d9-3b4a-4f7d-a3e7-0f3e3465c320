<template>
    <div>
        <div>
            <tab-switch :content="content" @add_list="add_list" @change_color="change_color"></tab-switch>
            <div class="container">
                <el-tabs v-model="activeName" @tab-click="tab_click" closable @tab-remove="remove_tab">
                    <el-tab-pane v-for="item  in  content.goods_list" :label="item.name" :name="item.name"
                                 :key="item.name">

                        <div style="margin-bottom: 10px">
                            <el-radio-group v-model="content.goods_list[currentTabIndex].type"
                                            v-if="content.goods_list[currentTabIndex]">
                                <el-radio :label="index" v-for="(item,index) in typeList" :key="index">{{item}}
                                </el-radio>
                            </el-radio-group>
                        </div>
                        <div style="margin-bottom: 10px">
                            <el-radio-group v-model="content.goods_list[currentTabIndex].is_custom"
                                            v-if="content.goods_list[currentTabIndex]">
                                <el-radio :label="index" v-for="(item,index) in is_custom" :key="index">{{item}}
                                </el-radio>
                            </el-radio-group>
                        </div>
                        <div>
                            <el-input placeholder="请输入新名称"
                                      v-model="content.goods_list[currentTabIndex].name"
                                      v-if="content.goods_list[currentTabIndex]"
                            >
                                <template slot="prepend">标签名称</template>
                            </el-input>
                        </div>
                        <h3>列表的商品</h3>
                        <div style="border: 1px solid #3c763d;margin-bottom: 10px">
                            <el-table :data="item.list" style="width: 100%;margin-top: 5px" height="300"
                                      ref="multipleTable">
                                <el-table-column label="药名">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.productName">{{scope.row.productName}}</span>
                                        <span v-else>{{scope.row.showName}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="规格">
                                    <template slot-scope="scope">
                                        {{scope.row.mediumPackageTitle}}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="fob" label="价格" width="80">
                                    <template slot-scope="scope">
                                        {{scope.row.fob}}
                                    </template>
                                </el-table-column>
                                <el-table-column fixed="right" label="操作" width="80">
                                    <template slot-scope="scope">
                                        <div class="edit-button">
                                            <el-button @click="list_handleDelete(scope.row)" type="warning" size="mini">
                                                删除
                                            </el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <div>
                            <el-table :data="item.goods_group" style="width: 100%;margin-top: 5px"
                                      height="150"
                                      ref="multipleTable">
                                <el-table-column fixed label="商品组名称">
                                    <template slot-scope="scope">
                                        {{scope.row.name}}
                                    </template>
                                </el-table-column>
                                <el-table-column fixed label="商品组里商品数量">
                                    <template slot-scope="scope">
                                        {{scope.row.ids.length}}
                                    </template>
                                </el-table-column>
                                <el-table-column fixed="right" label="操作" width="80">
                                    <template slot-scope="scope">
                                        <div class="edit-button">
                                            <el-button @click="group_delete(scope.row)" type="warning" size="mini">删除
                                            </el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <!--选择商品-->
        <all-link @select="onSetLink" :tabs="tabs" :params="{
                productlink: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                importGoods: {
                    minSel: 1,
                    search: {
                        status: 1,
                        branchCode: topic.branchCode
                    }
                },
                goodsGroup: {
                    seledShow: false,
                    minSel: 1,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
            }"></all-link>

    </div>
</template>

<script>
    import base from "../base";
    import tabSwitch from './tab'
    import {common} from 'api'
    import {AppWebsite} from 'config/index';

    export default {
        components: {
            tabSwitch
        },
        extends: base,
        contentDefault: {
            goods_list: [],
            tabs: {
                color: "#ffffff",
                lineColor: '#ffffff',
                bg_color: "#578ee1",
                body_color: "#578ee1"
            },
            active_name: ""
        },
        created() {
            this.$http({
                headers: {
                    terminalType: 1
                },
                method: "post",
                url:`${AppWebsite}app/category/getCategoryList`
            })
                .then(({data}) => {
                    this.activeName = data.data.categoryList[0].children[0].name;
                    if (this.content.goods_list.length === 0) {
                        this.content.goods_list = data.data.categoryList[0].children.map(item => {
                            let obj = {
                                goods_group: [],
                                list: [],
                                type: 1,
                                is_custom:0
                            };
                            obj.name = item.name;
                            obj.categoryId = item.id;
                            return obj
                        });
                    }
                })

        },
        data() {
            return {
                activeName: "",
                currentTabIndex: 0,
                tabs: [
                    {label: '商品', value: 'productlink'},
                    {label: '导入商品', value: 'importGoods'},
                    {label: '商品组', value: 'goodsGroup'}
                ],
                typeList: ['列表模式', '大图模式'],
                is_custom: ['接口获取商品', '自定义商品'],
            }
        },
        watch: {
            activeName(new_val, old_val) {
                if (new_val) {
                    this.content.active_name = new_val
                }
            }
        },
        computed: {
            group_banner_list() {
                let list = _.get(this, 'content.group_banner.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
            banner_list() {
                let list = _.get(this, 'content.banner.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
        },
        methods: {
            change_color(type, color) {
                if (type === "font") {
                    this.content.tabs.color = color
                } else if (type === "line") {
                    this.content.tabs.lineColor = color
                } else if (type === "bg") {
                    this.content.tabs.bg_color = color
                } else if (type === "body") {
                    this.content.tabs.body_color = color
                }
            },

            add_list(obj) {
                this.content.goods_list.push(obj)
            },
            tab_click() {
                this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.goods_list);
            },
            remove_tab(targetName) {
                const index = common.getRepeatResult('name', targetName, this.content.goods_list);
                this.content.goods_list.splice(index, 1)
            },

            onSetLink(link) {
                function handle_arr(arr=[]){
                    return arr.map((item)=>{
                        let obj={};
                        obj.init_img_url=item.init_img_url;
                        obj.imageUrl=item.imageUrl;
                        obj.productName=item.productName;
                        obj.showName=item.showName;
                        obj.mediumPackageTitle=item.mediumPackageTitle;
                        obj.fob=item.fob;
                        obj.id=item.id;
                        return obj
                    });
                }
                if (link.tag === "goods" || link.tag === "importGoods") {
                    let _self_arr= handle_arr(link.data);
                    if (this.content.goods_list[this.currentTabIndex].list.length > 0) {
                        this.content.goods_list[this.currentTabIndex].list =
                            [...common.removeRepeat(this.content.goods_list[this.currentTabIndex].list, _self_arr)]
                    } else {
                        this.content.goods_list[this.currentTabIndex].list = [..._self_arr]
                    }
                } else if (link.tag === "goodsGroup") {
                    let obj = {};
                    obj.name = link.data.name;
                    obj.ids = link.data.goods;
                    obj.code = link.data.code;
                    this.content.goods_list[this.currentTabIndex].goods_group.splice(0, 1, obj);
                }
            },

            list_handleDelete(row) {
                const index = this.content.goods_list[this.currentTabIndex].list.indexOf(row)
                this.content.goods_list[this.currentTabIndex].list.splice(index, 1)
            },
            group_delete(row) {
                const index = this.content.goods_list[this.currentTabIndex].goods_group.indexOf(row);
                this.content.goods_list[this.currentTabIndex].goods_group.splice(index, 1)

            },

        },

    }
</script>

<style scoped>

</style>
