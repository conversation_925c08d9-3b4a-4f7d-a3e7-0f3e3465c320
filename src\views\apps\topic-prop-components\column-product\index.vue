<template>
	<div>
		<!--滚动商品组-->
		背景颜色:
		<el-color-picker v-model="content.bgColor" @change="change_bgColor" size="mini"></el-color-picker>
		<div class="blank_10"></div>
		<p class="blank_20"></p>
		<all-link @select="onSetLink" :tabs="tabs" :params="{
				goodsGroup:{
					seledShow: false,
					 minSel: 1,
					returnGoods: 0,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
             }"></all-link>

	</div>
</template>

<script>
	import base from '../base'
	export default {
		extends: base,
		contentDefault: {
			bgColor: '#ffffff',
			list: [],
			code:'',
			branchCode:''
		},
		data() {
			return {
				tabs: [
					{label: '商品组', value: 'goodsGroup'}
				],
				goodsIds: [],
				selectItem: [],
				loading: false
			}
		},
		methods: {
			change_bgColor(val){
				if(val){
					this.content.bgColor = val;
				}else {
					this.content.bgColor = "#ffffff";
				}
			},
			onSetLink(obj) {
				console.log(obj)
				if(obj.tag == 'goodsGroup'){
					this.content.code=obj.data.code
					this.content.branchCode=obj.data.branchCode
				}else{
					this.content.code=''
				}

			}
		}
	}
</script>
