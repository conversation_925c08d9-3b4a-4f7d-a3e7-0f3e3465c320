<template>
	<div>
		<p class="blank_20">商品展示方式:</p>
		<el-row :gutter="20" style="margin:10px 0;">
			<el-col :span="6">
				<div class="block">
					<span class="demonstration">商品标题颜色：</span>
					<div>
						<el-color-picker v-model="content.styles.title.bg_color" size="mini"></el-color-picker>
					</div>
				</div>
			</el-col>
			<el-col :span="6">
				<div class="block">
					<span class="demonstration">商品内容文案颜色：</span>
					<div>
						<el-color-picker v-model="content.styles.con.bg_color" size="mini"></el-color-picker>
					</div>
				</div>
			</el-col>
			<el-col :span="6">
				<div class="block">
					<span class="demonstration">商品价格文案颜色：</span>
					<div>
						<el-color-picker v-model="content.styles.pricecon.bg_color" size="mini"></el-color-picker>
					</div>
				</div>
			</el-col>
			<el-col :span="6">
				<div class="block">
					<span class="demonstration">加购按钮颜色：</span>
					<div>
						<el-color-picker v-model="content.styles.btn.bg_color" size="mini"></el-color-picker>
					</div>
				</div>
			</el-col>
			<el-col :span="6">
				<div class="block">
					<span class="demonstration">背景颜色：</span>
					<div>
						<el-color-picker v-model="content.bgRes"  @change="change_bgRes" size="mini"></el-color-picker>
					</div>
				</div>
			</el-col>
		</el-row>
		<p class="blank_20"></p>
		<el-table :data="list"
				  :row-key="getRowKeys"
				  border fit highlight-current-row style="width: 100%"
		          v-if="list.length>0">
			<el-table-column label="商品名称" width="230">
				<template slot-scope="scope"><p>{{scope.row.showName}}</p>
				</template>
			</el-table-column>
			<el-table-column label="商品Id">
				<template slot-scope="scope">
					{{scope.row.id}}
				</template>
			</el-table-column>
			<el-table-column
					fixed="right"
					label="操作"
					width="120">
				<template slot-scope="scope">
					<el-button
							@click.native.prevent="deleteRow(scope.$index, list)"
							type="text"
							size="small">
						移除
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<p class="blank_20"></p>
		<all-link ref="all_link" @select="onSetLink" :tabs="tabs" :params="{
                    productlink: {
                        seledShow: false,
                        search: {
                            status: 1,
                            branchCode: topic.branchCode,
                            productType: 1,
                        }
                    }
                }"></all-link>
	</div>
</template>

<script>
	import base from '../base';
	import uploadImage from '../../components/upload-image';
	import {common} from 'api';

	export default {
		extends: base,
		contentDefault: {
			bgRes:"#f2f2f2",
			list: [],
			styles: {
				title: {
					bg_color: "#000000",
					size: 18
				},
				pricecon: {
					bg_color: "#ff0000",
					size: 14
				},
				con: {
					bg_color: "#dddddd",
					size: 14
				},
				btn: {
					bg_color: "#FF5050;",
					f_color: "#000000",
					size: 14
				}
			}
		},
		data() {
			return {
				tabs: [
					{label: '商品', value: 'productlink'}
				],
				goodsIds: [],
				selectItem: [],
				loading: false,
			}
		},
		computed: {
			list() {
				return _.get(this, 'content.list');
			}
		},
		components: {
			uploadImage
		},
		methods: {
			change_bgRes(val){
				if(val){
					this.content.bgRes = val;
				}else {
					this.content.bgRes = "#fff";
				}
			},
			deleteRow(index, rows) {
				rows.splice(index, 1);
			},
			formatTooltip(val) {
				return val / 100;
			},
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},

			getRowKeys(row) {
				if (!row.id) {
					return
				}
				return row.id
			},
			onSetLink(obj) {
				if (obj.data && obj.data.length > 1) {
					this.$message.warning('仅能选择1个商品，且商品只能单选，不能复选！');
				}
				this.content.list = obj.data.slice(0,1);
			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = this.list.indexOf(item)
					this.list.splice(index, 1)
				})
			},
			getImage(data) {
				if (data.image) {
					this.list[data.index].image = data.image
				}
			}
		}
	}
</script>
