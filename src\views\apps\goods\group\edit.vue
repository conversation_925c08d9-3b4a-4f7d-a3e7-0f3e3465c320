<template>
  <div class="create-topic-modal">
    <el-dialog width="80%" @close="afterClose" :visible.sync="visible" :title="title">
      <el-form
        :model="goodsGroupData.data"
        :rules="validate"
        :ref="validRef"
        label-position="right"
        size="small"
        label-width="100px"
        label-suffix="："
      >
        <el-form-item label="商品组ID" prop="code">
          <el-input
            v-model="goodsGroupData.data.code"
            :disabled="true"
            prefix-icon="el-icon-search"
            placeholder="商品组ID自动生成"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="组名" prop="name">
          <el-input v-model="goodsGroupData.data.name" placeholder="请填写名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-radio-group v-model="goodsGroupData.data.state">
            <el-radio v-for="(v, k, i) in goodsGroupData.state" :key="i" :label="Number(k)">{{ v }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="相关域" prop="branchCode">
          <el-select
            v-model="goodsGroupData.data.branchCode"
            :disabled="!!goodsGroupData.data.code"
            @change="branchChange"
            placeholder="选择区域"
            default-first-option
            filterable
          >
            <el-option
              v-for="(item, i) in goodsGroupData.branchs"
              :key="i"
              :value="item.branchCode"
              :label="item.branchName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="showGos && !!goodsGroupData.data.branchCode" label="选择商品" prop="goods">
          <el-badge :value="(goodsGroupData.data.goods || []).length" class="item" type="success">
            <el-button @click="showGosSel = !showGosSel" icon="el-icon-d-caret" size="mini" round></el-button>
          </el-badge>
          <all-link v-show="showGosSel" :tabs="tabs" @select="goodsSel"></all-link>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="goodsGroupData.data.remark"
            type="textarea"
            placeholder="填写备注"
            :autosize="{ maxRows: 2 }"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="visible = false">取消</el-button>
        <!-- 不可调用show()方法，会还原v-model绑定值 -->
        <el-button
          size="small"
          type="primary"
          :loading="loading"
          :disabled="loading"
          @click="save"
        >{{ loading ? '正在提交…' : '保存' }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from "api";

export default {
  name: "goodsGroupEdit",
  props: {
    goodsGroupData: {
      type: Object,
      default() {
        return {
          data: {}
        };
      }
    }
  },
  data() {
    return {
      title: "商品组",
      visible: false,
      loading: false,
      showGos: true,
      showGosSel: false,
      isAdmin: false,
      timId: 0,
      data: {
        state: 1
      },
      validRef: "form", //验证ref属性值
      validate: {
        name: [
          { required: true, message: "请填写组名", trigger: "blur" },
          { min: 3, max: 36, message: "长度在3 - 36之间", trigger: "blur" }
        ],
        state: [{ required: true, message: "请选择状态", trigger: "change" }],
        branchCode: [
          { required: true, message: "请选择区域", trigger: "change" }
        ],
        code: [
          { min: 2, max: 20, message: "长度在2 - 20之间", trigger: "blur" },
          {
            validator: (rule, val, callback) => {
              if (/\s/.test(val) || !/^[A-Za-z\d_-]+$/.test(val))
                var e = new Error(
                  "只能包含英文、数字、横线及下划线，且不能含有空格等"
                );
              return callback(e);
            },
            trigger: "blur"
          },
          { validator: this.codeValid, trigger: "blur" }
        ],
        goods: [{ required: true, validator: this.goodsValid, trigger: "blur" }]
      }
    };
  },
  computed: {
    tabs() {
      let data = this.goodsGroupData.data;
      let branchCode = data.branchCode;
      return [
        {
          label: "商品",
          value: "productlink",
          params: {
            minSel: 1,
            collShow: 1,
            search: {
              status: 1,
              productType: 1, //商品组添加单个商品过滤掉秒杀
              branchCode
            },
            data: {
              ids: data.goods
            }
          }
        },
        {
          label: "导入商品",
          value: "importGoods",
          params: {
            minSel: 1,
            search: { branchCode }
          }
        }
      ];
    }
  },
  methods: {
    save() {
      this.loading = !this.loading;
      this.$refs[this.validRef].validate(async ok => {
        if (!ok) {
          this.loading = !this.loading;
          return this.$message.error("验证失败！");
        }
        let data = Object.assign({}, this.goodsGroupData.data);
        delete data._goods;
        delete data.create_time;
        delete data.create_id;
        delete data.create_name;
        let method = !data.id ? "save" : "update";
        let r = await api.goodsGroup[method](data);
        this.loading = !this.loading;
        if (r.code == 200) {
          this.$message.success(`${!data.id ? "添加" : "编辑"}商品组成功。`);
          this.afterSave();
        } else {
          this.$notify({
            message: r.msg,
            type: "error",
            dangerouslyUseHTMLString: true, //允许html
            offset: 100, //偏移
            duration: 60000
          });
        }
      });
    },
    branchChange(v) {
      this.goodsGroupData.data.goods = []; //切换区域，则清空已选的其它区域商品
      if (!(this.showGos = !!v)) return;
      this.showGos = !this.showGos;
      this.$nextTick(() => {
        //此回调，是为了按区域编码，重新加载商品
        this.showGos = !this.showGos;
      });
    },
    goodsSel(v) {
      this.showGosSel = false;
      this.goodsGroupData.data.goods = v.ids;
    },
    show() {
      this.visible = true;
      let ggd = this.goodsGroupData;
      this.isAdmin = ggd.loginUser.userName == "admin"; //管理员功能限制
      if (!this.isAdmin)
        ggd.data.branchCode =
          ggd.data.branchCode || ggd.loginUser.branch.branchCode;
      this.$set(ggd.data, "state", ggd.data.state || this.data.state);
    },
    afterSave() {
      this.$emit("save-done");
      this.visible = false;
    },
    afterClose() {
      this.$emit("close");
      this.$refs[this.validRef].resetFields();
    },
    /**
     * 编号验证
     * @param rule
     * @param value
     * @param callback
     * @returns {Promise<*>}
     */
    codeValid(rule, value, callback) {
      let data = this.goodsGroupData.data;
      if (data.id || !data.code) {
        return callback();
      }
      if (this.timId) clearTimeout(this.timId);
      this.timId = setTimeout(async () => {
        //规定时间内，不频繁验证
        let pms = {
          code: data.code,
          branchCode: data.branchCode
        };
        let res = await api.goodsGroup.query(pms);
        if (res.code == 200 && res.data && res.data.length > 0) {
          var e = new Error("此编号已存在");
        }
        clearTimeout(this.timId);
        return callback(e);
      }, 1000);
    },
    /**
     * 商品验证
     * @param rule
     * @param value
     * @param callback
     * @returns {*}
     */
    goodsValid(rule, value, callback) {
      let goods = this.goodsGroupData.data.goods;
      if (!goods || !goods.length) var e = new Error("请选择商品");
      return callback(e);
    }
  }
};
</script>

<style scoped>
</style>
