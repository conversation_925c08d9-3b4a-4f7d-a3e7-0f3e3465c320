import * as http from 'utils/http'

export default {
    list(params) {
        return http.post('activity', params, {
	        contentType: 'application/json; charset=UTF-8'
        });
    },
	query(params) {
		return http.post('activity/query', params, {
			contentType: 'application/json; charset=UTF-8'
		});
	},
    get(id) {
        return http.get(`activity/${id}`)
    },
    add(params) {
        return http.post('activity/add', params, {
	        contentType: 'application/json; charset=UTF-8'
        })
    },
    update(id, params) {
        return http.post(`activity/${id}/update`, params, {
	        contentType: 'application/json; charset=UTF-8'
        })
    },
    remove(id) {
        return http.post(`activity/${id}/remove`)
    },
    /**
	 * 状态
	 */
	activityState () {
        return http.post('/dict/activityState');
    },
    /**
	 * 客户端类型
	 */
    activityCategory() {
        return http.post('/dict/activityCategory');
    },
	/**
	 * 页面类型
	 */
	activityPageType() {
		return http.post('/dict/activityPageType');
	}
}
