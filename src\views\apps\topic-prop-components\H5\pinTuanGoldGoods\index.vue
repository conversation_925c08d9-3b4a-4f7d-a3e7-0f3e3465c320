<template>
  <div class="topic-menu-list">
    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-col :span="24">
        <div class="block">
          <div>
            背景色<el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>
          </div>
          <div class="block">
            <el-upload
              style="display: inline-block;margin-right: 10px"
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg, image/png, image/gif"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadBgImage"
            >
              <el-button size="mini" class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
              <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>
            <el-button size="mini" @click="() => { content.bgImg = ''}">清除背景图</el-button>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- <el-row :gutter="20">
      <div class="title">期望关联的密钥</div>
      <el-col :span="24">
        <div class="block">
          <el-input placeholder="输入期望关联的密钥" v-model="content.pass_word">
            <template slot="prepend">期望关联的密钥</template>
          </el-input>
        </div>
      </el-col>
    </el-row> -->

    <!-- <el-row :gutter="20">
      <div class="title">展示个数设置</div>
      <el-col :span="24">
        <el-radio v-model="content.goods_num" label="3">一页3个</el-radio>
        <el-radio v-model="content.goods_num" label="6">一页6个</el-radio>
      </el-col>
    </el-row> -->
    <!-- 模块有效时间设置 -->
    <el-row :gutter="20" class="brand-time">
            <div class="title">模块有效时间设置</div>
            <el-col :span="24">
                <el-date-picker
                    v-model="content.timevalue"
					@change="change_time"
                    type="datetimerange"
                    :picker-options="pickerOptions"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    align="right">
                </el-date-picker>
            </el-col>
        </el-row>
        <el-row :gutter="20" class="brand-time">
      <div class="title">标题设置</div>
      <el-col :span="24">
        <el-input
          size="small"
          v-model="content.title"
          placeholder="请输入标题"
          @input="()=>{
            if(content.title.length>10){
              this.$message.error('标题最大允许输入10个汉字')
              content.title=content.title.substring(0, 10)
            }
          }"
        />
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <div class="title">列表基础配置</div>
      <el-col :span="24" style="margin: 20px">
        <el-radio-group v-model="content.styleType" @change="changeStyleType">
          <el-radio :label="1">默认样式</el-radio>
          <el-radio :label="2">定制样式（高毛）</el-radio>
        </el-radio-group>
      </el-col>
      <el-col :span="24">
        展示页数：
        <el-select v-model="content.pageCount" placeholder="请选择">
          <el-option
            v-for="item in pageCount_option"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>

    <!-- <el-row :gutter="20">
      <div class="title">编辑品牌头图</div>
      <el-col :span="24">
        <el-button type="primary" @click="addDialog=true">上传头图</el-button>
      </el-col>
    </el-row> -->

    <br />
    <el-row :gutter="20">
      <div v-if="content.goods_group.length">
        <div class="title" style="margin-bottom: 0">预览已选商品组</div>
        <el-col :span="24">
          <el-table :data="content.goods_group" height="120" ref="multipleTable">
            <el-table-column prop="name" label="组名"></el-table-column>
            <el-table-column prop="code" label="编号"></el-table-column>
            <el-table-column prop="branchCode" label="区域号"></el-table-column>
          </el-table>
        </el-col>
      </div>
    </el-row>

    <!--上传图片弹框-->
    <!-- <el-dialog class="banner-dialog" title="设置信息" :visible.sync="addDialog">
      <el-upload
        class="topic-image-upload"
        ref="upload"
        accept="image/jpeg, image/png, image/gif"
        :show-file-list="false"
        :before-upload="() => {loading = true; return true;}"
        :on-success="onUploadImage"
      >
        <img v-if="content.title_img.bgRes" :src="content.title_img.bgRes" class="image" />
        <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
      </el-upload>

      <div class="level">
        <div class="topic-image-picker">
          <span>此字段用于：埋点统计的名称，默认赋予（用户可见名称） 可修改！</span>
          <el-input placeholder="页面名称" v-model="content.title_img.page_name">
            <template slot="prepend">用户可见名称</template>
          </el-input>
        </div>

        <div class="topic-image-picker">
          <el-input placeholder="链接地址" v-model="content.title_img.page_url">
            <template slot="prepend">跳转链接</template>
          </el-input>
        </div>
        <control-page @select="control_SetLink" :params="{page_type: topic.page_type === 'h5' ? 'h5' : '', branchCode: topic.branchCode}"></control-page>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialog=false">取 消</el-button>
        <el-button size="small" type="primary" @click="addDialog=false">确定</el-button>
      </div>
    </el-dialog> -->

    <!--选择商品-->
    <all-link
      @select="onSetLink"
      :tabs="tabs"
      :params="{
        page_type: topic.page_type === 'h5' ? `control-${topic.page_type}` : '',
        goodsGroup: {
          seledShow: false,
          minSel: 1,
          search: {
            state: 1,
            branchCode: topic.branchCode
          }
        }
      }"
    ></all-link>
  </div>
</template>

<script>
import base from "views/apps/topic-prop-components/base.vue";
import api from "api";

export default {
  extends: base,
  contentDefault: {
    pageCount: "8",
    // goods_num: "3",
    styleType: 1,
    list: [],
    goods_group: [],
    title_img: {
      bgRes: "",
      page_url: "",
      page_name: "",
      is_share: false
    },
    // pass_word: "",
    bgColor: "",
    bgImg: '',
    image: "",
    title:""
  },
  created() {
    this.content.pageCount = this.content.pageCount
      ? this.content.pageCount
      : "8";
  },
  data() {
    return {
      loading: false,
      addDialog: false,
      tabs: [{ label: "商品组", value: "goodsGroup" }],
      pageCount_option: [
        {
          value: "1",
          label: "1页"
        },
        {
          value: "2",
          label: "2页"
        },
        {
          value: "3",
          label: "3页"
        },
        {
          value: "4",
          label: "4页"
        },
        {
          value: "5",
          label: "5页"
        },
        {
          value: "6",
          label: "6页"
        },
        {
          value: "7",
          label: "7页"
        },
        {
          value: "8",
          label: "8页"
        }
      ],
      pickerOptions: {
                    shortcuts: [{
                        text: '未来一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来六个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来一年',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
				},
    };
  },
  methods: {
    // 模块有效时间设置
    change_time(){
      console.log('content.timevalue',this.content.timevalue);
      console.log('this.content.list',this.content.list);
				for (let item of this.content.list) {
					this.$set(item, "time", this.content.timevalue)
				}
				this.content.list=this.content.list.map(item=>{
					return item
				});
			},
    async onUploadBgImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.bgImg = res.data.url;
    },
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.image = res.data.url;
    },
    // 上传轮播图片
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.title_img.bgRes = res.data.url;
    },
    control_SetLink(link) {
      this.content.title_img.page_url = link.meta.page_url;
      this.content.title_img.page_name = link.meta.page_name;
    },
    onSetLink(link) {
      if (link.tag === "goods" || link.tag === "importGoods") {
        this.content.list = !this.content.list.length
          ? [...link.data]
          : [...api.common.removeRepeat(this.content.list, link.data)];
      } else if (link.tag === "goodsGroup") {
        let obj = {};
        obj.name = link.data.name;
        obj.branchCode = link.data.branchCode;
        obj.code = link.data.code;
        this.content.goods_group.splice(0, 1, obj);
        this.content.list = [];
      }
    },
    changeStyleType(e) {
      if(e == 2) {
        this.content.bgColor = '#FA2A2A';
      }
    }
  }
};
</script>


<style scoped lang="scss">
.topic-image-upload {
  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 200px;
    height: 200px;
    line-height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    font-size: 50px;
  }
}

.topic-image-upload .el-upload {
  width: 100%;
}

.el-row {
  text-align: center;

  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
.block {
  display: flex;
  padding: 5px 20px;
  justify-content: space-between;
}
</style>
