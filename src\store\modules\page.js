import {common} from 'api'
import * as types from '../mutation-types'
import Vue from 'vue'
const pageView = {
	state: {
		pubData: {},
		pageData: [],
		currentSwitch: '',
		currentRow: [0, 0],
		isEdit: false,
	},
	mutations: {
		[types.INIT_DATA](state,data){
			let {pubData,pageData,currentSwitch,currentRow}=_.cloneDeep(data)
			state.pubData=pubData
			state.pageData=pageData
			state.currentSwitch=currentSwitch
			state.currentRow=currentRow
		},
		[types.STORE_DATA](state,data){
			if(data.flag==='pub'){
				//公共组件数据
				state.pubData=_.cloneDeep(data.data)
			}else{
				//选项卡组件数据
				let json={}
				json.name=state.currentSwitch;
				json.data=[]
				json.data[state.currentRow[0]]=[]
				json.data[state.currentRow[0]][state.currentRow[1]]=_.cloneDeep(data.data)
				if(state.pageData.length===0){
					state.pageData.push(json)
				}else{
					const index=common.getRepeatResult('name',state.currentSwitch,state.pageData)
					if(index>=0){
						if(state.pageData[0].data[state.currentRow[0]]){
							state.pageData[index].data[state.currentRow[0]].splice([state.currentRow[1]],1,_.cloneDeep(data.data))
						}else{
							new Vue().$set(state.pageData[index].data,state.currentRow[0],[])
							new Vue().$set(state.pageData[index].data[state.currentRow[0]],state.currentRow[1],_.cloneDeep(data.data))
							// state.pageData[index].data[state.currentRow[0]]=[];
							// state.pageData[index].data[state.currentRow[0]][state.currentRow[1]]=_.cloneDeep(data.data);
						}
					}else{
						state.pageData.push(json)
					}
				}


			}
		},
		[types.SUB_CURRENT_SWITCH](state,data){
			state.currentSwitch=data
		},
		[types.SUB_CURRENT_ROW](state,data){
			state.currentRow=Object.assign({},data)
		},
		[types.SET_EDIT_STATUS](state,data){
			state.isEdit=data
		}
	},
	actions:{
		initData({commit},data){
			commit(types.INIT_DATA,data)
		},
		storeData({commit},data){
			commit(types.STORE_DATA,data)
		},
		subCurrentSwitch({commit},data){
			commit(types.SUB_CURRENT_SWITCH,data)
		},
		subCurrentRow({commit},data){
			commit(types.SUB_CURRENT_ROW,data)
		},
		setEditStatus({commit},data){
			commit(types.SET_EDIT_STATUS,data)
		}
	}
}
export default pageView
