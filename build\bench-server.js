require('./check-versions')()
const config = require('../config')
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = config.bench.env.NODE_ENV;
  process.env.BUILD_ENV = JSON.parse(config.bench.env.BUILD_ENV);
}
const env = process.env.BUILD_ENV;
const opn = require('opn')
const path = require('path')
const express = require('express')
const webpack = require('webpack')
const proxyMiddleware = require('http-proxy-middleware')
const webpackConfig = require('./webpack.dev.conf')
// default port where dev server listens for incoming traffic
const port = process.env.PORT || config[ env ].port
// automatically open browser, if not set will be false
const autoOpenBrowser = !!config[ env ].autoOpenBrowser
// Define HTTP proxies to your custom API backend
// https://github.com/chimurai/http-proxy-middleware
const proxyTable = config[ env ].proxyTable

const app = express()
const compiler = webpack(webpackConfig)
app.get('/', function (req, res, next) {
  var filepath = path.join(__dirname, '..', 'index.html');
  // 使用webpack提供的outputFileSystem
  compiler.outputFileSystem.readFile(filepath, function (err, result) {
    if (err) {
      // something error
      return next(err);
    }
    result = result.toString();
    res.set('content-type', 'text/html');
    res.send(result);
    res.end();
  })
})
var devMiddleware = require('webpack-dev-middleware')(compiler, {
  publicPath: webpackConfig.output.publicPath,
  quiet: true
})
var hotMiddleware = require('webpack-hot-middleware')(compiler, {
  log: () => {
  }
})
// force page reload when html-webpack-plugin template changes
compiler.plugin('compilation', function (compilation) {
  compilation.plugin('html-webpack-plugin-after-emit', function (data, cb) {
    hotMiddleware.publish({ action: 'reload' })
    cb()
  })
})
// proxy api requests
Object.keys(proxyTable).forEach(function (context) {
  var options = proxyTable[ context ]
  if (typeof options === 'string') {
    options = { target: options }
  }
  app.use(proxyMiddleware(options.filter || context, options))
})
// handle fallback for HTML5 history API
app.use(require('connect-history-api-fallback')())
// serve webpack bundle output
app.use(devMiddleware)
// enable hot-reload and state-preserving
// compilation error display
app.use(hotMiddleware)
// serve pure static assets
var staticPath = path.posix.join(config[ env ].assetsPublicPath, config[ env ].assetsSubDirectory)
app.use(staticPath, express.static('./static'))
var uri = 'http://localhost:' + port
var _resolve
var readyPromise = new Promise(resolve => {
  _resolve = resolve
})
console.log('> Starting ' + env + ' server...')
devMiddleware.waitUntilValid(() => {
  console.log('> Listening at ' + uri + '\n')
  // when env is testing, don't need open it
  if (autoOpenBrowser && env === 'bench') {
    opn(uri)
  }
  _resolve()
})
var server = app.listen(port)
module.exports = {
  ready: readyPromise,
  close: () => {
    server.close()
  }
}
