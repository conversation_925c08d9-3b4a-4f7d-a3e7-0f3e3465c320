<template>
  <div>
    <!-- 新增 -->
    <el-dialog
      class="tab-setting-dialog"
      :title="`${isEdit ? '编辑' : '新建'}`"
      :before-close="addDialogCancel"
      :visible.sync="addDialog"
    >
      <el-form
        label-position="right"
        ref="addRuleForm"
        :model="addForm"
        size="small"
        label-width="100px"
        label-suffix="："
      >
        <el-form-item label="tab名称" prop="activityName">
          <el-input
            v-model="addForm.activityName"
            size="mini"
            placeholder="请输入tab名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="有效期">
          <el-radio v-model="addForm.timeType" :label="1">固定时段</el-radio>
          <el-date-picker
            v-model="addForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :picker-options="{
              disabledDate: (time) => {
                const times =
                  new Date(new Date().toLocaleDateString()).getTime() +
                  1095 * 8.64e7 -
                  1;
                return (
                  time.getTime() < Date.now() - 8.64e7 || time.getTime() > times
                ); // 如果没有后面的-8.64e7就是不可以选择今天的
              },
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker
          ><br />
          <el-radio v-model="addForm.timeType" :label="2">周期循环</el-radio>
          <el-button
            style="margintop: 10px"
            @click="toloopcirculateTime"
            type="primary"
            size="mini"
            >配置</el-button
          >
        </el-form-item>

        <el-form-item label="位置" prop="position">
          <el-select v-model="addForm.position">
            <el-option :label="'左'" :value="'1'"> </el-option>
            <el-option :label="'右'" :value="'2'"> </el-option>
           
          </el-select>
        </el-form-item>


        <el-form-item label="商品组ID" prop="goodsId">
          <el-input
            v-model="addForm.goodsId"
            size="mini"
            placeholder="请输入商品组Id"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addDialogConfirm"
          >确定</el-button
        >
      </div>

    </el-dialog>
    <loopcirculateTime
      ref="loopcirculateTime"
      @loopcirculateTimeBack="loopcirculateTimeBack"
    ></loopcirculateTime>
  </div>
</template>


<script>
import loopcirculateTime from "../../../../components/loopcirculateTime.vue";
import swiperPoint from "views/apps/components/public/swiper-point";
import api from "api";
export default {
  components: { swiperPoint, loopcirculateTime },
  props: {
    value: Object,
    topic: Object,
    categoryList: Array,
  },
  data() {
    return {
      isEdit: false,
      addDialog: false,
      options: [],
      selectLoading: false,
      isShowHrefDialog: false,
      addForm: {
        goodsId: "",
        goodsType: "",
        activityName: "",
        position:"",
        crowdId: "",
        crowdValue: "",
        timeType: "1",
        circulateTime: {},
      },
      addFormSelectLink: "",
      bannerLocationList: [
        {
          id: 1,
          name: "第一帧",
        },
        {
          id: 2,
          name: "第二帧",
        },
        {
          id: 3,
          name: "第三帧",
        },
        {
          id: 4,
          name: "第四帧",
        },
        {
          id: 5,
          name: "第五帧",
        },
        {
          id: 6,
          name: "第六帧",
        },
        {
          id: 7,
          name: "第七帧",
        },
        {
          id: 8,
          name: "第八帧",
        },
      ],
    };
  },
  methods: {
    open() {
      this.isEdit = false;
      this.addDialog = true;
    },

    onSetLink(link) {
      this.addFormSelectLink = link.meta.page_url;
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    //打开时间循环
    toloopcirculateTime() {
      this.$refs.loopcirculateTime.showVisible = true;
    },
    changeCrowdType() {
      this.addForm.crowdId = "";
      this.addForm.crowdValue = "";
    },
    //循环时间回调
    loopcirculateTimeBack(data) {
      this.addForm.circulateTime = data;
    },
    addDialogCancel() {
      this.resetAddForm();
      this.addDialog = false;
    },

    hrefCancel() {
      this.addFormSelectLink = "";
      this.isShowHrefDialog = false;
    },
    hrefConfirm() {
      this.addForm.hrefUrl = this.addFormSelectLink;
      this.isShowHrefDialog = false;
    },
    resetAddForm() {
      this.addForm = {
        validityTime: "", //有效期
        crowdType: 1, //人群switch
        wordId: "", //词组id
        wordName: "", //词组名称
        crowdValue: "", // 人群
        wordValue: "",
        hrefType: 1,
        hrefUrl: "",
        timeType: 1,
        circulateTime: "",
        status: 1,
      };
    },
    async addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        if (
          this.content.list.findIndex(
            (item) => item.wordName === this.addForm.wordName
          ) > -1
        ) {
          this.$message("热词组名称已存在！");
          return;
        }
        this.$set(this.content.list, this.content.list.length, this.addForm);
        this.$message.success("添加成功！");
        this.addDialog = false;
      });
    },
  },
};
</script>

<style scoped>
.cowdtype-radio {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>