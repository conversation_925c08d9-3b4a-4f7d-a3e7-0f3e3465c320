<template>
	<div>
		<!--商品列表-->
		<div>
			背景颜色:
			<el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>
			文字颜色:
			<el-color-picker v-model="content.color" size="mini"></el-color-picker>
			文字选中颜色:
			<el-color-picker v-model="content.hoverColor" size="mini"></el-color-picker>
		</div>
		<p class="blank_20"></p>
		排列类型：
		<el-radio-group v-model="content.type">
			<el-radio :label="index" v-for="(item,index) in typeList" :key="index">{{item}}</el-radio>
		</el-radio-group>
		<p class="blank_20"></p>
		添加购物车按钮：
		<el-checkbox v-model="content.isBtn">{{content.isBtn?'添加':'取消'}}</el-checkbox>
		<p class="blank_20"></p>
		<el-button type="primary" size="mini" @click="handleDelete">删除</el-button>
		<p class="blank_20"></p>
		<el-table :data="list" :row-key="getRowKeys" border fit highlight-current-row style="width: 100%"
		          v-if="list.length>0"
		          @selection-change="handleSelection">
			<el-table-column
				  type="selection"
				  width="55"/>
			<el-table-column prop="title" label="榜名" width="120">
				<template slot-scope="scope">
					<template>
						<el-input v-model.lazy="scope.row.title" class="edit-input" size="mini"/>
					</template>
				</template>
			</el-table-column>
			<el-table-column label="商品组名">
				<template slot-scope="scope">
					{{scope.row.goodsName}}/{{scope.row.goodsNum}}
				</template>
			</el-table-column>
			<el-table-column label="默认/选中">
				<template slot-scope="scope">
					<upload-image :index="scope.$index" :image="scope.row.image" :hoverImage="scope.row.hoverImage"
					              v-on:listenImage="getImage"></upload-image>
				</template>
			</el-table-column>
		</el-table>
		<p class="blank_20"></p>
		<all-link @select="onSetLink" :tabs="tabs" :params="{
				goodsGroup: {
					radio: 0,
					returnGoods: 0,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    }
                }
             }"></all-link>
	</div>
</template>

<script>
	import base from "../base";
	import uploadImage from './upload-image'

	export default {
		extends: base,
		name: "topList",
		contentDefault: {
			list: [],
			color: '#999', //文字颜色
			hoverColor: '#00DC82', //文字选中颜色,
			bgColor: '#ccc', //背景颜色,
			type:0,
			isBtn:false //添加购物车按钮
		},
		data() {
			return {
				form: {name: ""},
				rules: {
					name: [
						{required: true, message: '请输入榜单名称', trigger: 'blur'},
						{min: 2, max: 10, message: '长度在 3 到 10 个字符', trigger: 'blur'}
					]
				},
				tabs: [
					{label: '商品组', value: 'goodsGroup'}
				],
				selectItem: [],
				loading: false,
				goodsIds: [],
				typeList:['列表模式','大图模式']
			}
		},
		components: {
			uploadImage
		},
		computed: {
			list() {
				var list = _.get(this, 'content.list')
				if (list) {
					if (list.length > 0) {
						this.$nextTick(function () {
							this.setSort()
						})
					}
					return list
				} else {
					return []

				}
			}
		},
		methods: {
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},
			getRowKeys(row) {
				if (!row.id) {
					return
				}
				return row.id
			},
			onSetLink(obj) {
				obj.data.forEach((item, index) => this.list.push({
					id: item.id,
					goodsName: item.name,
					code:item.code,
					goodsIds: item.goods,
					goodsNum: item.goods.length,
					title: '',
					image: '',
					hoverImage: '',
					page_url: '',
					page_name: ''
				}));
			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = this.list.indexOf(item)
					this.list.splice(index, 1)
				})
			},
			getImage(data) {
				if (data.image) {
					this.list[data.index].image = data.image
				} else if (data.hoverImage) {
					this.list[data.index].hoverImage = data.hoverImage
				}

			}

		}

	}
</script>
<style lang="scss" scoped>
	.topic-image-upload {
		width: 45%;

		.image {
			display: block;
			width: 100%;

		}

		.uploader-icon {
			width: 100%;
			height: 100%;
			line-height: 100%;
			border: 1px solid $border-base;
			font-size: 30px;
		}
	}

	.topic-image-picker {
		padding-top: 10px;
		padding-bottom: 10px;
	}

	.icon-text {
		white-space: nowrap !important;
		text-overflow: ellipsis;
	}

</style>
