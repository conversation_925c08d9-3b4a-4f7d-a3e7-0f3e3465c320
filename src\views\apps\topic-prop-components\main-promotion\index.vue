<template>
	<div>
		<!--品牌促销-->
		<p class="blank_20"></p>
		<el-radio-group v-model="content.type">
			<el-radio :label="index" v-for="(item,index) in countList" :key="index">{{item}}</el-radio>
		</el-radio-group>
		<p class="blank_20"></p>
		<div v-if="content.type===0">
			<el-row>
				<el-col :span="8">
					<el-input type="text" size="mini" v-model="content.code"
					          autocomplete="off" placeholder="请输入套餐编号"></el-input>
				</el-col>
				<el-col :span="16">
					<el-button type="primary" size="mini" @click="handleSearch" style="margin-left:10px;">查询</el-button>
				</el-col>
			</el-row>
			<p class="blank_20"></p>
			选择套餐样式：
			<el-radio-group v-model="content.styleNum">
				<el-radio :label="item" v-for="(item,index) in styleList" :key="index">{{`样式${item}`}}</el-radio>
			</el-radio-group>
			<div  v-show="content.styleNum===2">
				<p class="blank_20"></p>
				是否显示标题：
				<el-checkbox v-model="content.isTitle">{{content.isTitle?'隐藏':'显示'}}</el-checkbox>
			</div>
			<div style="width:100px;">
				<upload-image  :image="content.image"
				              v-on:listenImage="getImage"></upload-image>
			</div>
			<p class="blank_10"></p>
			<el-row :gutter="10">
				<el-col :span="6">
					<el-upload
						  class="topic-image-upload"
						  ref="upload"
						  accept="image/jpeg,image/jpg,image/png,image/gif"
						  :show-file-list="false"
						  :on-success="onUploadImg">
						<el-button size="mini" class="btn-block" type="primary">上传背景图</el-button>
						<div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
					</el-upload>
				</el-col>
				<el-col :span="6">
					<el-upload
						  class="topic-image-upload"
						  ref="upload"
						  accept="image/jpeg,image/jpg,image/png,image/gif"
						  :show-file-list="false"
						  :on-success="onUploadFlag">
						<el-button size="mini" class="btn-block" type="primary" >上传小图标</el-button>
						<div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
					</el-upload>
				</el-col>
			</el-row>
			<p class="blank_20"></p>
			<el-row>
				<el-col :span="8">
					<el-input type="text" size="mini" v-model="content.link" @keyup.enter.native="handleSearch"
					          autocomplete="off" placeholder="选中活动页连接"></el-input>
				</el-col>
				<el-col :span="16">
				</el-col>
			</el-row>
			<p class="blank_20"></p>
		</div>
		<div v-else-if="content.type===1">
			<el-button type="primary" @click="handleDelete" size="mini">删除</el-button>
			<p class="blank_20"></p>
			添加购物车按钮：
			<el-checkbox v-model="content.isBtn">{{content.isBtn?'添加':'取消'}}</el-checkbox>
			<p class="blank_20"></p>
			<el-table :data="list" :row-key="getRowKeys" border fit highlight-current-row style="width: 100%"
			          v-if="list.length>0"
			          @selection-change="handleSelection">
				<el-table-column
					  type="selection"
					  width="55"/>
				<el-table-column label="商品组名" prop="goodsName">
				</el-table-column>
				<el-table-column label="跳转页面" prop="page_name">
				</el-table-column>
				<el-table-column label="活动图片">
					<template slot-scope="scope">
						<upload-image :index="scope.$index" :image="scope.row.image"
						              v-on:listenImage="getImage"></upload-image>
					</template>
				</el-table-column>
			</el-table>
			<p class="blank_20"></p>
		</div>
		<all-link @select="onSetLink" :tabs="tabs" :params="{
				page:{
					branchCode: topic.branchCode
				},
				goodsGroup:{
					radio: 0,
					returnGoods: 0,
                    search: {
                        state: 1,
                        branchCode: topic.branchCode
                    },
                    data: {
                        ids: goodsIds
                    }
                }
             }"></all-link>

	</div>
</template>

<script>
	import base from '../base'
	import uploadImage from '../../components/upload-image'
	import {common, activePackage} from 'api'

	export default {
		name: 'mainPromotion',
		extends: base,
		contentDefault: {
			type: 0,
			code: 0,
			branchCode: 0,
			list: [],
			count: 3,
			image: '',
			isBtn:false, //添加购物车按钮
			styleNum:3,
			isTitle:false,
			bgImage:'http://upload.ybm100.com/ybm/applayoutbanner/6e41f728-4f27-4b57-8f16-c883a17f1158.png',
			flag:'http://upload.ybm100.com/ybm/applayoutbanner/f2e8af0f-268b-4481-bf2d-a33b94879bd1.png',
			searchNum:1
	},
		data() {
			return {
				tabs: [
					{label: '活动页', value: 'page'},
					{label: '商品组', value: 'goodsGroup'}
				],
				countList: ['优+套餐', '商品组'],
				goodsIds: [],
				styleList:[1,2,3]
			}
		},
		computed: {
			list() {
				var list = _.get(this, 'content.list')
				if (list) {
					if (list.length > 0) {
						this.$nextTick(function () {
							this.setSort()
						})
					}
					return list
				} else {
					return []
				}
			}
		},
		components: {
			uploadImage
		},
		methods: {
			onUploadImg(res, file) {
				if (res.code !== 200) {
					this.$message({
						message: `[${res.code}]${res.msg}`,
						type: 'warning'
					})
					return;
				}
				this.content.bgImage = res.data.url;
			},
			onUploadFlag(res, file){
				if (res.code !== 200) {
					this.$message({
						message: `[${res.code}]${res.msg}`,
						type: 'warning'
					})
					return;
				}
				this.content.flag = res.data.url;
			},
			handleSelection(val) {
				if (val.length === 0) {
					return
				}
				this.selectItem = val
			},
			getRowKeys(row) {
				if (!row.id) {
					return
				}
				return row.id
			},
			onSetLink(obj) {
				if (this.content.type === 0) {
					this.content.link = obj.meta.page_url;
					return;
				}
				if (obj.tag == 'goodsGroup') {

					obj.data.map((item, index)=> this.list.push({
						id: item.id,
						goodsName: item.name,
						code:item.code,
						goodsIds: item.goods,
						image: '',
						page_url: '',
						page_name: ''
					}));
				} else {
					if (this.selectItem.length === 0 || this.selectItem.length !== 1)
						return this.$message.warning('请先选中1个标签');
					let index = this.list.indexOf(this.selectItem[0]);
					this.list[index].page_url = obj.meta.page_url;
					this.list[index].page_name = obj.meta.page_name;
				}
			},
			handleDelete() {
				this.selectItem.forEach(item => {
					const index = this.list.indexOf(item)
					this.list.splice(index, 1)
				})
			},
			getImage(data) {
				if(this.content.searchNum!==1)this.content.image = data.image;
				if (data.image && this.content.type === 1) {
					this.list[data.index].image = data.image
				}
			},
			handleSearch() {
				if (!this.content.code || isNaN(parseInt(this.content.code))) {
					this.$message.error('请输入正确的查询编码')
					return
				}
				//package_id:1195
				let params = {
					'packageId': this.content.code,
					'effectiveStatus': 1,
					'branchCode': this.topic.branchCode
				};
				activePackage.select(params).then(res => {
					if (res.status !== 'success') {
						this.$message.warning('查询套餐已失效，请重新输入')
						return
					}
					if(!res.resultPage.rows||res.resultPage.rows.length===0){
						this.$message.warning('套餐无效')
						return
					}
					this.content.searchNum++;
					this.content.branchCode = this.topic.branchCode
				}).catch(err => {
					console.log(err)
					this.$message.error('查询错误,请重新输入')
				})


			}
		}
	}
</script>
