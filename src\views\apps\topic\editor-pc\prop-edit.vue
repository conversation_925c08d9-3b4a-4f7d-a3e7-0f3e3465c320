<template>
  <el-dialog @closed="afterClose" :visible.sync="baseShow" title="设置基础属性" center>
    <el-container>
      <el-tooltip content="高度值" placement="top">
        <el-aside width="50">
          <el-form :model="base.styles" :rules="validate" :ref="validRefs[1]" size="mini">
            <el-form-item>
              <el-slider
                :max="1000"
                v-model="base.styles.height"
                label="高度"
                vertical
                show-input
                style="height: 130px; margin: 0 15px;">
              </el-slider>
            </el-form-item>

            <!--隐藏单位配置-->
            <!--<el-form-item prop="unit">-->
            <!--<el-select v-model="base.styles.heightUnit" placeholder="单位" size="mini" default-first-option filterable clearable>-->
            <!--<el-option-->
            <!--v-for="(v, k, i) in sizeUnit"-->
            <!--:value="k"-->
            <!--:label="k">-->
            <!--<span>{{k}}</span>-->
            <!--<span class="sel-option-hint">{{v}}</span>-->
            <!--</el-option>-->
            <!--</el-select>-->
            <!--</el-form-item>-->

          </el-form>
        </el-aside>
      </el-tooltip>
      <el-container>
        <el-header height="30">
          <el-form :model="base.common" :rules="validate" :ref="validRefs[0]" size="mini">
            <el-form-item prop="name">
              <el-input size="mini" :readonly="true" v-model="base.common.name" placeholder="默认(字母数字)" clearable>
                <b slot="prepend">楼层名</b>
              </el-input>
            </el-form-item>
          </el-form>
        </el-header>
        <el-container style="padding: 15px;">
          <el-aside width="50%">
            <el-form :model="base.styles" :rules="validate" :ref="validRefs[3]" size="mini">
              <el-row>
                <el-col :span="24">
                  <label>内边距：</label>
                </el-col>
                <!--隐藏单位-->
                <!--<el-col :span="8">-->
                <!--<el-form-item prop="unit">-->
                <!--<el-select v-model="base.styles.paddingUnit" placeholder="单位" size="mini" align="right" default-first-option filterable clearable>-->
                <!--<el-option-->
                <!--v-for="(v, k, i) in sizeUnit"-->
                <!--:value="k"-->
                <!--:label="k">-->
                <!--<span>{{k}}</span>-->
                <!--<span class="sel-option-hint">{{v}}</span>-->
                <!--</el-option>-->
                <!--</el-select>-->
                <!--</el-form-item>-->
                <!--</el-col>-->
              </el-row>
              <el-row>
                <el-col :span="12" :offset="6">
                  <span>上</span>
                  <el-form-item>
                    <el-input-number v-model="base.styles.padding[0]" :min="0" :max="500" size="mini" placeholder="上"
                                     clearable></el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <span>左</span>
                  <el-form-item>
                    <el-input-number v-model="base.styles.padding[3]" :min="0" :max="500" controls-position="right"
                                     size="mini" placeholder="左" clearable></el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <span>右</span>
                  <el-form-item>
                    <el-input-number v-model="base.styles.padding[1]" :min="0" :max="500" controls-position="right"
                                     size="mini" placeholder="右" clearable></el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="12" :offset="6">
                  <span>下</span>
                  <el-form-item>
                    <el-input-number v-model="base.styles.padding[2]" :min="0" :max="500" size="mini" placeholder="下"
                                     clearable></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-aside>
          <el-aside width="50%">
            <el-form :model="base.styles" :rules="validate" :ref="validRefs[2]" size="mini">
              <el-row>
                <el-col :span="24">
                  <label>外边距：</label>
                </el-col>
                <!--隐藏单位-->
                <!--<el-col :span="8">-->
                <!--<el-form-item prop="unit">-->
                <!--<el-select v-model="base.styles.marginUnit" placeholder="单位" size="mini" align="right" default-first-option filterable clearable>-->
                <!--<el-option-->
                <!--v-for="(v, k, i) in sizeUnit"-->
                <!--:value="k"-->
                <!--:label="k">-->
                <!--<span>{{k}}</span>-->
                <!--<span class="sel-option-hint">{{v}}</span>-->
                <!--</el-option>-->
                <!--</el-select>-->
                <!--</el-form-item>-->
                <!--</el-col>-->
              </el-row>
              <el-row>
                <el-col :span="12" :offset="6">
                  <span>上</span>
                  <el-form-item>
                    <el-input-number v-model="base.styles.margin[0]" :min="0" :max="500" size="mini" placeholder="上"
                                     clearable></el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <span>左</span>
                  <el-form-item>
                    <el-input-number v-model="base.styles.margin[3]" :min="0" :max="500" controls-position="right"
                                     size="mini" placeholder="左" clearable></el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <span>右</span>
                  <el-form-item>
                    <el-input-number v-model="base.styles.margin[1]" :min="0" :max="500" controls-position="right"
                                     size="mini" placeholder="右" clearable></el-input-number>
                  </el-form-item>
                </el-col>
                <el-col :span="12" :offset="6">
                  <span>下</span>
                  <el-form-item>
                    <el-input-number v-model="base.styles.margin[2]" :min="0" :max="500" size="mini" placeholder="下"
                                     clearable></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-aside>
        </el-container>
      </el-container>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button @click="baseShow = false" size="mini">取 消</el-button>
      <el-button @click="commitBase();" :loading="loading" type="primary" size="mini">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    name: "propEidt",
    props: {
      params: Object
    },
    data() {
      let sizeDefaultUnit = 'pt';
      return {
        loading: false,
        baseShow: false,
        base: {
          common: {},
          styles: {
            margin: [],
            padding: [],
            heightUnit: sizeDefaultUnit,
            marginUnit: sizeDefaultUnit,
            paddingUnit: sizeDefaultUnit
          }
        },
        sizeDefaultUnit: sizeDefaultUnit,
        sizeUnit: {
          'px': '像素',
          '%': '百分比',
          'ex': '字母x高度',
          'em': '元素字体高度',
          'cm': '厘米',
          'mm': '毫米',
          'in': '英寸',
          'pt': '点',
          'pc': '皮卡'
        },
        validRefs: ['common', 'height', 'margin', 'padding'],   //验证ref属性值
        validate: {
          name: [
            {required: true, message: '请填写楼层名称', trigger: 'blur'},
            {min: 2, max: 36, message: '长度在2 - 36之间', trigger: 'blur'}
          ],
          unit: [
            {
              validator: (rule, val, callback) => {
                let stl = this.base.styles;
                let h = Number(stl.height || 0) && !stl.heightUnit,
                  m = stl.margin.reduce((count, item) => count += Number(item || 0), 0) && !stl.marginUnit,
                  p = stl.padding.reduce((count, item) => count += Number(item || 0), 0) && !stl.paddingUnit; //计算总值是否 > 0
                if (h || m || p)
                  var e = new Error('请选择单位');
                return callback(e);
              }, trigger: 'blur'
            }
          ]
        }
      };
    },
    methods: {
      show() {
        this.baseShow = true;
        _.merge(this.base, this.params.base);
      },
      afterClose() {
        this.$emit('close');
        for (let i = 0, len = this.validRefs.length; i < len; i++)
          this.$refs[this.validRefs[i]].resetFields();
      },
      commitBase() {
        this.loading = true;
        let vlen = this.validRefs.length;
        let prs = new Array(vlen);
        for (let i = 0; i < vlen; i++)
          prs[i] = new Promise(res => this.$refs[this.validRefs[i]].validate(ok => res(ok)));
        Promise.all(prs.map(item => item.catch(e => e))).then(data => {
          for (let i = 0, len = data.length; i < len; i++)
            if (!data[i]) {
              this.loading = !this.loading;
              this.$message.error('验证失败！');
              return;
            }
          let base = _.cloneDeep(this.base);
          this.$emit('commit', base);
          this.baseShow = this.loading = !this.loading;
        });
      }
    }
  }
</script>

<style lang="scss" rel="stylesheet/scss" scoped>


  .el-dialog {
    .el-aside {
      padding: 0 5px;
      border-right: $border-base;
    }

    /*.el-col {
            margin-bottom: 5px;
        }*/
    .el-input-number {
      width: 100px;
    }

    .el-select {
      width: 70px;
    }
  }

  .sel-option-hint {
    float: right;
    color: #8492a6;
    font-size: 12px;
  }
</style>
