<template>
  <section class="tabbar">
    <div class="title">位置设置</div>
    <div class="position">
      <!-- <div class="text">
        <p>设置悬浮导航坐标</p>
        <span>说明：悬浮导航区域不可覆盖到店铺页头区域，可自定义上下位置及距内容区的距离</span>
      </div> -->
      <div class="padding">
        <div class="head">
          <p>距离浏览器底部距离</p>
          <div class="flex">
            <el-input v-model="content.headpx" placeholder="请输入内容"></el-input>
            <span>px</span>
          </div>
        </div>
        <div class="cont">
          <p>距离浏览器左侧距离</p>
          <div class="flex">
            <el-input v-model="content.contpx" placeholder="请输入内容"></el-input>
            <span>px</span>
          </div>
        </div>
      </div>
      <!-- <div class="demo">
        <p>预览示意图</p>
        <div class="bg">
          <div class="flcont">
            <div class="head">页头</div>
            <div class="cont"></div>
            <div class="float" style=""></div>
          </div>
        </div>
      </div>-->
    </div>

    <div class="title">模块有效时间设置</div>
    <div class="timevalue">
      <el-date-picker
        v-model="content.timevalue"
        type="datetimerange"
        :picker-options="pickerOptions"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        align="right"
      ></el-date-picker>
    </div>

    <div class="title">模块背景设置</div>
    <div class="background-img">
      <el-upload
        class="topic-pic-upload"
        ref="upload"
        accept="image/jpeg,image/jpg, image/png, image/gif"
        :max-size="1"
        :show-file-list="false"
        :before-upload="() => {loading = true; return true;}"
        :on-success="onUploadImage"
      >
        <el-button class="btn-block" type="primary" size="small" :loading="loading">上传背景图</el-button>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>
      <el-button
        class="btn-block"
        size="small"
        :loading="loading"
        @click="content.bgImage = ''"
      >清除背景图</el-button>
    </div>

    <div class="title">模块颜色设置</div>
    <div class="colors">
      <span>楼层区域默认颜色：</span>
      <el-color-picker v-model="content.floorDefaultColor" size="mini"></el-color-picker>
      <span>楼层区域激活颜色：</span>
      <el-color-picker v-model="content.floorActiveColor" size="mini"></el-color-picker>
      <span>默认字体颜色：</span>
      <el-color-picker v-model="content.color" size="mini"></el-color-picker>
      <span>激活字体颜色：</span>
      <el-color-picker v-model="content.hoverColor" size="mini"></el-color-picker>
    </div>

    <div class="title">添加导航入口</div>
    <div class="block">
      <el-button class="btn-block" type="primary" @click="add_editContent=true" size="small">添加快捷入口</el-button>
    </div>

    <el-dialog class="banner-dialog" title="添加导航入口" :visible.sync="add_editContent">
      <el-form ref="form" :model="editData" label-width="120px">
        <el-form-item label="菜单ICON：">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="() => {editImgLoading = true; return true;}"
            :on-success="uploadEditContImage"
          >
            <img v-if="editData.title" :src="editData.title" class="image" />
            <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="有效时间：">
          <el-date-picker
            v-model="editData.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="入口名称：">
          <el-input v-model="editData.entry" class="entry-name"></el-input>
        </el-form-item>
        <el-form-item label="文字颜色：">
          <el-color-picker v-model="editData.frontColor" size="mini" @change="onSelect('editing')"></el-color-picker>
        </el-form-item>
        <div class="linkOptions">
          <el-radio-group v-model="editData.linkType">
            <el-radio :label="'link'">链接</el-radio>
            <el-radio :label="'point'">锚点</el-radio>
            <!-- <el-radio :label="'topic'">店铺</el-radio> -->
            <el-radio :label="'stores'">商详</el-radio>
          </el-radio-group>
        </div>
      </el-form>

      <div class="topic-image-picker">
        <el-input placeholder="链接地址" v-model="editData.link.meta.page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="editData.linkType==='link'">
        <all-link
          @select="onSetLink"
          :tabs="[{label: '活动页', value: 'page'}]"
          :params="{branchCode: topic.branchCode, from: 'pc'}"
        ></all-link>
      </div>

      <div v-if="editData.linkType==='point'">
        <el-radio-group v-model="editData.link.meta.page_url" @change="handleChangeFloor">
          <el-radio-button
            v-for="count in coreLength"
            :key="count"
            :label="`#floor${count - 1}`"
          >楼层{{count}}</el-radio-button>
        </el-radio-group>
      </div>

      <!-- <div v-if="editData.linkType==='topic'">
        <control-page @select="onSetLink" :params="{branchCode: topic.branchCode}"></control-page>
      </div> -->

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditContent">取 消</el-button>
        <el-button size="small" type="primary" @click="add_confirmEdit">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog class="banner-dialog" title="编辑导航入口" :visible.sync="editContent">
      <el-form ref="form" :model="editData" label-width="120px">
        <el-form-item label="菜单ICON：">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg, image/png, image/gif"
            :show-file-list="false"
            :before-upload="() => {editImgLoading = true; return true;}"
            :on-success="uploadEditContImage"
          >
            <img v-if="editData.title" :src="editData.title" class="image" />
            <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="有效时间：">
          <el-date-picker
            v-model="editData.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="入口名称：">
          <el-input v-model="editData.entry" class="entry-name"></el-input>
        </el-form-item>
        <el-form-item label="文字颜色：">
          <el-color-picker v-model="editData.frontColor" size="mini" @change="onSelect('editing')"></el-color-picker>
        </el-form-item>
        <div class="linkOptions">
          <el-radio-group v-model="editData.linkType">
            <el-radio :label="'link'">链接</el-radio>
            <el-radio :label="'point'">锚点</el-radio>
            <!-- <el-radio :label="'topic'">店铺</el-radio> -->
            <el-radio :label="'stores'">商详</el-radio>
          </el-radio-group>
        </div>
      </el-form>

      <div class="topic-image-picker">
        <el-input placeholder="链接地址" v-model="editData.link.meta.page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>

      <div v-if="editData.linkType==='link'">
        <all-link
          @select="onSetLink"
          :tabs="[{label: '活动页', value: 'page'}]"
          :params="{branchCode: topic.branchCode, from: 'pc'}"
        ></all-link>
      </div>

      <div v-if="editData.linkType==='point'">
        <el-radio-group v-model="editData.link.meta.page_url" @change="handleChangeFloor">
          <el-radio-button
            v-for="count in coreLength"
            :key="count"
            :label="`#floor${count - 1}`"
          >楼层{{count}}</el-radio-button>
        </el-radio-group>
      </div>

      <!-- <div v-if="editData.linkType==='topic'">
        <control-page @select="onSetLink" :params="{branchCode: topic.branchCode}"></control-page>
      </div> -->

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditContent">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmEdit">确定</el-button>
      </div>
    </el-dialog>

    <el-table :data="content.list" size="mini" :row-key="getRowKeys" style="width: 100%">
      <el-table-column type="index" width="50" label="序号"></el-table-column>
      <el-table-column prop="title" label="图标">
        <template slot-scope="scope">
          <img v-if="scope.row.title" :src="scope.row.title" alt="图" class="title-image" />
          <i v-else class="el-icon-circle-plus-outline no-img"></i>
        </template>
      </el-table-column>

      <el-table-column prop="entry" label="展示状态">
        <template slot-scope="scope">
          <span v-html="format_text(scope.row.timevalue)"></span>
        </template>
      </el-table-column>

      <el-table-column prop="entry" label="入口名称">
        <template slot-scope="scope">
          <span v-html="scope.row.entry" class="entry-name" :style="{'color':scope.row.frontColor}"></span>
        </template>
      </el-table-column>

      <el-table-column prop="entry" label="链接" :show-overflow-tooltip="true" width="140">
        <template slot-scope="scope">
          <span>{{scope.row.link.meta.page_url}}</span>
          <!-- <el-input
            type="text"
            size="mini"
            v-model="scope.row.link.meta.page_url"
          >{{scope.row.link.meta.page_url}}</el-input> -->
        </template>
      </el-table-column>

      <!-- <el-table-column label="链接类型">
        <template slot-scope="scope">
          <span>{{scope.row.linkType==='stores'?'店铺链接':'专题页链接'}}</span>
        </template>
      </el-table-column>-->

      <el-table-column fixed="right" label="操作" width="160">
        <template slot-scope="scope">
          <el-button size="mini" @click="toDelete(scope.row, scope.$index)">删除</el-button>
          <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 连接地址选择 -->
    <!-- <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link> -->
  </section>
</template>
<script>
import base from "../base";
import api from "api";

export default {
  extends: base,
  contentDefault: {
    list: [
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/3faadb73-cd5a-4567-9176-bdb75a14e64c.png",
        entry: "高毛专区",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 1,
            page_url:
              "https://app-v4.ybm100.com/static/xyyvue/dist/#/highmarginnew?ybm_title=高毛专区"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/3faadb73-cd5a-4567-9176-bdb75a14e64c.png",
        entry: "高毛专区",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 1,
            page_url:
              "https://app-v4.ybm100.com/static/xyyvue/dist/#/highmarginnew?ybm_title=高毛专区"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/3faadb73-cd5a-4567-9176-bdb75a14e64c.png",
        entry: "高毛专区",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 1,
            page_url:
              "https://app-v4.ybm100.com/static/xyyvue/dist/#/highmarginnew?ybm_title=高毛专区"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/3faadb73-cd5a-4567-9176-bdb75a14e64c.png",
        entry: "高毛专区",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 1,
            page_url:
              "https://app-v4.ybm100.com/static/xyyvue/dist/#/highmarginnew?ybm_title=高毛专区"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/3faadb73-cd5a-4567-9176-bdb75a14e64c.png",
        entry: "高毛专区",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 1,
            page_url:
              "https://app-v4.ybm100.com/static/xyyvue/dist/#/highmarginnew?ybm_title=高毛专区"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/3faadb73-cd5a-4567-9176-bdb75a14e64c.png",
        entry: "高毛专区",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 1,
            page_url:
              "https://app-v4.ybm100.com/static/xyyvue/dist/#/highmarginnew?ybm_title=高毛专区"
          }
        }
      },
      {
        title:
          "http://upload.ybm100.com/ybm/app/layout/快捷入口/3faadb73-cd5a-4567-9176-bdb75a14e64c.png",
        entry: "高毛专区",
        frontColor: "#000000",
        operation: "1",
        timevalue: [
          new Date("2019-06-28T03:42:31.942Z"),
          new Date("2030-06-27T03:42:31.942Z")
        ],
        link: {
          meta: {
            id: 1,
            page_url:
              "https://app-v4.ybm100.com/static/xyyvue/dist/#/highmarginnew?ybm_title=高毛专区"
          }
        }
      }
    ],
    color: "#333333",
    hoverColor: "#333333",
    floorDefaultColor: "#F2f2f2",
    floorActiveColor: "#ffffff",
    bgImage: "",
    headpx: 0,
    contpx: 0,
    timevalue: ""
  },
  props: {
    pageTimeValue: Array,
    coreLength: Number
  },
  data() {
    return {
      add_editContent: false,
      editContent: false,
      editData: {
        linkType: "link",
        title: "",
        entry: "",
        frontColor: "#000000",
        operation: "",
        timevalue: "",
        link: {
          meta: {
            page_url: ""
          }
        }
      },
      editImgLoading: false,
      textArr: [],
      loading: false,
      selectionTable: [] //请选中的图标列表添加连接
    };
  },
  mounted() {
    if (this.pageTimeValue) {
      this.content.timevalue = this.content.timevalue || this.pageTimeValue;
    }
  },
  watch: {
    "pageTimeValue"(new_val) {
      if (new_val) {
        this.content.timevalue = new_val;
      }
    }
  },
  computed: {},
  methods: {
    pickerOptions: {
      shortcuts: [
        {
          text: "未来一周",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来一个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来三个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来六个月",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
            picker.$emit("pick", [start, end]);
          }
        },
        {
          text: "未来一年",
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
            picker.$emit("pick", [start, end]);
          }
        }
      ]
    },
    async uploadEditContImage(res, file) {
      this.editImgLoading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.editData.title = res.data.url;
    },
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.bgImage = res.data.url;
      // this.content.floorDefaultColor = "";
    },
    onSelect(val, isEditEntry) {
      if (isEditEntry == "editing") {
        if (val) {
          this.editData.frontColor = val;
        } else {
          this.editData.frontColor = "#000000";
        }
      } else {
        if (val) {
          this.content.bgRes = val;
        } else {
          this.content.bgRes = "#ffffff";
        }
      }
    },
    handleChangeFloor(value) {
      this.editData.link.meta.page_url = value;
    },
    onSetLink(link) {
      this.editData.link.meta.page_url = link.meta.page_url;
    },
    closeEditContent() {
      this.editContent = false;
      this.add_editContent = false;
      this.editData = {
        linkType: "link",
        title: "",
        entry: "",
        frontColor: "#000000",
        operation: "",
        timevalue: "",
        link: {
          meta: {
            page_url: ""
          }
        }
      };
    },
    async confirmEdit() {
      if (!this.editData.title) {
        this.$message.warning("请上传图片");
        return false;
      }

      if (!this.editData.entry) {
        this.$message.warning("请填写文字");
        return false;
      }

      if (!this.editData.link) {
        this.$message.warning("请填写链接");
        return false;
      }
      let linkErrMsg = '';
      if (this.editData.linkType === 'link' && this.editData.link.meta.page_url) {
        const result = await api.topic.checkPageUrl({ url: this.editData.link.meta.page_url });
        if (((result || {}).data || {}).status != 200) {
          linkErrMsg = '跳转链接不存在，请检查';
        }
      }
      if (linkErrMsg) {
        this.$message.error(linkErrMsg);
        return false;
      }
      if (this.isContEdit) {
        // entry: document.querySelector('.ql-editor').innerText,//只取文本
        this.psData = {
          title: this.editData.title,
          timevalue: this.editData.timevalue,
          entry: `${this.editData.entry}`,
          frontColor: this.editData.frontColor,
          link: this.editData.link,
          linkType: this.editData.linkType
        };
        this.content.list.splice(this.nowIndex, 1, this.psData);
      } else {
        this.content.list.push(Object.assign([], this.content.list));
      }
      this.closeEditContent();
    },
    async add_confirmEdit() {
      // if (!this.editData.title) {
      //   this.$message.warning("请上传图片");
      //   return false;
      // }

      if (!this.editData.entry) {
        this.$message.warning("请填写文字");
        return false;
      }

      if (!this.editData.link) {
        this.$message.warning("请填写链接");
        return false;
      }
      let linkErrMsg = '';
      if (this.editData.linkType === 'link' && this.editData.link.meta.page_url) {
        const result = await api.topic.checkPageUrl({ url: this.editData.link.meta.page_url });
        if (((result || {}).data || {}).status != 200) {
          linkErrMsg = '跳转链接不存在，请检查';
        }
      }
      if (linkErrMsg) {
        this.$message.error(linkErrMsg);
        return false;
      }
      this.psData = {
        title: this.editData.title,
        entry: `${this.editData.entry}`,
        frontColor: this.editData.frontColor,
        link: this.editData.link,
        timevalue: this.editData.timevalue,
        linkType: this.editData.linkType
      };
      this.content.list.push(this.psData);
      this.closeEditContent();
    },
    toDelete(data, index) {
      this.content.list.splice(index, 1);
    },
    toEdit(data, index, status) {
      if (status == "1") {
        //新建时清空
        this.editData.title = "";
        this.editData.timevalue = "";
        this.editData.entry = "";
        this.editData.frontColor = "#000";
        this.editData.link = {
          meta: {
            page_url: ""
          }
        };
      } else {
        this.editData = JSON.parse(JSON.stringify(data));
        // this.editData.entry = data.entry;
        // this.editData.timevalue = data.timevalue;
        // this.editData.title = data.title;
        // this.editData.frontColor = data.frontColor;
        // this.editData.link = data.link;
        // this.editData.linkType = data.linkType ? data.linkType : "topic";
      }
      this.editContent = true;
      this.nowData = data;
      this.nowIndex = index;
      this.isContEdit = true;
      this.addContPic = true;
    },
    format_text(data) {
      if (!data) {
        return "<b style='color: red'>请设置时间</b>";
      }
      const _date = new Date().getTime();
      const start = new Date(data[0]).getTime();
      const end = new Date(data[1]).getTime();
      if (_date <= end && _date >= start) {
        return "<b style='color: #13c2c2'>展示中</b>";
      } else if (_date < start) {
        return `<b style='color: #000000'>即将在${new Date(
          start
        ).toLocaleDateString()}展示</b>`;
      } else {
        return "<b style='color: #000000'>已过期</b>";
      }
    },
    handleSelectionChange(val) {
      this.selectionTable = val;
    },
    selectUrl(link) {
      this.selectionTable.map((item, array, index) => {
        let dataListIndex = this.dataList.indexOf(item);
        if (dataListIndex >= 0) {
          this.dataList[dataListIndex].link = link.meta.page_url;
        }
      });
    }
    // onSetLink(link) {
    //   this.selectUrl(link);
    //   this.$refs.multipleTable.clearSelection(); //清空选项
    //   this.content.list = this.dataList;
    // }
  }
  // watch: {
  //   "content.floorDefaultColor"(new_val) {
  //     if (new_val) {
  //       this.content.bgImage = "";
  //     }
  //   }
  // }
};
</script>

<style lang="scss" scoped rel="stylesheet/scss">
.tabbar {
  .position {
    // display: flex;
    padding: 15px 20px;
    .padding {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 20px;
      .head,
      .cont {
        max-width: 50%;
        span {
          padding-left: 15px;
        }
        .flex {
          display: flex;
        }
      }
    }
    p {
      padding-right: 15px;
      white-space: nowrap;
      font-weight: 600;
      margin-bottom: 15px;
    }
    .demo {
      padding: 15px 20px;
      padding-top: 25px;
      p {
        text-align: center;
        color: #999;
        margin: 0 0 15px 0;
        font-weight: normal;
      }
      .bg {
        height: 400px;
        background: #e8e8e8;
        padding: 30px 0;
        .flcont {
          position: relative;
          width: 330px;
          margin: auto;
        }
        .head {
          height: 50px;
          background: #f8f8f8;
          line-height: 50px;
          text-align: center;
        }
        .cont {
          margin-top: 10px;
          background: #f8f8f8;
          height: 340px;
        }
        .float {
          width: 40px;
          background: #f8f8f8;
          height: 100px;
          position: absolute;
        }
      }
    }
  }
  .title-image {
    width: 64px;
    height: 64px;
  }
  .banner-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }
    .image {
      width: 64px !important;
      height: 64px;
    }
  }
  .topic-image-upload {
    .image {
      display: block;
      width: 100%;
    }

    .uploader-icon {
      width: 200px;
      height: 200px;
      line-height: 200px;
      border: 1px solid $border-base;
      font-size: 50px;
    }
  }
  .title {
    text-align: left;
    line-height: 35px;
    background-color: #f2f2f2;
    padding-left: 15px;
  }
  .timevalue,
  .background-img {
    padding: 20px 0;
  }
  .timevalue {
    text-align: center;
  }
  .background-img {
    display: flex;
    padding-left: 15px;
    .btn-block:last-child {
      width: auto;
      margin-left: 60px;
    }
  }
  .colors,
  .block {
    display: flex;
    padding: 20px 0 20px 15px;
    align-items: center;
  }
  .btn-block {
    width: auto;
  }
}
.topic-image-upload {
  width: 45%;

  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 100%;
    height: 100%;
    line-height: 100%;
    border: 1px solid $border-base;
    font-size: 30px;
  }
}

.topic-image-picker {
  padding-top: 10px;
  padding-bottom: 10px;
}

.icon-text {
  white-space: nowrap !important;
  text-overflow: ellipsis;
}

.pic-select {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
</style>
