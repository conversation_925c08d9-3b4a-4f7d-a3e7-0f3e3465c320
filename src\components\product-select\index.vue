<template>
    <section>
        <el-table ref="multipleTable" v-loading="loading" :data="dataList" tooltip-effect="dark" style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55">
            </el-table-column>
            <el-table-column prop="imageUrl" label="图片" width="150%" align="center">
                <template slot-scope="scope">
                    <img :src="$options.filters.imgUrl(scope.row.imageUrl)" class="avatar good-images" alt="图片" />
                </template>
            </el-table-column>
            <el-table-column prop="mediumPackageTitle" label="包装" width="120" align="left">
            </el-table-column>
            <el-table-column prop="mediumPackageNum" label="数量" width="50" align="right">
            </el-table-column>
            <el-table-column prop="productUnit" label="单位" width="50" align="center">
            </el-table-column>
            <el-table-column prop="spec" label="规格" width="150%" align="center">
            </el-table-column>
            <el-table-column prop="retailPrice" label="售价(￥)" width="80" align="right">
            </el-table-column>
            <el-table-column prop="mediumPackageNum" label="数量" width="50" align="right">
            </el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="[pageSize]"
          :page-size="pageSize"
          prev-text="上一页"
          next-text="下一页"
          layout="prev,pager,next, jumper, ->,sizes,total"
          :total="pageSize*pageCount"
          :current-page.sync="pageNum"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
    </section>
</template>
<script>
import api from 'api';
import {AppWebsite} from 'config';

export default {
    name:'product-select',
    data(){
        return{
            dataList:[],
            loading:false,
            searchParam: {}
        }
    },
    created(){
        this.getData()
    },
    methods:{
        getData(){
            //获取商品数据
            this.loading=true;
            let pms={
                url: AppWebsite + 'app/sku/select',
                dataType: 'json',
                data: {
                    offset: this.pageNum,
                    limit: this.pageSize
                },
                head: {
                    terminalType: 1,
                    'Content-Type': 'application/json;charset=UTF-8'
                }
            };
            pms.data = Object.assign(pms.data, this.searchParam);
            // let res = await api.proxy.post(pms);
            // this.loading = false;
            // if (res.code != 1) {
            //     this.$message.error(res.msg);
            // } else {
            //     let page = res.result;
            //     this.$nextTick(() => {
            //         this.dataList = page.list;
            //         this.totalSize = page.total;
            //     });
            // }
        }
    }
}
</script>

