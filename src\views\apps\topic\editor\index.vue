<template>
  <div
    class="topic-editor"
    @click.self="selectedIndex = -1"
    v-loading="loading"
  >
    <div class="topic-editor-head">
      <el-form
        label-position="left"
        size="small"
        label-width="80px"
        label-suffix="："
      >
        <el-row :gutter="40">
          <el-col :span="6">
            <el-form-item label="页面配置名称" label-width="110px">
              <el-input
                v-model="core.page_name"
                placeholder="页面名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="开始结束时间" label-width="110px">
              <el-date-picker
                v-model="core.timevalue"
                type="datetimerange"
                :picker-options="pickerOptions2"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <!-- <el-form-item> -->
            <el-dropdown @command="confirmRollback($event, $route.params.id)">
              <el-button type="primary" size="small">
                <span>回滚</span>
                <i class="el-icon-caret-bottom el-icon-right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="v in getVersions"
                  :class="{ active: v.active }"
                  :command="v.mtime"
                  :key="v.mtime"
                >
                  <span>{{ v.mtime | datetime }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <!-- </el-form-item> -->
          </el-col>
          <el-col :span="2" style="margin-left: 20px">
            <el-button size="small" @click="goback2start">返回列表</el-button>
          </el-col>
          <el-col :span="2" style="margin-left: 20px">
            <el-button
              type="primary"
              size="small"
              @click="showOrHide"
              :icon="showAll ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
              >{{ showAll ? "收起" : "展开" }}</el-button
            >
          </el-col>
          <!-- https://wiki.int.ybm100.com/pages/viewpage.action?pageId=442816609 循环设置移到组件内部去配置-->
          <!-- <el-col :span="5">
            <el-form-item label="循环展示" v-if="this.core.sceneType == 7">
              <el-switch v-model="core.isLoopShow" active-text="启用" inactive-text="关闭" />
              <el-button :disabled="!core.isLoopShow" @click="settingTime" type="primary" size="mini">配置</el-button>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row :gutter="40">
          <el-col :span="7">
            <el-form-item label-width="110px" label="用户可见名称">
              <el-input
                style="width: 200px"
                v-model="core.page_title"
                placeholder="CMS页面"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="是否启用">
              <el-switch
                v-model="value3"
                active-text="启用"
                inactive-text="停用"
              ></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label-width="90px" label="页面背景色">
              <el-color-picker
                v-model="core.background"
                @change="onSelect('background', $event)"
              ></el-color-picker>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item
              label-width="90px"
              label="标题背景色"
              :style="{
                visibility: core.page_type == 'h5' ? 'visible' : 'hidden',
              }"
            >
              <el-color-picker
                v-model="core.titleBackground"
                @change="onSelect('titleBackground', $event)"
              ></el-color-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item
              label="标题颜色"
              :style="{ display: core.page_type == 'h5' ? 'block' : 'none' }"
            >
              <el-select style="width: 100px" v-model="core.titleColor">
                <el-option label="黑色" value="#000000" />
                <el-option label="白色" value="#ffffff" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :span="8"
            v-if="
              core.page_type === 'diaNewLog' ||
              core.page_type === 'exhibitionPosition' ||
              core.page_type === 'new_home'||core.page_type==='tab'
            "
          >
            <el-form-item label="人群范围">
              <el-tag>{{ core.crowdId || "全部人群" }}</el-tag>
              {{ core.crowdId ? (core.crowdValue || showCrowdValue) : '' }}
              <el-button
                v-if="core.page_type === 'diaNewLog' || core.page_type === 'new_home'||core.page_type==='tab'"
                size="small"
                type="primary"
                @click="changeCrowdVis = true"
                >配置</el-button>
              <div class="merchatId-div" v-if="core.page_type === 'new_home' ||core.page_type==='tab'">
                merchantId:
                <el-input v-model="merchantId"></el-input>
              </div>
              <el-button
                v-if="core.page_type === 'new_home' ||core.page_type==='tab'"
                size="small"
                type="primary"
                @click="preview(2)"
                >按筛选结果预览</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="40" v-show="showAll">
          <el-col
            :span="5"
            v-if="core.page_type == 'h5' || core.page_type == 'newControlMall'"
          >
            <el-form-item label="是否展示搜索框" label-width="120px">
              <el-switch
                v-model="core.isShowSearch"
                active-text="展示"
                inactive-text="隐藏"
              ></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="core.isShowSearch">
            <el-form-item label="搜索范围">
              <el-select v-model="core.searchRange" placeholder="请选择">
                <el-option label="平台" :value="1">平台</el-option>
                <el-option label="专区" :value="2">专区</el-option>
                <el-option label="专区及以外" :value="3">专区及以外</el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="core.isShowSearch && core.searchRange == 3">
            <el-input
              size="small"
              v-model="core.exhibitionIdConnect"
              placeholder="请输入需关联的商品组ID"
            />
          </el-col>
        </el-row>

        <el-row :gutter="40" v-show="showAll">
          <el-col :span="5" v-if="core.page_type == 'h5'">
            <el-form-item label="是否支持分享" label-width="120px">
              <el-switch
                v-model="core.isShowShare"
                active-text="展示"
                inactive-text="隐藏"
              ></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="core.isShowShare">
            <el-form-item label="分享链接标题" label-width="110px">
              <el-input
                size="small"
                v-model="core.pageShareTitle"
                placeholder="请输入"
                style="width: 170px"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="core.isShowShare" style="width: 26%">
            <el-form-item label="分享链接摘要" label-width="110px">
              <el-input
                size="small"
                v-model="core.pageShareDesc"
                placeholder="请输入"
                style="width: 200px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="core.isShowShare">
            <!-- <img v-if="core.pageShareImg && core.isShowShare" :src="core.pageShareImg" alt="" /> -->
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :max-size="1"
              :file-list="core.sharePageList"
              :on-remove="handleRemoveShareImg"
              :before-upload="handleBeforeUpload"
              :on-success="onUploadImg"
              list-type="picture"
            >
              <el-button type="primary" size="small">上传缩略图</el-button>
              <div slot="tip" class="el-upload__tip">
                图片文件大小上限：500kb，标准尺寸比例：60x60 px
              </div>
            </el-upload>
          </el-col>
        </el-row>

        <el-row :gutter="40" v-show="showAll">
          <el-col
            :span="5"
            v-if="
              core.page_type == 'h5' &&
              core.categoryList &&
              core.categoryList[0] &&
              core.categoryList[0].title &&
              core.categoryList[0].title.includes('综合页')
            "
          >
            <el-form-item label="活动规则" label-width="120px">
              <el-switch
                v-model="core.activityRules"
                active-text="展示"
                inactive-text="隐藏"
              ></el-switch>
              <el-button
                v-if="core.activityRules"
                type="primary"
                size="small"
                @click="isShowActivityRules = true"
                >配置</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>

        <el-dialog
          @closed="isShowActivityRules = false"
          :visible.sync="isShowActivityRules"
          width="60%"
          title="活动规则配置"
          append-to-body
          center
        >
          <div style="display: flex; align-items: center">
            入口名称
            <el-input
              style="width: 200px; margin-left: 10px"
              v-model="core.activityRulesInfo.entryName"
              size="mini"
              maxlength="5"
              placeholder="请输入入口名称"
            />
          </div>
          <div style="display: flex; align-items: center; margin-top: 20px">
            标题名称
            <el-input
              style="width: 200px; margin-left: 10px"
              v-model="core.activityRulesInfo.titleName"
              size="mini"
              maxlength="10"
              placeholder="请输入标题名称"
            />
          </div>
          <div v-if="isShowActivityRules" style="margin-top: 10px">
            <editor
              id="tinymce"
              v-model="core.activityRulesInfo.tinymceHtml"
              :init="einit"
            >
            </editor>
          </div>
          <div>注：最多配置1000个字符。</div>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="isShowActivityRules = false"
              >确定</el-button
            >
          </span>
        </el-dialog>

        <el-row :gutter="40" v-show="showAll">
          <el-col
            :span="5"
            v-if="
              core.page_type == 'h5' &&
              core.categoryList &&
              core.categoryList[0] &&
              core.categoryList[0].title &&
              core.categoryList[0].title.includes('综合页')
            "
          >
            <el-form-item label="活动引导" label-width="120px">
              <el-switch
                v-model="core.activityGuide"
                active-text="展示"
                inactive-text="隐藏"
              ></el-switch>
              <el-button
                v-if="core.activityGuide"
                type="primary"
                size="small"
                @click="handleSetActivity"
                >配置</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>

        <el-dialog
          @closed="isShowActivityGuide = false"
          :visible.sync="isShowActivityGuide"
          width="60%"
          title="活动引导配置"
          append-to-body
          center
        >
          <el-tabs
            v-model="core.activeName"
            type="card"
            @tab-click="handleChangeTab"
          >
            <el-tab-pane label="活动信息引导" name="activity">
              <el-table
                v-if="core.activeName === 'activity'"
                :data="core.associatedActivitiesList"
                size="mini"
                class="tableBox"
                style="margin: 0 0 20px"
                ref="tableBox"
                :row-key="(row) => row.id"
              >
                <el-table-column label="活动类型">
                  <template slot-scope="scope">
                    <div>
                      <el-select
                        size="small"
                        v-model="scope.row.activityType"
                        placeholder="请选择类型"
                        default-first-option
                        filterable
                      >
                        <el-option
                          v-for="(item, index) in activityTypeOptions"
                          :key="index"
                          :label="item.name"
                          :value="item.scene"
                        ></el-option>
                      </el-select>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="活动ID" width="300">
                  <template slot-scope="scope">
                    <div>
                      <el-select
                        style="width: 70%; display: inline-block"
                        class="inline-input"
                        size="mini"
                        v-model="scope.row.activityValue"
                        placeholder="请输入活动id"
                        filterable
                        :filter-method="querySearchActivityId"
                        clearable
                        @clear="handleClearActivity(scope.row, $event)"
                        @change="handleSelectAcitvity(scope.row, $event)"
                        @focus="changeActivityValue(scope.row, $event)"
                        :loading="selectLoading"
                      >
                        <el-option
                          v-for="item in activityOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                      <el-button
                        @click="handelSureBind(scope.$index)"
                        type="primary"
                        size="mini"
                        >确定</el-button
                      >
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="活动名称">
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.activityName }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <div>
                      <el-button
                        @click="handelAddActivity(scope.$index)"
                        type="primary"
                        size="mini"
                        >添加</el-button
                      >
                      <el-button
                        v-if="scope.$index > 0"
                        @click="handleDeleteActivity(scope.$index)"
                        type="danger"
                        size="mini"
                        >删除</el-button
                      >
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>

            <el-tab-pane label="浮窗活动" name="floatingWindow">
              <el-table
                v-if="core.activeName === 'floatingWindow'"
                :data="core.associatedFloatingWindowList"
                size="mini"
                class="tableBox"
                style="margin: 0 0 20px"
                ref="tableBox"
                :row-key="(row) => row.id"
              >
                <el-table-column label="活动类型">
                  <template slot-scope="scope">
                    <div>
                      <el-select
                        size="small"
                        v-model="scope.row.activityType"
                        placeholder="请选择类型"
                        default-first-option
                        filterable
                      >
                        <el-option
                          v-for="(item, index) in floatingWindowTypeOptions"
                          :key="index"
                          :label="item.name"
                          :value="item.scene"
                        ></el-option>
                      </el-select>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="活动ID" width="300">
                  <template slot-scope="scope">
                    <div>
                      <el-select
                        style="width: 70%; display: inline-block"
                        class="inline-input"
                        size="mini"
                        v-model="scope.row.activityValue"
                        placeholder="请输入活动id"
                        filterable
                        :filter-method="querySearchActivityId"
                        clearable
                        @clear="handleClearActivity(scope.row, $event)"
                        @change="handleSelectAcitvity(scope.row, $event)"
                        @focus="changeActivityValue(scope.row, $event)"
                        :loading="selectLoading"
                      >
                        <el-option
                          v-for="item in floatingWindowOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                      <el-button
                        @click="handelSureBind(scope.$index)"
                        type="primary"
                        size="mini"
                        >确定</el-button
                      >
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="活动名称">
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.activityName }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <div>
                      <el-button
                        @click="handelAddActivity(scope.$index)"
                        type="primary"
                        size="mini"
                        >添加</el-button
                      >
                      <el-button
                        v-if="scope.$index > 0"
                        @click="handleDeleteActivity(scope.$index)"
                        type="danger"
                        size="mini"
                        >删除</el-button
                      >
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="isShowActivityGuide = false"
              >确定</el-button
            >
          </span>
        </el-dialog>

        <el-row v-if="core.page_type === 'diaNewLog'" v-show="showAll">
          <el-col :span="4">
            <el-form-item label="优先级">
              <el-input-number
                v-model="core.priority"
                :precision="0"
                :min="1"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-dialog :visible.sync="dialogVisible" width="30%">
      <img width="100%" :src="qrCode" alt />
    </el-dialog>

    <div class="topic-editor-main" @click.self="selectedIndex = -1">
      <list-panel
        class="topic-editor-list"
        ref="list"
        :core="core"
        @drag="onPickupComponent"
        @click.native.self="selectedIndex = -1"
      ></list-panel>

      <view-panel
        class="topic-editor-view"
        ref="view"
        :core="core"
        :dragging="dragging"
        :selectedIndex="selectedIndex"
        @select="(index) => (selectedIndex = index)"
        @drop="onDropComponent"
        @custom="onCustomCell"
        @click.native.self="selectedIndex = -1"
      >
        {{ core.page_title || "页面" }}
      </view-panel>

      <prop-panel
        class="topic-editor-prop"
        ref="prop"
        :core="core"
        :key="selectedCell ? selectedCell.instance_id : 'page'"
        :cell="selectedCell"
        :topic="topicBase"
        :categoryList="core.categoryList"
        :index="selectedIndex"
        :isNewLayout="core.isNewLayout"
        :page_type="core.page_type"
        :coreLength="core.layout && core.layout.length"
        @remove="onRemoveCell"
        @moveup="onMoveupCell"
        @movedown="onMovedownCell"
        @update-common="onUpdateCellCommon"
        @update-content="onUpdateCellContent"
        @click.native.self="selectedIndex = -1"
      >
        <div class="topic-editor-prop-page" slot="page">
          <div class="topic-editor-prop-page-buttons">
            <el-button
              size="small"
              type="primary"
              @click="save()"
              :loading="saving"
              >保存</el-button
            >
            <el-button size="small" @click="preview()">预览链接</el-button>
            <el-button
              size="small"
              type="danger"
              @click="publish()"
              :loading="publishing"
              >发布</el-button
            >
            <el-button size="small" @click="onlineView()">正式链接</el-button>
          </div>
        </div>
      </prop-panel>
    </div>

    <el-dialog title="人群范围配置" :visible.sync="changeCrowdVis" width="60%">
      <el-form ref="form" label-width="120px">
        <el-form-item label="人群范围：">
          <el-radio-group v-model="crowdType" @change="changeCrowdType">
            <el-radio :label="1">全部人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="crowdType === 2" label="指定人群：">
          <el-select
            v-model="crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <!-- <el-autocomplete
            style="width: 100%"
            class="inline-input"
            v-model="crowdValue"
            :fetch-suggestions="querySearchCrowd"
            placeholder="请输入人群id"
            :trigger-on-focus="false"
            @select="handleSelectCrowd"
            @input="changeCrowdValue"
          ></el-autocomplete> -->
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="changeCrowdVis = false">取 消</el-button>
        <el-button type="primary" @click="handleChangeCrow">确 定</el-button>
      </span>
    </el-dialog>
    <!-- <loop-by-week
      v-if="showTimeVis"
      :core="core"
      @cancelModal="cancelModal"
      @confirmSetLoopTime="confirmSetLoopTime"
    >
    </loop-by-week> -->

    <!--<itemgroup-modal ref="itemgroupModal" @select-itemgroup="onSelectItemgroup"></itemgroup-modal>-->
  </div>
</template>

<script>
import ShareIconDefault from "./share-icon.png";
import ListPanel from "./list-panel";
import ViewPanel from "./view-panel";
import PropPanel from "./prop-panel";
import api from "api";
import topic from "api/topic";
import { API_SERVER } from "config";
import { AppWebsite } from "config";

import tinymce from "tinymce/tinymce";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/skins/ui/oxide/skin.min.css"; //富文本样式
import "tinymce/icons/default"; //富文本样式
import "tinymce/plugins/wordcount"; // 字数统计插件
import "./tinymcePlugins/ax_wordlimit/plugin.min.js"; // 字数限制
// 配置富文本
import "tinymce/themes/silver/theme.min.js"; //引入富文本的主要脚本

export default {
  name: "EditorIndex",
  data() {
    return {
      merchantId: '',
      showCrowdValue: '',
      einit: {
        ax_wordlimit_num: 1001,
        ax_wordlimit_callback: (editor, txt, num) => {
          setTimeout(() => {
            this.$message.warning("最多配置1000个字符");
            window.tinymce.get("tinymce").setContent(txt.substring(0, 1000));
          }, 500);
        },
        ax_wordlimit_delay: 500,
        language: "zh_CN",
        height: 300, //编辑器高度
        branding: false, //是否禁用“Powered by TinyMCE”
        menubar: true, //顶部菜单栏显示,
        plugins: [
          "a11ychecker",
          "advlist",
          "advcode",
          "advtable",
          "autolink",
          "checklist",
          "export",
          "lists",
          "link",
          "image",
          "charmap",
          "preview",
          "anchor",
          "searchreplace",
          "visualblocks",
          "powerpaste",
          "fullscreen",
          "formatpainter",
          "insertdatetime",
          "media",
          "table",
          "help",
          "wordcount",
          "ax_wordlimit",
        ],
        toolbar:
          "undo redo | casechange blocks | bold italic backcolor | \
          alignleft aligncenter alignright alignjustify | \
          bullist numlst checklist outdent indent | removeformat | a11ycheck code table help",
      },
      showAll: true, // 顶部展示全部
      isShowActivityGuide: false,
      isShowActivityRules: false,
      crowdId: "",
      crowdValue: "",
      crowdType: 0,
      changeCrowdVis: false,
      qrCode: "",
      loading: true,
      dialogVisible: false,
      page: "",
      core: {
        activityGuide: false,
        activityRules: false,
        activityRulesInfo: {
          entryName: "",
          titleName: "",
          tinymceHtml: "",
        },
      },
      dragging: false,
      selectedIndex: -1,
      selectedComponent: null,
      saving: false,
      publishing: false,
      value3: true,
      isLoopShow: false,
      branchs: null,
      selectLoading: false,
      options: [],
      activityTypeOptions: [],
      activityOptions: [],
      floatingWindowTypeOptions: [],
      floatingWindowOptions: [],
      currentActivity: null,
      activityBase: {
        activityType: "",
        curActivityId: "",
        activityId: "",
        activityValue: "",
        activityName: "",
        isSure: false,
      },
      coreInit: {
        activityGuide: false,
        activityRules: false,
        activityRulesInfo: {
          entryName: "",
          titleName: "",
          tinymceHtml: "",
        },
        page_name: "",
        page_title: "",
        background: "#fff",
        titleBackground: "#ffffff",
        titleColor: "#000000",
        layout: [],
        searchRange: 1,
        sharePageList: [],
        activeName: "activity",
        associatedActivitiesList: [
          {
            activityType: "",
            curActivityId: "",
            activityId: "",
            activityValue: "",
            activityName: "",
            isSure: false,
          },
        ],
        associatedFloatingWindowList: [
          {
            activityType: "",
            curActivityId: "",
            activityId: "",
            activityValue: "",
            activityName: "",
            isSure: false,
          },
        ],
      },
      showTimeVis: false,
      pickerOptions2: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  computed: {
    getVersions() {
      let tempVer = (this.core || {}).versions || [];
      tempVer = [...tempVer].reverse();
      return tempVer;
    },
    dialogImageUrl() {
      return _.get(this.core, "previewImg");
    },
    selectedCell() {
      return _.get(this.core, "layout." + this.selectedIndex);
    },
    topicBase() {
      return _.pick(this.core, [
        "_id",
        "id",
        "page_id",
        "page_type",
        "page_name",
        "page_title",
        "category",
        "branchCode",
        "url",
        "startDate",
        "endDate",
        "state",
        "create_time",
      ]);
    },
    shareIcon: {
      get() {
        return this.core.share_icon || ShareIconDefault;
      },
      set(value) {
        this.core.share_icon = value;
      },
    },
  },
  components: {
    listPanel: ListPanel,
    viewPanel: ViewPanel,
    propPanel: PropPanel,
    Editor,
    //ItemgroupModal
  },
  watch: {
    async ["$route"]() {
      //var loading = Loading.service()
      this.core = _.merge({}, this.coreInit);
      try {
        await this.getCore();
      } catch (e) {}
      //loading.close()
    },
  },
  methods: {
    // 展开/收起
    showOrHide() {
      this.showAll = !this.showAll;
    },
    removeHTMLTag(str) {
      str = str.replace(/<\/?[^>]*>/g, "");
      str = str.replace(/[ | ]*\n/g, "\n");
      str = str.replace(/&nbsp;/gi, "");
      str = str.replace(/\s/g, "");
      return str;
    },
    handleChangeTab() {
      this.getActivityTypeEnum();
    },
    handleSetActivity() {
      this.isShowActivityGuide = true;
      this.getActivityTypeEnum();
    },
    async getActivityTypeEnum() {
      const res = await api.topic.getActivityTypeEnum({
        scenes: this.core.activeName === "activity" ? "1" : "2",
      });
      if (res.data && res.data.success) {
        if (this.core.activeName === "activity") {
          this.activityTypeOptions = res.data.data.types || [];
          const def = this.activityTypeOptions[0];
          if (def) {
            this.core.associatedActivitiesList.forEach((item) => {
              if (item.activityType === "") {
                item.activityType = def.scene;
              }
            });
          }
        } else {
          this.floatingWindowTypeOptions = res.data.data.types || [];
          const def = this.floatingWindowTypeOptions[0];
          if (def) {
            this.core.associatedFloatingWindowList.forEach((item) => {
              if (item.activityType === "") {
                item.activityType = def.scene;
              }
            });
          }
        }
      }
    },
    handleDeleteActivity(index) {
      this.$confirm("确认删除此条数据吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (this.core.activeName === "activity") {
            this.core.associatedActivitiesList.splice(index, 1);
          } else {
            this.core.associatedFloatingWindowList.splice(index, 1);
          }
        })
        .catch(() => {});
    },
    handelAddActivity(index) {
      // if (this.core.associatedActivitiesList.length === 10) {
      //     this.$message.warning('最多只能添加10个楼层');
      //     return false;
      // }
      if (this.core.activeName === "activity") {
        this.core.associatedActivitiesList.splice(index + 1, 0, {
          ...this.activityBase,
        });
      } else {
        this.core.associatedFloatingWindowList.splice(index + 1, 0, {
          ...this.activityBase,
        });
      }
    },
    handleClearActivity(row) {
      row.activityId = "";
      row.activityName = "";
      row.activityValue = "";
      row.curActivityId = "";
      if (this.core.activeName === "activity") {
        this.activityOptions = [];
      } else {
        this.floatingWindowOptions = [];
      }
    },
    handelSureBind(index) {
      if (this.core.activeName === "activity") {
        this.core.associatedActivitiesList[index].isSure = true;
        this.core.associatedActivitiesList[index].activityId =
          this.core.associatedActivitiesList[index].curActivityId;
      } else {
        this.core.associatedFloatingWindowList[index].isSure = true;
        this.core.associatedFloatingWindowList[index].activityId =
          this.core.associatedFloatingWindowList[index].curActivityId;
      }
      this.$message.success("绑定成功");
    },
    handleSelectAcitvity(row, e) {
      if (e) {
        if (this.core.activeName === "activity") {
          row.activityName = this.activityOptions[0].label;
          row.curActivityId = this.activityOptions[0].value;
          row.activityValue = this.activityOptions[0].value;
          this.activityOptions[0].label = this.activityOptions[0].value;
          setTimeout(() => {
            this.activityOptions = [];
          });
        } else {
          row.activityName = this.floatingWindowOptions[0].label;
          row.curActivityId = this.floatingWindowOptions[0].value;
          row.activityValue = this.floatingWindowOptions[0].value;
          this.floatingWindowOptions[0].label =
            this.floatingWindowOptions[0].value;
          setTimeout(() => {
            this.floatingWindowOptions = [];
          });
        }
      }
    },
    changeActivityValue(row) {
      this.currentActivity = row;
    },
    async querySearchActivityId(queryString) {
      this.selectLoading = true;
      const pms = {
        scene: (this.currentActivity || {}).activityType || "",
        id: queryString,
      };
      const res = await api.topic.getActivityInfos(pms);
      const { data } = res;
      this.selectLoading = false;
      if (data.success) {
        if (
          Object.keys(data.data).length &&
          Object.keys(data.data.activity).length
        ) {
          if (this.core.activeName === "activity") {
            this.activityOptions = [
              {
                label: data.data.activity.name || "",
                value: queryString,
              },
            ];
          } else {
            this.floatingWindowOptions = [
              {
                label: data.data.activity.name || "",
                value: queryString,
              },
            ];
          }
        } else {
          if (this.core.activeName === "activity") {
            this.activityOptions = [];
          } else {
            this.floatingWindowOptions = [];
          }
        }
      }
    },
    handleChangeCrow() {
      if (this.crowdType === 1) {
        this.core.crowdId = "";
        // this.core.crowdValue = "全部人群";
        this.changeCrowdVis = false;
        return false;
      }
      if (this.crowdType === 2 && !this.crowdId) {
        this.$message.error("请选择正确的人群");
        return;
      }
      this.changeCrowdVis = false;
      this.core.crowdId = this.crowdId;
      this.core.crowdValue = this.crowdValue;
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    selectCrowd(e) {
      if (e) {
        this.crowdId = Number(this.options[0].value.trim());
        this.crowdValue = this.options[0].label;
      } else {
        this.crowdId = "";
        this.crowdValue = "";
      }
      this.$forceUpdate();
    },
    // changeCrowdValue(e) {
    //   if (!e) {
    //     this.crowdId = '';
    //   }
    //   this.$forceUpdate();
    // },
    // handleSelectCrowd(item) {
    //   this.crowdId = item.id;
    // },
    // async querySearchCrowd(queryString, cb) {
    //   const pms = {
    //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
    //     dataType: "json",
    //     data: {},
    //     head: {
    //       "Content-Type": "application/json;charset=UTF-8"
    //     }
    //   };
    //   const res = await api.proxy.post(pms);
    //   if (res.success) {
    //     const { data } = res;
    //     cb([{
    //       id: queryString,
    //       value: data.name || ""
    //     }]);
    //     return false;
    //   }
    // },
    changeCrowdType() {
      this.crowdId = "";
      this.crowdValue = "";
    },
    async getCrowdValue(val) {
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.code == 1000) {
        this.showCrowdValue = res.data.name;
        this.core.crowdValue = res.data.name;
      }
    },
    logInsert(params) {
      topic.logInsert(params).then((res) => {});
    },
    onSelect(type, val) {
      console.log(type, val);
      this.core[type] = this.toColor16(val);
      if (val) {
        this.core[type] = this.toColor16(val);
      } else {
        this.core[type] = this.toColor16("#ffffff");
      }
    },
    handleRemoveShareImg() {
      this.core.sharePageList = [];
      this.core.pageShareImg = "";
    },
    async handleBeforeUpload(file) {
      const isLt500K = file.size / 1024 < 500;
      const isSize = await new Promise(function (resolve, reject) {
        let width = 60; // 限制图片尺寸为60X60
        let height = 60;
        let _URL = window.URL || window.webkitURL;
        let img = new Image();
        img.onload = function () {
          let valid = img.width === width && img.height === height;
          valid ? resolve() : reject();
        };
        img.src = _URL.createObjectURL(file);
      })
        .then(() => {
          return file;
        })
        .catch(() => {
          return false;
        });
      if (!isSize) {
        this.$message.error("图片尺寸限制为60x60");
      }
      if (!isLt500K) {
        this.$message({
          type: "warning",
          message: "附件大小超限，文件不能超过 500Kb",
        });
        return false;
      }
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.core.pageShareImg = res.data.url;
      this.core.sharePageList = [{ name: "", url: res.data.url }];
    },
    toColor16(str) {
      if (/^(rgb|RGB)/.test(str)) {
        var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
        var strHex = "#";
        for (var i = 0; i < aColor.length; i++) {
          var hex = Number(aColor[i]).toString(16);
          if (hex === "0") {
            hex += hex;
          }
          strHex += hex;
        }
        if (strHex.length !== 7) {
          strHex = str;
        }
        return strHex.toUpperCase();
      } else {
        return str;
      }
    },
    // 布局系列
    onPickupComponent(component) {
      this.selectedComponent = component;
      this.dragging = true;
      this.selectedIndex = -1;
    },
    onDropComponent(index) {
      if (index !== null && index >= 0) {
        this.core.layout.splice(
          index,
          0,
          this.$refs.prop.createCell(this.selectedComponent)
        );
      }
      this.selectedComponent = null;
      this.dragging = false;
    },
    onRemoveCell() {
      this.core.layout.splice(this.selectedIndex, 1);
      this.selectedIndex = -1;
    },
    onMoveupCell() {
      if (this.selectedIndex <= 0) return;
      var target = this.core.layout[this.selectedIndex];
      var destination = this.core.layout[this.selectedIndex - 1];
      this.core.layout.splice(this.selectedIndex - 1, 2, target, destination);
      this.selectedIndex--;
    },
    onMovedownCell() {
      if (
        this.selectedIndex === -1 &&
        this.selectedIndex >= this.core.layout.length - 1
      )
        return;
      var target = this.core.layout[this.selectedIndex];
      var destination = this.core.layout[this.selectedIndex + 1];
      this.core.layout.splice(this.selectedIndex, 2, destination, target);
      this.selectedIndex++;
    },
    // 格子系列
    onUpdateCellContent(content) {
      // 配置面板组件通知本组件更新配置数据，-> 触发deep watch core 回调 -> 传递新的core给子页面
      this.selectedCell.content = content;
      this.$refs.view.coreUpdate();
    },
    onUpdateCellCommon(common) {
      this.selectedCell.common = common;
      this.$refs.view.coreUpdate();
    },
    onCustomCell(data) {
      this.$refs.prop.custom(data);
    },
    getValueByName(list, name) {
      if (list && list.length) {
        return list.some((item) => {
          return item.name === name;
        });
      }
    },
    setComplexComponent(categoryList) {
      let hasComplex = false;
      let index = 0;
      categoryList.map((item, index) => {
        if (item.title === "综合页（主会场，广告位，商品流）") {
          hasComplex = true;
          index = index;
        }
      });
      if (!hasComplex) return false;
      let hasNewBanner, hasBottomFastEntry, hasRecommendFlow;
      const list = categoryList[index].children;
      hasNewBanner = this.getValueByName(list, "control_newBanner");
      hasBottomFastEntry = this.getValueByName(list, "bottomFastEntry");
      hasRecommendFlow = this.getValueByName(list, "recommend_flow");
      if (!hasNewBanner) {
        list.unshift({
          name: "control_newBanner",
          title: "轮播",
        });
      }
      if (!hasBottomFastEntry) {
        list.push({
          name: "bottomFastEntry",
          title: "底部快捷入口",
        });
      }
      if (!hasRecommendFlow) {
        list.push({
          name: "recommend_flow",
          title: "推荐商品流",
        });
      }
    },
    async getCore() {
      this.loading = true;
      const result = await api.topic.get(this.$route.params.id);
      this.loading = false;
      if (result.code === 200) {
        this.core = _.merge({}, this.coreInit, result.data);
        this.crowdType = this.core.crowdId ? 2 : 1;
        this.crowdValue = this.core.crowdValue || this.core.crowdId || "";
        this.crowdId = this.core.crowdId || "";
        if (this.core.crowdId && !this.core.crowdValue) {
          this.getCrowdValue(this.core.crowdId);
        }
        const { layout } = this.core;
        const { categoryList } = this.core;
        this.setComplexComponent(categoryList);
        let hasNewShopping = false;
        let hasNewSteamer = false;
        let hasHotZoneStreamer = false;
        let hasPopularQualityShops = false;
        this.streamers = [];
        if (layout && layout.length) {
          layout.map((item, index) => {
            if (item.name === "newShoppingGuide") {
              hasNewShopping = true;
            }
            if (item.name === "newStreamer") {
              hasNewSteamer = true;
            }
            if (item.name == "hotZoneStreamer") {
              hasHotZoneStreamer = true;
            }
            if (item.name === "streamer") {
              this.streamers.push(index);
            }
            if (item.name === "popularQualityShops") {
              hasPopularQualityShops = true;
            }
          });
        }
        if (
          !hasNewShopping &&
          this.core.isNewLayout &&
          this.core.isNewLayout === 1
        ) {
          layout.splice(4, 0, {
            instance_id: "1597594394362567",
            title: "新导购模块",
            name: "newShoppingGuide",
            styles: {
              height: "205pt",
              margin: "10pt 10pt 10pt 10pt",
              padding: "0pt 0pt 0pt 0pt",
            },
            common: {
              name: "新导购模块",
            },
            content: {
              modeType: 6,
              timevalue: [],
              module_list: [
                {
                  groupId: "",
                  specifyGoodIdList: [],
                  showGoodType: 1,
                  linkType: "",
                  module_type: "",
                  module_title: "",
                  dynamicId: "",
                  jump_url: "",
                  describe: {
                    text: "",
                  },
                },
                {
                  groupId: "",
                  specifyGoodIdList: [],
                  showGoodType: 1,
                  linkType: "",
                  module_type: "",
                  module_title: "",
                  dynamicId: "",
                  jump_url: "",
                  describe: {
                    text: "",
                  },
                },
                {
                  groupId: "",
                  specifyGoodIdList: [],
                  showGoodType: 1,
                  linkType: "",
                  module_type: "",
                  module_title: "",
                  dynamicId: "",
                  jump_url: "",
                  describe: {
                    text: "",
                  },
                },
                {
                  groupId: "",
                  specifyGoodIdList: [],
                  showGoodType: 1,
                  linkType: "",
                  module_type: "",
                  module_title: "",
                  dynamicId: "",
                  jump_url: "",
                  describe: {
                    text: "",
                  },
                },
                {
                  module_type: "GaoMao",
                  module_title: "高毛专区",
                  describe: {
                    text: "毛利高 赚得多",
                  },
                },
                {
                  module_type: "NewSku",
                  module_title: "新品首推",
                  describe: {
                    text: "天天焕新 发现好药",
                  },
                },
              ],
            },
          });
        }
        if (
          this.streamers.length &&
          this.core.isNewLayout &&
          this.core.isNewLayout === 1
        ) {
          this.streamers.map((index) => {
            if (layout[index].content && layout[index].content.list) {
              layout[index].common.name = "新横幅广告";
              layout[index].title = "新横幅广告";
              layout[index].name = "newStreamer";
            } else {
              layout[index] = {
                instance_id: `1589269336743526${index}`,
                title: "新横幅广告",
                name: "newStreamer",
                styles: {
                  height: "120pt",
                  padding: "0pt 0pt 0pt 0pt",
                  margin: "0pt 0pt 0pt 0pt",
                },
                common: {
                  name: "新横幅广告",
                },
                content: {
                  list: [],
                  branchCode: "XS000000",
                },
              };
            }
          });
        }
        if (!hasHotZoneStreamer && (this.core || {}).isNewLayout == 1) {
          layout.map((item, index) => {
            if (item.name == "newStreamer") {
              layout.splice(index + 1, 0, {
                instance_id: `1608793956981${index + 1}`,
                title: "多热区横幅广告",
                name: "hotZoneStreamer",
                styles: {
                  height: "120pt",
                  padding: "0pt 0pt 0pt 0pt",
                  margin: "0pt 0pt 0pt 0pt",
                },
                common: {
                  name: "多热区横幅广告",
                },
                content: {
                  list: [],
                  branchCode: "XS000000",
                },
              });
            }
          });
        }
        if (
          !hasPopularQualityShops &&
          this.core.isNewLayout &&
          this.core.isNewLayout === 3
        ) {
          layout.splice(8, 0, {
            instance_id: "16187939569872323",
            title: "人气好店",
            name: "popularQualityShops",
            styles: {
              height: "120pt",
              padding: "0pt 0pt 0pt 0pt",
              margin: "0pt 0pt 0pt 0pt",
            },
            common: {
              name: "人气好店",
            },
            content: {
              list: [],
              branchCode: "XS420000",
            },
          });
        }
        this.value3 = this.core.state === 1 ? true : false;
      } else {
        this.$message({
          message: result.msg,
          type: "warning",
        });
      }
    },
    async save() {
      this.saving = true;
      //校验订单展位
      try{
        if (this.core.page_type === "exhibitionPosition"&&this.core.sceneType == 8) {
        if (this.core.layout.length > 1) {
          this.$message.error("请从一个组件上添加优惠券");
          this.saving = false;
          return false;
        }else if(this.core.layout.length==0) {
          this.$message.error("请添加优惠券");
          this.saving = false;
          return false;
        }else if(this.core.layout[0].content.list.length==0){
          this.$message.error("请添加优惠券");
          this.saving = false;
          return false;
        }
      }else if (this.core.page_type === "exhibitionPosition"&&this.core.sceneType == 9) {
        if (this.core.layout.length > 1) {
          this.$message.error("只能使用一个组件");
          this.saving = false;
          return false;
        }
      }
      }catch(e){

      }
      if (this.core.timevalue) {
      }
      if (this.core.page_type === "exhibitionPosition") {
        if (this.core.layout.length > 5) {
          this.$message.error("同一个展位仅支持添加5个组件");
          this.saving = false;
          return false;
        }
        let setEffectiveTime = true;
        let atIndex = 0;
        this.core.layout.forEach((item, index) => {
          if (item.name === "mainGoods") {
            if (!setEffectiveTime) return false;
            const { content } = item;
            if (content.mode === 1) {
              if (!(content.timevalue && content.timevalue.length)) {
                atIndex = index;
                setEffectiveTime = false;
                return false;
              }
            } else {
              let num = 0;
              if (!(content.weekData || []).length) {
                atIndex = index;
                setEffectiveTime = false;
                return false;
              }
              (content.weekData || []).forEach((item) => {
                if (!(item.timeSlot || []).length) {
                  num += 1;
                }
              });
              if (num === 7) {
                atIndex = index;
                setEffectiveTime = false;
                return false;
              }
            }
          }
        });
        if (!setEffectiveTime) {
          this.$message.error(
            `您还没有选择时间范围，请检查第${atIndex + 1}个组件配置`
          );
          this.saving = false;
          return false;
        }
      }
      if (this.core.page_type === "h5") {
        let hasEmpty = false;
        let errMag = "";
        let pinTuanIndex = this.core.layout.findIndex((item) => {
          return item.name == "pinTuan";
        });
        if (pinTuanIndex !== -1) {
          (this.core.layout[pinTuanIndex].content.list || []).forEach(
            (item) => {
              if (
                item.ptStatus == 0 &&
                !((item.dataSource || [])[0] || {}).code
              ) {
                hasEmpty = true;
                errMag = "拼团组件进行中的基础商品数据不能为空";
              }
              if (item.listType === 3) {
                item.dataSource.forEach((i, index) => {
                  if (i.crowdType == 2 && (!i.crowdValue || !i.crowdId)) {
                    hasEmpty = true;
                    errMag = "拼团组件自定义楼层中的人群范围不完整";
                  }
                  if (i.code && !i.showName) {
                    hasEmpty = true;
                    errMag = "拼团组件中自定义商品组展示名称不能为空";
                  }
                });
              }
            }
          );
        }
        if (hasEmpty) {
          this.$message.error(errMag);
          this.saving = false;
          return;
        }
      }
      //不校验必填，若没有填写，默认为1
      if (this.core.page_type === "diaNewLog" && !this.core.priority) {
        this.core.priority = 1;
      }
      if (this.core.page_type === "index") {
        let hasEmpty = false;
        let recommendTabIndex = this.core.layout.findIndex((item) => {
          return item.name == "recommendTab";
        });
        if (recommendTabIndex !== -1) {
          this.core.layout[recommendTabIndex].content.goods_list.map((item) => {
            if (
              item.tabType == "productGroup" &&
              item.goods_group.length == 0
            ) {
              hasEmpty = true;
            }
          });
        }
        if (hasEmpty) {
          this.$message.error("商品流选项卡中，存在未添加商品组的选项卡");
          this.saving = false;
          return;
        }
      }
      if (this.core.timevalue) {
        if (this.core.timevalue.length > 0) {
          this.core.startDate = this.core.timevalue[0];
          this.core.endDate = this.core.timevalue[1];
        } else {
          this.$alert("您还没有选择时间范围，将自动填充！", "提醒", {
            confirmButtonText: "确定",
            callback: (action) => {},
          });
          this.$set(this.core, "timevalue", []);
          this.core.timevalue.push(this.core.startDate);
          this.core.timevalue.push(this.core.endDate);
        }
      } else {
        this.$alert("您还没有选择时间范围，将自动填充！", "提醒", {
          confirmButtonText: "确定",
          callback: (action) => {},
        });
        this.$set(this.core, "timevalue", []);
        this.core.timevalue.push(this.core.startDate);
        this.core.timevalue.push(this.core.endDate);
      }
      this.core.state = this.value3 === true ? 1 : -1;
      if (this.core.layout.length > 0) {
        this.core.layout.forEach(function (value, i) {
          if (value.name === "floorSpacing") {
            value.styles.height = value.content.floorSpaceHeight + "pt";
          }
        });
      }
      if (this.core.searchRange == 3 && !this.core.exhibitionIdConnect) {
        this.$message.error("专区及以外搜索范围的商品组ID不能为空");
        this.saving = false;
        return;
      }

      if (
        this.core.page_type === "h5" ||
        this.core.page_type == "newControlMall"
      ) {
        // 专区搜索
        let str = JSON.stringify(this.core.layout);
        let exhibitionIdArr1 = str.match(/ZS\d{18}/g) || []; // --包含 ZS+18位数字 的商品组id
        let exhibitionIdArr2 = str.match(/ZS_System_[\w|\:]{1,}/g) || []; // --包含ZS_System_xxx的商品组id，xxx是中文转拼音可能存在冒号
        let exhibitionIdArr3 = str.match(/RUS_NEW_\d{17}/g) || []
        let childExhibitions = exhibitionIdArr1.concat(exhibitionIdArr2, exhibitionIdArr3);
        // this.core.layout.map((item, index) => {
        //   if(item.name === 'pinTuan' && (item.content || {}).componentVersion != 2) {
        //     childExhibitions.push('ZS_System_PINTUAN', 'ZS_System_GroupBuyingNotStart')
        //   }
        // })
        childExhibitions =
          this.core.searchRange == 3
            ? [this.core.exhibitionIdConnect]
            : childExhibitions;
        // 当exhibitionId为空时，获取当前页面的子商品组列表，若子商品组列表为空，则不请求。否则请求接口。
        // 当exhibitionId不为空时，获取当前页面的子商品组列表，请求接口。
        if (this.core.exhibitionId || childExhibitions.length) {
          this.saveExhibitionGroup(childExhibitions);
        } else {
          this.confirmSave();
        }
      } else {
        this.confirmSave();
      }
    },
    async saveExhibitionGroup(childExhibitions) {
      let params = {
        exhibitionId: this.core.exhibitionId || null,
        branchCode: this.core.branchCode || "XS000000",
        childExhibitions,
        sourceValue: this.core.page_title,
        searchStatus: this.core.isShowSearch,
        pageStatus: this.value3,
        searchType: this.core.searchRange,
        pageSource: this.core.page_type == "newControlMall" ? 2 : 1,
        pageShowName: this.core.page_title,
        pageName: this.core.page_name,
        pageId: this.core.page_id,
      };
      const result = await api.topic.saveExhibitionGroup(params);
      this.saving = false;
      if (result.data.success) {
        this.$set(
          this.core,
          "exhibitionId",
          (result.data.data || {}).exhibitionId || null
        );
        this.confirmSave();
      } else {
        this.$message.error(result.data.msg);
      }
    },
    getPointPam(data){
    // 活动页埋点参数保存
    console.log(this.core)
      try{
        this.core.layout.forEach((item,index)=>{
          if(['wonderActive','moreActive','categiory_flow','mainOne','mainTwo','mainThree','mainFour','mainOnePlusTwo','mainTwoPlusOne',
            'mainOnePlusThree','mainOnePlusFour','mainTwoPlusTwo','commodity_flow','control_goldGoods','pinTuanGoldGoods','seckill_flow','couponNew',"coupon","banner"
          ].includes(item.name)){
              item.content.buriedPoint={
              activityID:this.core['page_id'],
              activityName:this.core['page_title'],
              componentName:item.title+item.name,
              componentTitle:item.content['main_title']||item.content['title'],
              componentPosition:index+1
            }
          }
        })
      }catch(err){
        console.log(err)
      }
    },
    async confirmSave() {
     //活动页埋点参数保存
     if(this.core.page_type=='h5'){
      this.getPointPam();
     }   
      const result = await api.topic.update(this.$route.params.id, this.core);
      this.saving = false;
      if (result.code === 200) {
        this.$message.success("保存成功");
        this.logInsert(
          Object.assign(
            {
              type: "页面保存",
            },
            this.core
          )
        );
      } else {
        this.$message.error(result.msg);
      }
    },
    async publish() {
      this.$confirm("确定发布该页面?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.publishing = true;
        try {
          const res = await api.topic.publishPage({pageId:this.$route.params.id});
        }catch (e) {
          console.log(e);
          
        }
        const result = await api.topic.publish(this.$route.params.id);
        if (result.code === 200) {
          const result = await api.topic.get(this.$route.params.id);
          if (result.code === 200) {
            this.core = _.merge({}, this.coreInit, result.data);
          }
          this.$message.success("发布成功");
          this.logInsert(
            Object.assign(
              {
                type: "页面发布",
              },
              this.core
            )
          );
        } else {
          this.$message.error(result.msg);
        }
        this.publishing = false;
      });
    },
    preview(type=1) {
      if (type == 2 && !this.merchantId) {
        this.$message.error("请输入merchantId!");
        return;
      }
      let url = `${API_SERVER}topic/preview?id=${
        this.$route.params.id
      }&time=${new Date()}${type == 2 ? `&isFilerPreview=1&crowId=${this.core.crowdId}&merchantId=${this.merchantId}` : ''}`;
      var iWidth = 380; //弹出窗口的宽度;
      var iHeight = 600; //弹出窗口的高度;
      //window.screen.height获得屏幕的高，window.screen.width获得屏幕的宽
      var iTop = (window.screen.height - 30 - iHeight) / 2; //获得窗口的垂直位置;
      var iLeft = (window.screen.width - 10 - iWidth) / 2; //获得窗口的水平位置;
      window.open(
        url,
        "_blank",
        "height=" +
          iHeight +
          ",,innerHeight=" +
          iHeight +
          ",width=" +
          iWidth +
          ",innerWidth=" +
          iWidth +
          ",top=" +
          iTop +
          ",left=" +
          iLeft +
          ",menubar=no,scrollbars=auto,resizeable=no,location=no,status=no"
      );
    },
    onlineView() {
      let suffix = _.get(this.core, "suffix", "");
      if (suffix) {
        let url = _.get(this.core, "url", "");
        if (url) {
          var k = url.split("/");
          var l = k[k.length - 1];
          url = url.replace(l, "") + suffix + l;
          this.qrCode =
            AppWebsite + "/cms/getQrCodeByUrl?url=" + url + "&" + new Date();
          this.dialogVisible = true;
          /*window.open(url);*/
        }
      } else {
        this.$message.success("没有发布正式连接");
      }
    },
    async confirmRollback(mtime, id) {
      try {
        await this.$confirm(`确认回滚到 ${mtime} ?`, "提示", { type: "info" });
      } catch (e) {
        return;
      }
      const result = await api.topic.rollback(id, {
        version: mtime,
      });
      if (result.code === 200) {
        this.$message.success("回滚成功");
        this.getCore();
        this.logInsert(
          Object.assign(
            {
              type: "页面回滚",
            },
            this.core
          )
        );
      } else {
        this.$message.error(result.msg);
      }
    },
    onSelectItemgroup(row) {
      this.core.rel_itemgroup.group_id = row.item_group_info.id;
      this.core.rel_itemgroup.group_name = row.item_group_info.name;
    },
    showModal(refName) {
      this.$refs[refName].show(true);
    },
    dict() {
      let prs = new Array(1);
      prs[0] = new Promise((res) => res(api.dict.branchHasOpen())).then(
        (bho) => {
          if (bho.code == 200) this.$nextTick(() => (this.branchs = bho.data));
          else this.$message.error(bho.msg);
          return bho;
        }
      );
      return Promise.all(prs.map((item) => item.catch((e) => e))); //并发请求
    },
    goback2start() {
      this.$confirm("请确定是否已进行保存？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.$router.replace("/");
      });
    },
    settingTime() {
      if ((this.core.timevalue || []).length === 0) {
        this.$message.error("请先设置开始结束时间");
        return;
      }
      this.showTimeVis = true;
    },
    cancelModal() {
      this.showTimeVis = false;
    },
    confirmSetLoopTime(data) {
      this.$set(this.core, "weekData", data);
      this.showTimeVis = false;
    },
  },
  filters: {
    datetime(dt) {
      return (dt || "").replace(
        /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/,
        function (match, y, m, d, h, i, s) {
          return [y, m, d].join("-") + " " + [h, i, s].join(":");
        }
      );
    },
    // getBranchName(code, branchs) {
    //   let branchName = "";
    //   if (!code || !branchs || !branchs.length) return branchName;
    //   for (let i = 0, len = branchs.length; i < len; i++) {
    //     let branch = branchs[i];
    //     if (branch.branchCode == code) {
    //       branchName = branch.branchName;
    //       break;
    //     }
    //   }
    //   return branchName;
    // }
  },
  mounted() {
    this.dict().then((data) => {
      this.$store.dispatch("sideBar/setSideBarState", false);
      this.getCore();
    });
    tinymce.init({});
    
  },
};
</script>

<style lang="scss" rel="stylesheet/scss">
.tox,
.tox-silver-sink,
.tox-tinymce-aux {
  z-index: 99999;
}
</style>
<style lang="scss" scoped rel="stylesheet/scss">
.topic-editor {
  min-width: 1320px + 18px;
  height: 100%;
  padding: 15px;
  .topic-editor-head {
    display: flex;
    width: 1310px;
    margin-left: auto;
    margin-right: auto;
    padding: 5px 5px 0;
    margin-bottom: 10px;
    border: 1px solid $border-color-base;
    line-height: 36px;

    .el-form-item {
      margin-bottom: 10px;
    }
    .merchatId-div {
      margin: 10px 0;
      display: flex;
      align-items: center;
    }
  }

  .topic-editor-share {
    display: flex;
    flex-flow: wrap;

    .topic-editor-share-left {
      height: 84px;
    }
    .topic-editor-share-right {
      flex: 1;
      margin-left: 10px;
    }
    .topic-editor-share-top {
      width: 100%;
      margin-bottom: 10px;
      .el-input {
        float: right;
        width: 403px;
      }
    }
    .topic-editor-share-icon {
      width: 95px;
      height: 95px;
      border: 1px dashed $border-color-base;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  .topic-editor-main {
    display: flex;
    margin-left: auto;
    margin-right: auto;
    width: 1320px;
    align-items: stretch;
    min-height: 600px;
  }

  .topic-editor-list {
    position: relative;
    z-index: 1;
    overflow-x: hidden;
    overflow-y: auto;
    width: 280px;
    max-height: 800px;
    margin-right: 1px;
    border: 1px solid $border-color-base;
    box-sizing: border-box;
  }

  .topic-editor-view {
    overflow-x: hidden;
    overflow-y: auto;
    width: 390px;
    max-height: 800px;
    margin: 0 5px;
    border: 1px solid $border-color-base;
    box-sizing: border-box;
  }

  .topic-editor-prop {
    overflow-x: hidden;
    overflow-y: auto;
    //width: 650px;
    flex: 1;
    max-height: 800px;
    border: 1px solid $border-color-base;
    box-sizing: border-box;

    .topic-editor-prop-page-buttons {
      display: flex;
      padding: 5px 0 5px 5px;
      border-bottom: $border-base;
      .el-button {
        flex: 1;
        margin-right: 5px;
        min-width: auto;
      }
    }
  }
}
</style>
