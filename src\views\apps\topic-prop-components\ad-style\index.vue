<template>
    <div class="images-box">
        <div style="margin: 10px 0">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-radio-group v-model="content.activeKey">
                        <el-radio :label="index" v-for="(item,index) in menu" :key="index" :disabled="index>1">{{item}}</el-radio>
                    </el-radio-group>
                </el-col>
            </el-row>
        </div>

        <div class="block" style="margin-bottom: 4px">
            <el-date-picker
                    v-model="content.timevalue"
                    type="datetimerange"
                    :picker-options="pickerOptions2"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    align="right">
            </el-date-picker>
        </div>
        <div class="bg-img">
            <el-container style="height: auto; border: 1px solid #eee">
                <el-header height="50px"
                           style="background-color: rgb(19, 194, 194);text-align: center;padding-top: 15px">
                    <label class="demonstration">背景色:</label>
                    <el-color-picker v-model="content.color" size="mini" @active-change="onSelect"></el-color-picker>
                    <span style="margin-left: 50px"></span>
                    <a @click="imgOnclick" style="cursor: pointer">清除背景图</a>
                </el-header>
                <el-main>
                </el-main>
            </el-container>
        </div>
        <el-upload
                class="topic-image-upload"
                ref="upload"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                :show-file-list="false"
                :before-upload="() => {loading = true; return true;}"
                :on-success="onUploadImg">
            <el-button class="btn-block" type="primary" :loading="loading">上传背景图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
        </el-upload>
        <br>
        <div class="content-setting">内容设置</div>
        <el-table :data="list" size="mini"  :row-key="getRowKeys"   style="width: 100%">
            <el-table-column label="图片">
                <template slot-scope="scope">
                    <div class="container">
                        <div class="img">
                            <img v-if="scope.row.image" :src="scope.row.image" alt="图" class="title-image"/>
                            <i v-else class="el-icon-circle-plus-outline no-img"></i>
                        </div>
                        <div class="button-list">
                            <el-button v-if="scope.row.operation =='2'" size="mini"
                                       @click="toEdit(scope.row, scope.$index, '2')" type="primary">编辑
                            </el-button>
                            <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑
                            </el-button>
                        </div>
                    </div>
                    <div class="link-desc">{{scope.row.link | link}}</div>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog class="banner-dialog" title="修改图片" :visible.sync="addDialog">
            <el-upload
                    class="topic-image-upload"
                    ref="upload"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    :show-file-list="false"
                    :before-upload="() => {loading = true; return true;}"
                    :on-success="onUploadImage">
                <img v-if="dataForm.image" :src="dataForm.image" class="image">
                <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
                <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
            </el-upload>

            <div class="topic-image-picker">
                <el-input placeholder="链接地址" v-model="dataForm.link.meta.page_url">
                    <template slot="prepend">跳转链接</template>
                </el-input>
            </div>
            <page-link @select="onSetLink" :params="{branchCode: topic.branchCode}"></page-link>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeAddDialog">取 消</el-button>
                <el-button size="small" type="primary" @click="confirm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import base from '../base'
    export default {
        extends: base,
        data() {
            return {
                loading: false,
                addDialog: false,
                dataForm: {
                    image: '',
                    link: {
                        meta: {
                            page_url: ''
                        }
                    },
                },
                pickerOptions2: {
                    shortcuts: [{
                        text: '未来一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来六个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '未来一年',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                value4: [new Date(2000, 10, 10, 10, 10), new Date(2000, 10, 11, 10, 10)],
                value5: '',
            }
        },
        computed: {

            list() {
                var list = _.get(this, 'content.list')
                if (list) {
                    if(list[0].link.meta){
                        this.$nextTick(function () {
                            this.setSort()
                        })
                    }
                    return list
                } else {
                    return [];
                }
            },
        },
        filters: {
            link(data) {
                if (!data.type) {
                    return '';
                }
                return data.meta.page_url;
            }
        },
        methods: {
            closeAddDialog() {
                this.addDialog = false;
            },
            toEdit(data, index) {
                if (status == '1') {
                    //新建时图片清空
                    this.dataForm.image = '';
                }
                this.currentData = data;
                this.currentIndex = index;
                this.dataForm = Object.assign({}, data);
                this.isEdit = true;
                this.addDialog = true;
            },
            toAdd() {
                this.isEdit = false;
                this.dataForm = {
                    image: '',
                    link: {
                        meta: {
                            page_url: ''
                        }
                    },
                };
                this.addDialog = true;
            },
            onSetLink(link) {
                this.dataForm.link = link;
            },
            async onUploadImage(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.dataForm.image = res.data.url
            },
            confirm() {
                if (!this.dataForm.image) {
                    this.$message.warning('请上传图片');
                    return false;
                }
                this.closeAddDialog();
                if (this.isEdit) {
                    this.psData = {
                        image: this.dataForm.image,
                        link: this.dataForm.link
                    }
                    this.list.splice(this.currentIndex, 1, this.psData);
                    this.content.list.splice(this.currentIndex, 1, this.psData);
                } else {
                    this.list.push(Object.assign({}, this.list));
                    this.content.list.push(Object.assign({}, this.list));
                }
            },
            onSelect(val) {
                this.content.bgRes = val;
            },
            async onUploadImg(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.image = res.data.url;
                this.content.bgRes = res.data.url;
            },
            imgOnclick() {
                this.content.bgRes = '';
                this.content.color = '#ffffff',
                    this.content.image = '';
            },
            async onUploadImg(res, file) {
                this.loading = false;
                if (res.code !== 200) {
                    this.$message({
                        message: `[${res.code}]${res.msg}`,
                        type: 'warning'
                    })
                    return;
                }
                this.content.bgRes = res.data.url;
            },
            onSelect(val) {
                this.content.color = val;
                this.content.bgRes = this.toColor16(val);
            },
            imgOnclick() {
                this.content.bgRes = '';
                this.content.color = '#ffffff';
            },
            toColor16(str) {
                if (/^(rgb|RGB)/.test(str)) {
                    var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
                    var strHex = "#";
                    for (var i = 0; i < aColor.length; i++) {
                        var hex = Number(aColor[i]).toString(16);
                        if (hex === "0") {
                            hex += hex;
                        }
                        strHex += hex;
                    }

                    if (strHex.length !== 7) {
                        strHex = str;
                    }
                    return strHex.toUpperCase();
                } else {
                    return str;
                }
            }
        },
    }
</script>

<style lang="scss" scoped rel="stylesheet/scss">


    .images-box {
        .container {
            display: flex;
            align-items: center;
            .img {
                width: 78%;
                img {
                    display: block;
                    max-width: 300px;
                    height: 100px;
                }
            }
            .button-list {
                margin-left: 10px;
            }
        }
        .content-setting {
            color: #fff;
            background-color: #13c2c2;
            padding: 10px;
            text-align: center;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .el-icon-circle-plus-outline {
            font-size: 35px;
            color: #c7bdbd;
        }
        .topic-image-upload {
            .image {
                display: block;
                width: 100%;
            }
            .uploader-icon {
                width: 200px;
                height: 200px;
                line-height: 200px;
                border: 1px solid $border-base;
                font-size: 50px;
            }
        }
        .topic-image-picker {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }
</style>
<style lang="scss" rel="stylesheet/scss">
    .topic-banner {
        .banner-dialog {
            .el-dialog__body {
                padding-top: 10px;
            }
        }
    }
</style>
