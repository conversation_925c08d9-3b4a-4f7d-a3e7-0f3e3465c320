<template>
  <div class="two-images">
    <div class="blank_10"></div>
    <el-button type="primary" @click="handleAdd" size="mini">添加</el-button>
		<el-button type="primary" @click="handleDelete" size="mini">删除</el-button>
    <label class="demonstration">背景色:</label>
    <el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>
    <p class="blank_20"></p>
    <el-table
      :data="list" :row-key="getRowKeys" border fit highlight-current-row style="width: 100%"
		  v-if="list.length>0"
		  @selection-change="handleSelection"
    >
			<el-table-column
        type="selection"
        width="55"
      />
			<el-table-column label="关联优惠券ID">
				<template slot-scope="scope" >
					<template>
            <el-select
              v-model="scope.row.voucherTitle"
              :loading="selectLoading"
              filterable
              :filter-method="optionFilter"
              placeholder="请输入优惠券id"
              clearable
              @clear="options = []"
              @change="selectCoupon($event, scope.$index)"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <!-- <el-autocomplete
              size="small"
              v-model.trim="scope.row.value"
              :fetch-suggestions="querySearchCoupon"
              placeholder="请输入优惠券id"
              :trigger-on-focus="false"
              @select="handleSelectCoupon($event, scope.$index)"
            ></el-autocomplete> -->
					</template>
				</template>
			</el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <span v-if="scope.$index !== 0" :style="{cursor: 'pointer', color: '#13c2c2'}" @click="move(scope.row, scope.$index, 'up')">上移 </span>
          <span v-if="scope.$index !== list.length-1" :style="{cursor: 'pointer', color: '#13c2c2'}" @click="move(scope.row, scope.$index, 'down')"> 下移</span>
          <!-- <span v-if="(scope.row || {}).templateId !== (list[0] || {}).templateId" :style="{cursor: 'pointer', color: '#13c2c2'}" @click="move(scope.row, scope.$index, 'up')">上移 </span>
          <span v-if="(scope.row || {}).templateId !== (list[list.length - 1] || {}).templateId" :style="{cursor: 'pointer', color: '#13c2c2'}" @click="move(scope.row, scope.$index, 'down')"> 下移</span> -->
        </template>
      </el-table-column>
		</el-table>
  </div>
</template>

<script>
import base from "../../base";
import api from "api";
import { AppWebsite } from "config";
export default {
  extends: base,
  contentDefault: {
    list: [],
  },
  data() {
    return {
      loading: false,
      options: [],
      selectItem: [],
      selectLoading: false,
      couponObj: {},
    };
  },
 
  computed: {
    list() {
      var list = _.get(this, "content.list");
      if (list) {
        return list;
      } else {
        return [];
      }
    }
  },
  methods: {
    handleAdd() {
      this.list.push({ id: '' })
    },
    handleDelete() {
      this.selectItem.forEach(item => {
        const index = this.list.indexOf(item)
        this.list.splice(index, 1)
      })
    },
    getRowKeys(row) {
      if (!row.id) {
        return
      }
      return row.id
    },
    handleSelection(val) {
      this.selectItem = val || [];
    },
    async optionFilter(val) {
      this.selectLoading = true;
      const res = await api.stores.searchCoupon([val]);
      const { data } = res;
      if (data && data.success) {
        this.selectLoading = false;
        if (!Object.keys(data.data).length) {
          return false;
        }
        const { list } = data.data;
        if (list && list.length) {
          this.couponObj = list[0];
          this.options = [{
            label: list[0].voucherTitle || list[0].templateName,
            value: val,
          }]
        } else {
          this.options = []
        }
      } else {
        this.selectLoading = false;
        this.options = []
      }
    },
    selectCoupon(e, index) {
      this.options = [];
      this.list.splice(index, 1, this.couponObj);
    },
    // handleSelectCoupon(curr, index) {
    //   this.list.splice(index, 1, curr)
    // },
    // async querySearchCoupon(queryString, cb) {
    //   const res = await api.stores.searchCoupon([queryString]);
    //   const { data } = res;
    //   if (data && data.success) {
    //     if (!Object.keys(data.data).length) {
    //       return false;
    //     }
    //     const { list } = data.data;
    //     if (list && list.length) {
    //       let curr = list[0];
    //       curr.id = curr.queryString || curr.templateId;
    //       curr.value = curr.voucherTitle;
    //       cb([curr]);
    //     }
    //   }
    // },
    // 上移下移
    move(record, index, type) {
      if(type === 'up') {
        if (index > 0) {
          // 获取当前点击的上一条数据
          const upDate = this.list[index - 1]
          // 移除上一条数据
          this.list.splice(index - 1, 1)
          // 把上一条数据插入当前点击的位置
          this.list.splice(index, 0, upDate)
        }
      } else {
        const downDate = this.list[index + 1];
        this.list.splice(index + 1, 1)
        this.list.splice(index, 0, downDate)
      }
    }
  },
  watch: {
    list(val){
      val.map((item, index)=> {
        if(item.value == '') {
          this.list.splice(index, 1)
        }
      })
    },
  },
};
</script>

<style lang="scss" rel="stylesheet/scss">

</style>
