<template>
    <div>
        <div class="info-form sys-form" v-loading="loading">
            <breadcrumb></breadcrumb>
            <el-row>
                <div class="grid-content module-title">
                    <span>{{accountInfo.realName}}</span>
                    <!--<el-button size="mini" type="primary" @click="edit">编辑成员</el-button>-->
                </div>
            </el-row>
            <el-row :gutter="40">
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">账号</div>
                        <span>{{accountInfo.userName}}</span>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">手机</div>
                        <span>{{accountInfo.mobile}}</span>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="40">
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">邮箱</div>
                        <span>{{accountInfo.mail}}</span>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">密码</div>
                        <!--<span>请在admin后台修改</span>-->
                        <!--<el-button size="mini" type="text" @click="showPwdDialog = true;">修改密码</el-button>-->
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="40">
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">最近登录</div>
                        <span>{{accountInfo.lastLogin | dateFmt}}</span>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">创建时间</div>
                        <span>{{accountInfo.create_time | dateFmt}}</span>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="40">
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">角色</div>
                        <ul class="role-list">
                            <li class="role" :key="index" v-for="(role,index) in accountInfo.roleList">
                                {{role.roleName}}
                            </li>
                            <li class="role">&nbsp;</li>
                        </ul>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="grid-content">
                        <div class="grid-content-title">组织</div>
                        <ul class="role-list">
                            <li class="role" :key="index" v-for="(org,index) in orgNameList">{{org.distName}}</li>
                        </ul>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="40">
                <el-col :span="24">
                    <div class="grid-content account-status-wrap">
                        <div class="grid-content-title">账号状态</div>
                        <span>{{accountInfo.status == 1 ? '启用' : '停用'}}</span>
                        <!--<div class="button-wrap">
                            <el-button size="mini" @click="updateIfUseMember" :loading="sending">
                                <i :class="{'iconfont icon-tingyong1':accountInfo.status == 1}"></i>
                                <span>{{sending ? '正在提交...' : accountInfo.status == 0 ? '启用账号' : '停用账号'}}</span>
                            </el-button>
                            <el-button size="mini" @click="deleteMember" :loading="deleteing">
                                <i class="iconfont iconfont icon-chuyidong"></i>
                                <span>{{deleteing ? '正在提交...' : '删除成员'}}</span>
                            </el-button>
                        </div>-->
                    </div>
                </el-col>
            </el-row>
        </div>
        <el-dialog
                title="修改密码"
                :visible.sync="showPwdDialog"
                :close-on-click-modal="false"
                @close="pwdForm = {}"
                width="460px">
            <el-form class="dialog-form" :model="pwdForm" :rules="pwdRules" ref="pwdForm" label-width="90px" label-position="left" size="small">
                <el-form-item label="新密码：" prop="password">
                    <el-input type="password" v-model="pwdForm.password" placeholder="英文、数字及英文符号，不能含空格等"></el-input>
                </el-form-item>
                <el-form-item label="确认密码：" prop="checkPass">
                    <el-input type="password" v-model="pwdForm.checkPass" placeholder="请再次输入新密码"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="showPwdDialog = false">取 消</el-button>
                <el-button size="small" type="primary" :loading="sending" @click="updatePwd">{{sending ? '正在提交...' : '确定'}}</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
    import api from 'api';
    import md5 from 'md5';
    import breadcrumb from '../components/breadcrumb.vue';
    import { fetch, getDate } from '../../../utils/time-format';

    export default {
        name: 'Home',
        data() {
            return {
                sending: false,
                loading: false,
                deleteing: false,
                accountInfo: {},
                showPwdDialog: false,
	            allOrgList: [],
	            selectOrgList: [],
	            orgNameList: [],
                pwdForm: {},
                pwdRules: {
                    password: [
                        { required: true, message: '请输入新密码', trigger: 'blur' },
                        { min: 5, max: 20, message: '长度应为5-20个字符', trigger: 'blur' },
	                    {
		                    validator: (rule, val, callback) => {
			                    if (/\s/.test(val)
				                    || !/^[A-Za-z\d\.\$_@,;!&]+$/.test(val))
				                    var e = new Error('密码只能包含英文、数字及部分英文符号（"_", ",", ".", "$", "@", ";", "!", "&"），且不能含有空格等');
			                    return callback(e);
		                    }, trigger: 'blur'
	                    }
                    ],
                    checkPass: [
	                    { required: true, message: '请再次确认密码', trigger: 'blur' },
                        {
                            validator: (rule, val, callback) => {
                                if (val !== this.pwdForm.password)
                                    var e = new Error('两次输入密码不一致');
                                return callback(e);
                            }, trigger: 'blur'
                        }
                    ]
                }
            }
        },
	    filters: {
		    dateFmt(date) {
			    return date ? getDate(date) : '';
		    }
	    },
        async mounted() {
            this.$store.dispatch('breadcrumb/addPath', {
                title: '成员资料',
                subTitle: '成员资料',
                action: 'sysUserInfo'
            });
            this.loading = true;
            const result = await api.user.get(this.$route.params.id);
            this.loading = false;
            if (result.code === 200) {
                this.accountInfo = result.data;
                await this.loadOrg();
                const topOrgList = [], secondOrgList = [];
                this.accountInfo.orgList.forEach(org => {
                    if (org.parentId == 0) {
                        org.level = 1;
                        topOrgList.push(org)
                    }
                })
                this.accountInfo.orgList.forEach(org => {
                    topOrgList.forEach(topOrg => {
                        if (org.parentId === topOrg.orgId) {
                            org.level = 2;
                            secondOrgList.push(org)
                        }
                    })
                })
                this.accountInfo.orgList.forEach(org => {
                    secondOrgList.forEach(secondOrg => {
                        if (org.parentId === secondOrg.orgId) {
                            org.level = 3;
                        }
                    })
                })
                this.accountInfo.orgList.forEach(org => {
                    this.handleOrgClick(org);
                })
                this.resetSelectOrgList()
            } else {
                this.$message.error(result.msg);
            }
        },
        components: {
            breadcrumb
        },
        methods: {
            handleOrgClick(currentOrg) {
                const data = {
                    orgId: currentOrg.orgId,
                    parentId: currentOrg.parentId,
                    name: currentOrg.name,
                    level: currentOrg.level,
                }
                switch (data.level) {
                case 1:
                    const flag = this.selectOrgList.some(topOrg => {
                        return topOrg.orgId === data.orgId;
                    })
                    !flag && this.selectOrgList.push(data);
                    break;
                case 2:
                    let parent;
                    this.selectOrgList.some(topOrg => {
                        if (topOrg.orgId === data.parentId) {
                            parent = topOrg;
                            return true;
                        }
                    })
                    if (!parent) {
                        parent = this.getOrg(data.parentId);
                        parent.children = [ data ];
                        this.selectOrgList.push(parent);
                    }
                    if (parent && parent.children) {
                        const flag = parent.children.some(childOrg => {
                            return childOrg.orgId === data.orgId;
                        })
                        !flag && parent.children.push(data);
                    }
                    if (parent && !parent.children) {
                        parent.children = [ data ];
                    }
                    break;
                case 3:
                    let upParent;
                    this.selectOrgList.some(topOrg => {
                        return topOrg.children && topOrg.children.some(child => {
                            if (child.orgId === data.parentId) {
                                upParent = child;
                                return true;
                            }
                        })
                    })
                    if (!upParent) {
                        upParent = this.getOrg(data.parentId);
                        upParent.children = [ data ];
                        const topParent = this.getOrg(upParent.parentId);
                        topParent.children = [ upParent ];
                        this.selectOrgList.push(topParent);
                    }
                    if (upParent && upParent.children) {
                        const flag = upParent.children.some(childOrg => {
                            return childOrg.orgId === data.orgId;
                        })
                        !flag && upParent.children.push(data);
                    }
                    if (upParent && !upParent.children) {
                        upParent.children = [ data ];
                    }
                    break;
                }
                this.resetSelectOrgList();
            },
            getOrg(orgId) {
                let distOrg;
                this.allOrgList.some(org => {
                    if (orgId === org.orgId) {
                        distOrg = org;
                        return true;
                    }
                })
                return {
                    orgId: distOrg.orgId,
                    parentId: distOrg.parentId,
                    name: distOrg.name,
                };
            },
            async loadOrg() {
                this.loading = true;
                const result = await api.org.list();
                this.loading = false;
                if (result.code === 200) {
                    result.data.forEach(data => {
                        data.children = [];
                        this.allOrgList.push({
                            id: data.id,
                            parentId: data.parentId,
                            name: data.name,
                        })
                    })
                }
            },
            resetSelectOrgList() {
                const orgList = [];
                this.selectOrgList.forEach(topOrg => {
                    if (topOrg.children && topOrg.children.length) {
                        topOrg.children.forEach(secondOrg => {
                            if (secondOrg.children && secondOrg.children.length) {
                                secondOrg.children.forEach(thirdOrg => {
                                    thirdOrg.distName = topOrg.name + '/' + secondOrg.name + '/' + thirdOrg.name;
                                    orgList.push(thirdOrg)
                                })
                            } else {
                                secondOrg.distName = topOrg.name + '/' + secondOrg.name;
                                orgList.push(secondOrg)
                            }
                        })
                    } else {
                        topOrg.distName = topOrg.name;
                        orgList.push(topOrg)
                    }
                    this.orgNameList = orgList;
                })
            },
            edit() {
                this.$router.push({ name: 'editUser', params: { id: this.$route.params.id } })
            },
            async updateIfUseMember() {
                this.sending = true;
                const result = await api.user.update(
                    this.$route.params.id,
                    {status:this.accountInfo.status ? 0:1}
                )
                this.sending = false;
                if (result.code === 200) {
                    this.$message.success('操作成功');
                    this.accountInfo.status = this.accountInfo.status ? 0:1;
                } else {
                    this.$message.error(result.msg);
                }
            },
            async deleteMember() {
                this.deleteing = true;
                const result = await api.user.remove(this.$route.params.id)
                this.deleteing = false;
                if (result.code == 200) {
                    this.$message.success('操作成功');
                    this.$router.go(-1);
                } else {
                    this.$message.error(result.msg);
                }
            },
            async updatePwd() {
                this.$refs.pwdForm.validate(async (valid) => {
                    if (!valid)
	                    return false;
                    const result = await api.user.update(this.$route.params.id, {
                        password: md5(this.pwdForm.password),
                    });
                    if (result.code === 200) {
                        this.$message.success('密码修改成功');
                        this.showPwdDialog = false;
                    } else {
                        this.$message.error(result.msg);
                    }
                });
            }
        }
    }
</script>
<style lang="scss" rel="stylesheet/scss">


    .sys-form {
        margin-top: 0;
        padding-left: 30px;
        .module-title {
            position: relative;
            padding-top: 10px !important;
            padding-bottom: 10px !important;
            border-bottom: 1px solid $extra-black !important;
            .el-button {
                @include middle-center-y();
                right: 0;
            }
        }
        .role-list {
            display: flex;
            .role {
                margin-right: 20px;
            }
        }
        .account-status-wrap {
            position: relative;
            .button-wrap {
                position: absolute;
                right: 0;
                bottom: 8px;
                .iconfont {
                    color: $color-primary;
                    margin-right: 5px;
                }
            }
        }
    }
</style>
