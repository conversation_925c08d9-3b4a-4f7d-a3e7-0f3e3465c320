<template>
  <div class="topic-image">
    <!--分享文案设置-->
    <!-- <el-row :gutter="20">
      <div class="title">分享文案编辑</div>
      <el-col :span="12">
        <div class="block">
          <el-input placeholder="请输入内容" v-model="content.share_title">
            <template slot="prepend">分享标题</template>
          </el-input>
        </div>
      </el-col>

      <el-col :span="12">
        <div class="block">
          <el-input placeholder="请输入内容" v-model="content.share_desc">
            <template slot="prepend">分享描述文案</template>
          </el-input>
        </div>
      </el-col>
    </el-row> -->

    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块背景设置</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="block">
            <el-input placeholder="请输入内容" v-model="content.meddle_bgRes_height">
              <template slot="prepend">banner背景高度</template>
            </el-input>
          </div>
        </el-col>
      </el-row>
      <el-row style="marginTop: 20px">
        <el-col :span="8">
          <div class="block">
          <span class="demonstration">banner底部背景色</span>
          <div>
            <el-color-picker v-model="content.bgColor" size="mini"></el-color-picker>
          </div>
        </div>
        </el-col>
        <el-col :span="8">
          <div class="block">
          <span class="demonstration">banner底部区域背景图</span>
          <div>
            <el-upload
              class="topic-image-upload"
              ref="upload"
              accept="image/jpeg,image/jpg,image/png,image/gif"
              :max-size="1"
              :show-file-list="false"
              :before-upload="() => {loading = true; return true;}"
              :on-success="onUploadImg">
            <el-button type="primary" size="mini">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
          </div>
        </div>
        </el-col>
        <el-col :span="8">
          <div class="block">
          <span class="demonstration">清除背景图</span>
          <div>
            <el-button type="primary" size="mini" @click="clearImg">清除背景图</el-button>
          </div>
        </div>
        </el-col>
      </el-row>
      
    </el-row>

    <swiper-point :content="content"></swiper-point>

    <!--上传轮播图片和编辑设置-->
    <el-row :gutter="20">
      <div class="title">上传轮播和轮播编辑</div>
      <el-col :span="24">
        <el-button @click="toAdd" class="btn-block mb-10" type="primary">上传banner</el-button>
      </el-col>
    </el-row>

    <el-tabs type="card" value="first">
      <el-tab-pane label="生效中" name="first">
        <el-table :data="in_time_list" size="mini" style="width: 100%">
          <el-table-column label="图片">
            <template slot-scope="scope">
              <div class="container-table">
                <div class="img">
                  <img :src="scope.row.image" />
                </div>

                <div class="button-list">
                  <div class="link-desc">
                    <span>人群范围:</span>
                    <span>{{scope.row.crowdValue || '全部人群'}}</span>
                  </div>
                  <div class="demonstration">
                    <p>开始结束时间:</p>
                  
                        <div v-if="scope.row.timeType&&scope.row.timeType==2" style="width: 200px;">
                            <div> 周期循环</div>
                            <template v-if="scope.row.circulateTime">
                              <div v-for="(item,index) in scope.row.circulateTime.circulateList" :key="index">
                            每{{ {1:"月 ",2:"周 ",3:"日 "}[scope.row.circulateTime.circulateType] }}{{ item.weekOrday }}&nbsp;{{scope.row.circulateTime.circulateType==1?'号':" "}} <span v-if="Array.isArray( item.selectTimeData)">{{ item.selectTimeData.join("-") }}</span>
                            </div>
                            </template>
                          
                        
                          </div>
                      <div v-else><p>{{scope.row.timevalue|dateFilter}}</p></div>
                  </div>
                  <div class="link-desc">
                    <p>页面链接:</p>
                    <p>{{scope.row.link | link}}</p>
                  </div>
                  <div class="link-desc">
                    刷新区域颜色:
                    <div
                      :style="{
                                backgroundColor:scope.row.refresh_bgRes,
                                width:'20px',
                                height:'20px',
                                borderRadius:'10px',
                                display:'inline-block'

                                }"
                    ></div>
                  </div>
                  <div v-if="topic.page_type === 'exhibitionPosition'">人群范围：{{scope.row.crowdValue || '全部人群'}}</div>
                  <el-button size="mini" @click="toEdit(scope.row, scope.$index)">编辑</el-button>
                  <el-button size="mini" @click="toRemove(scope.row)" type="danger">删除</el-button>
                  <el-button
                    type="primary"
                    icon="el-icon-caret-top"
                    :disabled="scope.$index===0?true:false"
                    @click="handle_sort(scope.row,'up')"
                    circle
                  ></el-button>
                  <el-button
                    type="primary"
                    icon="el-icon-caret-bottom"
                    :disabled="scope.$index===in_time_list.length-1?true:false"
                    @click="handle_sort(scope.row,'down')"
                    circle
                  ></el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="待生效" name="second">
        <el-table :data="after_time_list" :row-key="getRowKeys" size="mini" style="width: 100%">
          <el-table-column label="图片">
            <template slot-scope="scope">
              <div class="container-table">
                <div class="img">
                  <img :src="scope.row.image" />
                </div>

                <div class="button-list">
                  <div class="link-desc">
                    <span>人群范围:</span>
                    <span>{{scope.row.crowdValue || '全部人群'}}</span>
                  </div>
                  <div class="demonstration">
                    <p>开始结束时间:</p>
                    <p>{{scope.row.timevalue|dateFilter}}</p>
                  </div>
                  <div class="link-desc">
                    <p>页面链接:</p>
                    <p>{{scope.row.link | link}}</p>
                  </div>
                  <div class="link-desc">
                    刷新区域颜色:
                    <div
                      :style="{
                                backgroundColor:scope.row.refresh_bgRes,
                                width:'20px',
                                height:'20px',
                                borderRadius:'10px',
                                display:'inline-block'

                                }"
                    ></div>
                  </div>
                  <el-button size="mini" @click="toEdit(scope.row, scope.$index)">编辑</el-button>
                  <el-button size="mini" @click="toRemove(scope.row)" type="danger">删除</el-button>
                  <el-button
                    type="primary"
                    icon="el-icon-caret-top"
                    :disabled="scope.$index===0?true:false"
                    @click="handle_sort(scope.row,'up')"
                    circle
                  ></el-button>
                  <el-button
                    type="primary"
                    icon="el-icon-caret-bottom"
                    :disabled="scope.$index===in_time_list.length-1?true:false"
                    @click="handle_sort(scope.row,'down')"
                    circle
                  ></el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="已失效" name="third">
        <el-table :data="before_time_list" :row-key="getRowKeys" size="mini" style="width: 100%">
          <el-table-column label="图片">
            <template slot-scope="scope">
              <div class="container-table">
                <div class="img">
                  <img :src="scope.row.image" />
                </div>

                <div class="button-list">
                  <div class="link-desc">
                    <span>人群范围:</span>
                    <span>{{scope.row.crowdValue || '全部人群'}}</span>
                  </div>
                  <div class="demonstration">
                    <p>开始结束时间:</p>
                    <p>{{scope.row.timevalue|dateFilter}}</p>
                  </div>
                  <div class="link-desc">
                    <p>页面链接:</p>
                    <p>{{scope.row.link | link}}</p>
                  </div>
                  <div class="link-desc">
                    刷新区域颜色:
                    <div
                      :style="{
                                backgroundColor:scope.row.refresh_bgRes,
                                width:'20px',
                                height:'20px',
                                borderRadius:'10px',
                                display:'inline-block'

                                }"
                    ></div>
                  </div>
                  <el-button size="mini" @click="toEdit(scope.row, scope.$index)">编辑</el-button>
                  <el-button size="mini" @click="toRemove(scope.row)" type="danger">删除</el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="未设置时间" name="four">
        <el-table :data="no_time_list" :row-key="getRowKeys" size="mini" style="width: 100%">
          <el-table-column label="图片">
            <template slot-scope="scope">
              <div class="container-table">
                <div class="img">
                  <img :src="scope.row.image" />
                </div>
                <div class="button-list">
                  <div class="link-desc">
                    <span>人群范围:</span>
                    <span>{{scope.row.crowdValue || '全部人群'}}</span>
                  </div>
                  <div class="demonstration">
                    <p>开始结束时间:</p>
                    <p>{{scope.row.timevalue|dateFilter}}</p>
                  </div>
                  <div class="link-desc">
                    <p>页面链接:</p>
                    <p>{{scope.row.link | link}}</p>
                  </div>
                  <div class="link-desc">
                    刷新区域颜色:
                    <div
                      :style="{
                                backgroundColor:scope.row.refresh_bgRes,
                                width:'20px',
                                height:'20px',
                                borderRadius:'10px',
                                display:'inline-block'

                                }"
                    ></div>
                  </div>
                  <el-button size="mini" @click="toEdit(scope.row, scope.$index)">编辑</el-button>
                  <el-button size="mini" @click="toRemove(scope.row)" type="danger">删除</el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!--轮播的-->
    <el-dialog class="banner-dialog" title="banner设置" :visible.sync="addDialog">
      <el-form ref="form" :model="dataForm" label-width="80px" >
        <el-form-item label="人群范围：">
          <el-radio-group v-model="dataForm.crowdType" @change="changeCrowdType">
            <el-radio :label="1">全部人群</el-radio>
            <el-radio :label="2">指定人群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="指定人群：" v-if="dataForm.crowdType===2">
          <el-select
            v-model="dataForm.crowdValue"
            :loading="selectLoading"
            filterable
            :filter-method="optionFilter"
            placeholder="请输入人群id"
            clearable
            @clear="options = []"
            @change="selectCrowd"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-autocomplete
            style="width: 300px"
            class="inline-input"
            v-model="dataForm.crowdValue"
            :fetch-suggestions="querySearchCrowd"
            placeholder="请输入人群id"
            :trigger-on-focus="false"
            @select="handleSelectCrowd"
            @input="changeCrowdValue"
          ></el-autocomplete> -->
        </el-form-item>
   
        <el-form-item label="展示时间：">
              <el-radio  v-model="dataForm.timeType" label="1">固定时段</el-radio> 
              <el-date-picker
                v-model="dataForm.timevalue"
                type="datetimerange"
                :picker-options="pickerOptions1"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
              ></el-date-picker>
              <br>
              <el-radio  v-model="dataForm.timeType" label="2">周期循环</el-radio>  <el-button style="marginTop: 10px"  @click="toloopcirculateTime" type="primary" size="mini">配置</el-button>
        </el-form-item>
       
        <br>
      </el-form>  
      <el-upload
        class="topic-image-upload"
        ref="upload"
        accept="image/jpeg, image/jpg, image/png, image/gif"
        :show-file-list="false"
        :before-upload="() => {loading = true; return true;}"
        :on-success="onUploadImage"
      >
        <img v-if="dataForm.image" :src="dataForm.image" class="image" />
        <i v-loading="loading" v-else class="el-icon-plus uploader-icon"></i>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>
      <br />
      
      <br />
 
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="topic-image-picker">
          <span>链接类型：</span>
          <el-select v-model="dataForm.link.meta.linkType" placeholder="请选择">
            <el-option
              v-for="item in linkOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>

        <div class="topic-image-picker" v-if="dataForm.link.meta.linkType !== 'dynamic'">
          <el-input placeholder="链接地址" v-model.trim="dataForm.link.meta.page_url">
            <template slot="prepend">跳转链接</template>
          </el-input>
        </div>
        <div v-if="dataForm.link.meta.linkType === 'dynamic'">
          <div class="topic-image-picker">
            <el-input style="width: 300px; margin: 10px 0" placeholder="输入跳转id" v-model="dataForm.link.meta.dynamicId">
              <template slot="prepend">跳转id</template>
            </el-input>
            <el-button type="primary" @click="putDynamicLink(dataForm)">生成链接</el-button>
          </div>
          <el-input placeholder="链接地址" v-model.trim="dataForm.link.meta.page_url">
            <template slot="prepend">跳转链接</template>
          </el-input>
        </div>
          <!-- <el-input placeholder="链接地址" v-model.trim="dataForm.link.meta.page_url">
            <template slot="prepend">跳转链接</template>
          </el-input> -->
          <control-page @select="onSetLink" :params="{page_type: (topic.page_type === 'h5' || topic.page_type === 'exhibitionPosition') ? 'h5' : '', branchCode: topic.branchCode}"></control-page>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>
    <loopcirculateTime ref="loopcirculateTime" @loopcirculateTimeBack="loopcirculateTimeBack"></loopcirculateTime>
  </div>
</template>
<script>
import { AppWebsite, getUrlParam } from "config";
import api from 'api';
import base from "views/apps/topic-prop-components/base.vue";
import swiperPoint from "views/apps/components/public/swiper-point";
import loopcirculateTime from '../../../../components/loopcirculateTime.vue';
export default {
  name: "searchBox",
  extends: base,
  components: { swiperPoint,loopcirculateTime},
  contentDefault: {
    bannerType: "1",
    tab_bg_color1: "",
    tab_bg_color2: "",
    pro_obj: {
      pro_type: "longBar",
      pro_auto: 3000,
      pro_align_type: "center",
      default_color: "#ffffff",
      default_opacity: 30,
      active_color: "#555555",
      active_opacity: 100,
      component_name: "searchBox", //区分模块的标识
      crowdType: 1,
      crowdValue: '',
      crowdId: '',
    },
    list: [],
    active_icon_color: "#ffffff",
    default_icon_color: "#ffffff",
    top_bgRes: "#292933",
    hotWord_bgRes: "#292933",
    meddle_bgRes: "#292933",
    bottom_bgRes: "#292933",
    refresh_bgRes: "#292933",
    meddle_bgRes_height: "110",
    share_title: "控销商城",
    share_desc:
      "药帮忙开控销商城啦！品牌高毛，好卖又赚钱，品种多多，快来选购吧！"
  },
  data() {
    return {
      icon_color_list: [
        {
          label: "黑色",
          value: "#000000"
        },
        {
          label: "白色",
          value: "#ffffff"
        }
      ],
      pickerOptions0: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      pickerOptions1: {
        shortcuts: [
          {
            text: "未来一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来六个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "未来一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      loading: false,
      addDialog: false,
      dataForm: {
        image: "",
        link: {
          meta: {
            id: 0,
            page_url: "",
            linkType: 'topic',
            dynamicId: '',
          }
        },
        timevalue: "",
        bgRes: "#292933",
        rest_bgRes: "#ffffff",
        crowdType: 1,
        crowdValue: '',
        crowdId: '',
        timeType:'1',
        circulateTime:{}
      },
      can_change_list: false,
      selectLoading: false,
      options: [],
      linkOptions: [{
        value: "topic",
        label: "专题页链接"
      }, {
        value: "stores",
        label: "店铺页链接"
      }, {
        value: "dynamic",
        label: "动态商品链接"
      },
      {
        value: "redEnvelope",
        label: "绑定微信链接"
      }],
    };
  },
  filters: {
    link(data) {
      return data.meta.page_url;
    },
    dateFilter(date) {
      function formatDate(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return (
          year +
          "-" +
          (String(month).length > 1 ? month : "0" + month) +
          "-" +
          (String(day).length > 1 ? day : "0" + day) +
          " " +
          (String(hour).length > 1 ? hour : "0" + hour) +
          ":" +
          (String(minute).length > 1 ? minute : "0" + minute) +
          ":" +
          (String(second).length > 1 ? second : "0" + second)
        );
      }

      if (date) {
        let date1 = formatDate(new Date(date[0]));
        let date2 = formatDate(new Date(date[1]));
        // const nS=new Date(date).getTime()
        return date1 + "至" + date2;
      } else {
        return " ";
      }
    }
  },
  computed: {
    list() {
      let list = _.get(this, "content.list");
      if (list) {
        if (list.length > 0 && list[0].link.meta) {
          this.$nextTick(function() {
            this.setSort();
          });
        }
        return list;
      } else {
        return [];
      }
    },
    in_time_list() {
      const _date = new Date().getTime();
    /*   return this.content.list.filter(item => {
        if (!Array.isArray(item.timevalue)) return;
        const start = new Date(item.timevalue[0]).getTime();
        const end = new Date(item.timevalue[1]).getTime();
        if (_date <= end && _date >= start) {
          return item;
        }
      }); */
      let arr=this.content.list.filter(item => {
        if(item.timeType==2){ return item}
        if (!Array.isArray(item.timevalue)) return;
        const start = new Date(item.timevalue[0]).getTime();
        const end = new Date(item.timevalue[1]).getTime();
        console.log(item,11111)
        if ((_date <= end && _date >= start)) {
          return item;
        }
      });
      return arr
    },
    before_time_list() {
      const _date = new Date().getTime();
      return this.content.list.filter(item => {
        const end = new Date(item.timevalue[1]).getTime();
        if (_date > end&&item.timeType!=2) {
          return item;
        }
      });
    },
    after_time_list() {
      const _date = new Date().getTime();
      return this.content.list.filter(item => {
        const start = new Date(item.timevalue[0]).getTime();
        if (_date < start&&item.timeType!=2) {
          return item;
        }
      });
    },
    no_time_list() {
      return this.content.list.filter(item => {
        if (!item.timevalue&&item.timeType!=2) {
          return item;
        }
      });
    }
  },
  methods: {
    // handleSelectCrowd(item) {
    //   this.dataForm.crowdId = item.id;
    // },
    // changeCrowdValue(e) {
    //   if (!e) {
    //     this.dataForm.crowdId = '';
    //   }
    //   this.$forceUpdate();
    // },
    //打开时间循环
    toloopcirculateTime(){
      this.$refs.loopcirculateTime.showVisible=true
    },
    //循环时间回调
    loopcirculateTimeBack(data){
      this.dataForm.circulateTime=data
console.log(data)
    },
    putDynamicLink(item) {
      if (!item.link.meta.dynamicId) {
        this.$message({
          message: "请输入跳转id再点击生成链接",
          type: "warning"
        });
        return false;
      }
      item.link.meta.page_url = `ybmpage://homeSteadyChannel?strategyId=${item.link.meta.dynamicId}&title=${item.link.meta.page_name}`;
    },
    changeCrowdType() {
      this.dataForm.crowdId = '';
      this.dataForm.crowdValue = '';
    },
    async optionFilter(val) {
        this.selectLoading = true;
        const pms = {
          url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
          dataType: "json",
          data: {},
          head: {
            "Content-Type": "application/json;charset=UTF-8"
          }
        };
        const res = await api.proxy.post(pms);
        if (res.success) {
          const { data } = res;
          this.selectLoading = false;
          this.options = [{
            label: data.name,
            value: val,
          }]
        } else {
          this.selectLoading = false;
          this.options = []
        }
      },
      selectCrowd(e) {
        if (e) {
          this.dataForm.crowdId = Number(this.options[0].value.trim());
          this.dataForm.crowdValue = this.options[0].label;
        } else {
          this.dataForm.crowdId = '';
          this.dataForm.crowdValue = '';
        }
        this.$forceUpdate();
      },
    // async querySearchCrowd(queryString, cb) {
    //   const pms = {
    //     url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
    //     dataType: "json",
    //     data: {},
    //     head: {
    //       "Content-Type": "application/json;charset=UTF-8"
    //     }
    //   };
    //   const res = await api.proxy.post(pms);
    //   if (res.success) {
    //     const { data } = res;
    //     cb([{
    //       id: queryString,
    //       value: data.name || ""
    //     }]);
    //     return false;
    //   }
    // },
    handle_sort(item, type) {
      const index = this.content.list.indexOf(item);
      if (type === "up") {
        this.content.list.splice(index, 1);
        this.content.list.splice(index - 1, 0, item);
      } else {
        this.content.list.splice(index, 1);
        this.content.list.splice(index + 1, 0, item);
      }
    },
    clear_bgs() {
      this.content.top_bgRes = "#292933";
      this.content.meddle_bgRes = "#292933";
      this.content.bottom_bgRes = "#292933";
      this.content.hotWord_bgRes = "#292933";
    },

    // 设置轮播链接
    onSetLink(link) {
      // this.dataForm.link = link;
      this.dataForm.link.meta.page_url = link.meta.page_url;
      this.dataForm.link.meta.page_name = link.meta.page_name;
    },

    // 上传轮播图片
    async onUploadImage(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.dataForm.image = res.data.url;
    },

    // 上传banner对应的头部区域背景图片
    async UploadTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.top_bgRes = res.data.url;
    },
    UploadhotWord_bgRes(res) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.hotWord_bgRes = res.data.url;
    },
    // 上传banner对应的中间区域背景图片
    async UploadMeddleSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.meddle_bgRes = res.data.url;
    },
    // 上传banner对应的底部区域背景图片
    async UploadBottomSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning"
        });
        return;
      }
      this.content.bottom_bgRes = res.data.url;
    },
    // 确定上传
    async confirm() {
      if (!this.dataForm.timevalue&&this.dataForm.timeType!=2) {
          this.$message.warning("请选择有效时间");
          return false;
        }
        if (this.dataForm.timeType==2&&(!this.dataForm.circulateTime||Object.keys(this.dataForm.circulateTime).length==0||!this.dataForm.circulateTime.circulateList||this.dataForm.circulateTime.circulateList.length==0)) {
          this.$message.warning("请添加[周期循环] 时间段。");
          return false;
        }
      if (!this.dataForm.image) {
        this.$message.warning("请上传图片");
        return false;
      }
      if (this.dataForm.crowdType === 2 && !this.dataForm.crowdId) {
          this.$message.error('请选择正确的人群');
          return;
        }
      let linkErrMsg = '';
      if (this.dataForm.link.meta.linkType === 'topic' && this.dataForm.link.meta.page_url) {
        if (!new RegExp("^ybmpage://commonh5activity.*$").test(this.dataForm.link.meta.page_url)) {
          linkErrMsg = '跳转链接格式不正确，请检查';
        } else {
          let linkPageUrl = getUrlParam(this.dataForm.link.meta.page_url, 'url');
          const result = await api.topic.checkPageUrl({ url: linkPageUrl });
          if (((result || {}).data || {}).status != 200) {
            linkErrMsg = '跳转链接不存在，请检查';
          }
        }
      }
      if (linkErrMsg) {
        this.$message.error(linkErrMsg);
        return false;
      }
      if (!this.dataForm.timevalue) {
        this.dataForm.timevalue = "";
      }
      this.addDialog = false;
      if (this.isEdit) {
        this.currentData = Object.assign(this.currentData, this.dataForm);
        this.content.list.splice(this.currentIndex, 1, this.currentData);
      } else {
        this.content.list.push(Object.assign({}, this.dataForm));
      }
      this.can_change_list = !this.can_change_list;
    },

    // 上传轮播按钮
    toAdd() {
      this.dataForm = {
        image: "",
        link: {
          meta: {
            id: new Date().getTime(),
            page_url: "",
            linkType: 'topic',
            dynamicId: '',
          }
        },
        timevalue: "",
        bgRes: "#292933",
        rest_bgRes: "#ffffff",
        crowdType: 1,
        crowdValue: '',
        crowdId: '',
        timeType:'1',
        circulateTime:{}
      };
      this.$refs.loopcirculateTime.circulateTime={}
      this.addDialog = true;
      this.isEdit = false;
    },

    toEdit(data, index) {
      this.currentData = data;
      this.currentIndex = this.list.indexOf(data);
     
      if(!data.timeType){
        data.timeType="1"
      }
      this.dataForm = Object.assign({crowdType: 1, crowdValue: '', crowdId: ''}, data);
      if(this.dataForm.timeType==2&&this.dataForm.circulateTime){
        this.$refs.loopcirculateTime.circulateTime=this.dataForm.circulateTime
        this.$refs.loopcirculateTime.editInit()

      }
      this.isEdit = true;
      this.addDialog = true;
    },
    toRemove(data) {
      let _self = this;
      return function() {
        _self.list.splice(_self.list.indexOf(data), 1);
        _self.$message({
          type: "success",
          message: "删除成功!"
        });
      }.confirm(_self)();
    },
    async onUploadImg(res, file) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: 'warning'
        })
        return;
      }
      this.content.bgImg = res.data.url;
      console.log('back-content', this.content);
    },
    clearImg() {
      this.content.bgColor = '';
      this.content.bgImg = '';
    }
  },
  watch: {
    can_change_list: {
      immediate: true,
      handler(new_val, old_val) {
        setTimeout(() => {
          this.content.list = [
            ...this.in_time_list,
            ...this.after_time_list,
            ...this.before_time_list,
            ...this.no_time_list
          ];
        }, 100);
      }
    }
  }
};
</script>

<style lang="scss">
.container-table {
  margin: 10px auto;
  padding-bottom: 10px;
  display: flex;
  justify-content: space-around;

  .img {
    width: 50%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .button-list {
    width: 45%;
  }
}

.topic-image-upload {
  .image {
    display: block;
    width: 100%;
  }

  .uploader-icon {
    width: 200px;
    height: 200px;
    line-height: 200px;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    font-size: 50px;
  }
}

.topic-image-upload .el-upload {
  width: 100%;
}

.el-row {
  text-align: center;

  img {
    width: 100%;
  }

  .title {
    text-align: left;
    line-height: 30px;
    background-color: #f2f2f2;
    margin: 10px 0;
    padding-left: 10px;
  }
}
.topic-image-picker {
  margin-bottom: 10px;
}
</style>
