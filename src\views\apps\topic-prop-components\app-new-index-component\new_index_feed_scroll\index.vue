
<template>
  <div class="topic-search">
    <!--模块背景设置-->
    <el-row :gutter="20">
      <div class="title">模块楼层信息配置</div>
      <el-form label-width="100px">
        <el-col :span="12">
          <el-form-item label="活动id:">
            <el-input placeholder="请输入内容" v-model="queryParams.activityId">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="活动名称:">
            <el-input
              placeholder="请输入内容"
              v-model="queryParams.activityName"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="展示时间:">
            <el-input
              placeholder="请输入内容"
              v-model="queryParams.validityTime"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="帧数:">
            <el-select
              v-model="queryParams.bannerLocation"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in bannerLocationList"
                :value="item.id"
                :label="item.name"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="顺序号:">
            <el-input v-model="queryParams.sort"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="人群id:">
            <el-input v-model="queryParams.crowdValue"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态:">
            <el-select
              v-model.number="queryParams.status"
              placeholder="选择状态"
              default-first-option
              filterable
            >
              <el-option
                v-for="(v, k, i) in status"
                :label="v"
                :value="Number(k)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <div class="three-button">
      <el-button type="primary" @click="searchList" size="mini">查询</el-button>
      <el-button type="primary" @click="resetList" size="mini">重置</el-button>
      <el-button type="primary" @click="addList" size="mini">新建</el-button>
    </div>
    <el-table
      :data="dataList"
      size="mini"
      class="tableBox"
      style="margin: 0 0 20px"
      ref="tableBox"
      :row-key="(row) => row.id"
    >
      <el-table-column label="id" prop="activityId" width="100"></el-table-column>
      <el-table-column label="活动名称" prop="activityName"></el-table-column>
      <el-table-column label="帧数" prop="bannerLocation"></el-table-column>
      <el-table-column label="顺序" >
          <template slot-scope="scope">
            <el-input v-model="scope.row.sort" onkeyup="value=value.replace(/[^\d]/g,'')" @blur="changeSort(scope)" @keyup.enter.native="changeSort(scope)"></el-input>
          </template>
        </el-table-column>
      <el-table-column label="人群" show-overflow-tooltip>
        <template slot-scope="scope">
          <p>
            {{ scope.row.crowdValue || "全部人群" }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="展示时间" width="200">
        <template slot-scope="scope">
          {{ scope.row.validityTime[0] }} ~ {{ scope.row.validityTime[1] }}
        </template>
      </el-table-column>
      <el-table-column label="状态">
          <template slot-scope="scope">
            <div >
              {{ ['未开始', '上线', '已结束', '下线'][scope.row.status - 1] || '-' }}
            </div>
          </template>
        </el-table-column>
      <el-table-column label="预览">
        <template slot-scope="scope">
          <div style="width: 40px; height: 40px">
            <img :src="scope.row.image" alt="" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="150px">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="toEdit(scope.row, scope.$index)"
            >编辑
          </el-button>
          <el-button size="mini" type="text" @click="toRemove(scope.row)"
            >删除</el-button
          >
          <el-button size="mini" type="text" @click="online(scope.row)"
            >上线</el-button
          >
          <el-button size="mini" type="text" @click="outline(scope.row)"
            >下线</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增 -->
    <el-dialog
      class="feed-scroll-dialog"
      :title="`${isEdit ? '编辑' : '新建'}`"
      :before-close="addDialogCancel"
      :visible.sync="addDialog"
    >
      <el-form
        label-position="right"
        ref="addRuleForm"
        :model="addForm"
        size="small"
        label-width="100px"
        label-suffix="："
      >
        <el-form-item label="活动名称" prop="activityName">
          <el-input
            v-model="addForm.activityName"
            maxlength="20"
            size="mini"
            placeholder="请输入活动名称，20个字符以内"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="轮播位置" prop="activitySort">
          <div class="dialog-activity-sort">
            <el-select
              v-model="addForm.bannerLocation"
              placeholder="请选择"
              clearable
            >
              <el-option value="" label="全部"></el-option>
              <el-option
                v-for="item in bannerLocationList"
                :value="item.id"
                :label="item.name"
                :key="item.id"
              ></el-option>
            </el-select>
          </div>
        </el-form-item>

        <el-form-item label="指定人群" prop="crowdType">
          <el-radio-group v-model="addForm.crowdType" @change="changeCrowdType">
            <el-radio :label="1">显示该页面已选中人群</el-radio>
            <el-radio :label="2">
              指定人群
              <el-select
                v-if="addForm.crowdType === 2"
                style="margin-left: 10px"
                v-model.trim="addForm.crowdValue"
                :loading="selectLoading"
                filterable
                :filter-method="optionFilter"
                placeholder="请输入人群id"
                clearable
                @clear="options = []"
                @change="selectCrowd"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="展示时间">
          <el-radio v-model="addForm.timeType" :label="1">固定时段</el-radio>
          <el-date-picker
            v-model="addForm.validityTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :picker-options="{
              disabledDate: (time) => {
                const times =
                  new Date(new Date().toLocaleDateString()).getTime() +
                  1095 * 8.64e7 -
                  1;
                return (
                  time.getTime() < Date.now() - 8.64e7 || time.getTime() > times
                ); // 如果没有后面的-8.64e7就是不可以选择今天的
              },
            }"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker
          ><br />
          <el-radio v-model="addForm.timeType" :label="2">周期循环</el-radio>
          <el-button
            style="margintop: 10px"
            @click="toloopcirculateTime"
            type="primary"
            size="mini"
            >配置</el-button
          >
        </el-form-item>

        <el-form-item label="图片" prop="imgs">
          <el-upload
            :limit="1"
            :auto-upload="true"
            :file-list="imageList"
            :before-upload="(f) => (sending = true)"
            :on-change="
              (f, fs) => {
                imageList = fs;
              }
            "
            :on-remove="(f, fs) => (imageList = fs)"
            :on-exceed="uploadExceed"
            :on-error="uploadErr"
            :on-success="uploadSuccess"
            ref="uploadImg"
            class="upload-demo"
            list-type="picture-card"
            accept="image/jpeg,image/jpg,image/png,image/bmp,image/gif"
          >
            <i v-if="!imageList || !imageList.length" class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">
              <i>只能上传jpeg/jpg/png/bmp/gif文件，且不超过1M</i>
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="转跳链接" prop="hrefUrl">
          <el-input v-model="addForm.hrefUrl" size="mini" clearable></el-input>
          <el-button
            type="primary"
            size="small"
            @click="isShowHrefDialog = true"
            >more</el-button
          >
        </el-form-item>
      </el-form>
      <el-dialog
        title="跳转链接配置"
        :visible.sync="isShowHrefDialog"
        width="30%"
        append-to-body
      >
        <page-link
          @select="onSetLink"
          :params="{ branchCode: topic.branchCode }"
        ></page-link>
        <span slot="footer" class="dialog-footer">
          <el-button @click="hrefCancel">取 消</el-button>
          <el-button type="primary" @click="hrefConfirm">确 定</el-button>
        </span>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="addDialogCancel">取 消</el-button>
        <el-button size="small" type="primary" @click="addDialogConfirm"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <loopcirculateTime
      ref="loopcirculateTime"
      @loopcirculateTimeBack="loopcirculateTimeBack"
    ></loopcirculateTime>
  </div>
</template>
<script>
import loopcirculateTime from "../../../components/loopcirculateTime.vue";
import base from "../../base";
import swiperPoint from "views/apps/components/public/swiper-point";
import { AppWebsite, getUrlParam } from "config";
import api from "api";
import Sortable from "sortablejs";
let sortableObject = {};
export default {
  name: "feedScroll",
  extends: base,
  components: { swiperPoint, loopcirculateTime },
  contentDefault: {
    list: [],
  },
  data() {
    return {
      isEdit: false,
      isShowHrefDialog: false,
      currentDataIndex: undefined,
      status: [],
      addDialog: false,
      addFormSelectLink: "",
      options: [],
      imageList: [],
      sending: false,
      addForm: {
        bannerLocation: "",
        activityId: "",
        activityName: "",
        sort: "",
        crowdId: "",
        timeType: 1,
        validityTime: "",
        circulateTime: {},
        crowdType: "",
        crowdValue: "",
        hrefUrl: "",
        bannerImg: "",
      },
      queryParams: {
        activityId: "",
        activityName: "",
        sort: "",
        crowdValue: "",
        crowdId: "",
        validityTime: "", //有效期
        wordName: "", //词组名称
        crowdValue: "", // 人群
        status: "", //状态
        bannerLocation: "",
      },
      dataList: [],
      carouselList: {
        bannerLocation: "",
        crowdValue: "",
        status: "",
      },
      bannerLocationList: [
        {
          id: 1,
          name: "第一帧",
        },
        {
          id: 2,
          name: "第二帧",
        },
        {
          id: 3,
          name: "第三帧",
        },
        {
          id: 4,
          name: "第四帧",
        },
        {
          id: 5,
          name: "第五帧",
        },
        {
          id: 6,
          name: "第六帧",
        },
        {
          id: 7,
          name: "第七帧",
        },
        {
          id: 8,
          name: "第八帧",
        },
        {
          id: 9,
          name: "第九帧",
        },
      ],
      // 时间不能大于当前时间
      disabledDate: (time) => {
        return time.getTime() > Date.now();
      },
    };
  },
  filters: {
    link(data) {
      return data.meta.page_url;
    },
    dateFilter(date) {
      function formatDate(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return (
          year +
          "-" +
          (String(month).length > 1 ? month : "0" + month) +
          "-" +
          (String(day).length > 1 ? day : "0" + day) +
          " " +
          (String(hour).length > 1 ? hour : "0" + hour) +
          ":" +
          (String(minute).length > 1 ? minute : "0" + minute) +
          ":" +
          (String(second).length > 1 ? second : "0" + second)
        );
      }

      if (date) {
        let date1 = formatDate(new Date(date[0]));
        let date2 = formatDate(new Date(date[1]));
        // const nS=new Date(date).getTime()
        return date1 + "至" + date2;
      } else {
        return " ";
      }
    },
    jumpText(val) {
      if (!val) {
        return "app内部跳转";
      } else {
        if (val === "inLink") {
          return "app内部跳转";
        }
        return "跳转至外部";
      }
    },
  },
  mounted() {
    this.initData();
    this.rowDrop();
    // this.changeTab("notInvalid");
    this.getDict();
  },
  computed: {
    /**
     *   获取列的状态名称
     */
    getStatusName() {
      return function (timevalue, type) {
        let item = {};
        if (!timevalue) {
          item = {
            id: 4,
            name: "未设置时间",
          };
        } else {
          const _date = new Date().getTime();
          const start = new Date(timevalue[0]).getTime();
          const end = new Date(timevalue[1]).getTime();
          if (_date <= end && _date >= start) {
            item = {
              id: 1,
              name: "生效中",
            };
          } else if (_date > end) {
            item = {
              id: 3,
              name: "已失效",
            };
          } else if (_date < start) {
            item = {
              id: 2,
              name: "待生效",
            };
          }
        }
        if (type == "id") {
          return item.id;
        } else {
          return item.name;
        }
      };
    },
    // list() {
    //   let list = _.get(this, 'content.list')
    //   if (list) {
    //     if (list.length > 0 && list[0].link.meta) {
    //       this.$nextTick(function () {
    //         this.setSort()
    //       })
    //     }
    //     return list
    //   } else {
    //     return [];
    //   }
    // }
  },
  methods: {
    async getDict() {
      let status = await api.goods.status();
      if (status.code == 200) this.$nextTick(() => (this.status = status.data));
      else this.$message.error(status.msg);
    },
    changeSort(scope) {
      let ind = scope.$index;
      if (this.dataList[ind].sort <= 0) {
        this.$message.warning("请输入大于0的数字！")
        return;
      }
      if (this.dataList[ind].sort >= this.dataList.length) {
        this.dataList.splice(this.dataList.length, 0, this.dataList[ind]);
        this.dataList.splice(ind, 1)
      } else {
        if (this.dataList[ind].sort > ind) {
          this.dataList.splice(this.dataList[ind].sort, 0, this.dataList[ind]);
          this.dataList.splice(ind, 1);
        } else {
          this.dataList.splice(this.dataList[ind].sort - 1, 0, this.dataList[ind]);
          this.dataList.splice(ind + 1, 1);
        }
      }
      this.dataList.forEach((item, index) => {
        item.sort = index + 1;
      })
    },
    resetList() {},
    addDialogCancel() {
      this.resetAddForm();
      this.addDialog = false;
    },
    hrefCancel() {
      this.addFormSelectLink = "";
      this.isShowHrefDialog = false;
    },
    hrefConfirm() {
      this.addForm.hrefUrl = this.addFormSelectLink;
      this.isShowHrefDialog = false;
    },
    resetAddForm() {
      this.imageList = [];
      this.addForm = {
        bannerLocation: "",
        activityId: "",
        activityName: "",
        sort: "",
        crowdId: "",
        timeType: 1,
        validityTime: "",
        circulateTime: {},
        crowdType: "",
        crowdValue: "",
        hrefUrl: "",
        bannerImg: "",
      };
    },
    uploadSuccess(res, f, fs) {
      this.sending = false;
      if (res.code != 200) {
        this.$message.warning(`[${res.code}]${res.msg}`);
        return;
      }
      this.imageList = fs.map((f) => f.response.data);
      this.addForm.bannerImg = this.imageList[0].url;
    },
    uploadExceed(f, fs) {
      this.$notify.warning(`最多允许上传 ${fs.length} 个文件`);
    },
    uploadErr(e, f, fs) {
      this.sending = false;
      this.$notify.error(`文件“${f.name}”上传出错`);
    },
    addList() {
      this.isEdit = false;
      this.addDialog = true;
    },
    resetQueryParams() {
      this.queryParams = {
        validityTime: "", //有效期
        wordName: "", //词组名称
        crowdValue: "", // 人群
        status: "", //状态
      };
    },
    //打开时间循环
    toloopcirculateTime() {
      this.$refs.loopcirculateTime.showVisible = true;
    },
    //循环时间回调
    loopcirculateTimeBack(data) {
      this.addForm.circulateTime = data;
    },
    initData() {
      this.dataList = this.content.list;
      console.log(this.dataList);
    },
    rowDrop() {
      const _this = this;
      const tbody = document.querySelectorAll(
        ".el-table__body-wrapper > table > tbody"
      )[0];
      sortableObject = Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        ghostClass: "sortable-ghost",
        onEnd: (evt) => {
          const currRow = (_this.dataList || []).splice(evt.oldIndex, 1)[0];
          (_this.dataList || []).splice(evt.newIndex, 0, currRow);
          const currRowData = (_this.content.list || []).splice(
            evt.oldIndex,
            1
          )[0];
          (_this.content.list || []).splice(evt.newIndex, 0, currRowData);
        },
      });
    },
    changeCrowdValue(e) {
      if (!e) {
        this.addForm.crowdId = "";
      }
      this.$forceUpdate();
    },
    clear_bgs(type) {
      console.log(this.content, "Cotnet");
      this.content[type + "_url"] = "";
      this.content[type + "_color"] = "";
      this.content[type + "_transparency"] = 100;

      // this.content.top_bgRes = "#fff";
      // this.content.meddle_bgRes = "#fff";
      // this.content.bottom_bgRes = "#fff";
      // this.content.hotWord_bgRes = "#fff"
    },

    // 上传banner对应的头部区域背景图片
    async UploadTopSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      // this.content[type] = res.data.url
      this.$set(this.content, type, res.data.url);
      console.log("添加", this.content, "conetnt");
    },
    UploadhotWord_bgRes(res) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.hotWord_bgRes = res.data.url;
    },
    // 上传banner对应的中间区域背景图片
    async UploadMeddleSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.meddle_bgRes = res.data.url;
    },
    // 上传banner对应的底部区域背景图片
    async UploadBottomSearchBg(res, type) {
      this.loading = false;
      if (res.code !== 200) {
        this.$message({
          message: `[${res.code}]${res.msg}`,
          type: "warning",
        });
        return;
      }
      this.content.bottom_bgRes = res.data.url;
    },
    // 设置轮播链接
    onSetLink(link) {
      this.addFormSelectLink = link.meta.page_url;
    },
    changeCrowdType() {
      this.addForm.crowdId = "";
      this.addForm.crowdValue = "";
    },
    async addDialogConfirm() {
      this.$refs.addRuleForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        if (
          this.content.list.findIndex(
            (item) => item.activityName === this.addForm.activityName
          ) > -1 &&
          this.isEdit === false
        ) {
          this.$message("活动名称已存在！");
          return;
        }
        if (this.isEdit) {
          this.$set(this.content.list, this.currentDataIndex, this.addForm);
          console.log(this.content.list, 'this.content.list');
        } else {
          let id = 0;
          id = Math.floor(Math.random() * 90000) + 10000;
          this.addForm.activityId = id;
          this.$set(this.content.list, this.content.list.length, this.addForm);
        }

        this.content.list.forEach((item,index) => {
          item.sort = index + 1;
        })

        this.$message.success("添加成功！");
        this.addDialog = false;
      });
    },

    // 按照规则排序--排序规则优先级：人群 > 帧位 > 生效时间（生效中>待生效>未设置）
    sortByRule(data) {
      const samePeople = this.content.list.filter((item, index) => {
        return Number(item.crowdId) === Number(this.dataForm.crowdId);
      });
      // 相同人群的逻辑
      if (samePeople.length) {
        const sameLocation = samePeople.filter((item, index) => {
          return (
            Number(item.bannerLocation) === Number(this.dataForm.bannerLocation)
          );
        });
        // 相同人群下，相同帧位的逻辑
        if (sameLocation.length) {
          const sameStatus = sameLocation.filter((item, index) => {
            return (
              this.getStatusName(this.dataForm.timevalue) ===
              this.getStatusName(item.timevalue)
            );
          });
          let tempIndex = undefined;
          if (sameStatus.length) {
            this.content.list.forEach((item, index) => {
              if (item.id === sameStatus[sameStatus.length - 1].id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，相同状态，插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === "生效中") {
            this.content.list.forEach((item, index) => {
              if (sameLocation[0].id === item.id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，生效中插到前面
            this.content.list.splice(tempIndex, 0, data);
          } else if (this.getStatusName(this.dataForm.timevalue) === "待生效") {
            this.content.list.map((item, index) => {
              if (this.getStatusName(item.timevalue) === "生效中") {
                tempIndex = index + 1;
              }
            });
            if (!tempIndex) {
              // 说明没有生效中，找未设置的
              this.content.list.map((item, index) => {
                if (this.getStatusName(item.timevalue) === "未设置时间") {
                  tempIndex = index;
                }
              });
            }
            if (!tempIndex) {
              // 说明没有生效中未设置的，插到帧位最后面
              this.content.list.map((item, index) => {
                if (sameLocation[sameLocation.length - 1].id === item.id) {
                  tempIndex = index + 1;
                }
              });
            }
            // 相同人群，相同帧位，待生效插到生效中后面或未设置时间的前面或同帧位最后面
            this.content.list.splice(tempIndex, 0, data);
          } else {
            this.content.list.map((item, index) => {
              if (sameLocation[sameLocation.length - 1].id === item.id) {
                tempIndex = index;
              }
            });
            // 相同人群，相同帧位，未设置插到后面
            this.content.list.splice(tempIndex + 1, 0, data);
          }
        } else {
          // 相同人群下，不同帧位，比较帧位大小
          let tempIndex = undefined;
          // 找到第一个大于新增帧位的项，有则插入到前面，没有则插入到同人群下最后一位
          let maxItem = samePeople.find((item, index) => {
            return item.bannerLocation > this.dataForm.bannerLocation;
          });
          if (maxItem) {
            this.content.list.map((item, index) => {
              if (item.id === maxItem.id) {
                tempIndex = index;
              }
            });
          } else {
            this.content.list.map((item, index) => {
              if (item.id === samePeople[samePeople.length - 1].id) {
                tempIndex = index + 1;
              }
            });
          }
          // 新增帧位小插到前面；新增帧位大插到后面
          this.content.list.splice(tempIndex, 0, data);
        }
      } else {
        // 新人群，直接添加
        let tempIndex = undefined;
        tempIndex = this.content.list.filter((item) => {
          return this.getStatusName(item.timevalue) !== "已失效";
        }).length;
        this.content.list.splice(tempIndex, 0, data);
      }
      this.$nextTick(() => {
        this.searchList();
      });
      // this.content.list.push(Object.assign({}, data));
      // this.$set(this.dataList, this.dataList.length, data)
    },
    changeTab(type) {
      this.activeTab = type;
      this.carouselList.status = "";
      this.searchList();
    },
    //生成唯一id
    genID(length) {
      return Number(
        Math.random().toString().substr(3, length) + Date.now()
      ).toString(36);
    },
    //查询
    searchList() {
      this.dataList = this.content.list;
      if (this.queryParams.validityTime.length) {
        this.dataList = this.dataList.filter((item, index) => {
          return (
            new Date(this.queryParams.validityTime[0]) * 1 >=
              new Date(item.validityTime[0]) * 1 &&
            new Date(this.queryParams.validityTime[1]) * 1 <=
              new Date(item.validityTime[1]) * 1
          );
        });
      }
      if (this.wordName) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.wordName === item.wordName;
        });
      }
      if (this.crowdValue) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.crowdValue === item.crowdId;
        });
      }
      if (this.status) {
        this.dataList = this.dataList.filter((item, index) => {
          return this.queryParams.status === item.status;
        });
      }
      console.log(this.content.list, 12123);
    },
    toEdit(data, index) {
      this.currentDataIndex = index;
      this.isEdit = true;

      let keys = Object.keys(data);
      for (let index = 0; index < keys.length; index++) {
        const key = keys[index];
        this.addForm[key] = data[key];
        if (key === "bannerImg") {
          this.imageList = [{ url: data[key] }];
        }
      }

      this.addDialog = true;
    },
    toRemove(data) {
      let _self = this;
      return function () {
        _self.content.list.splice(
          _self.content.list.findIndex((item) => item.id == data.id),
          1
        );
        _self.dataList.splice(
          _self.dataList.findIndex((item) => item.id == data.id),
          1
        );
        _self.$message({
          type: "success",
          message: "删除成功!",
        });
      }.confirm(_self)();
    },
    online() {},
    outline() {},
    async optionFilter(val) {
      this.selectLoading = true;
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${val}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        const { data } = res;
        this.selectLoading = false;
        this.options = [
          {
            label: data.name,
            value: val,
          },
        ];
      } else {
        this.selectLoading = false;
        this.options = [];
      }
    },
    selectCrowd(e) {
      if (e) {
        this.addForm.crowdId = Number(this.options[0].value.trim());
        this.addForm.crowdValue = this.options[0].label;
      } else {
        this.addForm.crowdId = "";
        this.addForm.crowdValue = "";
      }
      this.$forceUpdate();
    },
    async querySearchCrowd() {
      const pms = {
        url: AppWebsite + `cms/getChosenCustomerNameById?id=${queryString}`,
        dataType: "json",
        data: {},
        head: {
          "Content-Type": "application/json;charset=UTF-8",
        },
      };
      const res = await api.proxy.post(pms);
      if (res.success) {
        this.crowdName = data.name;
      }
    },
  },
  watch: {
    "addForm.timeType"(newdata, ordData) {
      if (newdata == 2) {
        this.addForm.timevalue = "";
      }
      if (newdata == 1) {
        this.addForm.circulateTime = {};
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.title {
  text-align: left;
  line-height: 30px;
  background-color: #f2f2f2;
  margin: 10px 0;
  padding-left: 10px;
}
.three-button {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
.tableBox {
  width: 100%;
}
.dialog-activity-sort {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>