
<template>
  <div class="fast-entry">
    <!--添加快捷入口-->
    <el-row :gutter="20">
      <div class="title">添加快捷入口</div>
      <el-col :span="8">
        <div class="block">
          <el-button class="btn-block" type="primary"
                     @click="add_editContent=true"
          >添加快捷入口
          </el-button>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg,image/png,image/gif"
            :show-file-list="false"
            :before-upload="() => {loading = true; return true;}"
            :on-success="onUploadImg">
            <el-button class="btn-block" type="primary" :loading="loading">替换背景图</el-button>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="block">
          <el-button class="btn-block" type="primary"
                     @click="content.bgRes=''">
            还原背景图
          </el-button>
        </div>
      </el-col>
    </el-row>
    <br>

    <el-table :data="list" size="mini" :row-key="getRowKeys" style="width: 100%">
      <el-table-column
        type="index"
        width="50">
      </el-table-column>
      <el-table-column prop="title" label="标题">
        <template slot-scope="scope">
          <img v-if="scope.row.title" :src="scope.row.title" alt="图" class="title-image"/>
          <i v-else class="el-icon-circle-plus-outline no-img"></i>

        </template>
      </el-table-column>

      <el-table-column prop="entry" label="有效时间">
        <template slot-scope="scope">
          <span v-html="format_text(scope.row.timevalue)"></span>
        </template>
      </el-table-column>


      <el-table-column prop="entry" label="链接">
        <template slot-scope="scope">
          <el-input type="text" size="mini" v-model="scope.row.link.meta.page_url">
            {{scope.row.link.meta.page_url}}
          </el-input>
        </template>
      </el-table-column>
      <el-table-column prop="operation" label="操作2">
        <template slot-scope="scope">
          <el-button size="mini" @click="toEdit(scope.row, scope.$index)" type="primary">编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 背景图上传弹层 -->
    <el-dialog class="banner-dialog" title="添加图片" :visible.sync="addPic" width="30%">
      <el-upload
        class="topic-image-upload uploader-btn-state"
        ref="upload"
        accept="image/jpeg,image/jpg,image/png,image/gif"
        :show-file-list="false"
        :before-upload="() => {upImgLoading = true; return true;}"
        :on-success="onUploadImage">
        <img v-if="dataForm.image" :src="dataForm.image" class="image">
        <i v-else v-loading="upImgLoading" class="el-icon-plus uploader-icon"></i>
        <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeAddPic">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmImg">确定</el-button>
      </div>
    </el-dialog>
    <!-- 编辑内容弹层 -->
    <el-dialog class="banner-dialog" title="编辑入口" :visible.sync="editContent">
      <el-form ref="form" :model="editData" label-width="120px">
        <el-form-item label="菜单ICON：">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg,image/png,image/gif"
            :show-file-list="false"
            :before-upload="() => {editImgLoading = true; return true;}"
            :on-success="uploadEditContImage">
            <img v-if="editData.title" :src="editData.title" class="image"/>
            <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </el-form-item>


        <el-form-item label="有效时间：">
          <el-date-picker
            v-model="editData.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right">
          </el-date-picker>
        </el-form-item>


      </el-form>
      <div class="topic-image-picker">
        <el-input placeholder="链接地址" v-model="editData.link.meta.page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>
      <control-page @select="onSetLink" :params="{branchCode: topic.branchCode}"></control-page>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditContent">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmEdit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 添加内容弹层 -->
    <el-dialog class="banner-dialog" title="添加入口" :visible.sync="add_editContent">
      <el-form ref="form" :model="editData" label-width="120px">
        <el-form-item label="菜单ICON：">
          <el-upload
            class="topic-image-upload"
            ref="upload"
            accept="image/jpeg,image/jpg,image/png,image/gif"
            :show-file-list="false"
            :before-upload="() => {editImgLoading = true; return true;}"
            :on-success="uploadEditContImage">
            <img v-if="editData.title" :src="editData.title" class="image"/>
            <i v-else v-loading="editImgLoading" class="el-icon-plus uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="有效时间：">
          <el-date-picker
            v-model="editData.timevalue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div class="topic-image-picker">
        <el-input placeholder="链接地址" v-model="editData.link.meta.page_url">
          <template slot="prepend">跳转链接</template>
        </el-input>
      </div>
      <control-page @select="onSetLink" :params="{branchCode: topic.branchCode}"></control-page>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeEditContent">取 消</el-button>
        <el-button size="small" type="primary" @click="add_confirmEdit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import base from "views/apps/topic-prop-components/base.vue";
  import event_bus from "utils/eventbus"

  export default {
    extends: base,
    contentDefault: {
      list: [
        {
          title: 'http://upload.ybm100.com/ybm/app/layout/快捷入口/3faadb73-cd5a-4567-9176-bdb75a14e64c.png',
          entry: '旗舰店1',
          frontColor: '#000000',
          operation: '1',
          timevalue: [new Date("2019-06-28T03:42:31.942Z"), new Date("2030-06-27T03:42:31.942Z")],
          link: {
            meta: {
              id: 1,
              page_url: ''
            }
          }
        },
        {
          title: 'http://upload.ybm100.com/ybm/app/layout/快捷入口/48a38774-bbec-40f4-9963-16067c8aa1da.png',
          entry: '旗舰店2',
          frontColor: '#000000',
          operation: '1',
          timevalue: [new Date("2019-06-28T03:42:31.942Z"), new Date("2030-06-27T03:42:31.942Z")],
          link: {
            meta: {
              id: 2,
              page_url: ''
            }
          }
        },
        {
          title: 'http://upload.ybm100.com/ybm/app/layout/快捷入口/06c30e6a-6c0b-4c0b-9720-07ec191638b7.png',
          entry: '旗舰店3',
          frontColor: '#000000',
          operation: '1',
          timevalue: [new Date("2019-06-28T03:42:31.942Z"), new Date("2030-06-27T03:42:31.942Z")],
          link: {
            meta: {
              id: 3,
              page_url: ""
            }
          }
        },
        {
          title: 'http://upload.ybm100.com/ybm/app/layout/快捷入口/4b6e4ea1-4bef-4fa3-901b-34d4d64aa645.png',
          entry: '旗舰店4',
          frontColor: '#000000',
          operation: '1',
          timevalue: [new Date("2019-06-28T03:42:31.942Z"), new Date("2030-06-27T03:42:31.942Z")],
          link: {
            meta: {
              id: 4,
              page_url: ""
            }
          }
        },
        {
          title: 'http://upload.ybm100.com/ybm/app/layout/快捷入口/2d40238a-1fba-4c7d-bffe-8a0c293786ea.png',
          entry: '旗舰店5',
          frontColor: '#000000',
          operation: '1',
          timevalue: [new Date("2019-06-28T03:42:31.942Z"), new Date("2030-06-27T03:42:31.942Z")],
          link: {
            meta: {
              id: 5,
              page_url: ""
            }
          }
        },
        {
          title: 'http://upload.ybm100.com/ybm/app/layout/快捷入口/ba0a5c10-e432-4ade-85e5-09d7b68c2f4f.png',
          entry: '旗舰店6',
          frontColor: '#000000',
          operation: '1',
          timevalue: [new Date("2019-06-28T03:42:31.942Z"), new Date("2030-06-27T03:42:31.942Z")],
          link: {
            meta: {
              id: 6,
              page_url: ""
            }
          }
        },
        {
          title: 'http://upload.ybm100.com/ybm/app/layout/快捷入口/ca05a3eb-140d-4973-a77e-f94c5ff97153.png',
          entry: '旗舰店7',
          frontColor: '#000000',
          operation: '1',
          timevalue: [new Date("2019-06-28T03:42:31.942Z"), new Date("2030-06-27T03:42:31.942Z")],
          link: {
            meta: {
              id: 7,
              page_url: ""
            }
          }
        }
      ],
      bgRes: '',
      color: '#ffffff',
      image: '',
      list_num: 1,
      pro_obj: {
        pro_type: "longBar",
        pro_auto: 0,
        pro_align_type: "center",
        default_color: "#ffffff",
        default_opacity: 30,
        active_color: "#555555",
        active_opacity: 100,
        component_name: "fastEntry", //区分模块的标识
      },
    },
    data() {
      return {
        loading: false,
        upImgLoading: false,
        editImgLoading: false,
        addPic: false,
        editContent: false,
        add_editContent: false,
        editCont: "",
        dataForm: {
          image: '',
        },
        editData: {
          title: '',
          entry: '',
          frontColor: '#000000',
          operation: '',
          timevalue: "",
          link: {
            meta: {
              page_url: ''
            }
          },
        },
        select_option: [
          {
            value: 1,
            label: '一个横排'
          }, {
            value: 2,
            label: '两个横排'
          }
        ],
        pro_options: [
          {
            value: 'longBar',
            label: '矩形'
          },
          {
            value: 'bar',
            label: '正方形'
          },
          {
            value: 'circle',
            label: '圆形'
          }
        ],
        pro_align_options: [
          {
            value: 'center',
            label: '居中'
          },
          {
            value: 'left',
            label: '左对齐'
          },
          {
            value: 'right',
            label: '右对齐'
          }
        ],
        pro_auto_options: [
          {
            value: 0,
            label: '不轮播'
          },
          {
            value: 3000,
            label: '3秒间隔'
          },
          {
            value: 4000,
            label: '4秒间隔'
          },
          {
            value: 5000,
            label: '5秒间隔'
          }
        ],
        pickerOptions: {
          shortcuts: [
            {
              text: "未来一周",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来三个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来六个月",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 180);
                picker.$emit("pick", [start, end]);
              }
            },
            {
              text: "未来一年",
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                end.setTime(start.getTime() + 3600 * 1000 * 24 * 365);
                picker.$emit("pick", [start, end]);
              }
            }
          ]
        },
      }
    },
    computed: {
      list() {
        var list = _.get(this, 'content.list')
        if (list) {
          this.$nextTick(function () {
            this.setSort()
          });
          return list
        } else {
          return [];
        }
      }
    },
    filters: {
      link(data) {
        if (!data.type) {
          return '';
        }
        return data.meta.page_url;
      }
    },
    methods: {
      change_default_height(val) {
        if (val === 2) {
          event_bus.$emit("change_default_height", "192pt")
        }
      },
      format_text(data) {
        if (!data) {
          return "<b style='color: red'>请设置时间</b>"
        }
        const _date = new Date().getTime();
        const start = new Date(data[0]).getTime();
        const end = new Date(data[1]).getTime();
        if (_date <= end && _date >= start) {
          return "<b style='color: #67C23A'>展示中</b>"
        } else if (_date < start) {
          return `<b style='color: #000000'>即将在${new Date(start).toLocaleDateString()}展示</b>`
        } else {
          return "<b style='color: #000000'>已过期</b>"
        }
      },
      formatTooltip(val) {
        return val / 100;
      },
      closeAddPic() {
        this.addPic = false;
      },
      onSetLink(link) {
        this.editData.link = link
      },
      toEdit(data, index, status) {
        if (status == '1') {
          //新建时清空
          this.editData.title = '';
          this.editData.timevalue = '';
          this.editData.entry = '';
          this.editData.frontColor = '#000';
          this.editData.link = {
            meta: {
              page_url: ''
            }
          };
        } else {
          this.editData.entry = data.entry;
          this.editData.timevalue = data.timevalue;
          this.editData.title = data.title;
          this.editData.frontColor = data.frontColor;
          this.editData.link = data.link;
        }

        this.editContent = true;
        this.nowData = data;
        this.nowIndex = index;
        this.isContEdit = true;
        this.addContPic = true;
      },
      toAdd() {
        this.isEdit = false;
        this.dataForm = {
          image: ''
        };
        this.addPic = true;
      },
      async onUploadImage(res, file) {
        this.upImgLoading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.dataForm.image = res.data.url
      },
      async uploadEditContImage(res, file) {
        this.editImgLoading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.editData.title = res.data.url
      },
      confirmImg() {
        if (!this.dataForm.image) {
          this.$message.warning('请上传图片');
          return false;
        }
        this.closeAddPic();
        if (this.isEdit) {
          this.currentData = {}
          this.list.splice(this.currentIndex, 1, this.currentData);
        } else {
          this.list.push(Object.assign({}, this.dataForm));
        }
      },
      closeEditContent() {
        this.editContent = false;
        this.add_editContent = false;
      },
      confirmEdit() {
        if (!this.editData.title) {
          this.$message.warning('请上传图片');
          return false;
        }

        if (!this.editData.link) {
          this.$message.warning('请填写链接');
          return false;
        }
        this.closeEditContent();
        if (this.isContEdit) {
          // entry: document.querySelector('.ql-editor').innerText,//只取文本
          this.psData = {
            title: this.editData.title,
            timevalue: this.editData.timevalue,
            entry: `${this.editData.entry}`,
            frontColor: this.editData.frontColor,
            link: this.editData.link
          }
          this.list.splice(this.nowIndex, 1, this.psData);
        } else {
          this.list.push(Object.assign([], this.list));
        }

      },
      add_confirmEdit() {
        if (!this.editData.title) {
          this.$message.warning('请上传图片');
          return false;
        }

        if (!this.editData.link) {
          this.$message.warning('请填写链接');
          return false;
        }
        this.closeEditContent();
        this.psData = {
          title: this.editData.title,
          entry: `${this.editData.entry}`,
          frontColor: this.editData.frontColor,
          link: this.editData.link,
          timevalue: this.editData.timevalue
        };
        this.list.push(this.psData);


      },
      onSelect(val, isEditEntry) {
        if (isEditEntry == 'editing') {
          if (val) {
            this.editData.frontColor = val;
          } else {
            this.editData.frontColor = "#000000";
          }
        } else {
          if (val) {
            this.content.bgRes = val;
          } else {
            this.content.bgRes = "#ffffff";
          }
        }
      },
      async onUploadImg(res, file) {
        this.loading = false;
        if (res.code !== 200) {
          this.$message({
            message: `[${res.code}]${res.msg}`,
            type: 'warning'
          })
          return;
        }
        this.content.bgRes = res.data.url;
      },
      imgOnclick() {
        this.content.bgRes = '#ffffff';

      },
      toColor16(str) {
        if (/^(rgb|RGB)/.test(str)) {
          var aColor = str.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
          var strHex = "#";
          for (var i = 0; i < aColor.length; i++) {
            var hex = Number(aColor[i]).toString(16);
            if (hex === "0") {
              hex += hex;
            }
            strHex += hex;
          }

          if (strHex.length !== 7) {
            strHex = str;
          }
          return strHex.toUpperCase();
        } else {
          return str;
        }
      }
    },
    watch: {
      add_editContent(new_val) {
        this.editData = {
          title: '',
          timevalue: '',
          entry: '',
          frontColor: '#000000',
          operation: '',
          link: {
            meta: {
              page_url: ''
            }
          }
        }
      }
    }
  }
</script>

<style lang="scss" rel="stylesheet/scss">


  .fast-entry {
    .container {
      display: flex;
      align-items: center;

      .img {
        width: 65%;

        img {
          display: block;
          width: 100%;
        }
      }

      .button-list {
        margin-left: 10px;
      }
    }

    .content-setting {
      color: #fff;
      background-color: #13c2c2;
      padding: 10px;
      text-align: center;
      font-size: 16px;
      margin-bottom: 10px;
    }

    .title-image {
      width: 64px;
      height: 64px;
    }

    .topic-image-upload {
      .image {
        display: block;
        width: 100%;
      }

      .uploader-icon {
        width: 200px;
        height: 200px;
        line-height: 200px;
        border: 1px solid $border-base;
        font-size: 50px;
      }
    }

    .entry-name {
      width: 70%;
    }

    .el-form-item {
      margin-bottom: 12px;
    }

    // single-upload
    .uploader-btn-state {
      text-align: center;
    }

    .topic-image-picker {
      padding: 10px 0;
    }

    .el-table {
      .cell {
        text-align: center;
        padding: 0;
      }

      th .cell {
        color: #606266;
      }
    }

    .banner-dialog {
      .el-dialog__body {
        padding-top: 10px;
      }

      .image {
        width: 64px;
        height: 64px;
      }
    }

    .no-img {
      font-size: 35px;
      display: block;
      color: #caccd0;
    }
  }

  .el-loading-spinner {
    top: auto !important;
    margin-top: auto !important;
  }

  .el-row {
    text-align: center;

    .title {
      text-align: left;
      line-height: 30px;
      background-color: #f2f2f2;
      margin: 10px 0;
      padding-left: 10px;
    }
  }
</style>


