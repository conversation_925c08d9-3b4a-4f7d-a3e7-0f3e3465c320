<template>
    <div>
        <div>
            <!--tab基础类配置组件-->
            <tab-switch :content="content"
                        @add_list="add_list"
                        @change_color="change_color"
                        @change_spread="change_spread"
            ></tab-switch>
            <el-row :gutter="20">
                <div class="title">配置选项卡内容</div>
                <el-col :span="6">
                    <el-select v-model="activeName"
                               size="mini"
                               placeholder="请选择">
                        <el-option
                                v-for="item in content.goods_list"
                                :key="item.name"
                                :label="item.name"
                                :value="item.name">
                        </el-option>
                    </el-select>
                </el-col>

                <el-col :span="6">
                    <el-button size="mini"
                               type="primary"
                               :disabled="!can_config"
                               @click="tabIsShow=true">配置
                    </el-button>
                </el-col>
                <el-col :span="6">
                    <el-button size="mini"
                               type="danger"
                               :disabled="!can_config"
                               @click="remove_tab">删除
                    </el-button>
                </el-col>

                <el-col :span="6">
                    <el-input v-model="cur_tab_name"
                              :disabled="!can_config"
                              size="mini"
                              placeholder="修改当前标签名称"></el-input>
                </el-col>
            </el-row>


            <el-row :gutter="20">
                <div class="title">选项卡顺序调整</div>
                <el-col :span="6">
                    <span class="demonstration">选择顺序</span>
                    <div>
                        <el-select v-model="content.handle_sort_index"
                                   size="mini"
                                   :disabled="!can_config"
                                   placeholder="请选择">
                            <el-option
                                    v-for="index in content.goods_list.length"
                                    :key="index"
                                    :label="index"
                                    :value="index">
                            </el-option>
                        </el-select>
                    </div>
                </el-col>
                <el-col :span="6">
                    <span class="demonstration">当前顺序</span>
                    <div v-show="!can_config">
                        <el-input
                                disabled
                                value=""
                                size="mini"></el-input>
                    </div>
                    <div v-show="can_config">
                        <el-input
                                disabled
                                :value="currentTabIndex+1"
                                size="mini"></el-input>
                    </div>
                </el-col>
            </el-row>
        </div>
        <!--选项卡内容-->
        <el-dialog
                :title="activeName"
                :visible.sync="tabIsShow"
                :show-close="false"
                width="50%"
                :before-close="handleClose">
            <el-tabs v-model="cur_config_name" type="card" @tab-click="">
                <el-tab-pane label="商品列表配置" name="goods_config">
                    <div v-if="can_config">
                        <el-row :gutter="20">
                            <div class="title">选择列表模式</div>
                            <el-col :span="24">
                                <div style="text-align: left">
                                    <el-radio-group v-model="content.goods_list[currentTabIndex].type"
                                                    v-if="content.goods_list[currentTabIndex]">
                                        <el-radio :label="index" v-for="(item,index) in typeList" :key="index">{{item}}
                                        </el-radio>
                                    </el-radio-group>
                                </div>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <div v-if="content.goods_list[currentTabIndex].list.length">
                                <div class="title" style="margin-bottom: 0">预览已选择的商品</div>
                                <el-col :span="24">
                                    <el-table :data="content.goods_list[currentTabIndex].list"
                                              height="300"
                                              ref="multipleTable">
                                        <el-table-column label="药名">
                                            <template slot-scope="scope">
                                                <span v-if="scope.row.productName">{{scope.row.productName}}</span>
                                                <span v-else>{{scope.row.showName}}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="id" label="药品ID">

                                        </el-table-column>
                                        <el-table-column prop="availableQty" label="库存">

                                        </el-table-column>
                                    </el-table>
                                </el-col>
                            </div>
                        </el-row>
                        <el-row :gutter="20">
                            <div v-if="content.goods_list[currentTabIndex].goods_group.length">
                                <div class="title" style="margin-bottom: 0">预览已选商品组</div>
                                <el-col :span="24">
                                    <el-table :data="content.goods_list[currentTabIndex].goods_group"
                                              height="120"
                                              ref="multipleTable">
                                        <el-table-column prop="name" label="组名">
                                        </el-table-column>
                                        <el-table-column prop="code" label="编号">
                                        </el-table-column>
                                        <el-table-column prop="branchCode" label="区域号">
                                        </el-table-column>
                                        <!--<el-table-column label="展示ID">-->
                                        <!--<template slot-scope="scope">-->
                                        <!--{{scope.row.code}}-->
                                        <!--</template>-->
                                        <!--</el-table-column>-->
                                    </el-table>
                                </el-col>
                            </div>
                        </el-row>
                        <el-row :gutter="20">
                            <div>
                                <el-col :span="24">
                                    <div style="color: red;margin-top: 10px">
                                        <!--选择商品组和选择商品只能生效一个,设置一个自动清除另一个-->
                                    </div>
                                    <!--选择商品-->
                                    <all-link ref="all_link"
                                              @select="onSetLink"
                                              :tabs="tabs_goods_option"
                                              :params="{
                                                goodsGroup: {
                                                    seledShow: false,
                                                    minSel: 1,
                                                    search: {
                                                        state: 1,
                                                        branchCode: topic.branchCode
                                                    }
                                                }
                                            }"></all-link>
                                </el-col>
                            </div>
                        </el-row>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="单图配置" name="banner_config">
                    <div v-if="can_config">
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <img :src="content.goods_list[this.currentTabIndex].img_url" alt="">
                            </el-col>
                            <el-col :span="24">
                              <el-col :span="24">
                                <el-upload
                                  class="upload-demo"
                                  ref="upload"
                                  accept="image/jpeg,image/jpg,image/png,image/gif"
                                  :file-list="content.goods_list[this.currentTabIndex].img_url_list"
                                  :on-success="onUploadImg"
                                  :on-remove="handleRemoveImg"
                                  list-type="picture">
                                  <el-button size="small" type="primary">点击上传</el-button>
                                  <div slot="tip" class="el-upload__tip">支持类型：png/jpg/jpeg/gif</div>
                                </el-upload>
                              </el-col>
                            </el-col>
                        </el-row>
                    </div>
                </el-tab-pane>
            </el-tabs>

            <div slot="footer" class="dialog-footer">
                <el-button @click="tabIsShow = false" size="mini">取 消</el-button>
                <el-button type="primary" @click="tabIsShow = false" size="mini">确 定</el-button>
            </div>
        </el-dialog>


    </div>
</template>

<script>
    import base from "views/apps/topic-prop-components/base";
    import tabSwitch from './tab'
    import {common} from 'api'

    export default {
        components: {
            tabSwitch
        },
        extends: base,
        contentDefault: {
            goods_list: [
                {
                    name: "高毛首页",
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                },
                {
                    name: "心脑血管",
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                },
                {
                    name: "抗菌消炎",
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                },
                {
                    name: "消化系统",
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                },
                {
                    name: "五官用药",
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                },
                {
                    name: "皮肤外用",
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                },
                {
                    name: "感冒用药",
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                },
                {
                    name: "滋补保健",
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                },
                {
                    name: "妇科用药",
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                },
                {
                    name: "风湿骨痛",
                    goods_group: [],
                    list: [],
                    type: 0,
                    img_url_list:[],
                },

            ],
            tabs: {
                default_font: "#ffffff",
                active_font: "#ffffff",
                active_line: '#FFD700 ',
                tab_bg: "#FF7777",
                spread_color: "#E85B5B",
                is_spread: true,
                split_color: "#ffffff",
                active_bg: "#FFD700",
            },
            list_color:"#FAFAFA",
            active_name: "",
            handle_sort_index: null,
            is_reload: false
        },
        created() {
            this.content.handle_sort_index = null;
            this.cur_tab_name = this.activeName
            this.content.active_name=this.content.goods_list[0].name
        },
        data() {
            return {
                up_loading: false,
                cur_tab_name: "",
                cur_config_name: "goods_config",
                tabIsShow: false,
                activeName: "",
                currentTabIndex: 0,
                tabs_goods_option: [
                    {label: '商品组', value: 'goodsGroup'}
                ],
                typeList: ['列表模式', '大图模式'],
            }
        },
        watch: {
            cur_tab_name(new_val) {
                if (new_val) {
                    if (this.content.goods_list[this.currentTabIndex].name !== new_val) {
                        this.content.goods_list[this.currentTabIndex].name = new_val
                        this.activeName = new_val
                    }
                }
            },
            tabIsShow(new_val) {
                if (!new_val) {
                    this.$refs.all_link.close_select()
                }
            },
            activeName(new_val, old_val) {
                if (new_val) {
                    this.content.active_name = new_val;
                    this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.goods_list);
                    this.cur_tab_name = new_val;
                    this.content.handle_sort_index = null
                }
            },
            "content.handle_sort_index"(new_val, old_val) {
                if (new_val) {
                    this.content.goods_list.splice(new_val - 1, 0, this.content.goods_list.splice(this.currentTabIndex, 1)[0])
                    this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.goods_list);
                }
            }
        },
        computed: {
            can_config() {
                if (this.content.goods_list.length && this.activeName) {
                    return true
                } else {
                    return false
                }
            },
            group_banner_list() {
                let list = _.get(this, 'content.group_banner.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
            banner_list() {
                let list = _.get(this, 'content.banner.list')
                if (list) {
                    return list
                } else {
                    return [];
                }
            },
        },
        methods: {
            change_spread(is_spread) {
                if (is_spread === "spread") {
                    this.content.tabs.is_spread = true
                } else {
                    this.content.tabs.is_spread = false
                }
            },
          async onUploadImg(res, file) {
            this.loading = false;
            if (res.code !== 200) {
              this.$message({
                message: `[${res.code}]${res.msg}`,
                type: 'warning'
              });
              return;
            }
            this.content.goods_list[this.currentTabIndex].img_url_list.push({name:file.name,url:res.data.url})
          },
          handleRemoveImg(file, fileList){
            this.content.goods_list[this.currentTabIndex].img_url_list=fileList.map(
              item=>{
                const obj={};
                obj.name=item.name;
                obj.url=item.url;
                return obj
              }
            );
          },
            handleClose(done) {
                this.$confirm('您已配置完此选项卡了么？')
                    .then(_ => {
                        done();
                    })
                    .catch(_ => {
                    });
            },
            change_color(type, color) {
                switch (type) {
                    case "default_font":
                        this.content.tabs.color = color;
                        break;
                    case "active_font":
                        this.content.tabs.active_font = color;
                        break;
                    case "active_line":
                        this.content.tabs.active_line = color;
                        break;
                    case "tab_bg":
                        this.content.tabs.tab_bg = color;
                        break;
                    case "spread_color":
                        this.content.tabs.spread_color = color;
                        break;
                    case "active_bg":
                        this.content.tabs.active_bg = color;
                        break;
                    case "split_color":
                        this.content.tabs.split_color = color;
                        break;
                    case "list_color":
                        this.content.list_color = color;
                        break;
                    default :
                        break
                }
            },
            add_list(obj) {
                this.content.goods_list.push(obj)
            },
            tab_click() {
                this.currentTabIndex = common.getRepeatResult('name', this.activeName, this.content.goods_list);
            },
            remove_tab() {
                const index = common.getRepeatResult('name', this.activeName, this.content.goods_list);
                this.content.goods_list.splice(index, 1)
                if (this.can_config) {
                    this.activeName = this.content.goods_list[0].name
                } else {
                    this.activeName = ""
                }
            },
            onSetLink(link) {
                function handle_arr(arr = []) {
                    return arr.map((item) => {
                        let obj = {};
                        obj.init_img_url = item.init_img_url;
                        obj.imageUrl = item.imageUrl;
                        obj.productName = item.productName;
                        obj.showName = item.showName;
                        obj.mediumPackageTitle = item.mediumPackageTitle;
                        obj.fob = item.fob;
                        obj.id = item.id;
                        obj.availableQty = item.availableQty;
                        return obj
                    });
                }

                if (link.tag === "goods" || link.tag === "importGoods") {
                    let _self_arr = handle_arr(link.data);
                    // if (this.content.goods_list[this.currentTabIndex].list.length > 0) {
                    //   this.content.goods_list[this.currentTabIndex].list =
                    //       [...common.removeRepeat(this.content.goods_list[this.currentTabIndex].list, _self_arr)]
                    // } else {
                    //   this.content.goods_list[this.currentTabIndex].list = [..._self_arr]
                    // }
                    this.content.goods_list[this.currentTabIndex].list = [..._self_arr]
                    this.content.goods_list[this.currentTabIndex].goods_group = []
                } else if (link.tag === "goodsGroup") {
                    let obj = {};
                    obj.name = link.data.name;
                    obj.branchCode = link.data.branchCode;
                    obj.code = link.data.code;
                    this.content.goods_list[this.currentTabIndex].goods_group.splice(0, 1, obj);
                    this.content.goods_list[this.currentTabIndex].list = []
                }
                this.re_load()

            },

            re_load() {
                this.content.is_reload = true
                setTimeout(() => {
                    this.content.is_reload = false
                }, 2000)
            },

            //备用函数
            list_handleDelete(row) {
                const index = this.content.goods_list[this.currentTabIndex].list.indexOf(row)
                this.content.goods_list[this.currentTabIndex].list.splice(index, 1)
            },
            group_delete(row) {
                const index = this.content.goods_list[this.currentTabIndex].goods_group.indexOf(row);
                this.content.goods_list[this.currentTabIndex].goods_group.splice(index, 1)

            },
        }
    }
</script>

<style scoped lang="scss">
    .el-row {
        text-align: center;

        .title {
            text-align: left;
            line-height: 30px;
            /*background-color: #f2f2f2;*/
            color: #13c2c2;
            padding-left: 10px;
            margin: 10px;
        }
    }

</style>
